import React, { useState, useEffect } from 'react';
import {
  Users,
  Search,
  Plus,
  Edit3,
  Trash2,
  Eye,
  CreditCard,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Phone,
  MapPin,
  Filter,
  Download,
  RefreshCw,
  UserPlus,
  Wallet,
  AlertCircle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { useApp, Customer } from '../context/AppContext';

interface Transaction {
  id: string;
  customerId: string;
  type: 'credit' | 'debit' | 'purchase' | 'payment';
  amount: number;
  description: string;
  date: Date;
  balanceAfter: number;
}

const CustomersAccounts: React.FC = () => {
  const {
    customers,
    addCustomer,
    updateCustomer,
    deleteCustomer,
    updateCustomerBalance
  } = useApp();

  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showTransactionModal, setShowTransactionModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [filterBalance, setFilterBalance] = useState<'all' | 'positive' | 'negative' | 'zero'>('all');
  const [sortBy, setSortBy] = useState<'name' | 'balance' | 'lastTransaction'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // إحصائيات العملاء
  const totalCustomers = customers.length;
  const totalBalance = customers.reduce((sum, customer) => sum + customer.balance, 0);
  const positiveBalances = customers.filter(c => c.balance > 0).length;
  const negativeBalances = customers.filter(c => c.balance < 0).length;
  const averageBalance = totalCustomers > 0 ? totalBalance / totalCustomers : 0;

  // فلترة وترتيب العملاء
  const filteredCustomers = customers
    .filter(customer => {
      const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           customer.phone.includes(searchTerm) ||
                           customer.address.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesFilter = filterBalance === 'all' ||
                           (filterBalance === 'positive' && customer.balance > 0) ||
                           (filterBalance === 'negative' && customer.balance < 0) ||
                           (filterBalance === 'zero' && customer.balance === 0);

      return matchesSearch && matchesFilter;
    })
    .sort((a, b) => {
      let comparison = 0;
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'balance':
          comparison = a.balance - b.balance;
          break;
        case 'lastTransaction':
          const aDate = a.lastTransaction || new Date(0);
          const bDate = b.lastTransaction || new Date(0);
          comparison = aDate.getTime() - bDate.getTime();
          break;
      }
      return sortOrder === 'asc' ? comparison : -comparison;
    });

  const formatCurrency = (amount: number) => {
    return `${amount.toFixed(2)} دج`;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('fr-FR');
  };

  const getBalanceColor = (balance: number) => {
    if (balance > 0) return 'text-green-400';
    if (balance < 0) return 'text-red-400';
    return 'text-gray-400';
  };

  const getBalanceIcon = (balance: number) => {
    if (balance > 0) return <TrendingUp className="w-4 h-4 text-green-400" />;
    if (balance < 0) return <TrendingDown className="w-4 h-4 text-red-400" />;
    return <DollarSign className="w-4 h-4 text-gray-400" />;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-3 rounded-2xl shadow-lg">
              <Users className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">حسابات العملاء</h1>
              <p className="text-gray-300">إدارة شاملة لحسابات وأرصدة العملاء</p>
            </div>
          </div>

          <div className="flex items-center space-x-4 space-x-reverse">
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-gradient-to-r from-green-500 to-emerald-400 hover:scale-105 transform transition-all duration-300 px-6 py-3 rounded-xl font-bold text-white shadow-2xl hover:shadow-green-500/25 flex items-center space-x-2 space-x-reverse"
            >
              <UserPlus className="w-5 h-5" />
              <span>عميل جديد</span>
            </button>

            <button className="bg-white/10 backdrop-blur-lg border border-white/20 hover:border-white/40 px-4 py-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse text-white">
              <Download className="w-5 h-5" />
              <span>تصدير</span>
            </button>

            <button className="bg-white/10 backdrop-blur-lg border border-white/20 hover:border-white/40 px-4 py-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse text-white">
              <RefreshCw className="w-5 h-5" />
              <span>تحديث</span>
            </button>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">إجمالي العملاء</p>
                <p className="text-3xl font-bold text-white">{totalCustomers}</p>
              </div>
              <div className="bg-blue-500/20 p-3 rounded-xl">
                <Users className="w-6 h-6 text-blue-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">إجمالي الأرصدة</p>
                <p className={`text-3xl font-bold ${getBalanceColor(totalBalance)}`}>
                  {formatCurrency(totalBalance)}
                </p>
              </div>
              <div className="bg-green-500/20 p-3 rounded-xl">
                <Wallet className="w-6 h-6 text-green-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">أرصدة موجبة</p>
                <p className="text-3xl font-bold text-green-400">{positiveBalances}</p>
              </div>
              <div className="bg-green-500/20 p-3 rounded-xl">
                <TrendingUp className="w-6 h-6 text-green-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">أرصدة سالبة</p>
                <p className="text-3xl font-bold text-red-400">{negativeBalances}</p>
              </div>
              <div className="bg-red-500/20 p-3 rounded-xl">
                <TrendingDown className="w-6 h-6 text-red-400" />
              </div>
            </div>
          </div>
        </div>

        {/* شريط البحث والفلاتر */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 mb-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
            {/* البحث */}
            <div className="lg:col-span-2">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="البحث بالاسم، الهاتف، أو العنوان..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full bg-white/10 border border-white/20 rounded-xl px-12 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 transition-all duration-300"
                />
              </div>
            </div>

            {/* فلتر الرصيد */}
            <div>
              <select
                value={filterBalance}
                onChange={(e) => setFilterBalance(e.target.value as any)}
                className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-blue-400 transition-all duration-300"
              >
                <option value="all">جميع الأرصدة</option>
                <option value="positive">أرصدة موجبة</option>
                <option value="negative">أرصدة سالبة</option>
                <option value="zero">أرصدة صفر</option>
              </select>
            </div>

            {/* الترتيب */}
            <div>
              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [field, order] = e.target.value.split('-');
                  setSortBy(field as any);
                  setSortOrder(order as any);
                }}
                className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-blue-400 transition-all duration-300"
              >
                <option value="name-asc">الاسم (أ-ي)</option>
                <option value="name-desc">الاسم (ي-أ)</option>
                <option value="balance-desc">الرصيد (الأعلى)</option>
                <option value="balance-asc">الرصيد (الأقل)</option>
                <option value="lastTransaction-desc">آخر معاملة</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* جدول العملاء */}
      <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-white/5">
              <tr>
                <th className="text-right py-4 px-6 text-gray-300 font-semibold">العميل</th>
                <th className="text-right py-4 px-6 text-gray-300 font-semibold">الهاتف</th>
                <th className="text-right py-4 px-6 text-gray-300 font-semibold">العنوان</th>
                <th className="text-right py-4 px-6 text-gray-300 font-semibold">الرصيد</th>
                <th className="text-right py-4 px-6 text-gray-300 font-semibold">آخر معاملة</th>
                <th className="text-center py-4 px-6 text-gray-300 font-semibold">الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {filteredCustomers.length === 0 ? (
                <tr>
                  <td colSpan={6} className="text-center py-12">
                    <div className="flex flex-col items-center space-y-4">
                      <Users className="w-16 h-16 text-gray-400" />
                      <p className="text-gray-400 text-lg">لا توجد عملاء</p>
                      <button
                        onClick={() => setShowAddModal(true)}
                        className="bg-gradient-to-r from-blue-500 to-purple-600 hover:scale-105 transform transition-all duration-300 px-6 py-3 rounded-xl font-bold text-white shadow-lg"
                      >
                        إضافة عميل جديد
                      </button>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredCustomers.map((customer) => (
                  <tr key={customer.id} className="border-t border-white/10 hover:bg-white/5 transition-all duration-300">
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <div className="bg-gradient-to-r from-blue-500 to-purple-600 w-10 h-10 rounded-full flex items-center justify-center">
                          <span className="text-white font-bold text-sm">
                            {customer.name.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <p className="text-white font-semibold">{customer.name}</p>
                          <p className="text-gray-400 text-sm">عميل منذ {new Date(customer.created_at).toLocaleDateString('ar-SA')}</p>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-2 space-x-reverse text-gray-300">
                        <Phone className="w-4 h-4" />
                        <span>{customer.phone}</span>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-2 space-x-reverse text-gray-300">
                        <MapPin className="w-4 h-4" />
                        <span className="truncate max-w-xs">{customer.address}</span>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        {getBalanceIcon(customer.balance)}
                        <span className={`font-bold ${getBalanceColor(customer.balance)}`}>
                          {formatCurrency(customer.balance)}
                        </span>
                      </div>
                    </td>
                    <td className="py-4 px-6 text-gray-300">
                      {formatDate(new Date(customer.updated_at))}
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center justify-center space-x-2 space-x-reverse">
                        <button
                          onClick={() => {
                            setSelectedCustomer(customer);
                            setShowDetailsModal(true);
                          }}
                          className="bg-blue-500/20 hover:bg-blue-500/30 p-2 rounded-lg transition-all duration-300 hover:scale-110"
                          title="عرض التفاصيل"
                        >
                          <Eye className="w-4 h-4 text-blue-400" />
                        </button>

                        <button
                          onClick={() => {
                            setSelectedCustomer(customer);
                            setShowTransactionModal(true);
                          }}
                          className="bg-green-500/20 hover:bg-green-500/30 p-2 rounded-lg transition-all duration-300 hover:scale-110"
                          title="إضافة معاملة"
                        >
                          <CreditCard className="w-4 h-4 text-green-400" />
                        </button>

                        <button
                          onClick={() => {
                            setSelectedCustomer(customer);
                            setShowEditModal(true);
                          }}
                          className="bg-yellow-500/20 hover:bg-yellow-500/30 p-2 rounded-lg transition-all duration-300 hover:scale-110"
                          title="تعديل"
                        >
                          <Edit3 className="w-4 h-4 text-yellow-400" />
                        </button>

                        <button
                          onClick={async () => {
                            if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
                              try {
                                await deleteCustomer(customer.id);
                              } catch (error) {
                                alert('حدث خطأ في حذف العميل');
                              }
                            }
                          }}
                          className="bg-red-500/20 hover:bg-red-500/30 p-2 rounded-lg transition-all duration-300 hover:scale-110"
                          title="حذف"
                        >
                          <Trash2 className="w-4 h-4 text-red-400" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* مودال إضافة عميل جديد */}
      {showAddModal && (
        <AddCustomerModal
          onClose={() => setShowAddModal(false)}
          onAdd={async (customerData) => {
            try {
              await addCustomer(customerData);
              setShowAddModal(false);
            } catch (error) {
              alert('حدث خطأ في إضافة العميل');
            }
          }}
        />
      )}

      {/* مودال تعديل العميل */}
      {showEditModal && selectedCustomer && (
        <EditCustomerModal
          customer={selectedCustomer}
          onClose={() => {
            setShowEditModal(false);
            setSelectedCustomer(null);
          }}
          onUpdate={async (updatedCustomer) => {
            try {
              await updateCustomer(updatedCustomer);
              setShowEditModal(false);
              setSelectedCustomer(null);
            } catch (error) {
              alert('حدث خطأ في تحديث العميل');
            }
          }}
        />
      )}

      {/* مودال إضافة معاملة */}
      {showTransactionModal && selectedCustomer && (
        <TransactionModal
          customer={selectedCustomer}
          onClose={() => {
            setShowTransactionModal(false);
            setSelectedCustomer(null);
          }}
          onTransaction={async (transaction) => {
            try {
              setTransactions([...transactions, transaction]);
              // تحديث رصيد العميل باستخدام دالة updateCustomerBalance
              await updateCustomerBalance(transaction.customerId, transaction.balanceAfter);
              setShowTransactionModal(false);
              setSelectedCustomer(null);
            } catch (error) {
              alert('حدث خطأ في تحديث رصيد العميل');
            }
          }}
        />
      )}

      {/* مودال تفاصيل العميل */}
      {showDetailsModal && selectedCustomer && (
        <CustomerDetailsModal
          customer={selectedCustomer}
          transactions={transactions.filter(t => t.customerId === selectedCustomer.id)}
          onClose={() => {
            setShowDetailsModal(false);
            setSelectedCustomer(null);
          }}
        />
      )}
    </div>
  );
};

// مودال إضافة عميل جديد
const AddCustomerModal: React.FC<{
  onClose: () => void;
  onAdd: (customerData: Omit<Customer, 'id' | 'created_at' | 'updated_at'>) => void;
}> = ({ onClose, onAdd }) => {
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    address: '',
    email: '',
    balance: 0,
    credit_limit: 0
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim() || !formData.phone.trim()) return;

    const customerData = {
      name: formData.name.trim(),
      phone: formData.phone.trim(),
      address: formData.address.trim(),
      email: formData.email.trim(),
      balance: formData.balance,
      credit_limit: formData.credit_limit,
      is_active: true
    };

    onAdd(customerData);
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl border border-white/20 w-full max-w-md shadow-2xl">
        <div className="p-6 border-b border-white/10">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-white flex items-center space-x-3 space-x-reverse">
              <UserPlus className="w-6 h-6 text-green-400" />
              <span>إضافة عميل جديد</span>
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors duration-300"
            >
              <XCircle className="w-6 h-6" />
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div>
            <label className="block text-gray-300 text-sm font-semibold mb-2">
              اسم العميل *
            </label>
            <input
              type="text"
              required
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 transition-all duration-300"
              placeholder="أدخل اسم العميل"
            />
          </div>

          <div>
            <label className="block text-gray-300 text-sm font-semibold mb-2">
              رقم الهاتف *
            </label>
            <input
              type="tel"
              required
              value={formData.phone}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 transition-all duration-300"
              placeholder="أدخل رقم الهاتف"
            />
          </div>

          <div>
            <label className="block text-gray-300 text-sm font-semibold mb-2">
              البريد الإلكتروني
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 transition-all duration-300"
              placeholder="أدخل البريد الإلكتروني"
            />
          </div>

          <div>
            <label className="block text-gray-300 text-sm font-semibold mb-2">
              العنوان
            </label>
            <textarea
              value={formData.address}
              onChange={(e) => setFormData({ ...formData, address: e.target.value })}
              className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 transition-all duration-300 resize-none"
              placeholder="أدخل العنوان"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-gray-300 text-sm font-semibold mb-2">
                الرصيد الابتدائي (دج)
              </label>
              <input
                type="number"
                step="0.01"
                value={formData.balance}
                onChange={(e) => setFormData({ ...formData, balance: parseFloat(e.target.value) || 0 })}
                className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 transition-all duration-300"
                placeholder="0.00"
              />
            </div>

            <div>
              <label className="block text-gray-300 text-sm font-semibold mb-2">
                حد الائتمان (دج)
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                value={formData.credit_limit}
                onChange={(e) => setFormData({ ...formData, credit_limit: parseFloat(e.target.value) || 0 })}
                className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 transition-all duration-300"
                placeholder="0.00"
              />
            </div>
          </div>

          <div className="flex space-x-4 space-x-reverse pt-4">
            <button
              type="submit"
              className="flex-1 bg-gradient-to-r from-green-500 to-emerald-400 hover:scale-105 transform transition-all duration-300 py-3 rounded-xl font-bold text-white shadow-lg"
            >
              إضافة العميل
            </button>
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-white/10 border border-white/20 hover:border-white/40 py-3 rounded-xl font-bold text-white transition-all duration-300"
            >
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// مودال تعديل العميل
const EditCustomerModal: React.FC<{
  customer: Customer;
  onClose: () => void;
  onUpdate: (customer: Customer) => void;
}> = ({ customer, onClose, onUpdate }) => {
  const [formData, setFormData] = useState({
    name: customer.name,
    phone: customer.phone,
    address: customer.address
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim() || !formData.phone.trim()) return;

    const updatedCustomer: Customer = {
      ...customer,
      name: formData.name.trim(),
      phone: formData.phone.trim(),
      address: formData.address.trim()
    };

    onUpdate(updatedCustomer);
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl border border-white/20 w-full max-w-md shadow-2xl">
        <div className="p-6 border-b border-white/10">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-white flex items-center space-x-3 space-x-reverse">
              <Edit3 className="w-6 h-6 text-yellow-400" />
              <span>تعديل بيانات العميل</span>
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors duration-300"
            >
              <XCircle className="w-6 h-6" />
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div>
            <label className="block text-gray-300 text-sm font-semibold mb-2">
              اسم العميل *
            </label>
            <input
              type="text"
              required
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 transition-all duration-300"
            />
          </div>

          <div>
            <label className="block text-gray-300 text-sm font-semibold mb-2">
              رقم الهاتف *
            </label>
            <input
              type="tel"
              required
              value={formData.phone}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 transition-all duration-300"
            />
          </div>

          <div>
            <label className="block text-gray-300 text-sm font-semibold mb-2">
              العنوان
            </label>
            <textarea
              value={formData.address}
              onChange={(e) => setFormData({ ...formData, address: e.target.value })}
              className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 transition-all duration-300 resize-none"
              rows={3}
            />
          </div>

          <div className="flex space-x-4 space-x-reverse pt-4">
            <button
              type="submit"
              className="flex-1 bg-gradient-to-r from-yellow-500 to-orange-400 hover:scale-105 transform transition-all duration-300 py-3 rounded-xl font-bold text-white shadow-lg"
            >
              حفظ التعديلات
            </button>
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-white/10 border border-white/20 hover:border-white/40 py-3 rounded-xl font-bold text-white transition-all duration-300"
            >
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// مودال إضافة معاملة
const TransactionModal: React.FC<{
  customer: Customer;
  onClose: () => void;
  onTransaction: (transaction: Transaction) => void;
}> = ({ customer, onClose, onTransaction }) => {
  const [formData, setFormData] = useState({
    type: 'credit' as 'credit' | 'debit',
    amount: 0,
    description: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.amount <= 0) return;

    const amount = formData.type === 'debit' ? -formData.amount : formData.amount;
    const newBalance = customer.balance + amount;

    const transaction: Transaction = {
      id: Date.now().toString(),
      customerId: customer.id,
      type: formData.type,
      amount: formData.amount,
      description: formData.description.trim() || (formData.type === 'credit' ? 'إيداع' : 'سحب'),
      date: new Date(),
      balanceAfter: newBalance
    };

    onTransaction(transaction);
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl border border-white/20 w-full max-w-md shadow-2xl">
        <div className="p-6 border-b border-white/10">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-white flex items-center space-x-3 space-x-reverse">
              <CreditCard className="w-6 h-6 text-green-400" />
              <span>إضافة معاملة</span>
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors duration-300"
            >
              <XCircle className="w-6 h-6" />
            </button>
          </div>
          <div className="mt-4 p-4 bg-white/5 rounded-xl">
            <p className="text-gray-300 text-sm">العميل: <span className="text-white font-semibold">{customer.name}</span></p>
            <p className="text-gray-300 text-sm">الرصيد الحالي: <span className={`font-bold ${customer.balance >= 0 ? 'text-green-400' : 'text-red-400'}`}>{customer.balance.toFixed(2)} دج</span></p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div>
            <label className="block text-gray-300 text-sm font-semibold mb-2">
              نوع المعاملة
            </label>
            <div className="grid grid-cols-2 gap-4">
              <button
                type="button"
                onClick={() => setFormData({ ...formData, type: 'credit' })}
                className={`p-4 rounded-xl border-2 transition-all duration-300 ${
                  formData.type === 'credit'
                    ? 'border-green-400 bg-green-500/20 text-green-400'
                    : 'border-white/20 bg-white/5 text-gray-300 hover:border-green-400/50'
                }`}
              >
                <TrendingUp className="w-6 h-6 mx-auto mb-2" />
                <span className="font-semibold">إيداع</span>
              </button>
              <button
                type="button"
                onClick={() => setFormData({ ...formData, type: 'debit' })}
                className={`p-4 rounded-xl border-2 transition-all duration-300 ${
                  formData.type === 'debit'
                    ? 'border-red-400 bg-red-500/20 text-red-400'
                    : 'border-white/20 bg-white/5 text-gray-300 hover:border-red-400/50'
                }`}
              >
                <TrendingDown className="w-6 h-6 mx-auto mb-2" />
                <span className="font-semibold">سحب</span>
              </button>
            </div>
          </div>

          <div>
            <label className="block text-gray-300 text-sm font-semibold mb-2">
              المبلغ (دج) *
            </label>
            <input
              type="number"
              step="0.01"
              min="0.01"
              required
              value={formData.amount || ''}
              onChange={(e) => setFormData({ ...formData, amount: parseFloat(e.target.value) || 0 })}
              className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 transition-all duration-300"
              placeholder="أدخل المبلغ"
            />
          </div>

          <div>
            <label className="block text-gray-300 text-sm font-semibold mb-2">
              الوصف
            </label>
            <input
              type="text"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 transition-all duration-300"
              placeholder="وصف المعاملة (اختياري)"
            />
          </div>

          {formData.amount > 0 && (
            <div className="p-4 bg-white/5 rounded-xl">
              <p className="text-gray-300 text-sm">
                الرصيد بعد المعاملة:
                <span className={`font-bold ml-2 ${
                  (customer.balance + (formData.type === 'debit' ? -formData.amount : formData.amount)) >= 0
                    ? 'text-green-400'
                    : 'text-red-400'
                }`}>
                  {(customer.balance + (formData.type === 'debit' ? -formData.amount : formData.amount)).toFixed(2)} دج
                </span>
              </p>
            </div>
          )}

          <div className="flex space-x-4 space-x-reverse pt-4">
            <button
              type="submit"
              className="flex-1 bg-gradient-to-r from-green-500 to-emerald-400 hover:scale-105 transform transition-all duration-300 py-3 rounded-xl font-bold text-white shadow-lg"
            >
              تنفيذ المعاملة
            </button>
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-white/10 border border-white/20 hover:border-white/40 py-3 rounded-xl font-bold text-white transition-all duration-300"
            >
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// مودال تفاصيل العميل
const CustomerDetailsModal: React.FC<{
  customer: Customer;
  transactions: Transaction[];
  onClose: () => void;
}> = ({ customer, transactions, onClose }) => {
  const formatCurrency = (amount: number) => `${amount.toFixed(2)} دج`;
  const formatDate = (date: Date) => date.toLocaleDateString('fr-FR');
  const formatTime = (date: Date) => date.toLocaleTimeString('fr-FR');

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'credit': return <TrendingUp className="w-4 h-4 text-green-400" />;
      case 'debit': return <TrendingDown className="w-4 h-4 text-red-400" />;
      case 'purchase': return <CreditCard className="w-4 h-4 text-blue-400" />;
      case 'payment': return <CheckCircle className="w-4 h-4 text-purple-400" />;
      default: return <DollarSign className="w-4 h-4 text-gray-400" />;
    }
  };

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'credit': return 'text-green-400';
      case 'debit': return 'text-red-400';
      case 'purchase': return 'text-blue-400';
      case 'payment': return 'text-purple-400';
      default: return 'text-gray-400';
    }
  };

  const getTransactionLabel = (type: string) => {
    switch (type) {
      case 'credit': return 'إيداع';
      case 'debit': return 'سحب';
      case 'purchase': return 'مشتريات';
      case 'payment': return 'دفع';
      default: return 'معاملة';
    }
  };

  const sortedTransactions = [...transactions].sort((a, b) => b.date.getTime() - a.date.getTime());

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl border border-white/20 w-full max-w-4xl max-h-[90vh] shadow-2xl overflow-hidden">
        {/* Header */}
        <div className="p-6 border-b border-white/10">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-white flex items-center space-x-3 space-x-reverse">
              <Eye className="w-6 h-6 text-blue-400" />
              <span>تفاصيل العميل</span>
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors duration-300"
            >
              <XCircle className="w-6 h-6" />
            </button>
          </div>
        </div>

        <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* معلومات العميل */}
          <div className="p-6 border-b border-white/10">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* البيانات الأساسية */}
              <div className="space-y-4">
                <h3 className="text-xl font-bold text-white mb-4">البيانات الأساسية</h3>

                <div className="bg-white/5 rounded-xl p-4">
                  <div className="flex items-center space-x-3 space-x-reverse mb-3">
                    <div className="bg-gradient-to-r from-blue-500 to-purple-600 w-12 h-12 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-lg">
                        {customer.name.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <p className="text-white font-bold text-lg">{customer.name}</p>
                      <p className="text-gray-400 text-sm">عميل منذ {formatDate(customer.createdAt)}</p>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center space-x-3 space-x-reverse text-gray-300">
                      <Phone className="w-4 h-4" />
                      <span>{customer.phone}</span>
                    </div>

                    <div className="flex items-center space-x-3 space-x-reverse text-gray-300">
                      <MapPin className="w-4 h-4" />
                      <span>{customer.address || 'لا يوجد عنوان'}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* الإحصائيات المالية */}
              <div className="space-y-4">
                <h3 className="text-xl font-bold text-white mb-4">الإحصائيات المالية</h3>

                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-white/5 rounded-xl p-4 text-center">
                    <div className="flex items-center justify-center mb-2">
                      <Wallet className="w-6 h-6 text-blue-400" />
                    </div>
                    <p className="text-gray-300 text-sm mb-1">الرصيد الحالي</p>
                    <p className={`text-xl font-bold ${customer.balance >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {formatCurrency(customer.balance)}
                    </p>
                  </div>

                  <div className="bg-white/5 rounded-xl p-4 text-center">
                    <div className="flex items-center justify-center mb-2">
                      <CreditCard className="w-6 h-6 text-purple-400" />
                    </div>
                    <p className="text-gray-300 text-sm mb-1">إجمالي المشتريات</p>
                    <p className="text-xl font-bold text-white">
                      {formatCurrency(customer.totalPurchases)}
                    </p>
                  </div>

                  <div className="bg-white/5 rounded-xl p-4 text-center">
                    <div className="flex items-center justify-center mb-2">
                      <RefreshCw className="w-6 h-6 text-green-400" />
                    </div>
                    <p className="text-gray-300 text-sm mb-1">عدد المعاملات</p>
                    <p className="text-xl font-bold text-white">
                      {customer.transactionCount}
                    </p>
                  </div>

                  <div className="bg-white/5 rounded-xl p-4 text-center">
                    <div className="flex items-center justify-center mb-2">
                      <AlertCircle className="w-6 h-6 text-yellow-400" />
                    </div>
                    <p className="text-gray-300 text-sm mb-1">آخر معاملة</p>
                    <p className="text-sm font-bold text-white">
                      {customer.lastTransaction ? formatDate(customer.lastTransaction) : 'لا توجد'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* تاريخ المعاملات */}
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-white">تاريخ المعاملات</h3>
              <span className="text-gray-400 text-sm">{transactions.length} معاملة</span>
            </div>

            {sortedTransactions.length === 0 ? (
              <div className="text-center py-12">
                <CreditCard className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-400 text-lg">لا توجد معاملات</p>
              </div>
            ) : (
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {sortedTransactions.map((transaction) => (
                  <div
                    key={transaction.id}
                    className="bg-white/5 rounded-xl p-4 border border-white/10 hover:border-white/20 transition-all duration-300"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        {getTransactionIcon(transaction.type)}
                        <div>
                          <p className="text-white font-semibold">
                            {getTransactionLabel(transaction.type)}
                          </p>
                          <p className="text-gray-400 text-sm">
                            {transaction.description}
                          </p>
                        </div>
                      </div>

                      <div className="text-left">
                        <p className={`font-bold text-lg ${getTransactionColor(transaction.type)}`}>
                          {transaction.type === 'debit' ? '-' : '+'}
                          {formatCurrency(transaction.amount)}
                        </p>
                        <p className="text-gray-400 text-sm">
                          {formatDate(transaction.date)} - {formatTime(transaction.date)}
                        </p>
                        <p className="text-gray-300 text-xs">
                          الرصيد: {formatCurrency(transaction.balanceAfter)}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-white/10 bg-white/5">
          <div className="flex justify-end space-x-4 space-x-reverse">
            <button
              onClick={onClose}
              className="bg-white/10 border border-white/20 hover:border-white/40 px-6 py-3 rounded-xl font-bold text-white transition-all duration-300"
            >
              إغلاق
            </button>
            <button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:scale-105 transform transition-all duration-300 px-6 py-3 rounded-xl font-bold text-white shadow-lg">
              طباعة كشف الحساب
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomersAccounts;
