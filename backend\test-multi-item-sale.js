const axios = require('axios');

// اختبار بيع متعدد المنتجات
async function testMultiItemSale() {
  try {
    console.log('🧪 بدء اختبار البيع متعدد المنتجات...');

    // بيانات البيع مع عدة منتجات
    const saleData = {
      customer_id: null,
      items: [
        {
          product_id: '1', // منتج موجود
          product_name: 'منتج تجريبي 1',
          quantity: 2,
          unit_price: 50.00,
          discount: 0,
          total_price: 100.00
        },
        {
          product_id: '2', // منتج موجود آخر
          product_name: 'منتج تجريبي 2',
          quantity: 1,
          unit_price: 75.00,
          discount: 0,
          total_price: 75.00
        },
        {
          product_id: null, // منتج غير معروف
          product_name: 'منتج غير معروف',
          quantity: 1,
          unit_price: 25.00,
          discount: 0,
          total_price: 25.00
        }
      ],
      subtotal: 200.00,
      tax_amount: 0,
      discount_amount: 0,
      total_amount: 200.00,
      payment_method: 'cash',
      amount_paid: 200.00,
      change_amount: 0,
      balance_amount: 0,
      notes: 'اختبار بيع متعدد المنتجات',
      cashier_name: 'اختبار'
    };

    console.log('📋 بيانات البيع:', JSON.stringify(saleData, null, 2));

    // إرسال الطلب
    const response = await axios.post('http://localhost:5002/api/sales', saleData, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    console.log('✅ نجح البيع!');
    console.log('📄 رد الخادم:', response.data);

  } catch (error) {
    console.error('❌ فشل البيع:');
    
    if (error.response) {
      console.error('📋 كود الحالة:', error.response.status);
      console.error('📋 رسالة الخطأ:', error.response.data);
    } else if (error.request) {
      console.error('📋 لم يتم الحصول على رد من الخادم');
      console.error('📋 تفاصيل الطلب:', error.request);
    } else {
      console.error('📋 خطأ في إعداد الطلب:', error.message);
    }
    
    console.error('📋 Stack trace:', error.stack);
  }
}

// تشغيل الاختبار
testMultiItemSale();
