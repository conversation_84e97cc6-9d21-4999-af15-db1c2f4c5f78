const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

console.log('🔍 بدء تشغيل السيرفر...');

// Middleware
app.use(cors());
app.use(express.json());

console.log('✅ تم تحميل Middleware');

// اختبار الاتصال
app.get('/api/test', (req, res) => {
  console.log('📞 تم استلام طلب اختبار');
  res.json({ 
    message: '✅ Backend API يعمل بنجاح!',
    timestamp: new Date().toISOString()
  });
});

console.log('✅ تم تحميل مسار الاختبار');

// تحميل المسارات مع معالجة الأخطاء
try {
  console.log('🔍 تحميل مسار الفئات...');
  app.use('/api/categories', require('./routes/categories'));
  console.log('✅ تم تحميل مسار الفئات');
} catch (error) {
  console.error('❌ خطأ في تحميل مسار الفئات:', error.message);
}

try {
  console.log('🔍 تحميل مسار المنتجات...');
  app.use('/api/products', require('./routes/products'));
  console.log('✅ تم تحميل مسار المنتجات');
} catch (error) {
  console.error('❌ خطأ في تحميل مسار المنتجات:', error.message);
}

try {
  console.log('🔍 تحميل مسار العملاء...');
  app.use('/api/customers', require('./routes/customers'));
  console.log('✅ تم تحميل مسار العملاء');
} catch (error) {
  console.error('❌ خطأ في تحميل مسار العملاء:', error.message);
}

// معالج الأخطاء العام
app.use((error, req, res, next) => {
  console.error('❌ خطأ عام:', error);
  res.status(500).json({ error: 'خطأ داخلي في الخادم' });
});

app.listen(PORT, () => {
  console.log(`🚀 السيرفر التجريبي يعمل على المنفذ ${PORT}`);
  console.log(`🔗 API متاح على: http://localhost:${PORT}/api`);
});

// معالج الأخطاء غير المتوقعة
process.on('uncaughtException', (error) => {
  console.error('❌ خطأ غير متوقع:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ رفض غير معالج:', reason);
});
