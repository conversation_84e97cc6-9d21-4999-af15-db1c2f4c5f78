# الاختبار النهائي للحل المضمون
Write-Host "🎯 اختبار الحل النهائي..." -ForegroundColor Green

# 1. اختبار البيع السريع
Write-Host "1️⃣ اختبار البيع السريع..." -ForegroundColor Cyan

$quickSale = @{
    name = "منتج تجريبي نهائي"
    price = 75.50
} | ConvertTo-Json

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5002/api/sales/quick-unknown" -Method POST -Body $quickSale -ContentType "application/json"
    Write-Host "✅ نجح البيع السريع!" -ForegroundColor Green
    Write-Host "🆔 المعرف: $($response.product.id)" -ForegroundColor Yellow
    Write-Host "📝 الاسم: $($response.product.name)" -ForegroundColor Yellow
    Write-Host "💰 السعر: $($response.product.price) دج" -ForegroundColor Yellow
} catch {
    Write-Host "❌ فشل البيع السريع: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. اختبار عرض المنتجات غير المعروفة
Write-Host "`n2️⃣ اختبار عرض المنتجات غير المعروفة..." -ForegroundColor Cyan

try {
    $products = Invoke-RestMethod -Uri "http://localhost:5002/api/sales/unknown-products" -Method GET
    Write-Host "✅ تم العثور على $($products.Count) منتج غير معروف" -ForegroundColor Green
    
    if ($products.Count -gt 0) {
        Write-Host "📋 آخر منتج:" -ForegroundColor Yellow
        $latest = $products[0]
        Write-Host "   الاسم: $($latest.name)" -ForegroundColor White
        Write-Host "   السعر: $($latest.price) دج" -ForegroundColor White
        Write-Host "   التاريخ: $($latest.sale_date)" -ForegroundColor White
    }
} catch {
    Write-Host "❌ فشل عرض المنتجات: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 انتهى الاختبار!" -ForegroundColor Green
