@echo off
echo 🎯 تشغيل إصلاح مشكلة المنتجات غير المعروفة...
echo.

REM البحث عن psql في المسارات المختلفة
set PSQL_PATH=""
if exist "C:\Program Files\PostgreSQL\16\bin\psql.exe" set PSQL_PATH="C:\Program Files\PostgreSQL\16\bin\psql.exe"
if exist "C:\Program Files\PostgreSQL\15\bin\psql.exe" set PSQL_PATH="C:\Program Files\PostgreSQL\15\bin\psql.exe"
if exist "C:\Program Files\PostgreSQL\14\bin\psql.exe" set PSQL_PATH="C:\Program Files\PostgreSQL\14\bin\psql.exe"

if %PSQL_PATH%=="" (
    echo ❌ لم يتم العثور على PostgreSQL
    echo يرجى تشغيل الأمر التالي يدوياً في pgAdmin:
    echo.
    type fix_unknown_products.sql
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على PostgreSQL في: %PSQL_PATH%
echo.

REM تشغيل سكريبت الإصلاح
%PSQL_PATH% -h localhost -p 5432 -U postgres -d pos_system -f fix_unknown_products.sql

if %ERRORLEVEL%==0 (
    echo.
    echo ✅ تم تطبيق الإصلاح بنجاح!
    echo 🔮 يمكنك الآن بيع المنتجات غير المعروفة بدون مشاكل
) else (
    echo.
    echo ❌ حدث خطأ في تطبيق الإصلاح
    echo يرجى تشغيل الأوامر يدوياً في pgAdmin
)

echo.
pause
