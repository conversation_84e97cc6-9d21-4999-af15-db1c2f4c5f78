@echo off
REM سكريبت استيراد قاعدة البيانات إلى PostgreSQL
REM Database Import Script for PostgreSQL

echo ========================================
echo       استيراد قاعدة بيانات نقطة البيع
echo       POS Database Import Script
echo ========================================

REM إعداد متغيرات البيئة
set PGHOST=localhost
set PGPORT=5432
set PGUSER=postgres
set PGPASSWORD=toossar

echo.
echo 1. إنشاء قاعدة البيانات...
echo Creating database...

REM إنشاء قاعدة البيانات
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -c "DROP DATABASE IF EXISTS pos_system_db;"
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -c "CREATE DATABASE pos_system_db WITH OWNER = postgres ENCODING = 'UTF8';"

if %ERRORLEVEL% NEQ 0 (
    echo خطأ في إنشاء قاعدة البيانات!
    echo Error creating database!
    pause
    exit /b 1
)

echo تم إنشاء قاعدة البيانات بنجاح!
echo Database created successfully!

echo.
echo 2. استيراد البيانات...
echo Importing data...

REM تشغيل ملفات SQL بالترتيب
echo - إنشاء الهيكل الأساسي...
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d pos_system_db -f "01_create_database.sql"

echo - إنشاء الجداول الأساسية...
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d pos_system_db -f "02_core_tables.sql"

echo - إنشاء جداول المبيعات...
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d pos_system_db -f "03_sales_tables.sql"

echo - إنشاء المشغلات والوظائف...
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d pos_system_db -f "04_triggers_functions.sql"

echo - إنشاء Views للتقارير...
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d pos_system_db -f "05_views_reports.sql"

echo - إعداد الأمان والصلاحيات...
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d pos_system_db -f "06_security_permissions.sql"

if %ERRORLEVEL% NEQ 0 (
    echo خطأ في استيراد البيانات!
    echo Error importing data!
    pause
    exit /b 1
)

echo.
echo 3. التحقق من الاستيراد...
echo Verifying import...

REM التحقق من الجداول
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d pos_system_db -c "SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'pos_system';"

REM التحقق من البيانات التجريبية
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d pos_system_db -c "SELECT 'Products: ' || COUNT(*) FROM pos_system.products UNION ALL SELECT 'Categories: ' || COUNT(*) FROM pos_system.categories UNION ALL SELECT 'Customers: ' || COUNT(*) FROM pos_system.customers;"

echo.
echo ========================================
echo تم استيراد قاعدة البيانات بنجاح!
echo Database imported successfully!
echo ========================================
echo.
echo يمكنك الآن فتح pgAdmin والاتصال بقاعدة البيانات:
echo You can now open pgAdmin and connect to the database:
echo.
echo Host: localhost
echo Port: 5432
echo Database: pos_system_db
echo Username: postgres
echo.
echo أو استخدام المستخدمين المخصصين:
echo Or use custom users:
echo - pos_admin_user (Admin)
echo - pos_manager_user (Manager)
echo - pos_cashier_user (Cashier)
echo - pos_readonly_user (ReadOnly)
echo.
pause
