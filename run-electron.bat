@echo off
title Kasheer Toosar - Run Application

echo ========================================
echo    Kasheer Toosar - POS System
echo ========================================
echo.

echo Starting Kasheer Toosar...
echo.

REM Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found
    echo Please download from: https://nodejs.org/
    pause
    exit /b 1
)

REM Install dependencies if not exist
if not exist "node_modules" (
    echo Installing dependencies for first time...
    call npm install --silent
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Install Electron if not exist
if not exist "node_modules\electron" (
    echo Installing Electron...
    call npm install electron --save-dev --silent
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install Electron
        pause
        exit /b 1
    )
)

REM Build Frontend if not exist
if not exist "dist" (
    echo Building frontend...
    call npm run build
    if %errorlevel% neq 0 (
        echo ERROR: Failed to build frontend
        pause
        exit /b 1
    )
)

echo All requirements ready
echo Starting application...
echo.

REM Run Electron
npx electron .

echo.
echo Thank you for using Kasheer Toosar!
pause
