@echo off
REM سكريپت التشغيل السريع لقاعدة البيانات
REM Quick Database Setup Script

echo ========================================
echo    تشغيل سريع لقاعدة بيانات نقطة البيع
echo    Quick POS Database Setup
echo ========================================

REM إعداد متغيرات البيئة مع كلمة السر المحددة
set PGHOST=localhost
set PGPORT=5432
set PGUSER=postgres
set PGPASSWORD=toossar

echo.
echo جاري إعداد قاعدة البيانات...
echo Setting up database...

REM حذف قاعدة البيانات إذا كانت موجودة وإنشاء جديدة
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -c "DROP DATABASE IF EXISTS pos_system_db;"
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -c "CREATE DATABASE pos_system_db WITH OWNER = postgres ENCODING = 'UTF8';"

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ خطأ في إنشاء قاعدة البيانات!
    echo ❌ Error creating database!
    echo.
    echo تأكد من:
    echo Make sure:
    echo - PostgreSQL يعمل بشكل صحيح
    echo - PostgreSQL is running properly
    echo - كلمة السر صحيحة: toossar
    echo - Password is correct: toossar
    echo - المنفذ 5432 متاح
    echo - Port 5432 is available
    echo.
    pause
    exit /b 1
)

echo ✅ تم إنشاء قاعدة البيانات بنجاح!
echo ✅ Database created successfully!

echo.
echo جاري تشغيل سكريپت الإعداد الشامل...
echo Running complete setup script...

REM تشغيل سكريپت الإعداد الشامل
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d pos_system_db -f "setup_database.sql"

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ خطأ في تشغيل سكريپت الإعداد!
    echo ❌ Error running setup script!
    echo.
    echo تأكد من وجود ملف setup_database.sql في نفس المجلد
    echo Make sure setup_database.sql exists in the same folder
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ تم إعداد قاعدة البيانات بالكامل!
echo ✅ Database setup completed!

echo.
echo جاري التحقق من البيانات...
echo Verifying data...

REM التحقق من الجداول والبيانات
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d pos_system_db -c "SELECT 'الجداول المنشأة: ' || COUNT(*) FROM information_schema.tables WHERE table_schema = 'pos_system';"

psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d pos_system_db -c "SELECT 'المنتجات: ' || COUNT(*) FROM pos_system.products UNION ALL SELECT 'الفئات: ' || COUNT(*) FROM pos_system.categories UNION ALL SELECT 'العملاء: ' || COUNT(*) FROM pos_system.customers;"

echo.
echo ========================================
echo 🎉 تم الانتهاء بنجاح!
echo 🎉 Setup completed successfully!
echo ========================================
echo.
echo معلومات الاتصال بـ pgAdmin:
echo pgAdmin Connection Info:
echo.
echo 🔗 Host: localhost
echo 🔗 Port: 5432
echo 🔗 Database: pos_system_db
echo 🔗 Username: postgres
echo 🔗 Password: toossar
echo.
echo المستخدمون المتاحون:
echo Available Users:
echo 👤 pos_admin_user (مدير النظام)
echo 👤 pos_manager_user (مدير عام)
echo 👤 pos_cashier_user (كاشير)
echo 👤 pos_readonly_user (قراءة فقط)
echo.
echo يمكنك الآن فتح pgAdmin والاتصال بقاعدة البيانات!
echo You can now open pgAdmin and connect to the database!
echo.
pause
