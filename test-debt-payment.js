// اختبار دفع ديون الموردين
const axios = require('axios');

const API_BASE = 'http://localhost:5002/api';

async function testSupplierDebtPayment() {
    console.log('🧪 بدء اختبار دفع ديون الموردين...\n');

    try {
        // 1. جلب ديون الموردين الحالية
        console.log('1️⃣ جلب ديون الموردين...');
        const debtsResponse = await axios.get(`${API_BASE}/supplier-debts`);
        const debts = debtsResponse.data;
        
        console.log(`📊 عدد الديون: ${debts.length}`);
        
        if (debts.length === 0) {
            console.log('❌ لا توجد ديون للاختبار');
            return;
        }

        // العثور على دين غير مدفوع
        const unpaidDebt = debts.find(debt => debt.status === 'pending' && debt.paid_amount < debt.amount);
        
        if (!unpaidDebt) {
            console.log('❌ لا توجد ديون غير مدفوعة للاختبار');
            return;
        }

        console.log(`✅ تم العثور على دين للاختبار:`);
        console.log(`   - معرف الدين: ${unpaidDebt.id}`);
        console.log(`   - المورد: ${unpaidDebt.supplier_name}`);
        console.log(`   - المبلغ الإجمالي: ${unpaidDebt.amount} دج`);
        console.log(`   - المبلغ المدفوع: ${unpaidDebt.paid_amount} دج`);
        console.log(`   - المبلغ المتبقي: ${unpaidDebt.amount - unpaidDebt.paid_amount} دج`);
        console.log(`   - الحالة: ${unpaidDebt.status}\n`);

        // 2. جلب المصروفات قبل الدفع
        console.log('2️⃣ جلب المصروفات قبل الدفع...');
        const expensesBeforeResponse = await axios.get(`${API_BASE}/expenses`);
        const expensesBefore = expensesBeforeResponse.data;
        console.log(`📊 عدد المصروفات قبل الدفع: ${expensesBefore.length}\n`);

        // 3. دفع جزء من الدين
        const paymentAmount = Math.min(500, unpaidDebt.amount - unpaidDebt.paid_amount);
        console.log(`3️⃣ دفع ${paymentAmount} دج من الدين...`);
        
        const paymentData = {
            amount: paymentAmount,
            payment_method: 'cash',
            notes: 'اختبار دفع دين مورد'
        };

        const paymentResponse = await axios.post(`${API_BASE}/supplier-debts/${unpaidDebt.id}/pay`, paymentData);
        const paymentResult = paymentResponse.data;
        
        console.log('✅ نتيجة الدفع:');
        console.log(`   - الرسالة: ${paymentResult.message}`);
        console.log(`   - المبلغ المدفوع: ${paymentResult.paid_amount} دج`);
        console.log(`   - إجمالي المدفوع الجديد: ${paymentResult.new_paid_amount} دج`);
        console.log(`   - الحالة الجديدة: ${paymentResult.new_status}`);
        console.log(`   - تم إضافة المصروف: ${paymentResult.expense_added ? 'نعم' : 'لا'}\n`);

        // 4. التحقق من تحديث الدين
        console.log('4️⃣ التحقق من تحديث الدين...');
        const updatedDebtsResponse = await axios.get(`${API_BASE}/supplier-debts`);
        const updatedDebts = updatedDebtsResponse.data;
        const updatedDebt = updatedDebts.find(debt => debt.id === unpaidDebt.id);
        
        if (updatedDebt) {
            console.log('✅ تم تحديث الدين بنجاح:');
            console.log(`   - المبلغ المدفوع الجديد: ${updatedDebt.paid_amount} دج`);
            console.log(`   - الحالة الجديدة: ${updatedDebt.status}`);
            console.log(`   - المبلغ المتبقي: ${updatedDebt.amount - updatedDebt.paid_amount} دج\n`);
        } else {
            console.log('❌ لم يتم العثور على الدين المحدث\n');
        }

        // 5. التحقق من إضافة المصروف
        console.log('5️⃣ التحقق من إضافة المصروف...');
        const expensesAfterResponse = await axios.get(`${API_BASE}/expenses`);
        const expensesAfter = expensesAfterResponse.data;
        
        console.log(`📊 عدد المصروفات بعد الدفع: ${expensesAfter.length}`);
        
        if (expensesAfter.length > expensesBefore.length) {
            const newExpense = expensesAfter[0]; // أحدث مصروف
            console.log('✅ تم إضافة مصروف جديد:');
            console.log(`   - المبلغ: ${newExpense.amount} دج`);
            console.log(`   - الوصف: ${newExpense.description}`);
            console.log(`   - الفئة: ${newExpense.category}`);
            console.log(`   - طريقة الدفع: ${newExpense.payment_method}`);
            console.log(`   - الملاحظات: ${newExpense.notes || 'لا توجد'}\n`);
        } else {
            console.log('❌ لم يتم إضافة مصروف جديد\n');
        }

        console.log('🎉 انتهى الاختبار بنجاح!');

    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.response?.data || error.message);
    }
}

// تشغيل الاختبار
if (require.main === module) {
    testSupplierDebtPayment();
}

module.exports = { testSupplierDebtPayment };
