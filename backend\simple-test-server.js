const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 5002;

app.use(cors());
app.use(express.json());

// اختبار بسيط
app.get('/api/test', (req, res) => {
  console.log('✅ تم استلام طلب اختبار');
  res.json({ message: 'Backend يعمل!', time: new Date() });
});

// مسار supplier-debts
app.put('/api/supplier-debts/invoice/:invoiceId', (req, res) => {
  console.log('✅ تم استلام طلب تحديث دين المورد:', req.params.invoiceId);
  res.json({ message: 'تم التحديث بنجاح' });
});

app.listen(PORT, () => {
  console.log(`🚀 Server يعمل على المنفذ ${PORT}`);
});
