@echo off
title Installing Requirements and Building EXE

echo ========================================
echo   Installing All Requirements and Building EXE
echo ========================================
echo.

echo Step 1: Checking if Node.js is installed...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Node.js not found. Installing Node.js...
    echo Downloading Node.js installer...
    powershell -Command "Invoke-WebRequest -Uri 'https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi' -OutFile 'nodejs-installer.msi'"
    echo Installing Node.js...
    msiexec /i nodejs-installer.msi /quiet /norestart
    echo Waiting for installation to complete...
    timeout /t 30 /nobreak >nul
    del nodejs-installer.msi
    echo Node.js installed successfully!
    echo Please restart this script after Node.js installation is complete.
    pause
    exit /b 0
) else (
    echo Node.js is already installed.
)

echo Step 2: Installing project dependencies...
call npm install --force

echo Step 3: Installing Electron globally...
call npm install -g electron

echo Step 4: Installing pkg for creating EXE...
call npm install -g pkg

echo Step 5: Installing electron-builder...
call npm install electron-builder --save-dev

echo Step 6: Cleaning old build files...
if exist "dist" rmdir /s /q "dist" 2>nul
if exist "kasheer-toosar.exe" del "kasheer-toosar.exe" 2>nul

echo Step 7: Building frontend...
call npm run build

echo Step 8: Creating standalone EXE launcher...
echo const { spawn } = require('child_process'); > app-launcher.js
echo const path = require('path'); >> app-launcher.js
echo const http = require('http'); >> app-launcher.js
echo. >> app-launcher.js
echo console.log('Starting Kasheer Toosar POS System...'); >> app-launcher.js
echo. >> app-launcher.js
echo // Start the backend server >> app-launcher.js
echo const backend = spawn('node', [path.join(__dirname, 'backend', 'server.js')], { >> app-launcher.js
echo   stdio: 'pipe', >> app-launcher.js
echo   cwd: __dirname >> app-launcher.js
echo }); >> app-launcher.js
echo. >> app-launcher.js
echo // Wait for server to start then open browser >> app-launcher.js
echo setTimeout(() =^> { >> app-launcher.js
echo   const { exec } = require('child_process'); >> app-launcher.js
echo   exec('start http://localhost:5003'); >> app-launcher.js
echo   console.log('System started! Opening browser...'); >> app-launcher.js
echo }, 5000); >> app-launcher.js
echo. >> app-launcher.js
echo // Handle shutdown >> app-launcher.js
echo process.on('SIGINT', () =^> { >> app-launcher.js
echo   console.log('Shutting down...'); >> app-launcher.js
echo   backend.kill(); >> app-launcher.js
echo   process.exit(); >> app-launcher.js
echo }); >> app-launcher.js

echo Step 9: Creating package.json for EXE...
echo { > exe-package.json
echo   "name": "kasheer-toosar-exe", >> exe-package.json
echo   "version": "1.0.0", >> exe-package.json
echo   "main": "app-launcher.js", >> exe-package.json
echo   "bin": "app-launcher.js", >> exe-package.json
echo   "pkg": { >> exe-package.json
echo     "scripts": ["backend/**/*.js", "app-launcher.js"], >> exe-package.json
echo     "assets": ["dist/**/*", "backend/**/*", "package.json"] >> exe-package.json
echo   } >> exe-package.json
echo } >> exe-package.json

echo Step 10: Building final EXE file...
call pkg app-launcher.js --target node18-win-x64 --output kasheer-toosar-final.exe

echo Step 11: Cleaning temporary files...
del app-launcher.js 2>nul
del exe-package.json 2>nul

echo.
echo ========================================
echo   BUILD COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Your EXE file is ready: kasheer-toosar-final.exe
echo.
echo This file contains everything needed to run your POS system:
echo - Complete backend server
echo - Frontend interface  
echo - All dependencies
echo.
echo You can now copy this single EXE file to any Windows computer
echo and run it without installing anything else!
echo.
pause
