const fs = require('fs');
const path = require('path');
const pool = require('./database');

async function createAllTables() {
  try {
    console.log('🔄 بدء إنشاء جميع الجداول...');

    // قراءة ملف SQL
    const sqlFile = path.join(__dirname, 'create_all_tables.sql');
    const sql = fs.readFileSync(sqlFile, 'utf8');

    // تنفيذ SQL
    await pool.query(sql);

    console.log('✅ تم إنشاء جميع الجداول بنجاح!');

    // التحقق من الجداول المنشأة
    const result = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN (
        'categories', 'suppliers', 'products', 'customers', 
        'sales', 'sale_items', 'customer_debts', 'customer_debt_payments',
        'purchases', 'purchase_items'
      )
      ORDER BY table_name
    `);

    console.log('📋 الجداول المنشأة:');
    result.rows.forEach(row => {
      console.log(`  ✓ ${row.table_name}`);
    });

    // إدراج بيانات تجريبية للفئات
    await pool.query(`
      INSERT INTO categories (name, description, color, icon) VALUES
      ('مشروبات', 'جميع أنواع المشروبات', '#3b82f6', 'Coffee'),
      ('وجبات خفيفة', 'شيبس وحلويات', '#ef4444', 'Package')
      ON CONFLICT DO NOTHING
    `);

    console.log('✅ تم إدراج البيانات التجريبية للفئات');

    process.exit(0);
  } catch (error) {
    console.error('❌ خطأ في إنشاء الجداول:', error);
    process.exit(1);
  }
}

createAllTables();
