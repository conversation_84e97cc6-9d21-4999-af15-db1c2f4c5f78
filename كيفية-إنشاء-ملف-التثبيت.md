# 🚀 كيفية إنشاء ملف التثبيت (.exe/.msi) - كشير توسار

## 📋 الطريقة السريعة (3 خطوات فقط!)

### 1️⃣ تحضير الأيقونة
- ضع أيقونة برنامجك في مجلد `assets/` باسم `icon.ico`
- الحجم المطلوب: 256x256 بكسل
- إذا لم تكن لديك أيقونة، سيتم إنشاء واحدة افتراضية

### 2️⃣ تشغيل البناء السريع
```bash
# انقر مرتين على الملف
quick-build.bat
```
أو
```bash
# من سطر الأوامر
quick-build.bat
```

### 3️⃣ اختيار نوع البناء
- اختر الرقم `1` للبناء الكامل
- انتظر انتهاء العملية (5-10 دقائق)
- ستجد الملفات في مجلد `dist-electron/`

## 🎯 النتيجة النهائية

ستحصل على ملفات التثبيت التالية:

### 📦 ملف .exe (NSIS Installer)
- **الاسم**: `كشير-توسار-1.0.0.exe`
- **الحجم**: ~150-200 MB
- **المميزات**: 
  - مثبت باللغة العربية
  - اختصارات تلقائية
  - إمكانية اختيار مجلد التثبيت
  - إلغاء تثبيت سهل

### 📦 ملف .msi (Windows Installer)
- **الاسم**: `كشير-توسار-1.0.0.msi`
- **الحجم**: ~150-200 MB
- **المميزات**:
  - دعم Windows Installer
  - إدارة متقدمة للتحديثات
  - دعم Group Policy

## 🔧 إعدادات متقدمة

### تخصيص معلومات التطبيق:
عدّل ملف `electron-package.json`:
```json
{
  "name": "اسم-التطبيق",
  "productName": "اسم التطبيق المعروض",
  "version": "1.0.0",
  "description": "وصف التطبيق",
  "author": "اسم المطور"
}
```

### تخصيص الأيقونات:
```
assets/
├── icon.ico     # أيقونة Windows (256x256)
├── icon.png     # أيقونة Linux (256x256)
└── icon.icns    # أيقونة macOS (512x512)
```

### تخصيص المثبت:
عدّل إعدادات `build` في `electron-package.json`:
```json
"nsis": {
  "shortcutName": "اسم الاختصار",
  "createDesktopShortcut": true,
  "createStartMenuShortcut": true,
  "language": "1025"
}
```

## 🛠️ حل المشاكل الشائعة

### ❌ خطأ: "Node.js غير مثبت"
**الحل**: 
1. حمّل Node.js من [nodejs.org](https://nodejs.org/)
2. ثبّت الإصدار LTS
3. أعد تشغيل Command Prompt

### ❌ خطأ: "electron-builder failed"
**الحل**:
```bash
# احذف node_modules وأعد التثبيت
rmdir /s node_modules
npm install
npm install electron electron-builder --save-dev
```

### ❌ خطأ: "Cannot find icon"
**الحل**:
1. تأكد من وجود ملف `icon.ico` في مجلد `assets/`
2. أو استخدم أي صورة PNG وحوّلها إلى ICO

### ❌ ملف التثبيت كبير جداً
**الحل**:
```json
// في electron-package.json
"compression": "maximum",
"files": [
  "!**/*.map",
  "!**/node_modules/**/*"
]
```

## 📱 اختبار ملف التثبيت

### قبل التوزيع:
1. **اختبر على جهاز نظيف**: جرب التثبيت على جهاز لا يحتوي على Node.js
2. **اختبر جميع الميزات**: تأكد من عمل جميع وظائف البرنامج
3. **اختبر إلغاء التثبيت**: تأكد من حذف جميع الملفات

### متطلبات جهاز العميل:
- **نظام التشغيل**: Windows 10/11 (64-bit مستحسن)
- **الذاكرة**: 4 GB RAM كحد أدنى
- **المساحة**: 500 MB مساحة فارغة
- **قاعدة البيانات**: PostgreSQL 12+ (يجب تثبيتها منفصلة)

## 🚀 التوزيع

### طرق التوزيع:
1. **رفع على موقع الشركة**
2. **إرسال عبر البريد الإلكتروني**
3. **توزيع عبر USB**
4. **رفع على خدمات التخزين السحابي**

### نصائح الأمان:
- وقّع الملف رقمياً لزيادة الثقة
- اختبر على برامج مكافحة الفيروسات
- أضف معلومات الاتصال للدعم الفني

## 📞 الدعم

إذا واجهت أي مشاكل:
1. راجع ملف `دليل-بناء-التطبيق.md`
2. تحقق من ملف `assets/README.md`
3. اتصل بالدعم الفني

---

## 🎉 تهانينا!

الآن لديك ملف تثبيت احترافي لبرنامج كشير توسار يمكن توزيعه على أجهزة العملاء! 🎊
