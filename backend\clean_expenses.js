const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
});

async function cleanExpensesData() {
  const client = await pool.connect();
  
  try {
    console.log('🧹 حذف البيانات الوهمية من جدول المصروفات...');
    
    // حذف جميع البيانات الوهمية من جدول المصروفات
    const deleteResult = await client.query('DELETE FROM pos_system.expenses');
    console.log(`✅ تم حذف ${deleteResult.rowCount} مصروف وهمي`);
    
    // التحقق من أن الجدول فارغ
    const countResult = await client.query('SELECT COUNT(*) as count FROM pos_system.expenses');
    const count = parseInt(countResult.rows[0].count);
    
    if (count === 0) {
      console.log('✅ جدول المصروفات فارغ الآن');
    } else {
      console.log(`⚠️ لا يزال هناك ${count} مصروف في الجدول`);
    }
    
    // إعادة تعيين sequence إذا كان موجوداً
    try {
      await client.query('ALTER SEQUENCE IF EXISTS pos_system.expenses_id_seq RESTART WITH 1');
      console.log('✅ تم إعادة تعيين sequence');
    } catch (error) {
      console.log('ℹ️ لا يوجد sequence للإعادة تعيين');
    }
    
    console.log('\n🎯 الآن النظام جاهز لإضافة المصروفات الحقيقية فقط');
    console.log('💡 سيتم إضافة المصروفات تلقائياً عند:');
    console.log('   - دفع فواتير المشتريات');
    console.log('   - دفع ديون الموردين');
    console.log('   - إضافة مصروفات مباشرة من واجهة المصروفات');
    
  } catch (error) {
    console.error('❌ خطأ في حذف البيانات:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

// تشغيل الدالة
cleanExpensesData();
