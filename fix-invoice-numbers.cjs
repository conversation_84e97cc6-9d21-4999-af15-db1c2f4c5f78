// إصلاح أرقام الفواتير
const { Pool } = require('pg');

async function fixInvoiceNumbers() {
  const pool = new Pool({
    host: 'localhost',
    port: 5432,
    database: 'pos_system_db',
    user: 'postgres',
    password: 'toossar',
  });

  try {
    console.log('🔧 إصلاح أرقام الفواتير...');
    
    // حذف الـ trigger القديم إن وجد
    await pool.query('DROP TRIGGER IF EXISTS trigger_generate_purchase_number ON pos_system.purchase_invoices');
    await pool.query('DROP FUNCTION IF EXISTS pos_system.generate_purchase_number()');
    console.log('✅ تم حذف الـ trigger القديم');

    // إنشاء sequence جديد
    await pool.query('DROP SEQUENCE IF EXISTS pos_system.purchase_number_seq');
    await pool.query('CREATE SEQUENCE pos_system.purchase_number_seq START 1');
    console.log('✅ تم إنشاء sequence جديد');

    // إنشاء وظيفة توليد رقم الفاتورة
    await pool.query(`
      CREATE OR REPLACE FUNCTION pos_system.generate_purchase_number()
      RETURNS TRIGGER AS $$
      BEGIN
          IF NEW.invoice_number IS NULL OR NEW.invoice_number = '' THEN
              NEW.invoice_number := 'PUR-' || TO_CHAR(CURRENT_DATE, 'YYYY') || '-' || 
                                   LPAD(NEXTVAL('pos_system.purchase_number_seq')::TEXT, 4, '0');
          END IF;
          RETURN NEW;
      END;
      $$ language 'plpgsql';
    `);
    console.log('✅ تم إنشاء وظيفة توليد الرقم');

    // إنشاء trigger جديد
    await pool.query(`
      CREATE TRIGGER trigger_generate_purchase_number
          BEFORE INSERT ON pos_system.purchase_invoices
          FOR EACH ROW EXECUTE FUNCTION pos_system.generate_purchase_number();
    `);
    console.log('✅ تم إنشاء trigger جديد');

    // تحديث الفواتير الموجودة
    const updateResult = await pool.query(`
      UPDATE pos_system.purchase_invoices 
      SET invoice_number = 'PUR-' || TO_CHAR(CURRENT_DATE, 'YYYY') || '-' || 
                          LPAD(ROW_NUMBER() OVER (ORDER BY created_at)::TEXT, 4, '0')
      WHERE invoice_number ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    `);
    console.log(`✅ تم تحديث ${updateResult.rowCount} فاتورة موجودة`);

    // إعادة تعيين sequence
    const countResult = await pool.query('SELECT COUNT(*) as count FROM pos_system.purchase_invoices');
    const nextSeq = parseInt(countResult.rows[0].count) + 1;
    await pool.query(`SELECT setval('pos_system.purchase_number_seq', ${nextSeq})`);
    console.log(`✅ تم تعيين sequence للرقم التالي: ${nextSeq}`);

    // عرض الفواتير المحدثة
    const invoicesResult = await pool.query('SELECT id, invoice_number, created_at FROM pos_system.purchase_invoices ORDER BY created_at');
    console.log('\n📋 الفواتير بعد التحديث:');
    invoicesResult.rows.forEach(invoice => {
      console.log(`  - ${invoice.invoice_number} (${invoice.id})`);
    });

    console.log('\n🎉 تم إصلاح أرقام الفواتير بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await pool.end();
  }
}

fixInvoiceNumbers();
