const pool = require('./database');

async function testDebtPaymentDirect() {
  console.log('🧪 اختبار تسديد الديون مباشرة...');
  
  try {
    // 1. البحث عن دين غير مسدد
    console.log('\n1️⃣ البحث عن ديون غير مسددة...');
    const debtsResult = await pool.query(`
      SELECT 
        cd.id,
        cd.amount,
        cd.paid_amount,
        cd.remaining_amount,
        cd.status,
        cd.customer_id,
        c.name as customer_name
      FROM pos_system.customer_debts cd
      JOIN pos_system.customers c ON cd.customer_id = c.id
      WHERE cd.status IN ('pending', 'partial')
      ORDER BY cd.created_at DESC
      LIMIT 3
    `);
    
    if (debtsResult.rows.length === 0) {
      console.log('❌ لا توجد ديون غير مسددة للاختبار');
      return;
    }
    
    console.log('✅ الديون غير المسددة:');
    debtsResult.rows.forEach(debt => {
      console.log(`   - العميل: ${debt.customer_name}`);
      console.log(`   - مبلغ الدين: ${debt.amount} دج`);
      console.log(`   - المبلغ المدفوع: ${debt.paid_amount} دج`);
      console.log(`   - المبلغ المتبقي: ${debt.remaining_amount} دج`);
      console.log(`   - الحالة: ${debt.status}`);
      console.log(`   - معرف الدين: ${debt.id}`);
      console.log('   ---');
    });
    
    // 2. اختبار تسديد جزئي مباشر في قاعدة البيانات
    const testDebt = debtsResult.rows[0];
    const paymentAmount = parseFloat(testDebt.remaining_amount) / 2; // دفع نصف المبلغ
    
    console.log(`\n2️⃣ اختبار تسديد جزئي مباشر...`);
    console.log(`   - الدين المختار: ${testDebt.customer_name} - ${testDebt.remaining_amount} دج`);
    console.log(`   - مبلغ الدفع: ${paymentAmount} دج`);
    
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // تسجيل الدفعة في جدول customer_debt_payments
      const paymentResult = await client.query(`
        INSERT INTO pos_system.customer_debt_payments (
          debt_id, amount, payment_method, payment_date, notes
        )
        VALUES ($1, $2, 'cash', CURRENT_DATE, 'دفعة اختبارية جزئية')
        RETURNING *
      `, [testDebt.id, paymentAmount]);
      
      // تحديث الدين
      const newPaidAmount = parseFloat(testDebt.paid_amount) + paymentAmount;
      const newRemainingAmount = parseFloat(testDebt.amount) - newPaidAmount;
      const newStatus = newRemainingAmount <= 0 ? 'paid' : 'partial';
      
      await client.query(`
        UPDATE pos_system.customer_debts 
        SET paid_amount = $1, status = $2, updated_at = CURRENT_TIMESTAMP
        WHERE id = $3
      `, [newPaidAmount, newStatus, testDebt.id]);
      
      await client.query('COMMIT');
      
      console.log('✅ تم تسديد الدفعة الجزئية بنجاح!');
      console.log(`   - مبلغ الدفع: ${paymentAmount} دج`);
      console.log(`   - حالة الدين الجديدة: ${newStatus}`);
      console.log(`   - المبلغ المتبقي: ${newRemainingAmount} دج`);
      
      // 3. التحقق من تسجيل الدفعة
      console.log('\n3️⃣ التحقق من تسجيل الدفعة...');
      const paymentCheck = await pool.query(`
        SELECT * FROM pos_system.customer_debt_payments 
        WHERE debt_id = $1 
        ORDER BY created_at DESC 
        LIMIT 1
      `, [testDebt.id]);
      
      if (paymentCheck.rows.length > 0) {
        const payment = paymentCheck.rows[0];
        console.log('✅ تم تسجيل الدفعة في الجدول:');
        console.log(`   - مبلغ الدفعة: ${payment.amount} دج`);
        console.log(`   - طريقة الدفع: ${payment.payment_method}`);
        console.log(`   - تاريخ الدفع: ${payment.payment_date}`);
        console.log(`   - الملاحظات: ${payment.notes}`);
      } else {
        console.log('❌ لم يتم تسجيل الدفعة في الجدول');
      }
      
      // 4. التحقق من تحديث حالة الدين
      console.log('\n4️⃣ التحقق من تحديث حالة الدين...');
      const updatedDebtResult = await pool.query(`
        SELECT * FROM pos_system.customer_debts WHERE id = $1
      `, [testDebt.id]);
      
      if (updatedDebtResult.rows.length > 0) {
        const updatedDebt = updatedDebtResult.rows[0];
        console.log('✅ حالة الدين بعد التسديد:');
        console.log(`   - المبلغ الأصلي: ${updatedDebt.amount} دج`);
        console.log(`   - المبلغ المدفوع: ${updatedDebt.paid_amount} دج`);
        console.log(`   - المبلغ المتبقي: ${updatedDebt.remaining_amount} دج`);
        console.log(`   - الحالة: ${updatedDebt.status}`);
        
        // 5. اختبار تسديد المبلغ المتبقي إذا كان هناك مبلغ متبقي
        if (parseFloat(updatedDebt.remaining_amount) > 0) {
          console.log('\n5️⃣ اختبار تسديد المبلغ المتبقي...');
          const finalPayment = parseFloat(updatedDebt.remaining_amount);
          
          await client.query('BEGIN');
          
          // تسجيل الدفعة النهائية
          await client.query(`
            INSERT INTO pos_system.customer_debt_payments (
              debt_id, amount, payment_method, payment_date, notes
            )
            VALUES ($1, $2, 'cash', CURRENT_DATE, 'تسديد نهائي - اختبار')
          `, [testDebt.id, finalPayment]);
          
          // تحديث الدين للحالة النهائية
          await client.query(`
            UPDATE pos_system.customer_debts 
            SET paid_amount = amount, status = 'paid', updated_at = CURRENT_TIMESTAMP
            WHERE id = $1
          `, [testDebt.id]);
          
          await client.query('COMMIT');
          
          console.log('✅ تم تسديد المبلغ المتبقي بنجاح!');
          console.log(`   - مبلغ الدفع النهائي: ${finalPayment} دج`);
          console.log('🎉 تم سداد الدين بالكامل - سيختفي من قائمة المدينين');
        }
      }
      
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
    
    // 6. التحقق من قائمة المدينين بعد التسديد
    console.log('\n6️⃣ التحقق من قائمة المدينين بعد التسديد...');
    const finalDebtorsResult = await pool.query(`
      SELECT
        c.name,
        cd.amount as total_debt,
        cd.paid_amount,
        cd.remaining_amount,
        cd.status
      FROM pos_system.customers c
      INNER JOIN pos_system.customer_debts cd ON c.id = cd.customer_id
      WHERE cd.status IN ('pending', 'partial')
        AND c.is_active = true
      ORDER BY cd.remaining_amount DESC
    `);
    
    console.log(`✅ العملاء المدينون حالياً (${finalDebtorsResult.rows.length}):`);
    if (finalDebtorsResult.rows.length > 0) {
      finalDebtorsResult.rows.forEach(debtor => {
        console.log(`   - ${debtor.name}: ${debtor.remaining_amount} دج (${debtor.status})`);
      });
    } else {
      console.log('   - لا توجد ديون معلقة حالياً');
    }
    
    // 7. عرض تاريخ المدفوعات للدين المختبر
    console.log('\n7️⃣ تاريخ المدفوعات للدين المختبر...');
    const paymentsHistory = await pool.query(`
      SELECT 
        amount,
        payment_method,
        payment_date,
        notes,
        created_at
      FROM pos_system.customer_debt_payments 
      WHERE debt_id = $1 
      ORDER BY created_at ASC
    `, [testDebt.id]);
    
    if (paymentsHistory.rows.length > 0) {
      console.log(`✅ تاريخ المدفوعات (${paymentsHistory.rows.length} دفعة):`);
      paymentsHistory.rows.forEach((payment, index) => {
        console.log(`   ${index + 1}. ${payment.amount} دج - ${payment.payment_method} - ${payment.payment_date}`);
        if (payment.notes) {
          console.log(`      الملاحظات: ${payment.notes}`);
        }
      });
    } else {
      console.log('❌ لا توجد مدفوعات مسجلة');
    }
    
  } catch (error) {
    console.error('❌ خطأ في اختبار تسديد الديون:', error.message);
    console.error('تفاصيل الخطأ:', error);
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testDebtPaymentDirect()
    .then(() => {
      console.log('\n🎉 انتهى اختبار تسديد الديون المباشر!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل اختبار تسديد الديون:', error);
      process.exit(1);
    });
}

module.exports = testDebtPaymentDirect;
