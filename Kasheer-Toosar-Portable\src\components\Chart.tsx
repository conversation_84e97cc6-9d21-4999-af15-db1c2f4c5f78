import React from 'react';

const Chart: React.FC = () => {
  const data = [
    { day: '29/4', value: 0 },
    { day: '30/4', value: 0 },
    { day: '1/5', value: 0 },
    { day: '2/5', value: 0 },
    { day: '3/5', value: 0 },
    { day: '4/5', value: 0 },
    { day: '5/5', value: 0 },
    { day: '6/5', value: 0 },
    { day: '7/5', value: 0 },
    { day: '8/5', value: 0 },
    { day: '9/5', value: 0 },
    { day: '10/5', value: 0 },
    { day: '11/5', value: 0 },
    { day: '12/5', value: 0 },
    { day: '13/5', value: 0 },
    { day: '14/5', value: 0 },
    { day: '15/5', value: 0 },
    { day: '16/5', value: 0 },
    { day: '17/5', value: 0 },
    { day: '18/5', value: 0 }
  ];

  return (
    <div className="w-full h-32 flex items-end justify-between bg-slate-900 rounded-lg p-4 space-x-1">
      {data.map((item, index) => (
        <div key={index} className="flex-1 flex flex-col items-center">
          <div 
            className="w-full bg-blue-500 rounded-sm transition-all duration-300 hover:bg-blue-400"
            style={{ 
              height: `${Math.max(item.value, 2)}px`,
              minHeight: '2px'
            }}
          ></div>
          {index % 4 === 0 && (
            <span className="text-xs text-slate-500 mt-2 transform rotate-45 origin-left">
              {item.day}
            </span>
          )}
        </div>
      ))}
    </div>
  );
};

export default Chart;