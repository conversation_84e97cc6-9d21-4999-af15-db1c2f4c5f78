// 🧪 اختبار حساب الطلبات الصحيح
// هذا الملف يوضح الفرق بين الحساب الخاطئ والصحيح للطلبات

console.log('🧪 اختبار حساب الطلبات...\n');

// مثال على البيانات
const exampleData = {
  // فاتورة 1: تحتوي على 3 منتجات
  sale1: {
    id: 'SALE-001',
    items: [
      { product: 'منتج أ', quantity: 2 },
      { product: 'منتج ب', quantity: 1 },
      { product: 'منتج ج', quantity: 5 }
    ]
  },
  // فاتورة 2: تحتوي على منتج واحد
  sale2: {
    id: 'SALE-002',
    items: [
      { product: 'منتج د', quantity: 1 }
    ]
  },
  // فاتورة 3: تحتوي على منتجين
  sale3: {
    id: 'SALE-003',
    items: [
      { product: 'منتج أ', quantity: 3 },
      { product: 'منتج هـ', quantity: 2 }
    ]
  }
};

// ❌ الطريقة الخاطئة (الطريقة القديمة)
console.log('❌ الطريقة الخاطئة:');
console.log('   COUNT(*) من جدول sale_items');

let wrongCount = 0;
Object.values(exampleData).forEach(sale => {
  wrongCount += sale.items.length; // عدد المنتجات في كل فاتورة
});

console.log(`   النتيجة: ${wrongCount} طلب`);
console.log('   المشكلة: يحسب عدد المنتجات وليس عدد الفواتير!\n');

// ✅ الطريقة الصحيحة (الطريقة الجديدة)
console.log('✅ الطريقة الصحيحة:');
console.log('   COUNT(DISTINCT s.id) من جدول sales');

const correctCount = Object.keys(exampleData).length; // عدد الفواتير

console.log(`   النتيجة: ${correctCount} طلب`);
console.log('   الصحيح: يحسب عدد الفواتير الفعلية!\n');

// 📊 التوضيح
console.log('📊 التوضيح:');
console.log(`   عدد الفواتير الفعلية: ${correctCount}`);
console.log(`   إجمالي المنتجات: ${wrongCount}`);
console.log(`   الفرق: ${wrongCount - correctCount} منتج إضافي تم حسابه خطأً كطلبات\n`);

// 🔧 الإصلاح المطبق
console.log('🔧 الإصلاح المطبق في الكود:');
console.log('   قبل: COUNT(*) FROM sales s LEFT JOIN sale_items si...');
console.log('   بعد: COUNT(DISTINCT s.id) FROM sales s LEFT JOIN sale_items si...');
console.log('\n✅ تم إصلاح المشكلة بنجاح!');
