import React, { useState } from 'react';
import Sidebar from './components/Sidebar';
import Dashboard from './components/Dashboard';
import SalesScreen from './components/SalesScreen';
import OrdersHistory from './components/OrdersHistory';
import CustomersAccounts from './components/CustomersAccounts';
import Debtors from './components/Debtors';
import Creditors from './components/Creditors';
import ProductManagement from './components/ProductManagement';
import Categories from './components/Categories';
import ChargeStation from './components/ChargeStation';

import FinancialCenter from './components/FinancialCenter';
import Expenses from './components/Expenses';
import Income from './components/Income';
import PurchaseInvoices from './components/PurchaseInvoices';
import Suppliers from './components/Suppliers';
import LabelDesigner from './components/LabelDesigner';
import Settings from './components/Settings';
import PrinterSettings from './components/PrinterSettings';
import { AppProvider } from './context/AppContext';

function App() {
  // 🔄 استعادة الموقع المحفوظ أو الافتراضي
  const [currentView, setCurrentViewState] = useState(() => {
    const savedView = localStorage.getItem('currentView');
    return savedView || 'dashboard';
  });

  // 🎯 دالة تحديث الموقع مع الحفظ
  const setCurrentView = (view: string) => {
    localStorage.setItem('currentView', view);
    setCurrentViewState(view);
  };

  // معالج التنقل من التنبيهات
  React.useEffect(() => {
    // فحص التنقل المعلق
    const pendingNavigation = localStorage.getItem('pendingNavigation');
    if (pendingNavigation) {
      setCurrentView(pendingNavigation);
      localStorage.removeItem('pendingNavigation');
    }

    // مستمع رسائل التنقل
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'navigate') {
        setCurrentView(event.data.view);
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  const renderCurrentView = () => {
    switch (currentView) {
      case 'sales':
        return <SalesScreen />;
      case 'orders':
        return <OrdersHistory />;
      case 'customers':
        return <CustomersAccounts />;
      case 'debtors':
        return <Debtors />;
      case 'creditors':
        return <Creditors />;
      case 'product-management':
        return <ProductManagement />;
      case 'categories':
        return <Categories />;
      case 'charge-station':
        return <ChargeStation />;

      case 'financial-center':
        return <FinancialCenter />;
      case 'expenses':
        return <Expenses />;
      case 'income':
        return <Income />;
      case 'purchase-invoice':
        return <PurchaseInvoices />;
      case 'suppliers':
        return <Suppliers />;
      case 'label-designer':
        return <LabelDesigner />;
      case 'settings':
        return <Settings />;
      case 'printer-settings':
        return <PrinterSettings />;
      case 'dashboard':
      default:
        return <Dashboard />;
    }
  };

  return (
    <AppProvider>
      <div className="min-h-screen bg-slate-900 text-white" dir="rtl">
        <div className="flex">
          <Sidebar currentView={currentView} setCurrentView={setCurrentView} />
          <main className="flex-1 mr-64 transition-all duration-300">
            {renderCurrentView()}
          </main>
        </div>
      </div>
    </AppProvider>
  );
}

export default App;