-- إصلاح جدول sale_items لدعم المنتجات غير المعروفة
-- تشغيل هذا في pgAdmin Query Tool

-- 1. تعديل product_id ليقبل NULL
ALTER TABLE pos_system.sale_items 
ALTER COLUMN product_id DROP NOT NULL;

-- 2. إضافة أعمدة للمنتجات غير المعروفة
ALTER TABLE pos_system.sale_items 
ADD COLUMN IF NOT EXISTS is_unknown_product BOOLEAN DEFAULT FALSE;

ALTER TABLE pos_system.sale_items 
ADD COLUMN IF NOT EXISTS unknown_product_code VARCHAR(50);

-- 3. إنشاء فهرس للمنتجات غير المعروفة
CREATE INDEX IF NOT EXISTS idx_sale_items_unknown 
ON pos_system.sale_items(is_unknown_product) 
WHERE is_unknown_product = TRUE;

-- 4. رسالة إتمام
SELECT '✅ تم إصلاح جدول sale_items لدعم المنتجات غير المعروفة' AS message;
