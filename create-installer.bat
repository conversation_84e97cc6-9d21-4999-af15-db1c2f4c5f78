@echo off
title Creating Windows Installer

echo ========================================
echo   Creating Windows Installer (.exe)
echo ========================================

echo Step 1: Building frontend...
call npm run build

echo Step 2: Creating installer script...
echo ; Kasheer Toosar Installer > installer.nsi
echo !define APPNAME "Kasheer Toosar" >> installer.nsi
echo !define COMPANYNAME "Toosar" >> installer.nsi
echo !define DESCRIPTION "نظام نقاط البيع" >> installer.nsi
echo !define VERSIONMAJOR 1 >> installer.nsi
echo !define VERSIONMINOR 0 >> installer.nsi
echo !define VERSIONBUILD 0 >> installer.nsi
echo !define HELPURL "http://toosar.com" >> installer.nsi
echo !define UPDATEURL "http://toosar.com" >> installer.nsi
echo !define ABOUTURL "http://toosar.com" >> installer.nsi
echo !define INSTALLSIZE 100000 >> installer.nsi
echo. >> installer.nsi
echo RequestExecutionLevel admin >> installer.nsi
echo InstallDir "$PROGRAMFILES\${APPNAME}" >> installer.nsi
echo. >> installer.nsi
echo Name "${APPNAME}" >> installer.nsi
echo outFile "Kasheer-Toosar-Setup.exe" >> installer.nsi
echo. >> installer.nsi
echo page directory >> installer.nsi
echo page instfiles >> installer.nsi
echo. >> installer.nsi
echo section "install" >> installer.nsi
echo 	setOutPath $INSTDIR >> installer.nsi
echo 	file /r "backend" >> installer.nsi
echo 	file /r "dist" >> installer.nsi
echo 	file /r "node_modules" >> installer.nsi
echo 	file "package.json" >> installer.nsi
echo 	file "start.bat" >> installer.nsi
echo. >> installer.nsi
echo 	writeUninstaller "$INSTDIR\uninstall.exe" >> installer.nsi
echo. >> installer.nsi
echo 	createDirectory "$SMPROGRAMS\${APPNAME}" >> installer.nsi
echo 	createShortCut "$SMPROGRAMS\${APPNAME}\${APPNAME}.lnk" "$INSTDIR\start.bat" >> installer.nsi
echo 	createShortCut "$DESKTOP\${APPNAME}.lnk" "$INSTDIR\start.bat" >> installer.nsi
echo. >> installer.nsi
echo 	WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayName" "${APPNAME}" >> installer.nsi
echo 	WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "UninstallString" "$INSTDIR\uninstall.exe" >> installer.nsi
echo 	WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "InstallLocation" "$INSTDIR" >> installer.nsi
echo 	WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "Publisher" "${COMPANYNAME}" >> installer.nsi
echo 	WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "VersionMajor" ${VERSIONMAJOR} >> installer.nsi
echo 	WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "VersionMinor" ${VERSIONMINOR} >> installer.nsi
echo 	WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "EstimatedSize" ${INSTALLSIZE} >> installer.nsi
echo sectionEnd >> installer.nsi
echo. >> installer.nsi
echo section "uninstall" >> installer.nsi
echo 	delete "$SMPROGRAMS\${APPNAME}\${APPNAME}.lnk" >> installer.nsi
echo 	delete "$DESKTOP\${APPNAME}.lnk" >> installer.nsi
echo 	rmDir "$SMPROGRAMS\${APPNAME}" >> installer.nsi
echo 	rmDir /r "$INSTDIR" >> installer.nsi
echo 	DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" >> installer.nsi
echo sectionEnd >> installer.nsi

echo Step 3: Downloading NSIS...
if not exist "nsis-portable.zip" (
    powershell -Command "Invoke-WebRequest -Uri 'https://sourceforge.net/projects/nsis/files/NSIS%%203/3.08/nsis-3.08.zip/download' -OutFile 'nsis-portable.zip'"
)

echo Step 4: Extracting NSIS...
if not exist "nsis" (
    powershell -Command "Expand-Archive -Path 'nsis-portable.zip' -DestinationPath '.'"
    ren "nsis-3.08" "nsis"
)

echo Step 5: Creating installer...
"nsis\makensis.exe" installer.nsi

echo.
echo ========================================
echo   INSTALLER CREATED SUCCESSFULLY!
echo ========================================
echo.
echo Your installer file: Kasheer-Toosar-Setup.exe
echo.
echo This installer will:
echo - Install all files to Program Files
echo - Create desktop shortcut
echo - Create start menu entry
echo - Add to Windows Add/Remove Programs
echo.
pause
