const fs = require('fs');
const path = require('path');
const pool = require('./database');

async function createSalesTables() {
  try {
    console.log('🔄 بدء إنشاء جداول المبيعات...');

    // قراءة ملف SQL
    const sqlFile = path.join(__dirname, 'create_sales_tables.sql');
    const sql = fs.readFileSync(sqlFile, 'utf8');

    // تنفيذ SQL
    await pool.query(sql);

    console.log('✅ تم إنشاء جداول المبيعات بنجاح!');

    // التحقق من الجداول المنشأة
    const result = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('sales', 'sale_items', 'customer_debts', 'customer_debt_payments')
      ORDER BY table_name
    `);

    console.log('📋 الجداول المنشأة:');
    result.rows.forEach(row => {
      console.log(`  ✓ ${row.table_name}`);
    });

    // التحقق من أعمدة جدول customers
    const customersColumns = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'customers' 
      AND column_name IN ('balance', 'credit_limit')
      ORDER BY column_name
    `);

    console.log('📋 أعمدة جدول customers المضافة:');
    customersColumns.rows.forEach(row => {
      console.log(`  ✓ ${row.column_name}`);
    });

    process.exit(0);
  } catch (error) {
    console.error('❌ خطأ في إنشاء جداول المبيعات:', error);
    process.exit(1);
  }
}

createSalesTables();
