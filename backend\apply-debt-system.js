const pool = require('./database');
const fs = require('fs');
const path = require('path');

async function applyDebtSystem() {
  try {
    console.log('🚀 بدء تطبيق نظام فاتورة الدين الموحدة...');
    
    // قراءة ملف SQL
    const sqlFile = path.join(__dirname, 'database-upgrade-debt-system.sql');
    const sqlContent = fs.readFileSync(sqlFile, 'utf8');
    
    // تطبيق التحديثات
    await pool.query(sqlContent);
    
    console.log('✅ تم تطبيق نظام فاتورة الدين الموحدة بنجاح!');
    console.log('📋 الجداول المُنشأة:');
    console.log('   - customer_debt_invoices (فواتير الدين الرئيسية)');
    console.log('   - debt_invoice_items (عناصر فاتورة الدين)');
    console.log('   - debt_payments (دفعات الدين)');
    console.log('🔧 الدوال المُنشأة:');
    console.log('   - calculate_customer_debt_balance()');
    console.log('   - update_debt_invoice_balance()');
    
    // اختبار النظام
    console.log('🧪 اختبار النظام...');
    
    // إنشاء فاتورة دين تجريبية
    const testResult = await pool.query(`
      INSERT INTO pos_system.customer_debt_invoices 
      (customer_id, invoice_number, credit_limit, payment_terms, notes)
      SELECT 
        id, 
        'DEBT-' || UPPER(SUBSTRING(name FROM 1 FOR 3)) || '-001',
        5000.00,
        30,
        'فاتورة دين تجريبية'
      FROM pos_system.customers 
      LIMIT 1
      RETURNING id, invoice_number
    `);
    
    if (testResult.rows.length > 0) {
      console.log(`✅ تم إنشاء فاتورة دين تجريبية: ${testResult.rows[0].invoice_number}`);
    }
    
    console.log('🎉 النظام جاهز للاستخدام!');
    
  } catch (error) {
    console.error('❌ خطأ في تطبيق نظام الدين:', error);
  } finally {
    process.exit(0);
  }
}

applyDebtSystem();
