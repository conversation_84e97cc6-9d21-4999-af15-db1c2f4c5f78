const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// إعداد الاتصال بقاعدة البيانات
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'pos_system',
  password: '123',
  port: 5432,
});

async function setupPurchaseTables() {
  const client = await pool.connect();
  
  try {
    console.log('🔄 بدء إنشاء جداول المشتريات والموردين...');
    
    // قراءة ملف SQL
    const sqlFile = path.join(__dirname, 'create_purchase_tables.sql');
    const sql = fs.readFileSync(sqlFile, 'utf8');
    
    // تنفيذ SQL
    await client.query(sql);
    
    console.log('✅ تم إنشاء جداول المشتريات والموردين بنجاح!');
    
    // التحقق من الجداول المنشأة
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'pos_system' 
      AND table_name LIKE '%purchase%' OR table_name = 'suppliers'
      ORDER BY table_name;
    `);
    
    console.log('📋 الجداول المنشأة:');
    tablesResult.rows.forEach(row => {
      console.log(`  ✓ ${row.table_name}`);
    });
    
    // التحقق من الموردين المدرجين
    const suppliersResult = await client.query('SELECT COUNT(*) as count FROM pos_system.suppliers');
    console.log(`👥 عدد الموردين المدرجين: ${suppliersResult.rows[0].count}`);
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء الجداول:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// تنفيذ الإعداد
setupPurchaseTables();
