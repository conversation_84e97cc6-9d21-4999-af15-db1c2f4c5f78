const { Pool } = require('pg');
require('dotenv').config();

console.log('🔍 فحص اتصال قاعدة البيانات...');
console.log('📋 إعدادات قاعدة البيانات:');
console.log('Host:', process.env.DB_HOST);
console.log('Port:', process.env.DB_PORT);
console.log('Database:', process.env.DB_NAME);
console.log('User:', process.env.DB_USER);
console.log('Schema:', process.env.DB_SCHEMA);

const pool = new Pool({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
});

async function checkDatabase() {
  try {
    console.log('\n🔗 محاولة الاتصال...');
    const client = await pool.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح!');
    
    // تعيين المخطط
    await client.query(`SET search_path TO ${process.env.DB_SCHEMA}, public`);
    console.log('✅ تم تعيين المخطط بنجاح');
    
    // فحص الجداول
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = $1
    `, [process.env.DB_SCHEMA]);
    
    console.log('\n📊 الجداول الموجودة:');
    tablesResult.rows.forEach(row => {
      console.log('  -', row.table_name);
    });
    
    // فحص جدول العملاء
    const customersCount = await client.query('SELECT COUNT(*) FROM customers');
    console.log('\n👥 عدد العملاء:', customersCount.rows[0].count);
    
    // فحص جدول ديون العملاء
    try {
      const debtsCount = await client.query('SELECT COUNT(*) FROM customer_debts');
      console.log('💳 عدد الديون:', debtsCount.rows[0].count);
      
      // فحص أعمدة جدول الديون
      const debtColumns = await client.query(`
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'customer_debts' AND table_schema = $1
      `, [process.env.DB_SCHEMA]);
      
      console.log('\n🔍 أعمدة جدول الديون:');
      debtColumns.rows.forEach(col => {
        console.log(`  - ${col.column_name}: ${col.data_type}`);
      });
      
    } catch (error) {
      console.log('❌ خطأ في جدول الديون:', error.message);
    }
    
    client.release();
    console.log('\n🎉 انتهى الفحص بنجاح!');
    
  } catch (error) {
    console.error('\n❌ خطأ في الاتصال:', error.message);
    console.error('تفاصيل الخطأ:', error.stack);
  }
  
  process.exit(0);
}

checkDatabase();
