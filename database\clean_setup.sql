-- إعداد قاعدة البيانات النظيف - بدون بيانات تجريبية
-- Clean Database Setup - No Sample Data
-- للاستخدام في pgAdmin Query Tool

-- ===================================
-- 1. إنشاء الامتدادات والأنواع
-- ===================================

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

CREATE SCHEMA IF NOT EXISTS pos_system;
SET search_path TO pos_system, public;

-- إنشاء أنواع البيانات المخصصة
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('admin', 'manager', 'cashier');
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'payment_method') THEN
        CREATE TYPE payment_method AS ENUM ('cash', 'card', 'credit', 'mixed');
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'sale_status') THEN
        CREATE TYPE sale_status AS ENUM ('completed', 'pending', 'cancelled', 'refunded');
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'transaction_type') THEN
        CREATE TYPE transaction_type AS ENUM ('income', 'expense');
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'connection_type') THEN
        CREATE TYPE connection_type AS ENUM ('usb', 'network', 'bluetooth');
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'paper_type') THEN
        CREATE TYPE paper_type AS ENUM ('thermal', 'normal');
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'print_density') THEN
        CREATE TYPE print_density AS ENUM ('light', 'medium', 'dark');
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'print_speed') THEN
        CREATE TYPE print_speed AS ENUM ('slow', 'medium', 'fast');
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'font_size') THEN
        CREATE TYPE font_size AS ENUM ('small', 'medium', 'large');
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'line_spacing') THEN
        CREATE TYPE line_spacing AS ENUM ('tight', 'normal', 'loose');
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'theme_type') THEN
        CREATE TYPE theme_type AS ENUM ('dark', 'light');
    END IF;
END $$;

-- ===================================
-- 2. الجداول الأساسية
-- ===================================

-- جدول الإعدادات العامة
CREATE TABLE IF NOT EXISTS settings (
    id SERIAL PRIMARY KEY,
    store_name VARCHAR(255) NOT NULL DEFAULT '',
    store_address TEXT DEFAULT '',
    store_phone VARCHAR(50) DEFAULT '',
    store_email VARCHAR(255) DEFAULT '',
    store_tax_number VARCHAR(100) DEFAULT '',
    store_logo TEXT DEFAULT '',
    user_name VARCHAR(255) NOT NULL DEFAULT '',
    user_email VARCHAR(255) DEFAULT '',
    user_role user_role DEFAULT 'admin',
    currency VARCHAR(10) DEFAULT 'DZD',
    language VARCHAR(10) DEFAULT 'ar',
    timezone VARCHAR(50) DEFAULT 'Africa/Algiers',
    date_format VARCHAR(20) DEFAULT 'DD/MM/YYYY',
    tax_rate DECIMAL(5,2) DEFAULT 19.00,
    enable_tax BOOLEAN DEFAULT true,
    enable_discount BOOLEAN DEFAULT true,
    enable_barcode BOOLEAN DEFAULT true,
    low_stock_alert BOOLEAN DEFAULT true,
    email_notifications BOOLEAN DEFAULT false,
    sound_notifications BOOLEAN DEFAULT true,
    theme theme_type DEFAULT 'dark',
    primary_color VARCHAR(20) DEFAULT '#3b82f6',
    font_size font_size DEFAULT 'medium',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول إعدادات الطابعة
CREATE TABLE IF NOT EXISTS printer_settings (
    id SERIAL PRIMARY KEY,
    connection_type connection_type DEFAULT 'usb',
    printer_name VARCHAR(255) DEFAULT '',
    ip_address INET,
    port INTEGER DEFAULT 9100,
    bluetooth_address VARCHAR(50) DEFAULT '',
    paper_width INTEGER DEFAULT 58,
    paper_type paper_type DEFAULT 'thermal',
    print_density print_density DEFAULT 'medium',
    print_speed print_speed DEFAULT 'medium',
    print_logo BOOLEAN DEFAULT true,
    print_header BOOLEAN DEFAULT true,
    print_footer BOOLEAN DEFAULT true,
    print_barcode BOOLEAN DEFAULT true,
    print_qr_code BOOLEAN DEFAULT false,
    auto_open_cash_drawer BOOLEAN DEFAULT false,
    print_sound BOOLEAN DEFAULT true,
    copies INTEGER DEFAULT 1,
    cut_paper BOOLEAN DEFAULT true,
    header_text TEXT DEFAULT '',
    footer_text TEXT DEFAULT 'شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى',
    font_size font_size DEFAULT 'medium',
    font_family VARCHAR(50) DEFAULT 'arial',
    line_spacing line_spacing DEFAULT 'normal',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الفئات
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    color VARCHAR(20) DEFAULT '#3b82f6',
    icon VARCHAR(50) DEFAULT 'Package',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المنتجات
CREATE TABLE IF NOT EXISTS products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    barcode VARCHAR(100) UNIQUE,
    category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    cost DECIMAL(10,2) NOT NULL CHECK (cost >= 0),
    stock INTEGER NOT NULL DEFAULT 0 CHECK (stock >= 0),
    min_stock INTEGER DEFAULT 0 CHECK (min_stock >= 0),
    unit VARCHAR(50) DEFAULT 'قطعة',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول العملاء
CREATE TABLE IF NOT EXISTS customers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    address TEXT,
    email VARCHAR(255),
    balance DECIMAL(10,2) DEFAULT 0.00,
    credit_limit DECIMAL(10,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الموردين
CREATE TABLE IF NOT EXISTS suppliers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    address TEXT,
    email VARCHAR(255),
    balance DECIMAL(10,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ===================================
-- 3. جداول المبيعات والمعاملات
-- ===================================

-- جدول المبيعات
CREATE TABLE IF NOT EXISTS sales (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sale_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
    subtotal DECIMAL(10,2) NOT NULL CHECK (subtotal >= 0),
    tax_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (tax_amount >= 0),
    discount_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (discount_amount >= 0),
    total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount >= 0),
    payment_method payment_method NOT NULL,
    amount_paid DECIMAL(10,2) NOT NULL CHECK (amount_paid >= 0),
    change_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (change_amount >= 0),
    status sale_status DEFAULT 'completed',
    notes TEXT,
    cashier_name VARCHAR(255),
    receipt_printed BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول عناصر المبيعات
CREATE TABLE IF NOT EXISTS sale_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sale_id UUID NOT NULL REFERENCES sales(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE RESTRICT,
    product_name VARCHAR(255) NOT NULL,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(10,2) NOT NULL CHECK (unit_price >= 0),
    discount DECIMAL(10,2) DEFAULT 0.00 CHECK (discount >= 0),
    total_price DECIMAL(10,2) NOT NULL CHECK (total_price >= 0),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول فواتير المشتريات
CREATE TABLE IF NOT EXISTS purchase_invoices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    supplier_id UUID REFERENCES suppliers(id) ON DELETE SET NULL,
    subtotal DECIMAL(10,2) NOT NULL CHECK (subtotal >= 0),
    tax_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (tax_amount >= 0),
    discount_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (discount_amount >= 0),
    total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount >= 0),
    amount_paid DECIMAL(10,2) DEFAULT 0.00 CHECK (amount_paid >= 0),
    remaining_amount DECIMAL(10,2) GENERATED ALWAYS AS (total_amount - amount_paid) STORED,
    is_paid BOOLEAN GENERATED ALWAYS AS (amount_paid >= total_amount) STORED,
    invoice_date DATE NOT NULL DEFAULT CURRENT_DATE,
    due_date DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول عناصر فواتير المشتريات
CREATE TABLE IF NOT EXISTS purchase_invoice_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID NOT NULL REFERENCES purchase_invoices(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE RESTRICT,
    product_name VARCHAR(255) NOT NULL,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_cost DECIMAL(10,2) NOT NULL CHECK (unit_cost >= 0),
    discount DECIMAL(10,2) DEFAULT 0.00 CHECK (discount >= 0),
    total_cost DECIMAL(10,2) NOT NULL CHECK (total_cost >= 0),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المعاملات المالية
CREATE TABLE IF NOT EXISTS financial_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_number VARCHAR(50) UNIQUE NOT NULL,
    type transaction_type NOT NULL,
    category VARCHAR(100) NOT NULL,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    description TEXT NOT NULL,
    reference_id UUID,
    reference_type VARCHAR(50),
    payment_method payment_method,
    notes TEXT,
    transaction_date DATE NOT NULL DEFAULT CURRENT_DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول حركة المخزون
CREATE TABLE IF NOT EXISTS inventory_movements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    movement_type VARCHAR(20) NOT NULL CHECK (movement_type IN ('in', 'out', 'adjustment')),
    quantity INTEGER NOT NULL,
    previous_stock INTEGER NOT NULL,
    new_stock INTEGER NOT NULL,
    reason VARCHAR(100) NOT NULL,
    reference_id UUID,
    reference_type VARCHAR(50),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول ديون العملاء
CREATE TABLE IF NOT EXISTS customer_debts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    sale_id UUID REFERENCES sales(id) ON DELETE SET NULL,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    paid_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (paid_amount >= 0),
    remaining_amount DECIMAL(10,2) GENERATED ALWAYS AS (amount - paid_amount) STORED,
    is_paid BOOLEAN GENERATED ALWAYS AS (paid_amount >= amount) STORED,
    debt_date DATE NOT NULL DEFAULT CURRENT_DATE,
    due_date DATE,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'partial', 'paid')),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول مدفوعات ديون العملاء
CREATE TABLE IF NOT EXISTS customer_debt_payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    debt_id UUID NOT NULL REFERENCES customer_debts(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    payment_method payment_method NOT NULL,
    payment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول ديون الموردين
CREATE TABLE IF NOT EXISTS supplier_debts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    supplier_id UUID NOT NULL REFERENCES suppliers(id) ON DELETE CASCADE,
    purchase_id UUID REFERENCES purchase_invoices(id) ON DELETE SET NULL,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    paid_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (paid_amount >= 0),
    remaining_amount DECIMAL(10,2) GENERATED ALWAYS AS (amount - paid_amount) STORED,
    is_paid BOOLEAN GENERATED ALWAYS AS (paid_amount >= amount) STORED,
    debt_date DATE NOT NULL DEFAULT CURRENT_DATE,
    due_date DATE,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'overdue')),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول مدفوعات ديون الموردين
CREATE TABLE IF NOT EXISTS supplier_debt_payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    debt_id UUID NOT NULL REFERENCES supplier_debts(id) ON DELETE CASCADE,
    supplier_id UUID NOT NULL REFERENCES suppliers(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    payment_method payment_method NOT NULL,
    payment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول سجل المراجعة
CREATE TABLE IF NOT EXISTS audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_name VARCHAR(255) NOT NULL,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    role user_role DEFAULT 'cashier',
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول جلسات المستخدمين
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول سجلات النظام
CREATE TABLE IF NOT EXISTS system_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    level VARCHAR(20) NOT NULL CHECK (level IN ('info', 'warning', 'error', 'critical')),
    message TEXT NOT NULL,
    module VARCHAR(100),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    ip_address INET,
    details JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ===================================
-- 4. إنشاء الفهارس للأداء
-- ===================================

CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode);
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);
CREATE INDEX IF NOT EXISTS idx_products_active ON products(is_active);
CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone);
CREATE INDEX IF NOT EXISTS idx_suppliers_name ON suppliers(name);
CREATE INDEX IF NOT EXISTS idx_categories_active ON categories(is_active);
CREATE INDEX IF NOT EXISTS idx_sales_customer ON sales(customer_id);
CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(created_at);
CREATE INDEX IF NOT EXISTS idx_sales_status ON sales(status);
CREATE INDEX IF NOT EXISTS idx_sale_items_sale ON sale_items(sale_id);
CREATE INDEX IF NOT EXISTS idx_sale_items_product ON sale_items(product_id);
CREATE INDEX IF NOT EXISTS idx_purchase_invoices_supplier ON purchase_invoices(supplier_id);
CREATE INDEX IF NOT EXISTS idx_financial_transactions_type ON financial_transactions(type);
CREATE INDEX IF NOT EXISTS idx_inventory_movements_product ON inventory_movements(product_id);
CREATE INDEX IF NOT EXISTS idx_customer_debts_customer ON customer_debts(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_debts_status ON customer_debts(status);
CREATE INDEX IF NOT EXISTS idx_supplier_debts_supplier ON supplier_debts(supplier_id);
CREATE INDEX IF NOT EXISTS idx_supplier_debts_status ON supplier_debts(status);
CREATE INDEX IF NOT EXISTS idx_supplier_debt_payments_debt ON supplier_debt_payments(debt_id);
CREATE INDEX IF NOT EXISTS idx_supplier_debt_payments_supplier ON supplier_debt_payments(supplier_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_user ON audit_log(user_name);
CREATE INDEX IF NOT EXISTS idx_audit_log_table ON audit_log(table_name);
CREATE INDEX IF NOT EXISTS idx_audit_log_date ON audit_log(created_at);
CREATE INDEX IF NOT EXISTS idx_audit_log_action ON audit_log(action);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs(level);
CREATE INDEX IF NOT EXISTS idx_system_logs_module ON system_logs(module);
CREATE INDEX IF NOT EXISTS idx_system_logs_date ON system_logs(created_at);

-- ===================================
-- 5. المشغلات والوظائف الأساسية
-- ===================================

-- وظيفة تحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- تطبيق المشغل على الجداول
CREATE TRIGGER update_settings_updated_at BEFORE UPDATE ON settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_printer_settings_updated_at BEFORE UPDATE ON printer_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customers_updated_at BEFORE UPDATE ON customers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_suppliers_updated_at BEFORE UPDATE ON suppliers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sales_updated_at BEFORE UPDATE ON sales
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_supplier_debts_updated_at BEFORE UPDATE ON supplier_debts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customer_debts_updated_at BEFORE UPDATE ON customer_debts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- وظيفة توليد رقم البيع
CREATE SEQUENCE IF NOT EXISTS sale_number_seq START 1;
CREATE OR REPLACE FUNCTION generate_sale_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.sale_number IS NULL OR NEW.sale_number = '' THEN
        NEW.sale_number := 'SALE-' || TO_CHAR(CURRENT_DATE, 'YYYYMMDD') || '-' ||
                          LPAD(NEXTVAL('sale_number_seq')::TEXT, 4, '0');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_generate_sale_number
    BEFORE INSERT ON sales
    FOR EACH ROW EXECUTE FUNCTION generate_sale_number();

-- وظيفة توليد رقم فاتورة الشراء
CREATE SEQUENCE IF NOT EXISTS purchase_number_seq START 1;
CREATE OR REPLACE FUNCTION generate_purchase_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.invoice_number IS NULL OR NEW.invoice_number = '' THEN
        NEW.invoice_number := 'PUR-' || TO_CHAR(CURRENT_DATE, 'YYYYMMDD') || '-' ||
                             LPAD(NEXTVAL('purchase_number_seq')::TEXT, 4, '0');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_generate_purchase_number
    BEFORE INSERT ON purchase_invoices
    FOR EACH ROW EXECUTE FUNCTION generate_purchase_number();

-- ===================================
-- 6. إدراج الإعدادات الافتراضية فقط
-- ===================================

INSERT INTO settings DEFAULT VALUES ON CONFLICT DO NOTHING;
INSERT INTO printer_settings DEFAULT VALUES ON CONFLICT DO NOTHING;

-- ===================================
-- 7. رسالة إتمام
-- ===================================

DO $$
BEGIN
    RAISE NOTICE '✅ تم إنشاء قاعدة البيانات بنجاح!';
    RAISE NOTICE '✅ Database created successfully!';
    RAISE NOTICE '';
    RAISE NOTICE '📊 الجداول: 22 جدول رئيسي';
    RAISE NOTICE '📊 Tables: 22 main tables';
    RAISE NOTICE '';
    RAISE NOTICE '🔍 الفهارس: 35 فهرس للأداء';
    RAISE NOTICE '🔍 Indexes: 35 performance indexes';
    RAISE NOTICE '';
    RAISE NOTICE '⚡ المشغلات: 12 مشغل تلقائي';
    RAISE NOTICE '⚡ Triggers: 12 automatic triggers';
    RAISE NOTICE '';
    RAISE NOTICE '🚫 بدون بيانات تجريبية - جاهز للاستخدام';
    RAISE NOTICE '🚫 No sample data - Ready for production';
    RAISE NOTICE '';
    RAISE NOTICE '🎉 يمكنك الآن البدء في استخدام النظام!';
    RAISE NOTICE '🎉 You can now start using the system!';
END $$;
