const axios = require('axios');

// اختبار إحصائيات لوحة التحكم
async function testDashboardStats() {
  try {
    console.log('🧪 اختبار إحصائيات لوحة التحكم...');

    const response = await axios.get('http://localhost:5002/api/financial/dashboard-stats', {
      timeout: 10000
    });

    console.log('✅ نجح تحميل الإحصائيات!');
    console.log('📊 الإحصائيات:');
    console.log('📅 اليوم:');
    console.log(`   - المبيعات: ${response.data.stats.today.sales} دج`);
    console.log(`   - الطلبات (الفواتير): ${response.data.stats.today.orders} فاتورة`);
    console.log(`   - الأرباح: ${response.data.stats.today.profit} دج`);
    
    console.log('📈 الإجمالي:');
    console.log(`   - الإيرادات: ${response.data.stats.total.revenue} دج`);
    console.log(`   - الطلبات (الفواتير): ${response.data.stats.total.orders} فاتورة`);
    console.log(`   - الأرباح: ${response.data.stats.total.profit} دج`);
    console.log(`   - المنتجات: ${response.data.stats.total.products} منتج`);

  } catch (error) {
    console.error('❌ فشل في تحميل الإحصائيات:');
    
    if (error.response) {
      console.error('📋 كود الحالة:', error.response.status);
      console.error('📋 رسالة الخطأ:', error.response.data);
    } else if (error.request) {
      console.error('📋 لم يتم الحصول على رد من الخادم');
    } else {
      console.error('📋 خطأ في إعداد الطلب:', error.message);
    }
  }
}

// تشغيل الاختبار
testDashboardStats();
