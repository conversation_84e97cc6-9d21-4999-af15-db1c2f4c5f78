const express = require('express');
const router = express.Router();
const pool = require('../database');

// عرض جميع المنتجات مع الفئات
router.get('/', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT
        p.*,
        c.name as category_name,
        c.color as category_color
      FROM pos_system.products p
      LEFT JOIN pos_system.categories c ON p.category_id = c.id
      WHERE p.is_active = true
      ORDER BY p.created_at DESC
    `);
    res.json(result.rows);
  } catch (error) {
    console.error('خطأ في عرض المنتجات:', error);
    res.status(500).json({ error: 'خطأ في عرض المنتجات' });
  }
});

// عرض منتج واحد
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query(`
      SELECT
        p.*,
        c.name as category_name
      FROM pos_system.products p
      LEFT JOIN pos_system.categories c ON p.category_id = c.id
      WHERE p.id = $1 AND p.is_active = true
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'المنتج غير موجود' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('خطأ في عرض المنتج:', error);
    res.status(500).json({ error: 'خطأ في عرض المنتج' });
  }
});

// إضافة منتج جديد
router.post('/', async (req, res) => {
  try {
    const {
      name,
      description,
      barcode,
      category_id,
      price,
      cost,
      stock,
      min_stock
    } = req.body;

    console.log('📦 بيانات المنتج المرسلة:', req.body);

    const result = await pool.query(`
      INSERT INTO pos_system.products (
        name, description, barcode, category_id,
        price, cost, stock, min_stock
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `, [
      name,
      description,
      barcode,
      category_id,
      price,
      cost,
      stock || 0,
      min_stock || 0
    ]);

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('خطأ في إضافة المنتج:', error);
    if (error.code === '23505') {
      res.status(400).json({ error: 'الباركود موجود مسبقاً' });
    } else {
      res.status(500).json({ error: 'خطأ في إضافة المنتج' });
    }
  }
});

// تعديل منتج
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      barcode,
      category_id,
      price,
      cost,
      stock,
      min_stock
    } = req.body;

    const result = await pool.query(`
      UPDATE pos_system.products
      SET
        name = $1,
        description = $2,
        barcode = $3,
        category_id = $4,
        price = $5,
        cost = $6,
        stock = $7,
        min_stock = $8,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $9
      RETURNING *
    `, [
      name,
      description,
      barcode,
      category_id,
      price,
      cost,
      stock,
      min_stock,
      id
    ]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'المنتج غير موجود' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('خطأ في تعديل المنتج:', error);
    res.status(500).json({ error: 'خطأ في تعديل المنتج' });
  }
});

// حذف منتج (حذف منطقي)
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(
      'UPDATE pos_system.products SET is_active = false WHERE id = $1 RETURNING *',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'المنتج غير موجود' });
    }

    res.json({ message: 'تم حذف المنتج بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف المنتج:', error);
    res.status(500).json({ error: 'خطأ في حذف المنتج' });
  }
});

// البحث في المنتجات
router.get('/search/:query', async (req, res) => {
  try {
    const { query } = req.params;
    const result = await pool.query(`
      SELECT
        p.*,
        c.name as category_name
      FROM pos_system.products p
      LEFT JOIN pos_system.categories c ON p.category_id = c.id
      WHERE p.is_active = true
      AND (
        p.name ILIKE $1 OR
        p.barcode ILIKE $1 OR
        p.description ILIKE $1
      )
      ORDER BY p.name
    `, [`%${query}%`]);

    res.json(result.rows);
  } catch (error) {
    console.error('خطأ في البحث:', error);
    res.status(500).json({ error: 'خطأ في البحث' });
  }
});

module.exports = router;
