const express = require('express');
const cors = require('cors');
const pool = require('./database');

const app = express();
const PORT = 5000;

// Middleware
app.use(cors());
app.use(express.json());

console.log('🚀 بدء تشغيل سيرفر اختبار الأقسام...');

// اختبار الاتصال
app.get('/api/test', (req, res) => {
  console.log('📞 تم استلام طلب اختبار');
  res.json({ 
    message: '✅ Backend API يعمل بنجاح!',
    timestamp: new Date().toISOString()
  });
});

// عرض الأقسام
app.get('/api/categories', async (req, res) => {
  try {
    console.log('📂 تم استلام طلب عرض الأقسام');
    
    const result = await pool.query(
      'SELECT * FROM pos_system.categories WHERE is_active = true ORDER BY created_at DESC'
    );
    
    // إضافة icon افتراضي للأقسام
    const categoriesWithIcons = result.rows.map(category => ({
      ...category,
      icon: 'Package' // icon افتراضي
    }));
    
    console.log('✅ إرسال بيانات الأقسام:', categoriesWithIcons.length, 'قسم');
    res.json(categoriesWithIcons);
  } catch (error) {
    console.error('❌ خطأ في عرض الفئات:', error);
    res.status(500).json({ error: 'خطأ في عرض الفئات' });
  }
});

// إضافة قسم
app.post('/api/categories', async (req, res) => {
  try {
    console.log('📂 تم استلام طلب إضافة قسم');
    console.log('📋 البيانات المرسلة:', req.body);
    
    const { name, description, color, icon } = req.body;
    
    // التحقق من البيانات المطلوبة
    if (!name) {
      console.log('❌ اسم القسم مطلوب');
      return res.status(400).json({ error: 'اسم القسم مطلوب' });
    }
    
    console.log('🔍 محاولة إدراج القسم في قاعدة البيانات...');
    
    // استعلام بدون عمود icon
    const result = await pool.query(
      `INSERT INTO pos_system.categories (name, description, color) 
       VALUES ($1, $2, $3) 
       RETURNING *`,
      [name, description || '', color || '#3b82f6']
    );
    
    console.log('✅ تم إدراج القسم في قاعدة البيانات:', result.rows[0]);
    
    // إضافة icon للاستجابة
    const categoryWithIcon = {
      ...result.rows[0],
      icon: icon || 'Package'
    };
    
    console.log('✅ إرسال استجابة القسم الجديد:', categoryWithIcon);
    res.status(201).json(categoryWithIcon);
    
  } catch (error) {
    console.error('❌ خطأ في إضافة الفئة:');
    console.error('رسالة:', error.message);
    console.error('كود:', error.code);
    console.error('تفاصيل:', error.detail);
    console.error('مكان:', error.where);
    
    if (error.code === '23505') {
      res.status(400).json({ error: 'اسم الفئة موجود مسبقاً' });
    } else {
      res.status(500).json({ error: 'خطأ في إضافة الفئة: ' + error.message });
    }
  }
});

// معالج الأخطاء العام
app.use((error, req, res, next) => {
  console.error('❌ خطأ عام:', error);
  res.status(500).json({ error: 'خطأ داخلي في الخادم' });
});

app.listen(PORT, () => {
  console.log(`🚀 سيرفر اختبار الأقسام يعمل على المنفذ ${PORT}`);
  console.log(`🔗 API متاح على: http://localhost:${PORT}/api`);
});

// معالج إغلاق الخادم
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف الخادم...');
  process.exit(0);
});
