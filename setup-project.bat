@echo off
chcp 65001 > nul
title 🛠️ كشير توسار - إعداد المشروع
color 0D

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🛠️ إعداد المشروع                         ║
echo ║                     كشير توسار                             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 هذا المعالج سيقوم بإعداد المشروع من الصفر
echo.

REM التحقق من المتطلبات
echo 🔍 التحقق من المتطلبات الأساسية...

REM فحص Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo.
    echo 📥 يرجى تحميل وتثبيت Node.js من:
    echo https://nodejs.org/
    echo.
    echo 🔧 بعد التثبيت، أعد تشغيل هذا الملف
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set "node_version=%%i"
echo ✅ Node.js مثبت - الإصدار: %node_version%

REM فحص npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متوفر
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set "npm_version=%%i"
echo ✅ npm متوفر - الإصدار: %npm_version%

echo.

REM خيارات الإعداد
echo 📋 خيارات الإعداد:
echo.
echo 1. 🚀 إعداد سريع (مستحسن)
echo 2. 🔧 إعداد مخصص
echo 3. 🧹 إعادة إعداد (حذف وإعادة تثبيت)
echo 4. 🔍 فحص الحالة الحالية فقط
echo.
set /p setup_type="اختر نوع الإعداد (1-4): "

if "%setup_type%"=="1" goto quick_setup
if "%setup_type%"=="2" goto custom_setup
if "%setup_type%"=="3" goto reset_setup
if "%setup_type%"=="4" goto check_status
goto invalid_choice

:quick_setup
echo.
echo 🚀 بدء الإعداد السريع...
echo.

REM تثبيت التبعيات الرئيسية
echo 📦 تثبيت التبعيات الرئيسية...
call npm install
if %errorlevel% neq 0 goto error_exit

REM تثبيت تبعيات Backend
echo 📦 تثبيت تبعيات Backend...
cd backend
call npm install
if %errorlevel% neq 0 (
    cd ..
    goto error_exit
)
cd ..

REM تثبيت Electron
echo 📦 تثبيت Electron...
call npm install electron electron-builder --save-dev
if %errorlevel% neq 0 goto error_exit

REM إنشاء مجلدات مفقودة
echo 📁 إنشاء المجلدات المطلوبة...
if not exist "assets" mkdir assets
if not exist "dist" mkdir dist
if not exist "database" mkdir database

echo ✅ تم الإعداد السريع بنجاح!
goto success

:custom_setup
echo.
echo 🔧 الإعداد المخصص...
echo.

echo 📋 اختر المكونات للتثبيت:
echo.
set /p install_main="1. تثبيت التبعيات الرئيسية؟ (y/n): "
set /p install_backend="2. تثبيت تبعيات Backend؟ (y/n): "
set /p install_electron="3. تثبيت Electron؟ (y/n): "
set /p install_dev="4. تثبيت أدوات التطوير؟ (y/n): "

echo.
echo 🔄 بدء التثبيت المخصص...

if /i "%install_main%"=="y" (
    echo 📦 تثبيت التبعيات الرئيسية...
    call npm install
    if %errorlevel% neq 0 goto error_exit
)

if /i "%install_backend%"=="y" (
    echo 📦 تثبيت تبعيات Backend...
    cd backend
    call npm install
    if %errorlevel% neq 0 (
        cd ..
        goto error_exit
    )
    cd ..
)

if /i "%install_electron%"=="y" (
    echo 📦 تثبيت Electron...
    call npm install electron electron-builder --save-dev
    if %errorlevel% neq 0 goto error_exit
)

if /i "%install_dev%"=="y" (
    echo 📦 تثبيت أدوات التطوير...
    call npm install @types/node @types/react @types/react-dom --save-dev
    if %errorlevel% neq 0 goto error_exit
)

echo ✅ تم الإعداد المخصص بنجاح!
goto success

:reset_setup
echo.
echo 🧹 إعادة الإعداد...
echo.

echo ⚠️  هذا سيحذف جميع التبعيات المثبتة ويعيد تثبيتها
set /p confirm="هل أنت متأكد؟ (y/n): "
if /i not "%confirm%"=="y" goto end

echo 🗑️  حذف node_modules...
if exist "node_modules" (
    echo جاري حذف node_modules الرئيسي...
    rmdir /s /q "node_modules"
)

if exist "backend\node_modules" (
    echo جاري حذف backend\node_modules...
    rmdir /s /q "backend\node_modules"
)

echo 🗑️  حذف ملفات القفل...
if exist "package-lock.json" del "package-lock.json"
if exist "backend\package-lock.json" del "backend\package-lock.json"

echo 📦 إعادة تثبيت التبعيات...
call npm install
if %errorlevel% neq 0 goto error_exit

cd backend
call npm install
if %errorlevel% neq 0 (
    cd ..
    goto error_exit
)
cd ..

call npm install electron electron-builder --save-dev
if %errorlevel% neq 0 goto error_exit

echo ✅ تم إعادة الإعداد بنجاح!
goto success

:check_status
echo.
echo 🔍 فحص الحالة الحالية...
echo.

REM فحص التبعيات الرئيسية
if exist "node_modules" (
    echo ✅ node_modules موجود
    
    if exist "node_modules\react" (
        echo ✅ React مثبت
    ) else (
        echo ❌ React غير مثبت
    )
    
    if exist "node_modules\electron" (
        echo ✅ Electron مثبت
    ) else (
        echo ❌ Electron غير مثبت
    )
    
    if exist "node_modules\typescript" (
        echo ✅ TypeScript مثبت
    ) else (
        echo ❌ TypeScript غير مثبت
    )
) else (
    echo ❌ node_modules غير موجود
)

REM فحص تبعيات Backend
if exist "backend\node_modules" (
    echo ✅ backend\node_modules موجود
) else (
    echo ❌ backend\node_modules غير موجود
)

REM فحص الملفات المهمة
echo.
echo 📁 فحص الملفات المهمة:
set "important_files=package.json electron-main.js test-electron.bat quick-build.bat"
for %%f in (%important_files%) do (
    if exist "%%f" (
        echo ✅ %%f
    ) else (
        echo ❌ %%f مفقود
    )
)

goto end

:invalid_choice
echo ❌ اختيار غير صحيح
pause
exit /b 1

:error_exit
echo.
echo ❌ حدث خطأ أثناء الإعداد!
echo.
echo 🔧 حلول مقترحة:
echo 1. تأكد من اتصال الإنترنت
echo 2. أغلق برامج مكافحة الفيروسات مؤقتاً
echo 3. شغل Command Prompt كمدير
echo 4. امسح مجلد node_modules وأعد المحاولة
echo.
pause
exit /b 1

:success
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎉 تم الإعداد بنجاح!                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 الخطوات التالية:
echo.
echo 1. 🔍 فحص صحة النظام:
echo    system-health-check.bat
echo.
echo 2. 🧪 اختبار التطبيق:
echo    test-electron.bat
echo.
echo 3. 🚀 تشغيل سريع:
echo    start-system.bat
echo.
echo 4. 🏗️ بناء ملف التثبيت:
echo    quick-build.bat
echo.

:end
pause
