{"name": "kashe<PERSON>-<PERSON><PERSON>", "productName": "كشير توسار - نظام نقاط البيع", "version": "1.0.0", "description": "نظام نقاط بيع متكامل باللغة العربية", "main": "main.js", "author": {"name": "فريق توسار", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://toosar.com", "scripts": {"electron": "electron .", "electron-dev": "NODE_ENV=development electron .", "build-electron": "electron-builder", "dist": "npm run build && electron-builder", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "build": {"appId": "com.toosar.kasheer", "productName": "كشير توسار", "directories": {"output": "dist-electron"}, "files": ["electron-main.js", "backend/**/*", "dist/**/*", "assets/**/*", "!backend/node_modules/**/*", "!**/*.map"], "extraResources": [{"from": "backend", "to": "backend", "filter": ["**/*", "!node_modules/**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "msi", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker", "artifactName": "كشير-توسار-${version}-${arch}.${ext}"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "installerIcon": "assets/icon.ico", "uninstallerIcon": "assets/icon.ico", "installerHeaderIcon": "assets/icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "كشير توسار", "include": "installer.nsh", "language": "1025", "displayLanguageSelector": false, "installerLanguages": ["Arabic"], "license": "LICENSE.txt"}, "msi": {"oneClick": false, "upgradeCode": "{12345678-1234-1234-1234-123456789012}", "shortcutName": "كشير توسار"}, "mac": {"target": "dmg", "icon": "assets/icon.icns", "category": "public.app-category.business"}, "linux": {"target": ["AppImage", "deb", "rpm"], "icon": "assets/icon.png", "category": "Office"}, "publish": null}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1"}, "dependencies": {"electron-updater": "^6.1.7"}}