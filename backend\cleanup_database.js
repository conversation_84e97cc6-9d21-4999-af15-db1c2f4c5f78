const pool = require('./database');

async function cleanupDatabase() {
  const client = await pool.connect();
  
  try {
    console.log('🧹 بدء تنظيف قاعدة البيانات...');
    
    // قائمة الجداول القديمة وغير المستخدمة
    const tablesToDrop = [
      'pos_system.purchases',           // نظام المشتريات القديم
      'pos_system.purchase_items',      // عناصر المشتريات القديمة
      'pos_system.expenses'             // جدول المصروفات القديم (سنستخدم financial_transactions)
    ];
    
    console.log('📋 الجداول المراد حذفها:');
    tablesToDrop.forEach(table => console.log(`   - ${table}`));
    
    // بدء معاملة قاعدة البيانات
    await client.query('BEGIN');
    
    try {
      for (const table of tablesToDrop) {
        try {
          // التحقق من وجود الجدول أولاً
          const checkResult = await client.query(`
            SELECT EXISTS (
              SELECT FROM information_schema.tables 
              WHERE table_schema = 'pos_system' 
              AND table_name = '${table.split('.')[1]}'
            );
          `);
          
          if (checkResult.rows[0].exists) {
            // حذف الجدول
            await client.query(`DROP TABLE IF EXISTS ${table} CASCADE`);
            console.log(`✅ تم حذف الجدول: ${table}`);
          } else {
            console.log(`ℹ️ الجدول غير موجود: ${table}`);
          }
        } catch (error) {
          console.log(`⚠️ خطأ في حذف ${table}: ${error.message}`);
        }
      }
      
      // عرض الجداول المتبقية قبل إنهاء المعاملة
      const remainingTables = await client.query(`
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'pos_system'
        ORDER BY table_name
      `);

      // تأكيد المعاملة
      await client.query('COMMIT');

      console.log('\n🎯 تم تنظيف قاعدة البيانات بنجاح!');
      console.log('\n📊 الجداول المتبقية (المستخدمة):');

      remainingTables.rows.forEach(row => {
        console.log(`   ✅ ${row.table_name}`);
      });
      
      console.log('\n💡 النظام الآن منظم ويستخدم:');
      console.log('   - purchase_invoices: فواتير المشتريات');
      console.log('   - purchase_invoice_items: تفاصيل المنتجات في الفواتير');
      console.log('   - financial_transactions: جميع المعاملات المالية (بدلاً من expenses)');
      console.log('   - suppliers: الموردين');
      console.log('   - products: المنتجات');
      console.log('   - sales: المبيعات');
      console.log('   - customers: العملاء');
      
    } catch (transactionError) {
      // إلغاء المعاملة في حالة الخطأ
      await client.query('ROLLBACK');
      throw transactionError;
    }
    
  } catch (error) {
    console.error('❌ خطأ في تنظيف قاعدة البيانات:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

// تشغيل التنظيف
cleanupDatabase();
