const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');

const app = express();
const PORT = 5000;

// إعداد قاعدة البيانات
const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'pos_system_db',
  user: 'postgres',
  password: 'toossar',
});

// تعيين schema افتراضي
pool.on('connect', (client) => {
  client.query('SET search_path TO pos_system, public');
});

app.use(cors());
app.use(express.json());

// مسار فواتير المشتريات
app.use('/api/purchases', require('./routes/purchases'));

// مسار اختبار
app.get('/api/test', (req, res) => {
  res.json({ message: 'الخادم يعمل!' });
});

app.listen(PORT, () => {
  console.log(`🚀 الخادم يعمل على المنفذ ${PORT}`);
});
