const pool = require('./database');

async function checkDatabaseStructure() {
  try {
    console.log('🔍 فحص هيكل قاعدة البيانات...');
    
    // فحص الجداول الجديدة
    const tables = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'pos_system' 
      AND table_name IN ('customer_debt_invoices', 'debt_invoice_items', 'debt_payments')
      ORDER BY table_name
    `);
    
    console.log('📋 الجداول الجديدة:');
    tables.rows.forEach(table => {
      console.log(`   ✅ ${table.table_name}`);
    });
    
    if (tables.rows.length === 0) {
      console.log('❌ لم يتم إنشاء الجداول الجديدة بعد!');
      return;
    }
    
    // فحص الأعمدة الجديدة في جدول sales
    const salesColumns = await pool.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_schema = 'pos_system' 
      AND table_name = 'sales'
      AND column_name IN ('debt_invoice_id', 'is_debt_sale', 'debt_status')
      ORDER BY column_name
    `);
    
    console.log('📊 الأعمدة الجديدة في جدول sales:');
    salesColumns.rows.forEach(col => {
      console.log(`   ✅ ${col.column_name} (${col.data_type})`);
    });
    
    // فحص البيانات الحالية
    const salesData = await pool.query(`
      SELECT 
        id,
        sale_number,
        payment_method,
        is_debt_sale,
        debt_status,
        debt_invoice_id,
        total_amount,
        status
      FROM pos_system.sales 
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    
    console.log('📄 عينة من المبيعات الحالية:');
    salesData.rows.forEach((sale, index) => {
      console.log(`${index + 1}. فاتورة ${sale.sale_number}:`);
      console.log(`   - طريقة الدفع: ${sale.payment_method}`);
      console.log(`   - بيع بالدين: ${sale.is_debt_sale || false}`);
      console.log(`   - حالة الدين: ${sale.debt_status || 'paid'}`);
      console.log(`   - المبلغ: ${sale.total_amount} دج`);
      console.log(`   - الحالة: ${sale.status}`);
      console.log('   ---');
    });
    
    // فحص فواتير الدين
    const debtInvoices = await pool.query(`
      SELECT 
        di.invoice_number,
        di.status,
        di.total_debt_amount,
        di.remaining_balance,
        c.name as customer_name
      FROM pos_system.customer_debt_invoices di
      JOIN pos_system.customers c ON di.customer_id = c.id
      ORDER BY di.created_at DESC
    `);
    
    console.log('💰 فواتير الدين:');
    if (debtInvoices.rows.length === 0) {
      console.log('   لا توجد فواتير دين حتى الآن');
    } else {
      debtInvoices.rows.forEach((invoice, index) => {
        console.log(`${index + 1}. ${invoice.invoice_number} - ${invoice.customer_name}`);
        console.log(`   - الحالة: ${invoice.status}`);
        console.log(`   - إجمالي الدين: ${invoice.total_debt_amount} دج`);
        console.log(`   - المتبقي: ${invoice.remaining_balance} دج`);
        console.log('   ---');
      });
    }
    
  } catch (error) {
    console.error('❌ خطأ في فحص قاعدة البيانات:', error);
  } finally {
    process.exit(0);
  }
}

checkDatabaseStructure();
