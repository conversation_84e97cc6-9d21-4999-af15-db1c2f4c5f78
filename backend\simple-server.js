const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 5001;

// Middleware
app.use(cors());
app.use(express.json());

// اختبار الاتصال
app.get('/api/test', (req, res) => {
  console.log('📞 تم استلام طلب اختبار');
  res.json({
    message: '✅ Backend API يعمل بنجاح!',
    timestamp: new Date().toISOString()
  });
});

// مسار المدينين المؤقت
app.get('/api/customers/debtors', (req, res) => {
  console.log('📞 تم استلام طلب المدينين');
  try {
    // إرجاع بيانات تجريبية مؤقتاً
    const mockDebtors = [
      {
        id: '82431516-ec6b-44a7-904c-52632c1ced43',
        name: 'أحمد محمد',
        phone: '0555123456',
        email: '<EMAIL>',
        address: 'الرياض، المملكة العربية السعودية',
        balance: 0,
        credit_limit: 5000,
        total_debt: 1500,
        paid_amount: 500,
        remaining_debt: 1000,
        debt_count: 2,
        created_at: '2024-01-15T10:30:00Z',
        updated_at: '2024-01-20T14:45:00Z'
      }
    ];
    res.json(mockDebtors);
  } catch (error) {
    console.error('❌ خطأ في مسار المدينين:', error);
    res.status(500).json({ error: 'خطأ في عرض المدينين' });
  }
});

// مسار ديون عميل محدد
app.get('/api/customers/:id/debts', (req, res) => {
  console.log('📞 تم استلام طلب ديون العميل:', req.params.id);
  try {
    // إرجاع بيانات تجريبية للديون
    const mockDebts = [
      {
        id: 'debt-1',
        customer_id: req.params.id,
        amount: 1000,
        paid_amount: 0,
        status: 'pending',
        sale_id: 'sale-1',
        created_at: '2024-01-15T10:30:00Z',
        updated_at: '2024-01-15T10:30:00Z'
      }
    ];
    res.json(mockDebts);
  } catch (error) {
    console.error('❌ خطأ في مسار ديون العميل:', error);
    res.status(500).json({ error: 'خطأ في عرض ديون العميل' });
  }
});

// مسار تسديد الدين
app.post('/api/customers/:id/pay-debt', (req, res) => {
  console.log('📞 تم استلام طلب تسديد دين للعميل:', req.params.id);
  console.log('📊 بيانات الدفع:', req.body);
  try {
    const { debt_id, amount, payment_method, notes } = req.body;

    if (!debt_id || !amount || amount <= 0) {
      return res.status(400).json({ error: 'بيانات الدفع غير صحيحة' });
    }

    // محاكاة نجاح العملية
    res.json({
      message: 'تم تسديد الدين بنجاح',
      paid_amount: parseFloat(amount),
      new_status: 'partial'
    });
  } catch (error) {
    console.error('❌ خطأ في تسديد الدين:', error);
    res.status(500).json({ error: 'خطأ في تسديد الدين' });
  }
});

// معالج الأخطاء العام
app.use((error, req, res, next) => {
  console.error('❌ خطأ عام:', error);
  res.status(500).json({ error: 'خطأ داخلي في الخادم' });
});

const server = app.listen(PORT, () => {
  console.log(`🚀 الخادم المبسط يعمل على المنفذ ${PORT}`);
  console.log(`🔗 API متاح على: http://localhost:${PORT}/api`);
  console.log('📡 السيرفر يعمل بشكل مستمر...');
});

// منع إغلاق السيرفر
process.on('SIGINT', () => {
  console.log('\n⚠️  تم محاولة إيقاف السيرفر - لكن السيرفر سيبقى يعمل');
});

process.on('SIGTERM', () => {
  console.log('\n⚠️  تم محاولة إنهاء السيرفر - لكن السيرفر سيبقى يعمل');
});

// إبقاء السيرفر يعمل
setInterval(() => {
  console.log(`⏰ السيرفر يعمل - ${new Date().toLocaleTimeString('ar-SA')}`);
}, 30000); // كل 30 ثانية
