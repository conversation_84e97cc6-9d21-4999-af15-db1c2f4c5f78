import React, { useState, useEffect } from 'react';
import {
  ShoppingCart,
  Plus,
  Search,
  Filter,
  Eye,
  Trash2,
  Package,
  DollarSign,
  Calendar,
  User,
  Phone,
  CheckCircle,
  AlertTriangle,
  Printer,
  Save,
  X,
  Minus,
  CreditCard,
  FileText,
  Check,
  Calculator
} from 'lucide-react';
import { useApp } from '../context/AppContext';

// 🎯 أنواع البيانات
interface PurchaseInvoice {
  id: string;
  invoice_number: string;
  supplier_id: string;
  supplier_name: string;
  supplier_phone: string;
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  amount_paid: number;
  remaining_amount: number;
  is_paid: boolean;
  invoice_date: string;
  due_date?: string;
  notes?: string;
  items_count: number;
  total_items: number;
  created_at: string;
}

interface PurchaseInvoiceItem {
  id?: string;
  product_id: string;
  product_name: string;
  quantity: number;
  unit_cost: number;
  discount: number;
  total_cost: number;
}

interface Supplier {
  id: string;
  name: string;
  phone: string;
  address: string;
  email: string;
  balance: number;
  total_purchases: number;
  total_debt: number;
  is_active: boolean;
}

interface Product {
  id: string;
  name: string;
  barcode: string;
  price: number;
  cost: number;
  stock: number;
  category_name: string;
}

const PurchaseInvoices: React.FC = () => {
  // 🎯 استخدام AppContext لإعادة تحميل البيانات
  const { loadProducts: reloadAppProducts } = useApp();

  // 🎯 الحالات الأساسية
  const [invoices, setInvoices] = useState<PurchaseInvoice[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'paid' | 'unpaid' | 'partial'>('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showSupplierModal, setShowSupplierModal] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<PurchaseInvoice | null>(null);

  // 🎯 حالات إنشاء الفاتورة
  const [newInvoice, setNewInvoice] = useState({
    supplier_id: '',
    items: [] as PurchaseInvoiceItem[],
    subtotal: 0,
    tax_amount: 0,
    discount_amount: 0,
    total_amount: 0,
    amount_paid: 0,
    invoice_date: new Date().toISOString().split('T')[0],
    due_date: '',
    notes: ''
  });

  const [currentItem, setCurrentItem] = useState<PurchaseInvoiceItem>({
    product_id: '',
    product_name: '',
    quantity: 1,
    unit_cost: 0,
    discount: 0,
    total_cost: 0
  });

  const [paymentAmount, setPaymentAmount] = useState(0);
  const [paymentNotes, setPaymentNotes] = useState('');

  // 🎯 حالة المورد الجديد
  const [newSupplier, setNewSupplier] = useState({
    name: '',
    phone: '',
    address: ''
  });

  // 🔄 تحميل البيانات
  useEffect(() => {
    loadInvoices();
    loadSuppliers();
    loadProducts();
  }, []);

  const loadInvoices = async () => {
    try {
      setLoading(true);
      console.log('🔄 تحميل فواتير المشتريات...');

      const response = await fetch('/api/purchases');
      console.log('📡 استجابة API:', response.status, response.statusText);

      if (response.ok) {
        const data = await response.json();

        // تحويل البيانات إلى الأرقام الصحيحة
        const processedData = data.map((invoice: any) => ({
          ...invoice,
          total_amount: parseFloat(invoice.total_amount) || 0,
          amount_paid: parseFloat(invoice.amount_paid) || 0,
          is_paid: (parseFloat(invoice.amount_paid) || 0) >= (parseFloat(invoice.total_amount) || 0)
        }));

        setInvoices(processedData);
        console.log('✅ تم تحميل فواتير المشتريات:', processedData.length);
      } else {
        console.error('❌ خطأ في الاستجابة:', response.status);
      }
    } catch (error) {
      console.error('❌ خطأ في تحميل فواتير المشتريات:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadSuppliers = async () => {
    try {
      console.log('🔄 تحميل الموردين...');

      const response = await fetch('/api/suppliers');
      console.log('📡 استجابة API الموردين:', response.status, response.statusText);

      if (response.ok) {
        const data = await response.json();
        setSuppliers(data);
        console.log('✅ تم تحميل الموردين:', data.length);
      } else {
        console.error('❌ خطأ في استجابة الموردين:', response.status);
      }
    } catch (error) {
      console.error('❌ خطأ في تحميل الموردين:', error);
    }
  };

  const loadProducts = async () => {
    try {
      console.log('🔄 تحميل المنتجات...');

      const response = await fetch('/api/products');
      console.log('📡 استجابة API المنتجات:', response.status, response.statusText);

      if (response.ok) {
        const data = await response.json();
        setProducts(data);
        console.log('✅ تم تحميل المنتجات:', data.length);
      } else {
        console.error('❌ خطأ في استجابة المنتجات:', response.status);
      }
    } catch (error) {
      console.error('❌ خطأ في تحميل المنتجات:', error);
    }
  };

  // 🎯 تصفية الفواتير
  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch =
      (invoice.invoice_number || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (invoice.supplier_name || '').toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      statusFilter === 'all' ||
      (statusFilter === 'paid' && invoice.is_paid) ||
      (statusFilter === 'unpaid' && !invoice.is_paid && (invoice.amount_paid || 0) === 0) ||
      (statusFilter === 'partial' && !invoice.is_paid && (invoice.amount_paid || 0) > 0);

    return matchesSearch && matchesStatus;
  });

  // 🎯 حساب الإحصائيات
  const stats = {
    totalInvoices: invoices.length,
    totalAmount: invoices.reduce((sum, inv) => sum + (inv.total_amount || 0), 0),
    paidAmount: invoices.reduce((sum, inv) => sum + (inv.amount_paid || 0), 0),
    unpaidAmount: invoices.reduce((sum, inv) => sum + ((inv.total_amount || 0) - (inv.amount_paid || 0)), 0),
    paidInvoices: invoices.filter(inv => inv.is_paid).length,
    unpaidInvoices: invoices.filter(inv => !inv.is_paid).length
  };

  // 🎯 إضافة منتج للفاتورة
  const addItemToInvoice = () => {
    if (!currentItem.product_id || currentItem.quantity <= 0 || currentItem.unit_cost <= 0) {
      alert('يرجى ملء جميع بيانات المنتج');
      return;
    }

    const totalCost = (currentItem.quantity * currentItem.unit_cost) - currentItem.discount;
    const itemWithTotal = { ...currentItem, total_cost: totalCost };

    setNewInvoice(prev => ({
      ...prev,
      items: [...prev.items, itemWithTotal]
    }));

    // إعادة تعيين المنتج الحالي
    setCurrentItem({
      product_id: '',
      product_name: '',
      quantity: 1,
      unit_cost: 0,
      discount: 0,
      total_cost: 0
    });

    // حساب المجاميع
    calculateTotals([...newInvoice.items, itemWithTotal]);
  };

  // 🎯 حساب المجاميع
  const calculateTotals = (items: PurchaseInvoiceItem[]) => {
    const subtotal = items.reduce((sum, item) => sum + item.total_cost, 0);
    const total = subtotal + newInvoice.tax_amount - newInvoice.discount_amount;

    setNewInvoice(prev => ({
      ...prev,
      subtotal,
      total_amount: total
    }));
  };

  // 🎯 إنشاء فاتورة جديدة
  const createInvoice = async () => {
    try {
      if (!newInvoice.supplier_id || newInvoice.items.length === 0) {
        alert('يرجى اختيار مورد وإضافة منتجات');
        return;
      }

      setLoading(true);

      // تحويل البيانات لتتطابق مع توقعات الباك إند
      const invoiceData = {
        supplier_id: newInvoice.supplier_id,
        subtotal: newInvoice.subtotal,
        tax_amount: newInvoice.tax_amount,
        discount_amount: newInvoice.discount_amount,
        total_amount: newInvoice.total_amount,
        amount_paid: newInvoice.amount_paid,
        due_date: newInvoice.due_date || null,
        notes: newInvoice.notes,
        items: newInvoice.items.map(item => ({
          product_id: item.product_id,
          product_name: item.product_name,
          quantity: item.quantity,
          unit_cost: item.unit_cost, // سيتم تحويله لـ unit_price في الخادم
          discount: item.discount,
          total_cost: item.total_cost // سيتم تحويله لـ total_price في الخادم
        }))
      };

      console.log('📋 بيانات الفاتورة المرسلة:', invoiceData);

      const response = await fetch('/api/purchases', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invoiceData)
      });

      if (response.ok) {
        const responseData = await response.json();
        console.log('✅ تم إنشاء فاتورة المشتريات بنجاح');
        console.log('📋 بيانات الاستجابة:', responseData);

        // استخراج بيانات الفاتورة من الاستجابة
        const createdInvoice = responseData.purchase || responseData;
        console.log('📋 بيانات الفاتورة المُنشأة:', createdInvoice);

        // التأكد من وجود معرف الفاتورة
        if (!createdInvoice.id) {
          console.error('❌ معرف الفاتورة مفقود في الاستجابة');
          alert('تم إنشاء الفاتورة لكن حدث خطأ في معالجة البيانات');
          return;
        }

        // 🔗 إنشاء دين تلقائي للمورد إذا كانت الفاتورة غير مدفوعة بالكامل
        const totalAmount = parseFloat(String(newInvoice.total_amount)) || 0;
        const amountPaid = parseFloat(String(newInvoice.amount_paid)) || 0;
        const remainingAmount = totalAmount - amountPaid;

        console.log(`🔍 فحص الدين:`);
        console.log(`   - إجمالي: ${totalAmount} دج`);
        console.log(`   - مدفوع: ${amountPaid} دج`);
        console.log(`   - متبقي: ${remainingAmount} دج`);

        if (remainingAmount > 0) {
          console.log(`💰 سيتم إنشاء دين للمورد: ${remainingAmount} دج`);
          console.log(`📋 معرف الفاتورة المُنشأة: ${createdInvoice.id}`);
          console.log(`🏢 معرف المورد: ${newInvoice.supplier_id}`);

          try {
            // إنشاء كائن فاتورة مع البيانات المطلوبة
            const invoiceForDebt = {
              id: createdInvoice.id,
              supplier_id: newInvoice.supplier_id,
              total_amount: totalAmount,
              amount_paid: amountPaid
            };

            await createSupplierDebt(invoiceForDebt, remainingAmount);
            console.log(`✅ تم استدعاء دالة إنشاء الدين بنجاح`);
          } catch (error) {
            console.error(`❌ خطأ في استدعاء دالة إنشاء الدين:`, error);
          }
        } else {
          console.log(`✅ الفاتورة مدفوعة بالكامل - لا حاجة لإنشاء دين`);
        }

        // 💰 إضافة المصروف تلقائياً إلى النظام (معطل مؤقت<|im_start|> لتجنب التكرار)
        // const invoiceForExpense = {
        //   id: createdInvoice.id,
        //   supplier_id: newInvoice.supplier_id,
        //   amount_paid: amountPaid
        // };
        // await addExpenseFromInvoice(invoiceForExpense);
        console.log('⚠️ إضافة المصروف التلقائي معطل مؤقت<|im_start|> لتجنب التكرار');

        setShowCreateModal(false);
        resetNewInvoice();
        loadInvoices();

        // 🔄 إعادة تحميل بيانات المنتجات في AppContext لتحديث المخزون
        await reloadAppProducts();
        console.log('🔄 تم إعادة تحميل بيانات المنتجات في AppContext');

        // 🔄 إعادة تحميل البيانات المحلية أيضاً
        await loadProducts();
        console.log('🔄 تم إعادة تحميل البيانات المحلية للمنتجات');

        // إشعار نجاح مع تفاصيل
        const supplier = suppliers.find(s => s.id === newInvoice.supplier_id);
        alert(`✅ تم إنشاء فاتورة المشتريات بنجاح!

📋 رقم الفاتورة: ${createdInvoice.id}
🏢 المورد: ${supplier?.name}
💰 المبلغ الإجمالي: ${formatCurrency(newInvoice.total_amount)}
💳 المبلغ المدفوع: ${formatCurrency(newInvoice.amount_paid)}
${remainingAmount > 0 ? `🔴 المبلغ المستحق: ${formatCurrency(remainingAmount)}` : '✅ مدفوعة بالكامل'}

${remainingAmount > 0 ? '🔗 تم إنشاء دين تلقائياً في صفحة الموردين' : ''}
💼 تم إضافة المصروف تلقائياً إلى النظام`);
      } else {
        const error = await response.json();
        alert(`خطأ: ${error.error}`);
      }
    } catch (error) {
      console.error('❌ خطأ في إنشاء فاتورة المشتريات:', error);
      alert('حدث خطأ في إنشاء الفاتورة');
    } finally {
      setLoading(false);
    }
  };

  // 🔗 إنشاء دين تلقائي للمورد
  const createSupplierDebt = async (invoice: any, debtAmount: number) => {
    try {
      console.log(`🔗 بدء إنشاء دين للمورد - فاتورة: ${invoice.id}, مبلغ: ${debtAmount}`);

      const supplier = suppliers.find(s => s.id === invoice.supplier_id);
      if (!supplier) {
        console.error(`❌ لم يتم العثور على المورد: ${invoice.supplier_id}`);
        return;
      }

      console.log(`🏢 المورد: ${supplier.name} (ID: ${supplier.id})`);

      const debtData = {
        supplier_id: invoice.supplier_id,
        amount: debtAmount,
        description: `فاتورة مشتريات رقم ${invoice.id}`,
        purchase_id: invoice.id,
        debt_date: new Date().toISOString(),
        status: 'pending'
      };

      console.log(`📤 إرسال بيانات الدين:`, debtData);

      const response = await fetch('/api/supplier-debts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(debtData)
      });

      console.log(`📡 استجابة الخادم: ${response.status} ${response.statusText}`);

      if (response.ok) {
        const result = await response.json();
        console.log('✅ تم إنشاء دين المورد تلقائياً:', result);
        console.log('✅ معرف الدين الجديد:', result.debt?.id);
      } else {
        const error = await response.text();
        console.error('❌ فشل في إنشاء دين المورد:', error);
        console.error('❌ حالة الاستجابة:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('❌ خطأ في إنشاء دين المورد:', error);
    }
  };



  // 🎯 إعادة تعيين الفاتورة الجديدة
  const resetNewInvoice = () => {
    setNewInvoice({
      supplier_id: '',
      items: [],
      subtotal: 0,
      tax_amount: 0,
      discount_amount: 0,
      total_amount: 0,
      amount_paid: 0,
      invoice_date: new Date().toISOString().split('T')[0],
      due_date: '',
      notes: ''
    });
  };

  // 🏢 إنشاء مورد جديد
  const createSupplier = async () => {
    try {
      if (!newSupplier.name.trim()) {
        alert('يرجى إدخال اسم المورد');
        return;
      }

      setLoading(true);
      console.log('🏢 إنشاء مورد جديد:', newSupplier.name);

      const response = await fetch('/api/suppliers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: newSupplier.name.trim(),
          phone: newSupplier.phone.trim(),
          address: newSupplier.address.trim(),
          balance: 0
        })
      });

      if (response.ok) {
        const savedSupplier = await response.json();
        console.log('✅ تم إنشاء المورد:', savedSupplier.name);

        // إعادة تحميل الموردين
        await loadSuppliers();

        // تحديد المورد الجديد في الفاتورة
        setNewInvoice(prev => ({
          ...prev,
          supplier_id: savedSupplier.id
        }));

        // إغلاق النافذة وإعادة تعيين النموذج
        setShowSupplierModal(false);
        setNewSupplier({
          name: '',
          phone: '',
          address: ''
        });

        alert(`تم إنشاء المورد بنجاح!\nاسم المورد: ${savedSupplier.name}`);
      } else {
        const error = await response.json();
        alert(`خطأ في إنشاء المورد: ${error.error}`);
      }
    } catch (error) {
      console.error('❌ خطأ في إنشاء المورد:', error);
      alert('حدث خطأ في إنشاء المورد');
    } finally {
      setLoading(false);
    }
  };

  // 🎯 تنسيق العملة
  const formatCurrency = (amount: number | string | null | undefined) => {
    if (amount === null || amount === undefined) return '0.00 دج';
    const numAmount = typeof amount === 'number' ? amount : parseFloat(String(amount)) || 0;
    return `${numAmount.toFixed(2)} دج`;
  };

  // 🎯 تنسيق التاريخ
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-DZ');
  };

  // 🎯 الحصول على لون الحالة
  const getStatusColor = (invoice: PurchaseInvoice) => {
    if (invoice.is_paid) return 'text-green-400 bg-green-500/20';
    if (invoice.amount_paid > 0) return 'text-yellow-400 bg-yellow-500/20';
    return 'text-red-400 bg-red-500/20';
  };

  // 🎯 الحصول على نص الحالة
  const getStatusText = (invoice: PurchaseInvoice) => {
    if (invoice.is_paid) return 'مدفوعة';
    if (invoice.amount_paid > 0) return 'مدفوعة جزئياً';
    return 'غير مدفوعة';
  };

  // 💳 معالجة دفعة جديدة
  const processPayment = async () => {
    if (!selectedInvoice || paymentAmount <= 0) {
      alert('يرجى إدخال مبلغ صحيح');
      return;
    }

    // حساب المبلغ المتبقي
    const remainingAmount = (selectedInvoice.total_amount || 0) - (selectedInvoice.amount_paid || 0);

    if (paymentAmount > remainingAmount) {
      alert('المبلغ المدخل أكبر من المبلغ المستحق');
      return;
    }

    try {
      setLoading(true);

      // تحديث الفاتورة
      const updatedAmountPaid = (selectedInvoice.amount_paid || 0) + paymentAmount;
      const updatedRemainingAmount = (selectedInvoice.total_amount || 0) - updatedAmountPaid;
      const isFullyPaid = updatedRemainingAmount <= 0;

      const response = await fetch(`/api/purchases/${selectedInvoice.id}/payment`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          amount_paid: updatedAmountPaid,
          notes: paymentNotes
        })
      });

      if (response.ok) {
        // 🔗 تحديث/تسديد دين المورد
        await updateSupplierDebt(selectedInvoice, paymentAmount, isFullyPaid);

        // 💰 المصروف يتم إضافته تلقائياً من خلال API تحديث دين المورد
        console.log('✅ المصروف سيتم إضافته تلقائياً من خلال API تحديث دين المورد');

        setShowPaymentModal(false);
        setPaymentAmount(0);
        setPaymentNotes('');
        setSelectedInvoice(null);
        loadInvoices();

        const supplier = suppliers.find(s => s.id === selectedInvoice.supplier_id);
        alert(`✅ تم تسجيل الدفعة بنجاح!

📋 فاتورة رقم: ${selectedInvoice.id}
🏢 المورد: ${supplier?.name}
💳 مبلغ الدفعة: ${formatCurrency(paymentAmount)}
💰 المبلغ المدفوع الإجمالي: ${formatCurrency(updatedAmountPaid)}
${isFullyPaid ? '✅ تم تسديد الفاتورة بالكامل' : `🔴 المبلغ المتبقي: ${formatCurrency(updatedRemainingAmount)}`}

🔗 تم تحديث دين المورد تلقائياً
💼 تم إضافة المصروف إلى النظام`);
      } else {
        const error = await response.json();
        alert(`خطأ: ${error.error}`);
      }
    } catch (error) {
      console.error('❌ خطأ في معالجة الدفعة:', error);
      alert('حدث خطأ في معالجة الدفعة');
    } finally {
      setLoading(false);
    }
  };

  // 🔗 تحديث دين المورد عند الدفع
  const updateSupplierDebt = async (invoice: PurchaseInvoice, paymentAmount: number, isFullyPaid: boolean) => {
    try {
      const response = await fetch(`/api/supplier-debts/invoice/${invoice.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          amount_paid: (invoice.amount_paid || 0) + paymentAmount,
          is_fully_paid: isFullyPaid,
          payment_date: new Date().toISOString()
        })
      });

      if (response.ok) {
        console.log('✅ تم تحديث دين المورد');
      } else {
        console.error('❌ فشل في تحديث دين المورد');
      }
    } catch (error) {
      console.error('❌ خطأ في تحديث دين المورد:', error);
    }
  };



  // 🗑️ حذف فاتورة مشتريات
  const deleteInvoice = async (invoice: PurchaseInvoice) => {
    const supplier = suppliers.find(s => s.id === invoice.supplier_id);
    const remainingAmount = (invoice.total_amount || 0) - (invoice.amount_paid || 0);

    const confirmMessage = `⚠️ هل أنت متأكد من حذف هذه الفاتورة؟

📋 رقم الفاتورة: ${invoice.id}
🏢 المورد: ${supplier?.name}
💰 المبلغ الإجمالي: ${formatCurrency(invoice.total_amount)}
💳 المبلغ المدفوع: ${formatCurrency(invoice.amount_paid)}
${remainingAmount > 0 ? `🔴 المبلغ المستحق: ${formatCurrency(remainingAmount)}` : ''}

⚠️ تحذير: سيتم حذف الفاتورة نهائياً وتحديث رصيد المورد تلقائياً`;

    if (!confirm(confirmMessage)) {
      return;
    }

    try {
      setLoading(true);
      console.log(`🗑️ حذف فاتورة المشتريات: ${invoice.id}`);

      const response = await fetch(`/api/purchases/${invoice.id}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ تم حذف الفاتورة بنجاح:', result);

        // إعادة تحميل الفواتير
        loadInvoices();

        // 🔄 إعادة تحميل بيانات المنتجات في AppContext لتحديث المخزون
        await reloadAppProducts();
        console.log('🔄 تم إعادة تحميل بيانات المنتجات بعد الحذف');

        // 🔄 إعادة تحميل البيانات المحلية أيضاً
        await loadProducts();
        console.log('🔄 تم إعادة تحميل البيانات المحلية بعد الحذف');

        alert(`✅ تم حذف الفاتورة بنجاح!

📋 رقم الفاتورة: ${invoice.id}
🏢 المورد: ${supplier?.name}
💰 تم تحديث رصيد المورد تلقائياً
${remainingAmount > 0 ? `🔄 تم إزالة دين قدره: ${formatCurrency(remainingAmount)}` : ''}`);
      } else {
        const error = await response.json();
        alert(`خطأ في حذف الفاتورة: ${error.error}`);
      }
    } catch (error) {
      console.error('❌ خطأ في حذف الفاتورة:', error);
      alert('حدث خطأ في حذف الفاتورة');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* 🎯 العنوان والإحصائيات */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="bg-purple-500/30 p-3 rounded-xl">
                <ShoppingCart className="w-8 h-8 text-purple-400" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white">فواتير المشتريات</h1>
                <p className="text-purple-200">إدارة ومتابعة فواتير المشتريات والموردين</p>
              </div>
            </div>

            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse shadow-lg"
            >
              <Plus className="w-5 h-5" />
              <span>فاتورة جديدة</span>
            </button>
          </div>

          {/* 📊 الإحصائيات */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-300 text-sm mb-1">إجمالي الفواتير</p>
                  <p className="text-3xl font-bold text-white">{stats.totalInvoices}</p>
                </div>
                <div className="bg-blue-500/30 p-3 rounded-xl">
                  <FileText className="w-6 h-6 text-blue-400" />
                </div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-300 text-sm mb-1">إجمالي المبلغ</p>
                  <p className="text-3xl font-bold text-white">{formatCurrency(stats.totalAmount)}</p>
                </div>
                <div className="bg-purple-500/30 p-3 rounded-xl">
                  <DollarSign className="w-6 h-6 text-purple-400" />
                </div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-300 text-sm mb-1">المبلغ المدفوع</p>
                  <p className="text-3xl font-bold text-green-400">{formatCurrency(stats.paidAmount)}</p>
                </div>
                <div className="bg-green-500/30 p-3 rounded-xl">
                  <CheckCircle className="w-6 h-6 text-green-400" />
                </div>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-300 text-sm mb-1">المبلغ المستحق</p>
                  <p className="text-3xl font-bold text-red-400">{formatCurrency(stats.unpaidAmount)}</p>
                </div>
                <div className="bg-red-500/30 p-3 rounded-xl">
                  <AlertTriangle className="w-6 h-6 text-red-400" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 🔍 البحث والتصفية */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 mb-8">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* البحث */}
            <div className="flex-1 relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="البحث برقم الفاتورة أو اسم المورد..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-white/10 border border-white/20 rounded-xl px-12 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>

            {/* تصفية الحالة */}
            <div className="relative">
              <Filter className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as any)}
                className="bg-white/10 border border-white/20 rounded-xl px-12 py-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent appearance-none cursor-pointer"
              >
                <option value="all" className="bg-gray-800">جميع الفواتير</option>
                <option value="paid" className="bg-gray-800">مدفوعة</option>
                <option value="unpaid" className="bg-gray-800">غير مدفوعة</option>
                <option value="partial" className="bg-gray-800">مدفوعة جزئياً</option>
              </select>
            </div>
          </div>
        </div>

        {/* 📋 جدول الفواتير */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 overflow-hidden">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-400"></div>
              <span className="mr-3 text-white">جاري التحميل...</span>
            </div>
          ) : filteredInvoices.length === 0 ? (
            <div className="text-center py-12">
              <ShoppingCart className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-400 text-lg">لا توجد فواتير مشتريات</p>
              <p className="text-gray-500 text-sm">ابدأ بإنشاء فاتورة مشتريات جديدة</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-white/5">
                  <tr>
                    <th className="text-right py-4 px-6 text-gray-300 font-semibold">رقم الفاتورة</th>
                    <th className="text-right py-4 px-6 text-gray-300 font-semibold">المورد</th>
                    <th className="text-right py-4 px-6 text-gray-300 font-semibold">التاريخ</th>
                    <th className="text-right py-4 px-6 text-gray-300 font-semibold">المبلغ الإجمالي</th>
                    <th className="text-right py-4 px-6 text-gray-300 font-semibold">المبلغ المدفوع</th>
                    <th className="text-right py-4 px-6 text-gray-300 font-semibold">المبلغ المستحق</th>
                    <th className="text-right py-4 px-6 text-gray-300 font-semibold">الحالة</th>
                    <th className="text-center py-4 px-6 text-gray-300 font-semibold">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredInvoices.map((invoice, index) => (
                    <tr
                      key={invoice.id}
                      className={`border-t border-white/10 hover:bg-white/5 transition-colors ${
                        index % 2 === 0 ? 'bg-white/2' : ''
                      }`}
                    >
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <div className="bg-purple-500/30 p-2 rounded-lg">
                            <FileText className="w-4 h-4 text-purple-400" />
                          </div>
                          <div>
                            <p className="text-white font-semibold">{invoice.invoice_number}</p>
                            <p className="text-gray-400 text-sm">{invoice.items_count} منتج</p>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <div>
                          <p className="text-white font-medium">{invoice.supplier_name || 'غير محدد'}</p>
                          {invoice.supplier_phone && (
                            <p className="text-gray-400 text-sm flex items-center">
                              <Phone className="w-3 h-3 ml-1" />
                              {invoice.supplier_phone}
                            </p>
                          )}
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <Calendar className="w-4 h-4 text-gray-400" />
                          <span className="text-white">{formatDate(invoice.created_at)}</span>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <span className="text-white font-semibold">{formatCurrency(invoice.total_amount)}</span>
                      </td>
                      <td className="py-4 px-6">
                        <span className="text-green-400 font-semibold">{formatCurrency(invoice.amount_paid)}</span>
                      </td>
                      <td className="py-4 px-6">
                        <span className={`font-semibold ${((invoice.total_amount || 0) - (invoice.amount_paid || 0)) > 0 ? 'text-red-400' : 'text-gray-400'}`}>
                          {formatCurrency((invoice.total_amount || 0) - (invoice.amount_paid || 0))}
                        </span>
                      </td>
                      <td className="py-4 px-6">
                        <span className={`px-3 py-1 rounded-full text-xs font-semibold ${getStatusColor(invoice)}`}>
                          {getStatusText(invoice)}
                        </span>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center justify-center space-x-2 space-x-reverse">
                          <button
                            onClick={() => {
                              setSelectedInvoice(invoice);
                              setShowPaymentModal(true);
                              setPaymentAmount((invoice.total_amount || 0) - (invoice.amount_paid || 0));
                            }}
                            disabled={invoice.is_paid}
                            className={`p-2 rounded-lg transition-colors ${
                              invoice.is_paid
                                ? 'bg-gray-500/20 text-gray-500 cursor-not-allowed'
                                : 'bg-green-500/20 text-green-400 hover:bg-green-500/30'
                            }`}
                            title={invoice.is_paid ? 'مدفوعة بالكامل' : 'إضافة دفعة'}
                          >
                            <CreditCard className="w-4 h-4" />
                          </button>

                          <button
                            className="p-2 rounded-lg bg-blue-500/20 text-blue-400 hover:bg-blue-500/30 transition-colors"
                            title="عرض التفاصيل"
                          >
                            <Eye className="w-4 h-4" />
                          </button>

                          <button
                            className="p-2 rounded-lg bg-purple-500/20 text-purple-400 hover:bg-purple-500/30 transition-colors"
                            title="طباعة"
                          >
                            <Printer className="w-4 h-4" />
                          </button>

                          <button
                            onClick={() => deleteInvoice(invoice)}
                            className="p-2 rounded-lg bg-red-500/20 text-red-400 hover:bg-red-500/30 transition-colors"
                            title="حذف"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* 🆕 نافذة إنشاء فاتورة جديدة */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-2xl border border-white/20 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
              <div className="p-6 border-b border-white/10">
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-bold text-white flex items-center space-x-2 space-x-reverse">
                    <Plus className="w-6 h-6 text-purple-400" />
                    <span>إنشاء فاتورة مشتريات جديدة</span>
                  </h2>
                  <button
                    onClick={() => setShowCreateModal(false)}
                    className="p-2 rounded-lg bg-red-500/20 text-red-400 hover:bg-red-500/30 transition-colors"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
              </div>

              <div className="p-6 space-y-6">
                {/* معلومات المورد */}
                <div className="bg-white/5 rounded-xl p-6 border border-white/10">
                  <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2 space-x-reverse">
                    <User className="w-5 h-5 text-blue-400" />
                    <span>معلومات المورد</span>
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <label className="block text-gray-300 text-sm">اختر مورد موجود</label>
                        <button
                          onClick={() => setShowSupplierModal(true)}
                          className="text-xs bg-gradient-to-r from-blue-500 to-cyan-600 hover:from-blue-600 hover:to-cyan-700 text-white px-3 py-1 rounded-lg font-semibold transition-all duration-300 hover:scale-105 flex items-center space-x-1 space-x-reverse"
                        >
                          <Plus className="w-3 h-3" />
                          <span>مورد جديد</span>
                        </button>
                      </div>
                      <select
                        value={newInvoice.supplier_id}
                        onChange={(e) => {
                          setNewInvoice(prev => ({
                            ...prev,
                            supplier_id: e.target.value
                          }));
                        }}
                        className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                      >
                        <option value="" className="bg-gray-800">اختر مورد من القائمة</option>
                        {suppliers.map(supplier => (
                          <option key={supplier.id} value={supplier.id} className="bg-gray-800">
                            {supplier.name} {supplier.phone && `- ${supplier.phone}`}
                          </option>
                        ))}
                      </select>
                      {suppliers.length === 0 && (
                        <p className="text-yellow-400 text-xs mt-1">لا توجد موردين. انقر على "مورد جديد" لإضافة مورد</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-gray-300 text-sm mb-2">تاريخ الاستحقاق</label>
                      <input
                        type="date"
                        value={newInvoice.due_date}
                        onChange={(e) => setNewInvoice(prev => ({ ...prev, due_date: e.target.value }))}
                        className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                      />
                    </div>
                  </div>
                </div>

                {/* إضافة منتجات */}
                <div className="bg-white/5 rounded-xl p-6 border border-white/10">
                  <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2 space-x-reverse">
                    <Package className="w-5 h-5 text-green-400" />
                    <span>إضافة منتجات</span>
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
                    <div>
                      <label className="block text-gray-300 text-sm mb-2">المنتج</label>
                      <select
                        value={currentItem.product_id}
                        onChange={(e) => {
                          const selectedProduct = products.find(p => p.id === e.target.value);
                          if (selectedProduct) {
                            setCurrentItem(prev => ({
                              ...prev,
                              product_id: selectedProduct.id,
                              product_name: selectedProduct.name,
                              unit_cost: selectedProduct.cost || 0
                            }));
                          }
                        }}
                        className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                      >
                        <option value="" className="bg-gray-800">اختر منتج</option>
                        {products.map(product => (
                          <option key={product.id} value={product.id} className="bg-gray-800">
                            {product.name} - مخزون: {product.stock}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-gray-300 text-sm mb-2">الكمية</label>
                      <input
                        type="number"
                        min="1"
                        value={currentItem.quantity}
                        onChange={(e) => {
                          const quantity = parseInt(e.target.value) || 1;
                          setCurrentItem(prev => ({
                            ...prev,
                            quantity,
                            total_cost: quantity * prev.unit_cost - prev.discount
                          }));
                        }}
                        className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                      />
                    </div>

                    <div>
                      <label className="block text-gray-300 text-sm mb-2">سعر الوحدة</label>
                      <input
                        type="number"
                        step="0.01"
                        min="0"
                        value={currentItem.unit_cost}
                        onChange={(e) => {
                          const unitCost = parseFloat(e.target.value) || 0;
                          setCurrentItem(prev => ({
                            ...prev,
                            unit_cost: unitCost,
                            total_cost: prev.quantity * unitCost - prev.discount
                          }));
                        }}
                        className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                      />
                    </div>

                    <div>
                      <label className="block text-gray-300 text-sm mb-2">الخصم</label>
                      <input
                        type="number"
                        step="0.01"
                        min="0"
                        value={currentItem.discount}
                        onChange={(e) => {
                          const discount = parseFloat(e.target.value) || 0;
                          setCurrentItem(prev => ({
                            ...prev,
                            discount,
                            total_cost: prev.quantity * prev.unit_cost - discount
                          }));
                        }}
                        className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                      />
                    </div>

                    <div className="flex items-end">
                      <button
                        onClick={addItemToInvoice}
                        className="w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white px-4 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105"
                      >
                        إضافة
                      </button>
                    </div>
                  </div>

                  {/* قائمة المنتجات المضافة */}
                  {newInvoice.items.length > 0 && (
                    <div className="bg-white/5 rounded-xl p-4 border border-white/10">
                      <h4 className="text-white font-semibold mb-3">المنتجات المضافة:</h4>
                      <div className="space-y-2">
                        {newInvoice.items.map((item, index) => (
                          <div key={index} className="flex items-center justify-between bg-white/5 rounded-lg p-3">
                            <div className="flex-1">
                              <p className="text-white font-medium">{item.product_name}</p>
                              <p className="text-gray-400 text-sm">
                                {item.quantity} × {formatCurrency(item.unit_cost)} = {formatCurrency(item.total_cost)}
                              </p>
                            </div>
                            <button
                              onClick={() => {
                                setNewInvoice(prev => ({
                                  ...prev,
                                  items: prev.items.filter((_, i) => i !== index)
                                }));
                                calculateTotals(newInvoice.items.filter((_, i) => i !== index));
                              }}
                              className="p-2 rounded-lg bg-red-500/20 text-red-400 hover:bg-red-500/30 transition-colors"
                            >
                              <Minus className="w-4 h-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* المجاميع والدفع */}
                <div className="bg-white/5 rounded-xl p-6 border border-white/10">
                  <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2 space-x-reverse">
                    <Calculator className="w-5 h-5 text-yellow-400" />
                    <span>المجاميع والدفع</span>
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <span className="text-gray-300">المجموع الفرعي:</span>
                        <span className="text-white font-semibold">{formatCurrency(newInvoice.subtotal)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-300">الضريبة:</span>
                        <span className="text-white font-semibold">{formatCurrency(newInvoice.tax_amount)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-300">الخصم:</span>
                        <span className="text-white font-semibold">{formatCurrency(newInvoice.discount_amount)}</span>
                      </div>
                      <div className="flex justify-between border-t border-white/20 pt-2">
                        <span className="text-white font-bold">المجموع الإجمالي:</span>
                        <span className="text-green-400 font-bold text-xl">{formatCurrency(newInvoice.total_amount)}</span>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <label className="block text-gray-300 text-sm mb-2">المبلغ المدفوع</label>
                        <div className="space-y-3">
                          <input
                            type="number"
                            step="0.01"
                            min="0"
                            max={newInvoice.total_amount}
                            value={newInvoice.amount_paid}
                            onChange={(e) => setNewInvoice(prev => ({ ...prev, amount_paid: parseFloat(e.target.value) || 0 }))}
                            className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                          />

                          {/* أزرار سريعة للدفع */}
                          {newInvoice.total_amount > 0 && (
                            <div className="flex space-x-2 space-x-reverse">
                              <button
                                type="button"
                                onClick={() => setNewInvoice(prev => ({ ...prev, amount_paid: 0 }))}
                                className="flex-1 bg-red-500/20 hover:bg-red-500/30 text-red-400 px-3 py-2 rounded-lg text-sm font-medium transition-colors"
                              >
                                بدون دفع
                              </button>
                              <button
                                type="button"
                                onClick={() => setNewInvoice(prev => ({ ...prev, amount_paid: prev.total_amount / 2 }))}
                                className="flex-1 bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-400 px-3 py-2 rounded-lg text-sm font-medium transition-colors"
                              >
                                نصف المبلغ
                              </button>
                              <button
                                type="button"
                                onClick={() => setNewInvoice(prev => ({ ...prev, amount_paid: prev.total_amount }))}
                                className="flex-1 bg-green-500/20 hover:bg-green-500/30 text-green-400 px-3 py-2 rounded-lg text-sm font-medium transition-colors"
                              >
                                دفع كامل
                              </button>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* 🎯 مؤشر حالة الدفع */}
                      {newInvoice.total_amount > 0 && (
                        <div className="bg-white/5 rounded-xl p-4 border border-white/10">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-gray-300 text-sm">حالة الدفع:</span>
                            <span className={`px-3 py-1 rounded-full text-xs font-semibold ${
                              newInvoice.amount_paid >= newInvoice.total_amount
                                ? 'text-green-400 bg-green-500/20'
                                : newInvoice.amount_paid > 0
                                  ? 'text-yellow-400 bg-yellow-500/20'
                                  : 'text-red-400 bg-red-500/20'
                            }`}>
                              {newInvoice.amount_paid >= newInvoice.total_amount
                                ? '✅ مدفوعة بالكامل'
                                : newInvoice.amount_paid > 0
                                  ? '⚠️ مدفوعة جزئياً'
                                  : '❌ غير مدفوعة'}
                            </span>
                          </div>

                          {newInvoice.amount_paid < newInvoice.total_amount && (
                            <div className="space-y-2">
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-400">المبلغ المتبقي:</span>
                                <span className="text-red-400 font-semibold">
                                  {formatCurrency(newInvoice.total_amount - newInvoice.amount_paid)}
                                </span>
                              </div>
                              <div className="bg-red-500/10 border border-red-400/20 rounded-lg p-3">
                                <p className="text-red-300 text-xs">
                                  🔗 <strong>سيتم إنشاء دين تلقائياً:</strong> سيتم إضافة المبلغ المتبقي كدين للمورد في صفحة الموردين
                                </p>
                              </div>
                            </div>
                          )}
                        </div>
                      )}

                      <div>
                        <label className="block text-gray-300 text-sm mb-2">ملاحظات</label>
                        <textarea
                          value={newInvoice.notes}
                          onChange={(e) => setNewInvoice(prev => ({ ...prev, notes: e.target.value }))}
                          rows={3}
                          className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none"
                          placeholder="ملاحظات إضافية..."
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* أزرار الحفظ */}
                <div className="flex items-center justify-end space-x-4 space-x-reverse pt-6 border-t border-white/10">
                  <button
                    onClick={() => setShowCreateModal(false)}
                    className="px-6 py-3 rounded-xl bg-gray-500/20 text-gray-300 hover:bg-gray-500/30 transition-colors"
                  >
                    إلغاء
                  </button>
                  <button
                    onClick={createInvoice}
                    disabled={loading || !newInvoice.supplier_id || newInvoice.items.length === 0}
                    className="px-6 py-3 rounded-xl bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white font-semibold transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 space-x-reverse"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span>جاري الحفظ...</span>
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4" />
                        <span>حفظ الفاتورة</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 💳 نافذة إضافة دفعة */}
        {showPaymentModal && selectedInvoice && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-2xl border border-white/20 w-full max-w-md">
              <div className="p-6 border-b border-white/10">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold text-white flex items-center space-x-2 space-x-reverse">
                    <CreditCard className="w-5 h-5 text-green-400" />
                    <span>إضافة دفعة</span>
                  </h2>
                  <button
                    onClick={() => setShowPaymentModal(false)}
                    className="p-2 rounded-lg bg-red-500/20 text-red-400 hover:bg-red-500/30 transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>

              <div className="p-6 space-y-4">
                <div className="bg-white/5 rounded-xl p-4 border border-white/10">
                  <p className="text-gray-300 text-sm">فاتورة رقم:</p>
                  <p className="text-white font-semibold">{selectedInvoice.invoice_number}</p>
                  <p className="text-gray-300 text-sm mt-2">المبلغ المستحق:</p>
                  <p className="text-red-400 font-bold text-xl">{formatCurrency((selectedInvoice.total_amount || 0) - (selectedInvoice.amount_paid || 0))}</p>
                </div>

                <div>
                  <label className="block text-gray-300 text-sm mb-2">مبلغ الدفعة</label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    max={(selectedInvoice.total_amount || 0) - (selectedInvoice.amount_paid || 0)}
                    value={paymentAmount}
                    onChange={(e) => setPaymentAmount(parseFloat(e.target.value) || 0)}
                    className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-green-500"
                  />
                </div>

                <div>
                  <label className="block text-gray-300 text-sm mb-2">ملاحظات الدفعة</label>
                  <textarea
                    value={paymentNotes}
                    onChange={(e) => setPaymentNotes(e.target.value)}
                    rows={3}
                    className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-green-500 resize-none"
                    placeholder="ملاحظات حول الدفعة..."
                  />
                </div>

                <div className="flex items-center justify-end space-x-4 space-x-reverse pt-4 border-t border-white/10">
                  <button
                    onClick={() => setShowPaymentModal(false)}
                    className="px-4 py-2 rounded-xl bg-gray-500/20 text-gray-300 hover:bg-gray-500/30 transition-colors"
                  >
                    إلغاء
                  </button>
                  <button
                    onClick={processPayment}
                    disabled={loading || paymentAmount <= 0 || paymentAmount > ((selectedInvoice.total_amount || 0) - (selectedInvoice.amount_paid || 0))}
                    className="px-4 py-2 rounded-xl bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 space-x-reverse"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span>جاري الحفظ...</span>
                      </>
                    ) : (
                      <>
                        <Check className="w-4 h-4" />
                        <span>تأكيد الدفعة</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 🏢 نافذة إضافة مورد جديد */}
        {showSupplierModal && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-2xl border border-white/20 w-full max-w-md">
              <div className="p-6 border-b border-white/10">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold text-white flex items-center space-x-2 space-x-reverse">
                    <User className="w-5 h-5 text-blue-400" />
                    <span>إضافة مورد جديد</span>
                  </h2>
                  <button
                    onClick={() => setShowSupplierModal(false)}
                    className="p-2 rounded-lg bg-red-500/20 text-red-400 hover:bg-red-500/30 transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>

              <div className="p-6 space-y-4">
                <div>
                  <label className="block text-gray-300 text-sm mb-2">اسم المورد *</label>
                  <input
                    type="text"
                    value={newSupplier.name}
                    onChange={(e) => setNewSupplier(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="أدخل اسم المورد..."
                  />
                </div>

                <div>
                  <label className="block text-gray-300 text-sm mb-2">رقم الهاتف</label>
                  <input
                    type="tel"
                    value={newSupplier.phone}
                    onChange={(e) => setNewSupplier(prev => ({ ...prev, phone: e.target.value }))}
                    className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="أدخل رقم الهاتف..."
                  />
                </div>

                <div>
                  <label className="block text-gray-300 text-sm mb-2">العنوان</label>
                  <input
                    type="text"
                    value={newSupplier.address}
                    onChange={(e) => setNewSupplier(prev => ({ ...prev, address: e.target.value }))}
                    className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="أدخل العنوان..."
                  />
                </div>



                <div className="flex items-center justify-end space-x-4 space-x-reverse pt-4 border-t border-white/10">
                  <button
                    onClick={() => setShowSupplierModal(false)}
                    className="px-4 py-2 rounded-xl bg-gray-500/20 text-gray-300 hover:bg-gray-500/30 transition-colors"
                  >
                    إلغاء
                  </button>
                  <button
                    onClick={createSupplier}
                    disabled={loading || !newSupplier.name.trim()}
                    className="px-4 py-2 rounded-xl bg-gradient-to-r from-blue-500 to-cyan-600 hover:from-blue-600 hover:to-cyan-700 text-white font-semibold transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 space-x-reverse"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span>جاري الحفظ...</span>
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4" />
                        <span>حفظ المورد</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PurchaseInvoices;
