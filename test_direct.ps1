Write-Host "🧪 اختبار مباشر..." -ForegroundColor Cyan

$body = '{"productName":"منتج تجريبي","price":100,"quantity":1}'

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5002/api/sales/unknown-product" -Method POST -Body $body -ContentType "application/json"
    Write-Host "✅ نجح!" -ForegroundColor Green
    $response | ConvertTo-Json
} catch {
    Write-Host "❌ فشل:" -ForegroundColor Red
    Write-Host $_.Exception.Message
}
