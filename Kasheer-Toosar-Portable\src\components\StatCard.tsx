import React from 'react';
import { DivideIcon as LucideIcon } from 'lucide-react';

interface StatCardProps {
  title: string;
  value: string;
  subtitle?: string;
  color: string;
  icon: LucideIcon;
  trend?: number;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, subtitle, color, icon: Icon, trend }) => {
  const colorClasses = {
    blue: 'border-blue-500 bg-blue-500/10',
    purple: 'border-purple-500 bg-purple-500/10',
    green: 'border-green-500 bg-green-500/10',
    orange: 'border-orange-500 bg-orange-500/10',
    red: 'border-red-500 bg-red-500/10',
    indigo: 'border-indigo-500 bg-indigo-500/10',
    teal: 'border-teal-500 bg-teal-500/10'
  };

  const iconColorClasses = {
    blue: 'text-blue-400',
    purple: 'text-purple-400',
    green: 'text-green-400',
    orange: 'text-orange-400',
    red: 'text-red-400',
    indigo: 'text-indigo-400',
    teal: 'text-teal-400'
  };

  return (
    <div className={`bg-slate-800 rounded-xl p-6 border-2 ${colorClasses[color as keyof typeof colorClasses]} hover:scale-105 transition-all duration-200`}>
      <div className="flex items-center justify-between mb-4">
        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${colorClasses[color as keyof typeof colorClasses]}`}>
          <Icon className={`w-6 h-6 ${iconColorClasses[color as keyof typeof iconColorClasses]}`} />
        </div>
        {trend !== undefined && (
          <div className={`text-sm font-medium ${trend > 0 ? 'text-green-400' : trend < 0 ? 'text-red-400' : 'text-slate-400'}`}>
            {trend > 0 ? '+' : ''}{trend}%
          </div>
        )}
      </div>
      
      <div>
        <h3 className="text-lg font-semibold text-white mb-1">{title}</h3>
        <div className="flex items-baseline space-x-reverse space-x-2">
          <span className={`text-2xl font-bold ${iconColorClasses[color as keyof typeof iconColorClasses]}`}>
            {value}
          </span>
          {subtitle && (
            <span className="text-sm text-slate-400">{subtitle}</span>
          )}
        </div>
      </div>
    </div>
  );
};

export default StatCard;