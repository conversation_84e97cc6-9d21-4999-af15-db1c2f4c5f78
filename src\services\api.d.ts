declare interface Customer {
  id: string;
  name: string;
  phone?: string;
  email?: string;
  address?: string;
  balance: number;
  credit_limit?: number;
}

declare interface CustomerDebt {
  id: number;
  customer_id: string;
  customer?: Customer;
  sale_id: number;
  amount: number;
  debt_date: string;
  paid_amount: number;
  remaining_amount: number;
  status: 'pending' | 'partial' | 'paid';
  notes?: string;
  created_at: string;
  updated_at: string;
}

declare interface DebtPayment {
  id: number;
  debt_id: number;
  amount: number;
  payment_method: 'cash' | 'transfer' | 'check';
  payment_date: string;
  notes?: string;
  created_at: string;
}

declare interface PaymentData {
  customer_id?: string;
  amount: number;
  payment_method: 'cash' | 'transfer' | 'check';
  notes?: string;
  payment_date?: string;
}

declare interface ApiResponse {
  success: boolean;
  error?: string;
  payment_id?: string;
  debtors?: CustomerDebt[];
  canDelete?: boolean;
  deleteInfo?: any;
  message?: string;
  payment?: DebtPayment;
  paid_amount?: number;
  updated_debts?: any[];
  total_remaining?: number;
}

declare const apiService: {
  getDebtorCustomers: () => Promise<ApiResponse>;
  payCustomerDebt: (customerId: string, paymentData: PaymentData) => Promise<ApiResponse>;
  deleteCustomerDebtInvoice: (customerId: string, confirm: boolean) => Promise<ApiResponse>;
};

export default apiService; 