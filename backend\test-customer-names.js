const pool = require('./database');

async function testCustomerNames() {
  console.log('🧪 اختبار عرض أسماء العملاء في المبيعات...');
  
  try {
    // 1. جلب المبيعات مع أسماء العملاء
    console.log('\n1️⃣ جلب المبيعات الحديثة...');
    const salesResult = await pool.query(`
      SELECT
        s.id,
        s.sale_number,
        s.customer_id,
        s.total_amount,
        s.payment_method,
        s.is_debt_sale,
        c.name as customer_name,
        s.created_at
      FROM pos_system.sales s
      LEFT JOIN pos_system.customers c ON s.customer_id = c.id
      ORDER BY s.created_at DESC
      LIMIT 10
    `);
    
    console.log(`✅ تم العثور على ${salesResult.rows.length} مبيعة:`);
    salesResult.rows.forEach((sale, index) => {
      const customerDisplay = sale.customer_name || 'عميل غير معروف';
      const saleType = sale.is_debt_sale ? '(آجل)' : '(نقدي)';
      
      console.log(`   ${index + 1}. ${sale.sale_number} - ${customerDisplay} ${saleType}`);
      console.log(`      المبلغ: ${sale.total_amount} دج - طريقة الدفع: ${sale.payment_method}`);
      console.log(`      معرف العميل: ${sale.customer_id || 'غير محدد'}`);
      console.log('      ---');
    });
    
    // 2. اختبار API endpoint للمبيعات
    console.log('\n2️⃣ اختبار API endpoint للمبيعات...');
    const fetch = require('node-fetch');
    
    try {
      const response = await fetch('http://localhost:5003/api/sales');
      const data = await response.json();
      
      if (data.success && data.sales) {
        console.log(`✅ API يعمل بشكل صحيح - ${data.sales.length} مبيعة`);
        
        // عرض أول 5 مبيعات من API
        console.log('\n📋 أول 5 مبيعات من API:');
        data.sales.slice(0, 5).forEach((sale, index) => {
          const customerDisplay = sale.customerName || 'عميل غير معروف';
          const saleType = sale.isDebtSale ? '(آجل)' : '(نقدي)';
          
          console.log(`   ${index + 1}. ${sale.sale_number} - ${customerDisplay} ${saleType}`);
          console.log(`      المبلغ: ${sale.total_amount} دج - طريقة الدفع: ${sale.paymentMethod}`);
        });
        
        // التحقق من وجود مبيعات بدون أسماء عملاء
        const salesWithoutCustomerName = data.sales.filter(sale => 
          !sale.customerName || sale.customerName === 'عميل غير معروف'
        );
        
        console.log(`\n📊 إحصائيات أسماء العملاء:`);
        console.log(`   - إجمالي المبيعات: ${data.sales.length}`);
        console.log(`   - مبيعات بدون اسم عميل: ${salesWithoutCustomerName.length}`);
        console.log(`   - مبيعات مع أسماء عملاء: ${data.sales.length - salesWithoutCustomerName.length}`);
        
        if (salesWithoutCustomerName.length > 0) {
          console.log('\n⚠️ مبيعات بدون أسماء عملاء:');
          salesWithoutCustomerName.slice(0, 3).forEach((sale, index) => {
            console.log(`   ${index + 1}. ${sale.sale_number} - ${sale.customerName}`);
            console.log(`      معرف العميل: ${sale.customer_id || 'غير محدد'}`);
          });
        }
        
      } else {
        console.log('❌ خطأ في API:', data.error || 'استجابة غير صحيحة');
      }
      
    } catch (apiError) {
      console.log('❌ خطأ في الاتصال بـ API:', apiError.message);
    }
    
    // 3. التحقق من العملاء المدينين
    console.log('\n3️⃣ التحقق من العملاء المدينين...');
    const debtorsResult = await pool.query(`
      SELECT
        c.id,
        c.name,
        c.phone,
        cd.amount as total_debt,
        cd.paid_amount,
        cd.remaining_amount,
        cd.status
      FROM pos_system.customers c
      INNER JOIN pos_system.customer_debts cd ON c.id = cd.customer_id
      WHERE cd.status IN ('pending', 'partial')
        AND c.is_active = true
      ORDER BY cd.remaining_amount DESC
    `);
    
    console.log(`✅ العملاء المدينون (${debtorsResult.rows.length}):`);
    if (debtorsResult.rows.length > 0) {
      debtorsResult.rows.forEach((debtor, index) => {
        console.log(`   ${index + 1}. ${debtor.name} (${debtor.phone})`);
        console.log(`      الدين الإجمالي: ${debtor.total_debt} دج`);
        console.log(`      المبلغ المدفوع: ${debtor.paid_amount} دج`);
        console.log(`      المبلغ المتبقي: ${debtor.remaining_amount} دج`);
        console.log(`      الحالة: ${debtor.status}`);
        console.log('      ---');
      });
    } else {
      console.log('   - لا توجد ديون معلقة حالياً');
    }
    
    // 4. اختبار API endpoint للمدينين
    console.log('\n4️⃣ اختبار API endpoint للمدينين...');
    try {
      const debtorsResponse = await fetch('http://localhost:5003/api/customers/debtors');
      const debtorsData = await debtorsResponse.json();
      
      if (Array.isArray(debtorsData)) {
        console.log(`✅ API المدينين يعمل بشكل صحيح - ${debtorsData.length} مدين`);
        
        debtorsData.forEach((debtor, index) => {
          console.log(`   ${index + 1}. ${debtor.name} (${debtor.phone})`);
          console.log(`      الدين المتبقي: ${debtor.remaining_debt} دج`);
        });
      } else {
        console.log('❌ خطأ في API المدينين:', debtorsData.error || 'استجابة غير صحيحة');
      }
      
    } catch (debtorsApiError) {
      console.log('❌ خطأ في الاتصال بـ API المدينين:', debtorsApiError.message);
    }
    
  } catch (error) {
    console.error('❌ خطأ في اختبار أسماء العملاء:', error.message);
    console.error('تفاصيل الخطأ:', error);
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testCustomerNames()
    .then(() => {
      console.log('\n🎉 انتهى اختبار أسماء العملاء!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل اختبار أسماء العملاء:', error);
      process.exit(1);
    });
}

module.exports = testCustomerNames;
