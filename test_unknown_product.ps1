# اختبار بيع منتج غير معروف
Write-Host "🧪 اختبار بيع منتج غير معروف..." -ForegroundColor Cyan

# بيانات البيع
$saleData = @{
    customer_id = $null
    subtotal = 25.00
    tax_amount = 0.00
    discount_amount = 0.00
    total_amount = 25.00
    payment_method = "cash"
    amount_paid = 25.00
    change_amount = 0.00
    notes = "اختبار منتج غير معروف"
    cashier_name = "مدير النظام"
    items = @(
        @{
            productId = "unknown-1703001234567"
            productName = "🔮 منتج غير معروف - 25.00 دج"
            quantity = 1
            unitPrice = 25.00
            totalPrice = 25.00
            discount = 0.00
        }
    )
} | ConvertTo-Json -Depth 3

Write-Host "📤 إرسال البيع..." -ForegroundColor Yellow
Write-Host $saleData

try {
    # إرسال البيع
    $response = Invoke-RestMethod -Uri "http://localhost:5002/api/sales" -Method POST -Body $saleData -ContentType "application/json"
    
    Write-Host "✅ نجح البيع!" -ForegroundColor Green
    Write-Host "🆔 معرف البيع: $($response.id)" -ForegroundColor Green
    Write-Host "🔢 رقم البيع: $($response.sale_number)" -ForegroundColor Green
    
    # التحقق من قاعدة البيانات
    Write-Host "🔍 التحقق من قاعدة البيانات..." -ForegroundColor Yellow
    
    $checkSale = Invoke-RestMethod -Uri "http://localhost:5002/api/sales/$($response.id)" -Method GET
    
    if ($checkSale) {
        Write-Host "✅ البيع موجود في قاعدة البيانات!" -ForegroundColor Green
        Write-Host "📋 تفاصيل البيع:" -ForegroundColor Cyan
        $checkSale | ConvertTo-Json -Depth 3
    } else {
        Write-Host "❌ البيع غير موجود في قاعدة البيانات!" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ فشل البيع:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}
