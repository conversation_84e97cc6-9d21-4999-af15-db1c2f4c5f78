import React, { useEffect, useRef } from 'react';

interface BarcodeGeneratorProps {
  value?: string;
  width?: number;
  height?: number;
  displayValue?: boolean;
  fontSize?: number;
  textAlign?: 'left' | 'center' | 'right';
  textPosition?: 'bottom' | 'top';
  background?: string;
  lineColor?: string;
}

const BarcodeGenerator: React.FC<BarcodeGeneratorProps> = ({
  value = '',
  width = 200,
  height = 50,
  displayValue = true,
  fontSize = 12,
  textAlign = 'center',
  textPosition = 'bottom',
  background = '#ffffff',
  lineColor = '#000000'
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // التأكد من وجود قيمة صالحة
  const safeValue = value && typeof value === 'string' && value.trim() !== ''
    ? value.trim()
    : `ORDER-${Date.now()}`;

  // Code 128 encoding tables
  const CODE128_B = {
    ' ': 0, '!': 1, '"': 2, '#': 3, '$': 4, '%': 5, '&': 6, "'": 7,
    '(': 8, ')': 9, '*': 10, '+': 11, ',': 12, '-': 13, '.': 14, '/': 15,
    '0': 16, '1': 17, '2': 18, '3': 19, '4': 20, '5': 21, '6': 22, '7': 23,
    '8': 24, '9': 25, ':': 26, ';': 27, '<': 28, '=': 29, '>': 30, '?': 31,
    '@': 32, 'A': 33, 'B': 34, 'C': 35, 'D': 36, 'E': 37, 'F': 38, 'G': 39,
    'H': 40, 'I': 41, 'J': 42, 'K': 43, 'L': 44, 'M': 45, 'N': 46, 'O': 47,
    'P': 48, 'Q': 49, 'R': 50, 'S': 51, 'T': 52, 'U': 53, 'V': 54, 'W': 55,
    'X': 56, 'Y': 57, 'Z': 58, '[': 59, '\\': 60, ']': 61, '^': 62, '_': 63,
    '`': 64, 'a': 65, 'b': 66, 'c': 67, 'd': 68, 'e': 69, 'f': 70, 'g': 71,
    'h': 72, 'i': 73, 'j': 74, 'k': 75, 'l': 76, 'm': 77, 'n': 78, 'o': 79,
    'p': 80, 'q': 81, 'r': 82, 's': 83, 't': 84, 'u': 85, 'v': 86, 'w': 87,
    'x': 88, 'y': 89, 'z': 90, '{': 91, '|': 92, '}': 93, '~': 94, 'DEL': 95
  };

  // Code 128 patterns (each character is represented by 6 bars/spaces)
  const CODE128_PATTERNS = [
    '11011001100', '11001101100', '11001100110', '10010011000', '10010001100',
    '10001001100', '10011001000', '10011000100', '10001100100', '11001001000',
    '11001000100', '11000100100', '10110011100', '10011011100', '10011001110',
    '10111001100', '10011101100', '10011100110', '11001110010', '11001011100',
    '11001001110', '11011100100', '11001110100', '11101101110', '11101001100',
    '11100101100', '11100100110', '11101100100', '11100110100', '11100110010',
    '11011011000', '11011000110', '11000110110', '10100011000', '10001011000',
    '10001000110', '10110001000', '10001101000', '10001100010', '11010001000',
    '11000101000', '11000100010', '10110111000', '10110001110', '10001101110',
    '10111011000', '10111000110', '10001110110', '11101110110', '11010001110',
    '11000101110', '11011101000', '11011100010', '11011101110', '11101011000',
    '11101000110', '11100010110', '11101101000', '11101100010', '11100011010',
    '11101111010', '11001000010', '11110001010', '10100110000', '10100001100',
    '10010110000', '10010000110', '10000101100', '10000100110', '10110010000',
    '10110000100', '10011010000', '10011000010', '10000110100', '10000110010',
    '11000010010', '11001010000', '11110111010', '11000010100', '10001111010',
    '10100111100', '10010111100', '10010011110', '10111100100', '10011110100',
    '10011110010', '11110100100', '11110010100', '11110010010', '11011011110',
    '11011110110', '11110110110', '10101111000', '10100011110', '10001011110',
    '10111101000', '10111100010', '11110101000', '11110100010', '10111011110',
    '10111101110', '11101011110', '11110101110', '11010000100', '11010010000',
    '11010011100', '1100011101011'
  ];

  const generateCode128 = (text: string): string => {
    try {
      // التحقق من وجود النص
      if (!text || typeof text !== 'string') {
        console.warn('Invalid text for barcode generation:', text);
        return '11011001100'; // Return a simple pattern if invalid
      }

      // Start with Code B (104)
      let encoded = [104];
      let checksum = 104;

      // Encode each character
      for (let i = 0; i < text.length; i++) {
        const char = text[i];
        const value = CODE128_B[char as keyof typeof CODE128_B];
        if (value !== undefined) {
          encoded.push(value);
          checksum += value * (i + 1);
        } else {
          // If character not found, use space
          encoded.push(0);
          checksum += 0 * (i + 1);
        }
      }

      // Add checksum
      encoded.push(checksum % 103);

      // Add stop pattern (106)
      encoded.push(106);

      // Convert to pattern string
      let pattern = '';
      for (const code of encoded) {
        if (CODE128_PATTERNS[code]) {
          pattern += CODE128_PATTERNS[code];
        }
      }

      // Add termination bar
      pattern += '11';

      return pattern;
    } catch (error) {
      console.error('Error generating barcode:', error);
      return '11011001100'; // Return a simple pattern if error
    }
  };

  const drawBarcode = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = width;
    canvas.height = height;

    // Clear canvas
    ctx.fillStyle = background;
    ctx.fillRect(0, 0, width, height);

    // Generate barcode pattern
    const pattern = generateCode128(safeValue);

    if (!pattern) return;

    // Calculate bar width (minimum 1 pixel)
    const barWidth = Math.max(1, Math.floor(width / pattern.length));
    const totalBarcodeWidth = barWidth * pattern.length;
    const startX = (width - totalBarcodeWidth) / 2;

    // Draw bars
    ctx.fillStyle = lineColor;
    let x = startX;

    for (let i = 0; i < pattern.length; i++) {
      if (pattern[i] === '1') {
        const barHeight = displayValue ? height - fontSize - 8 : height - 4;
        const y = textPosition === 'top' && displayValue ? fontSize + 5 : 2;
        ctx.fillRect(Math.floor(x), y, barWidth, barHeight);
      }
      x += barWidth;
    }

    // Draw text
    if (displayValue) {
      ctx.fillStyle = lineColor;
      ctx.font = `${fontSize}px monospace`;
      ctx.textAlign = textAlign;

      const textX = textAlign === 'center' ? width / 2 :
                   textAlign === 'right' ? width - 10 : 10;
      const textY = textPosition === 'bottom' ? height - 2 : fontSize;

      ctx.fillText(safeValue, textX, textY);
    }
  };

  useEffect(() => {
    drawBarcode();
  }, [safeValue, width, height, displayValue, fontSize, textAlign, textPosition, background, lineColor]);

  return (
    <div style={{ display: 'inline-block', textAlign: 'center' }}>
      <canvas
        ref={canvasRef}
        style={{
          border: '1px solid #ddd',
          backgroundColor: background,
          display: 'block',
          margin: '0 auto'
        }}
      />
    </div>
  );
};

export default BarcodeGenerator;
