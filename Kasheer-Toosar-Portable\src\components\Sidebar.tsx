import React, { useState, useEffect } from 'react';
import {
  Home,
  ShoppingCart,
  Package,
  Users,
  DollarSign,
  ShoppingBag,
  Settings,
  ChevronDown,
  BarChart3,
  FileText,
  Truck,
  Palette,
  Zap,

  Receipt,
  CreditCard,
  Printer
} from 'lucide-react';

interface SidebarProps {
  currentView: string;
  setCurrentView: (view: string) => void;
}

interface MenuItem {
  id: string;
  title: string;
  icon: React.ReactNode;
  hasSubmenu?: boolean;
  submenu?: { id: string; title: string; icon?: React.ReactNode }[];
}

const Sidebar: React.FC<SidebarProps> = ({ currentView, setCurrentView }) => {
  const [expandedMenus, setExpandedMenus] = useState<string[]>([]);

  const menuItems: MenuItem[] = [
    {
      id: 'dashboard',
      title: 'لوحة التحكم',
      icon: <Home className="w-5 h-5" />
    },
    {
      id: 'sales',
      title: 'المبيعات',
      icon: <ShoppingCart className="w-5 h-5" />,
      hasSubmenu: true,
      submenu: [
        { id: 'sales', title: 'شاشة البيع', icon: <ShoppingCart className="w-4 h-4" /> },
        { id: 'orders', title: 'سجل الطلبات', icon: <FileText className="w-4 h-4" /> }
      ]
    },
    {
      id: 'inventory',
      title: 'المخزون',
      icon: <Package className="w-5 h-5" />,
      hasSubmenu: true,
      submenu: [
        { id: 'product-management', title: 'إدارة الأصناف', icon: <Package className="w-4 h-4" /> },
        { id: 'categories', title: 'الأقسام', icon: <BarChart3 className="w-4 h-4" /> }
      ]
    },
    {
      id: 'customers',
      title: 'العملاء',
      icon: <Users className="w-5 h-5" />,
      hasSubmenu: true,
      submenu: [
        { id: 'customers', title: 'حسابات العملاء', icon: <Users className="w-4 h-4" /> },
        { id: 'debtors', title: 'العملاء المدينون', icon: <CreditCard className="w-4 h-4" /> },
        { id: 'creditors', title: 'العملاء الدائنون', icon: <DollarSign className="w-4 h-4" /> },
        { id: 'charge-station', title: '⚡ محطة شحن الأرصدة', icon: <Zap className="w-4 h-4" /> }
      ]
    },

    {
      id: 'financial',
      title: 'الإدارة المالية',
      icon: <DollarSign className="w-5 h-5" />,
      hasSubmenu: true,
      submenu: [
        { id: 'financial-center', title: '💰 المركز المالي', icon: <DollarSign className="w-4 h-4" /> },
        { id: 'expenses', title: 'المصروفات', icon: <Receipt className="w-4 h-4" /> },
        { id: 'income', title: 'الدخل', icon: <DollarSign className="w-4 h-4" /> }
      ]
    },
    {
      id: 'purchases',
      title: 'المشتريات',
      icon: <ShoppingBag className="w-5 h-5" />,
      hasSubmenu: true,
      submenu: [
        { id: 'purchase-invoice', title: '🛒 فواتير المشتريات', icon: <FileText className="w-4 h-4" /> },
        { id: 'suppliers', title: 'الموردين', icon: <Truck className="w-4 h-4" /> }
      ]
    },
    {
      id: 'tools',
      title: 'الأدوات',
      icon: <Settings className="w-5 h-5" />,
      hasSubmenu: true,
      submenu: [
        { id: 'label-designer', title: '🎨 مصمم الملصقات المحترف', icon: <Palette className="w-4 h-4" /> }
      ]
    },
    {
      id: 'settings-menu',
      title: 'الإعدادات',
      icon: <Settings className="w-5 h-5" />,
      hasSubmenu: true,
      submenu: [
        { id: 'settings', title: '⚙️ الإعدادات العامة', icon: <Settings className="w-4 h-4" /> },
        { id: 'printer-settings', title: '🖨️ إعدادات الطابعة', icon: <Printer className="w-4 h-4" /> }
      ]
    }
  ];

  // 🧠 تحديد القائمة الرئيسية للصفحة الحالية
  const getParentMenu = (viewId: string): string | null => {
    for (const item of menuItems) {
      if (item.hasSubmenu && item.submenu?.some(sub => sub.id === viewId)) {
        return item.id;
      }
    }
    return null;
  };

  // 🔄 تحديث القوائم المفتوحة عند تغيير الصفحة
  useEffect(() => {
    const parentMenu = getParentMenu(currentView);
    if (parentMenu) {
      setExpandedMenus([parentMenu]); // فتح القائمة الصحيحة فقط
    } else {
      setExpandedMenus([]); // إغلاق جميع القوائم إذا كانت الصفحة رئيسية
    }
  }, [currentView]);

  // 🎯 تبديل القائمة مع إغلاق الأخريات
  const toggleMenu = (menuId: string) => {
    setExpandedMenus(prev => {
      if (prev.includes(menuId)) {
        // إغلاق القائمة الحالية
        return prev.filter(id => id !== menuId);
      } else {
        // فتح القائمة الجديدة وإغلاق الأخريات
        return [menuId];
      }
    });
  };

  // 🎯 معالج النقر مع حفظ الموقع
  const handleItemClick = (itemId: string, hasSubmenu?: boolean) => {
    if (hasSubmenu) {
      toggleMenu(itemId);
    } else {
      // حفظ الصفحة الحالية في localStorage
      localStorage.setItem('currentView', itemId);
      setCurrentView(itemId);
    }
  };

  return (
    <div className="fixed right-0 top-0 h-screen w-64 bg-slate-800 border-l border-slate-700 overflow-y-auto">
      {/* Header */}
      <div className="p-6 border-b border-slate-700">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-reverse space-x-3">
            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">ق</span>
            </div>
            <div>
              <h1 className="text-xl font-bold text-white">قيمات</h1>
              <p className="text-xs text-slate-400">ROKNO.POS</p>
            </div>
          </div>
        </div>

        <div className="bg-slate-700 rounded-lg p-3 text-center">
          <p className="text-sm text-slate-300">قم بترقية حقنك الآن</p>
        </div>
      </div>

      {/* Navigation */}
      <nav className="p-4 space-y-2">
        {menuItems.map((item) => (
          <div key={item.id}>
            <button
              onClick={() => handleItemClick(item.id, item.hasSubmenu)}
              className={`w-full flex items-center justify-between p-3 rounded-lg transition-all duration-200 ${
                currentView === item.id || (item.hasSubmenu && expandedMenus.includes(item.id))
                  ? 'bg-blue-600 text-white'
                  : 'text-slate-300 hover:bg-slate-700 hover:text-white'
              }`}
            >
              <div className="flex items-center space-x-reverse space-x-3">
                {item.icon}
                <span className="font-medium">{item.title}</span>
              </div>
              {item.hasSubmenu && (
                <ChevronDown
                  className={`w-4 h-4 transition-transform duration-200 ${
                    expandedMenus.includes(item.id) ? 'rotate-180' : ''
                  }`}
                />
              )}
            </button>

            {/* Submenu */}
            {item.hasSubmenu && expandedMenus.includes(item.id) && (
              <div className="mr-6 mt-2 space-y-1">
                {item.submenu?.map((subItem) => (
                  <button
                    key={subItem.id}
                    onClick={() => {
                      // حفظ الصفحة الحالية في localStorage
                      localStorage.setItem('currentView', subItem.id);
                      setCurrentView(subItem.id);
                    }}
                    className={`w-full flex items-center space-x-reverse space-x-3 p-2 rounded-lg transition-all duration-200 ${
                      currentView === subItem.id
                        ? 'bg-blue-500 text-white'
                        : 'text-slate-400 hover:bg-slate-700 hover:text-white'
                    }`}
                  >
                    {subItem.icon}
                    <span className="text-sm">{subItem.title}</span>
                  </button>
                ))}
              </div>
            )}
          </div>
        ))}
      </nav>

      {/* Footer */}
      <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-slate-700 bg-slate-800">
        <div className="text-center">
          <p className="text-xs text-slate-400">EBMSOFT</p>
          <p className="text-xs text-slate-500">أحد مشاريع مؤسسة</p>
          <p className="text-xs text-slate-500">جميع الحقوق محفوظة © 2025</p>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;