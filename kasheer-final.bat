@echo off
title Kasheer Toosar - POS System
color 0A
cd /d "%~dp0"

echo.
echo ==========================================
echo     Kasheer Toosar - POS System
echo ==========================================
echo.

REM Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found!
    echo Please install Node.js from: https://nodejs.org
    echo.
    pause
    exit /b 1
)

echo [✓] Node.js found

REM Install dependencies if needed
if not exist "node_modules\vite" (
    echo [!] Installing dependencies...
    npm install --silent
    if %errorlevel% neq 0 (
        echo [X] Failed to install dependencies!
        pause
        exit /b 1
    )
    echo [✓] Dependencies installed
)

REM Build project if needed
if not exist "dist\index.html" (
    echo [!] Building project...
    npm run build --silent
    if %errorlevel% neq 0 (
        echo [X] Build failed!
        pause
        exit /b 1
    )
    echo [✓] Project built successfully
)

REM Check PostgreSQL (optional warning)
echo [!] Make sure PostgreSQL is running...

REM Start server in background
echo [!] Starting server...
start /min "Kasheer-Server" node backend\server.js

REM Wait for server
timeout /t 3 /nobreak >nul

REM Check if server is running
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:5003' -TimeoutSec 5 -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorlevel% equ 0 (
    echo [✓] Server is running
) else (
    echo [!] Server starting... (may take a moment)
)

REM Open browser
echo [!] Opening browser...
start http://localhost:5003

echo.
echo ==========================================
echo   SUCCESS! System is now running
echo   URL: http://localhost:5003
echo ==========================================
echo.
echo IMPORTANT:
echo - Keep this window open
echo - Close this window to stop the server
echo - Make sure PostgreSQL is running
echo.
echo Press any key to minimize this window...
pause >nul

REM Minimize window and keep monitoring
powershell -Command "(New-Object -ComObject Shell.Application).MinimizeAll()"

:monitor
timeout /t 60 /nobreak >nul
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:5003' -TimeoutSec 3 -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorlevel% neq 0 (
    echo [!] Server stopped! Restarting...
    start /min "Kasheer-Server" node backend\server.js
    timeout /t 5 /nobreak >nul
)
goto monitor
