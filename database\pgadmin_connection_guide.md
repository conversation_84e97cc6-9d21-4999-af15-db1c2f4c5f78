# 🔗 دليل الاتصال بـ pgAdmin
# pgAdmin Connection Guide

## 📋 **إعداد الخادم في pgAdmin**

### **1. إضافة خادم جديد:**
```
1. افتح pgAdmin 4
2. انقر بزر الماوس الأيمن على "Servers" في الشريط الجانبي
3. اختر "Create" → "Server..."
```

### **2. تبويب General:**
```
Name: POS System Server
Server group: Servers (افتراضي)
Comments: خادم نظام نقطة البيع المحلي
```

### **3. تبويب Connection:**
```
Host name/address: localhost
Port: 5432
Maintenance database: postgres
Username: postgres
Password: [كلمة مرور PostgreSQL الخاصة بك]
Save password: ✅ (اختياري)
```

### **4. تبويب SSL (اختياري):**
```
SSL mode: Prefer
```

### **5. حفظ الإعدادات:**
```
اضغط "Save" لحفظ إعدادات الخادم
```

## 🗄️ **الاتصال بقاعدة البيانات**

### **بعد إنشاء قاعدة البيانات:**
```
1. في pgAdmin، توسع "Servers" → "POS System Server"
2. توسع "Databases"
3. ستجد "pos_system_db"
4. انقر عليها للاتصال
```

### **استكشاف البيانات:**
```
pos_system_db/
├── Schemas/
│   ├── pos_system/
│   │   ├── Tables/
│   │   │   ├── categories
│   │   │   ├── products
│   │   │   ├── customers
│   │   │   ├── sales
│   │   │   ├── sale_items
│   │   │   └── ... (باقي الجداول)
│   │   ├── Views/
│   │   │   ├── daily_sales_stats
│   │   │   ├── top_selling_products
│   │   │   └── ... (باقي الـ Views)
│   │   ├── Functions/
│   │   └── Triggers/
│   └── public/
```

## 🔐 **المستخدمون والصلاحيات**

### **المستخدمون المتاحون:**

#### **1. مدير النظام (Admin):**
```
Username: pos_admin_user
Password: admin_secure_password_2024
Privileges: جميع الصلاحيات
```

#### **2. المدير العام (Manager):**
```
Username: pos_manager_user
Password: manager_secure_password_2024
Privileges: قراءة وكتابة معظم الجداول
```

#### **3. الكاشير (Cashier):**
```
Username: pos_cashier_user
Password: cashier_secure_password_2024
Privileges: المبيعات والعمليات اليومية
```

#### **4. قراءة فقط (ReadOnly):**
```
Username: pos_readonly_user
Password: readonly_secure_password_2024
Privileges: قراءة فقط للتقارير
```

### **إنشاء اتصال لكل مستخدم:**
```
1. كرر عملية إنشاء الخادم لكل مستخدم
2. غير Username و Password حسب المستخدم
3. اعطِ أسماء مختلفة مثل:
   - "POS Admin Connection"
   - "POS Manager Connection"
   - "POS Cashier Connection"
   - "POS ReadOnly Connection"
```

## 📊 **استكشاف البيانات**

### **1. عرض الجداول:**
```
1. توسع pos_system_db → Schemas → pos_system → Tables
2. انقر بزر الماوس الأيمن على أي جدول
3. اختر "View/Edit Data" → "All Rows"
```

### **2. تشغيل استعلامات:**
```
1. انقر بزر الماوس الأيمن على قاعدة البيانات
2. اختر "Query Tool"
3. اكتب استعلامك، مثل:
   SELECT * FROM pos_system.products;
4. اضغط F5 أو زر "Execute"
```

### **3. عرض التقارير:**
```
-- إحصائيات اليوم
SELECT * FROM pos_system.daily_sales_stats 
WHERE sale_date = CURRENT_DATE;

-- المنتجات الأكثر مبيعاً
SELECT * FROM pos_system.top_selling_products 
LIMIT 10;

-- المنتجات منخفضة المخزون
SELECT * FROM pos_system.low_stock_products;

-- الملخص المالي
SELECT * FROM pos_system.financial_summary;
```

## 🔧 **أدوات مفيدة في pgAdmin**

### **1. Query Tool:**
```
- تشغيل استعلامات SQL
- حفظ الاستعلامات المفضلة
- تصدير النتائج
```

### **2. Data Export/Import:**
```
- تصدير البيانات إلى CSV, JSON, XML
- استيراد البيانات من ملفات
```

### **3. Backup/Restore:**
```
- إنشاء نسخ احتياطية
- استعادة قواعد البيانات
```

### **4. Monitoring:**
```
- مراقبة الأداء
- عرض الاستعلامات النشطة
- إحصائيات الاستخدام
```

## 🚨 **استكشاف الأخطاء**

### **مشاكل الاتصال الشائعة:**

#### **1. خطأ "server closed the connection unexpectedly":**
```
الحل:
- تأكد من تشغيل PostgreSQL
- تحقق من إعدادات pg_hba.conf
- تأكد من صحة كلمة المرور
```

#### **2. خطأ "database does not exist":**
```
الحل:
- تأكد من إنشاء قاعدة البيانات أولاً
- تحقق من اسم قاعدة البيانات
```

#### **3. خطأ "permission denied":**
```
الحل:
- تحقق من صلاحيات المستخدم
- استخدم مستخدم postgres للإعداد الأولي
```

### **التحقق من حالة PostgreSQL:**

#### **Windows:**
```
services.msc → PostgreSQL
أو
net start postgresql-x64-13
```

#### **Linux:**
```
sudo systemctl status postgresql
sudo systemctl start postgresql
```

#### **macOS:**
```
brew services list | grep postgresql
brew services start postgresql
```

## 📞 **الدعم الإضافي**

### **الوثائق الرسمية:**
- [pgAdmin Documentation](https://www.pgadmin.org/docs/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

### **مجتمعات المساعدة:**
- [pgAdmin Support](https://www.pgadmin.org/support/)
- [PostgreSQL Community](https://www.postgresql.org/community/)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/pgadmin)
