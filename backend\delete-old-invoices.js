// حذف الفواتير القديمة
const { Pool } = require('pg');

const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'pos_system_db',
  user: 'postgres',
  password: 'toossar',
});

// تعيين schema افتراضي
pool.on('connect', (client) => {
  client.query('SET search_path TO pos_system, public');
});

async function deleteOldInvoices() {
  try {
    console.log('🗑️ بدء حذف الفواتير القديمة...');
    
    // الفواتير المراد حذفها
    const invoiceIds = [
      '0c16f02b-2fac-400f-a7fb-35871e02ff0d',
      'ef44c51a-983c-49b4-8557-1922b6bfb53a'
    ];
    
    // بدء معاملة قاعدة البيانات
    await pool.query('BEGIN');
    
    try {
      for (const invoiceId of invoiceIds) {
        console.log(`🗑️ حذف الفاتورة: ${invoiceId}`);
        
        // حذف عناصر الفاتورة أولاً
        const itemsResult = await pool.query(
          'DELETE FROM purchase_invoice_items WHERE invoice_id = $1',
          [invoiceId]
        );
        console.log(`📦 تم حذف ${itemsResult.rowCount} عنصر من الفاتورة`);
        
        // حذف الفاتورة
        const invoiceResult = await pool.query(
          'DELETE FROM purchase_invoices WHERE id = $1',
          [invoiceId]
        );
        
        if (invoiceResult.rowCount > 0) {
          console.log(`✅ تم حذف الفاتورة: ${invoiceId}`);
        } else {
          console.log(`⚠️ الفاتورة غير موجودة: ${invoiceId}`);
        }
      }
      
      // عرض عدد الفواتير المتبقية قبل إنهاء المعاملة
      const countResult = await pool.query('SELECT COUNT(*) as count FROM purchase_invoices');

      // تأكيد المعاملة
      await pool.query('COMMIT');

      console.log('✅ تم حذف جميع الفواتير القديمة بنجاح!');
      console.log(`📊 عدد الفواتير المتبقية: ${countResult.rows[0].count}`);
      
    } catch (transactionError) {
      // إلغاء المعاملة في حالة الخطأ
      await pool.query('ROLLBACK');
      throw transactionError;
    }
    
  } catch (error) {
    console.error('❌ خطأ في حذف الفواتير:', error);
  } finally {
    await pool.end();
  }
}

deleteOldInvoices();
