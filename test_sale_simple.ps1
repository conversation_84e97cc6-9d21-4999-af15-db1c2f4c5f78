$headers = @{
    'Content-Type' = 'application/json'
}

$saleData = @{
    customer_id = $null
    items = @(
        @{
            product_id = $null
            product_name = "Test Product"
            quantity = 1
            unit_price = 50.00
            discount = 0
            total_price = 50.00
        }
    )
    subtotal = 50.00
    tax_amount = 0.00
    discount_amount = 0.00
    total_amount = 50.00
    payment_method = "cash"
    amount_paid = 50.00
    change_amount = 0.00
    notes = "Debug test"
    cashier_name = "Developer"
}

$body = $saleData | ConvertTo-Json -Depth 10

Write-Host "Testing sale API..." -ForegroundColor Yellow

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5002/api/sales" -Method POST -Headers $headers -Body $body
    
    Write-Host "Sale successful!" -ForegroundColor Green
    Write-Host "Sale Number: $($response.sale_number)" -ForegroundColor Green
    Write-Host "Total: $($response.total_amount)" -ForegroundColor Green
    Write-Host "ID: $($response.id)" -ForegroundColor Green
    
    # Check if sale exists in database
    Write-Host "Checking database..." -ForegroundColor Cyan
    $checkResponse = Invoke-RestMethod -Uri "http://localhost:5002/api/sales" -Method GET -Headers $headers
    
    $foundSale = $checkResponse | Where-Object { $_.id -eq $response.id }
    if ($foundSale) {
        Write-Host "Sale found in database!" -ForegroundColor Green
    } else {
        Write-Host "Sale NOT found in database!" -ForegroundColor Red
    }
    
} catch {
    Write-Host "Sale failed:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    if ($_.ErrorDetails) {
        Write-Host "Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
}
