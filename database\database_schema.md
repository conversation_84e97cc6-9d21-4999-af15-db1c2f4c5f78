# مخطط قاعدة البيانات - نظام نقطة البيع
# Database Schema - POS System

## 🗄️ **نظرة عامة على قاعدة البيانات**

### **الجداول الرئيسية:**
- **settings** - الإعدادات العامة
- **printer_settings** - إعدادات الطابعة
- **categories** - فئات المنتجات
- **products** - المنتجات
- **customers** - العملاء
- **suppliers** - الموردين
- **sales** - المبيعات
- **sale_items** - عناصر المبيعات
- **purchase_invoices** - فواتير المشتريات
- **purchase_invoice_items** - عناصر فواتير المشتريات
- **financial_transactions** - المعاملات المالية
- **inventory_movements** - حركة المخزون
- **customer_debts** - ديون العملاء
- **customer_debt_payments** - مدفوعات الديون
- **audit_log** - سجل المراجعة

## 🔗 **العلاقات بين الجداول**

### **1. علاقات المنتجات:**
```
categories (1) ←→ (N) products
products (1) ←→ (N) sale_items
products (1) ←→ (N) purchase_invoice_items
products (1) ←→ (N) inventory_movements
```

### **2. علاقات المبيعات:**
```
customers (1) ←→ (N) sales
sales (1) ←→ (N) sale_items
sales (1) ←→ (1) financial_transactions
sales (1) ←→ (0..1) customer_debts
```

### **3. علاقات المشتريات:**
```
suppliers (1) ←→ (N) purchase_invoices
purchase_invoices (1) ←→ (N) purchase_invoice_items
purchase_invoices (1) ←→ (1) financial_transactions
```

### **4. علاقات الديون:**
```
customers (1) ←→ (N) customer_debts
customer_debts (1) ←→ (N) customer_debt_payments
```

## 📊 **مخطط ERD النصي**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   categories    │    │    products     │    │   sale_items    │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ id (PK)         │◄──┤│ id (PK)         │◄──┤│ id (PK)         │
│ name            │    │ name            │    │ sale_id (FK)    │
│ description     │    │ description     │    │ product_id (FK) │
│ color           │    │ barcode         │    │ product_name    │
│ icon            │    │ category_id(FK) │    │ quantity        │
│ is_active       │    │ price           │    │ unit_price      │
│ created_at      │    │ cost            │    │ discount        │
│ updated_at      │    │ stock           │    │ total_price     │
└─────────────────┘    │ min_stock       │    │ created_at      │
                       │ unit            │    └─────────────────┘
                       │ is_active       │              ▲
                       │ created_at      │              │
                       │ updated_at      │              │
                       └─────────────────┘              │
                                ▲                       │
                                │                       │
                                │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   customers     │    │     sales       │────┘
├─────────────────┤    ├─────────────────┤
│ id (PK)         │◄──┤│ id (PK)         │
│ name            │    │ sale_number     │
│ phone           │    │ customer_id(FK) │
│ address         │    │ subtotal        │
│ email           │    │ tax_amount      │
│ balance         │    │ discount_amount │
│ credit_limit    │    │ total_amount    │
│ is_active       │    │ payment_method  │
│ created_at      │    │ amount_paid     │
│ updated_at      │    │ change_amount   │
└─────────────────┘    │ status          │
         │              │ notes           │
         │              │ cashier_name    │
         │              │ receipt_printed │
         │              │ created_at      │
         │              │ updated_at      │
         │              └─────────────────┘
         │                       │
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│ customer_debts  │    │financial_trans  │
├─────────────────┤    ├─────────────────┤
│ id (PK)         │    │ id (PK)         │
│ customer_id(FK) │    │ transaction_num │
│ sale_id (FK)    │    │ type            │
│ amount          │    │ category        │
│ paid_amount     │    │ amount          │
│ remaining_amt   │    │ description     │
│ is_paid         │    │ reference_id    │
│ debt_date       │    │ reference_type  │
│ due_date        │    │ payment_method  │
│ notes           │    │ notes           │
│ created_at      │    │ transaction_dt  │
│ updated_at      │    │ created_at      │
└─────────────────┘    │ updated_at      │
         │              └─────────────────┘
         │
         ▼
┌─────────────────┐
│debt_payments    │
├─────────────────┤
│ id (PK)         │
│ debt_id (FK)    │
│ amount          │
│ payment_method  │
│ payment_date    │
│ notes           │
│ created_at      │
└─────────────────┘

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   suppliers     │    │purchase_invoices│    │purchase_inv_items│
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ id (PK)         │◄──┤│ id (PK)         │◄──┤│ id (PK)         │
│ name            │    │ invoice_number  │    │ invoice_id (FK) │
│ phone           │    │ supplier_id(FK) │    │ product_id (FK) │
│ address         │    │ subtotal        │    │ product_name    │
│ email           │    │ tax_amount      │    │ quantity        │
│ balance         │    │ discount_amount │    │ unit_cost       │
│ is_active       │    │ total_amount    │    │ discount        │
│ created_at      │    │ amount_paid     │    │ total_cost      │
│ updated_at      │    │ remaining_amt   │    │ created_at      │
└─────────────────┘    │ is_paid         │    └─────────────────┘
                       │ invoice_date    │
                       │ due_date        │
                       │ notes           │
                       │ created_at      │
                       │ updated_at      │
                       └─────────────────┘

┌─────────────────┐
│inventory_movemnts│
├─────────────────┤
│ id (PK)         │
│ product_id (FK) │──┐
│ movement_type   │  │
│ quantity        │  │
│ previous_stock  │  │
│ new_stock       │  │
│ reason          │  │
│ reference_id    │  │
│ reference_type  │  │
│ notes           │  │
│ created_at      │  │
└─────────────────┘  │
                     │
                     ▼
              ┌─────────────────┐
              │    products     │
              │   (reference)   │
              └─────────────────┘
```

## 🔧 **المشغلات والوظائف الرئيسية**

### **1. مشغلات تحديث المخزون:**
- `update_inventory_on_sale()` - تحديث المخزون عند البيع
- `update_inventory_on_purchase()` - تحديث المخزون عند الشراء

### **2. مشغلات المعاملات المالية:**
- `create_financial_transaction_on_sale()` - إنشاء معاملة مالية للبيع
- `create_financial_transaction_on_purchase()` - إنشاء معاملة مالية للشراء

### **3. مشغلات إدارة الديون:**
- `update_customer_balance()` - تحديث رصيد العميل
- `update_debt_on_payment()` - تحديث الدين عند الدفع

### **4. مشغلات النظام:**
- `update_updated_at_column()` - تحديث تاريخ التعديل تلقائياً
- `generate_sale_number()` - توليد رقم البيع تلقائياً
- `generate_purchase_number()` - توليد رقم فاتورة الشراء تلقائياً

## 📈 **Views للتقارير**

### **1. إحصائيات المبيعات:**
- `daily_sales_stats` - إحصائيات المبيعات اليومية
- `monthly_sales_performance` - أداء المبيعات الشهري
- `top_selling_products` - المنتجات الأكثر مبيعاً

### **2. إدارة المخزون:**
- `low_stock_products` - المنتجات منخفضة المخزون
- `inventory_movement_summary` - ملخص حركة المخزون

### **3. التقارير المالية:**
- `financial_summary` - الملخص المالي
- `profitability_analysis` - تحليل الربحية

### **4. إدارة العملاء:**
- `customer_statistics` - إحصائيات العملاء
- `outstanding_debts` - الديون المستحقة

## 🔐 **الأمان والصلاحيات**

### **الأدوار:**
- `pos_admin` - صلاحيات كاملة
- `pos_manager` - صلاحيات إدارية
- `pos_cashier` - صلاحيات الكاشير
- `pos_readonly` - قراءة فقط

### **المراجعة:**
- `audit_log` - سجل جميع العمليات الحساسة
- `log_audit_trail()` - تسجيل العمليات تلقائياً

## 🚀 **الفهارس للأداء**

### **فهارس البحث:**
- فهارس على أسماء المنتجات والباركود
- فهارس على تواريخ المبيعات والمعاملات
- فهارس على معرفات العملاء والموردين

### **فهارس الأداء:**
- فهارس مركبة للاستعلامات المعقدة
- فهارس النص الكامل للبحث في المنتجات
- فهارس الحالة والتواريخ للتقارير

## 📊 **إحصائيات قاعدة البيانات**

- **عدد الجداول:** 15 جدول رئيسي
- **عدد الـ Views:** 10 views للتقارير
- **عدد المشغلات:** 12 مشغل
- **عدد الوظائف:** 8 وظائف مخصصة
- **عدد الفهارس:** 25+ فهرس للأداء
- **أنواع البيانات المخصصة:** 12 enum type
