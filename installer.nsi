; Kashe<PERSON> Toosar Installer 
!define APPNAME "Kasheer Toosar" 
!define COMPANYNAME "Toosar" 
!define DESCRIPTION "نظام نقاط البيع" 
!define VERSIONMAJOR 1 
!define VERSIONMINOR 0 
!define VERSIONBUILD 0 
!define HELPURL "http://toosar.com" 
!define UPDATEURL "http://toosar.com" 
!define ABOUTURL "http://toosar.com" 
!define INSTALLSIZE 100000 
 
RequestExecutionLevel admin 
InstallDir "$PROGRAMFILES\${APPNAME}" 
 
Name "${APPNAME}" 
outFile "Kasheer-Toosar-Setup.exe" 
 
page directory 
page instfiles 
 
section "install" 
	setOutPath $INSTDIR 
	file /r "backend" 
	file /r "dist" 
	file /r "node_modules" 
	file "package.json" 
	file "start.bat" 
 
	writeUninstaller "$INSTDIR\uninstall.exe" 
 
	createDirectory "$SMPROGRAMS\${APPNAME}" 
	createShortCut "$SMPROGRAMS\${APPNAME}\${APPNAME}.lnk" "$INSTDIR\start.bat" 
	createShortCut "$DESKTOP\${APPNAME}.lnk" "$INSTDIR\start.bat" 
 
	WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayName" "${APPNAME}" 
	WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "UninstallString" "$INSTDIR\uninstall.exe" 
	WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "InstallLocation" "$INSTDIR" 
	WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "Publisher" "${COMPANYNAME}" 
	WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "VersionMajor" ${VERSIONMAJOR} 
	WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "VersionMinor" ${VERSIONMINOR} 
	WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "EstimatedSize" ${INSTALLSIZE} 
sectionEnd 
 
section "uninstall" 
	delete "$SMPROGRAMS\${APPNAME}\${APPNAME}.lnk" 
	delete "$DESKTOP\${APPNAME}.lnk" 
	rmDir "$SMPROGRAMS\${APPNAME}" 
	rmDir /r "$INSTDIR" 
	DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" 
sectionEnd 
