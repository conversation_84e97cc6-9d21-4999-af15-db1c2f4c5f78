#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import urllib.parse
from datetime import datetime

class APIHandler(BaseHTTPRequestHandler):
    def _set_headers(self):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

    def do_OPTIONS(self):
        self._set_headers()

    def do_GET(self):
        self._set_headers()
        
        if self.path == '/api/test':
            response = {
                'message': '✅ Backend API يعمل بنجاح!',
                'timestamp': datetime.now().isoformat()
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            
        elif self.path == '/api/customers/debtors':
            print('📞 تم استلام طلب المدينين')
            mock_debtors = [
                {
                    'id': '82431516-ec6b-44a7-904c-52632c1ced43',
                    'name': 'أحمد محمد',
                    'phone': '0555123456',
                    'email': '<EMAIL>',
                    'address': 'الرياض، المملكة العربية السعودية',
                    'balance': 0,
                    'credit_limit': 5000,
                    'total_debt': 1500,
                    'paid_amount': 500,
                    'remaining_debt': 1000,
                    'debt_count': 2,
                    'created_at': '2024-01-15T10:30:00Z',
                    'updated_at': '2024-01-20T14:45:00Z'
                }
            ]
            self.wfile.write(json.dumps(mock_debtors, ensure_ascii=False).encode('utf-8'))
            
        elif '/api/customers/' in self.path and '/debts' in self.path:
            customer_id = self.path.split('/')[3]
            print(f'📞 تم استلام طلب ديون العميل: {customer_id}')
            mock_debts = [
                {
                    'id': 'debt-1',
                    'customer_id': customer_id,
                    'amount': 1000,
                    'paid_amount': 0,
                    'status': 'pending',
                    'sale_id': 'sale-1',
                    'created_at': '2024-01-15T10:30:00Z',
                    'updated_at': '2024-01-15T10:30:00Z'
                }
            ]
            self.wfile.write(json.dumps(mock_debts, ensure_ascii=False).encode('utf-8'))
        else:
            self.send_response(404)
            self.end_headers()

    def do_POST(self):
        if '/api/customers/' in self.path and '/pay-debt' in self.path:
            customer_id = self.path.split('/')[3]
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            print(f'📞 تم استلام طلب تسديد دين للعميل: {customer_id}')
            print(f'📊 بيانات الدفع: {data}')
            
            self._set_headers()
            response = {
                'message': 'تم تسديد الدين بنجاح',
                'paid_amount': float(data.get('amount', 0)),
                'new_status': 'partial'
            }
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
        else:
            self.send_response(404)
            self.end_headers()

def run_server():
    port = 5001
    server_address = ('', port)
    httpd = HTTPServer(server_address, APIHandler)
    print(f'🚀 السيرفر Python يعمل على المنفذ {port}')
    print(f'🔗 API متاح على: http://localhost:{port}/api')
    print('✅ السيرفر جاهز للاستخدام!')
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print('\n🛑 إيقاف السيرفر...')
        httpd.shutdown()

if __name__ == '__main__':
    run_server()
