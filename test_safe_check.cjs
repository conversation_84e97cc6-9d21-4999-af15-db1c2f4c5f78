const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'pos_system',
  password: 'toossar',
  port: 5432,
});

async function checkDatabaseSafety() {
  const client = await pool.connect();
  try {
    console.log('🔍 فحص أمان قاعدة البيانات...\n');
    
    // 1. عد المنتجات الموجودة
    const productsCount = await client.query('SELECT COUNT(*) FROM products');
    console.log(`📦 عدد المنتجات الحالية: ${productsCount.rows[0].count}`);
    
    // 2. عد المبيعات الموجودة
    const salesCount = await client.query('SELECT COUNT(*) FROM sales');
    console.log(`💰 عدد المبيعات الحالية: ${salesCount.rows[0].count}`);
    
    // 3. عد عناصر المبيعات
    const saleItemsCount = await client.query('SELECT COUNT(*) FROM sale_items');
    console.log(`📋 عدد عناصر المبيعات: ${saleItemsCount.rows[0].count}`);
    
    // 4. عد العملاء
    const customersCount = await client.query('SELECT COUNT(*) FROM customers');
    console.log(`👥 عدد العملاء: ${customersCount.rows[0].count}`);
    
    // 5. فحص هيكل جدول sale_items
    const tableStructure = await client.query(`
      SELECT column_name, is_nullable, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'sale_items' 
      ORDER BY ordinal_position
    `);
    
    console.log('\n📊 هيكل جدول sale_items الحالي:');
    tableStructure.rows.forEach(row => {
      console.log(`   ${row.column_name}: ${row.data_type} (NULL: ${row.is_nullable})`);
    });
    
    // 6. التحقق من وجود المنتج الافتراضي
    const defaultProduct = await client.query(`
      SELECT id, name FROM products 
      WHERE id = '00000000-0000-0000-0000-000000000001'::UUID
    `);
    
    if (defaultProduct.rows.length > 0) {
      console.log(`\n✅ المنتج الافتراضي موجود: ${defaultProduct.rows[0].name}`);
    } else {
      console.log('\n⚠️ المنتج الافتراضي غير موجود (سيتم إنشاؤه)');
    }
    
    console.log('\n🛡️ جميع بياناتك آمنة ومحفوظة!');
    console.log('💡 التعديل المطلوب بسيط جداً ولن يؤثر على أي بيانات موجودة');
    
  } catch (error) {
    console.error('❌ خطأ في الاتصال:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

checkDatabaseSafety();
