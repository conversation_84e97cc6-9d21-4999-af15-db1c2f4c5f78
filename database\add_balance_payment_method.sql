-- إضافة طريقة الدفع بالرصيد إلى enum payment_method
-- Add balance payment method to payment_method enum

-- أولاً: التحقق من وجود enum payment_method وإنشاؤه إذا لم يكن موجوداً
DO $$
BEGIN
    -- التحقق من وجود enum payment_method
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'payment_method') THEN
        -- إنشاء enum payment_method مع جميع القيم المطلوبة
        CREATE TYPE payment_method AS ENUM ('cash', 'card', 'credit', 'mixed', 'balance');
        RAISE NOTICE 'تم إنشاء enum payment_method مع طريقة الدفع بالرصيد';
    ELSE
        -- التحقق من وجود قيمة balance في enum payment_method الموجود
        IF NOT EXISTS (
            SELECT 1 FROM pg_enum
            WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'payment_method')
            AND enumlabel = 'balance'
        ) THEN
            -- إضافة القيمة balance إلى enum payment_method الموجود
            ALTER TYPE payment_method ADD VALUE 'balance';
            RAISE NOTICE 'تم إضافة طريقة الدفع بالرصيد (balance) إلى enum الموجود';
        ELSE
            RAISE NOTICE 'طريقة الدفع بالرصيد (balance) موجودة بالفعل';
        END IF;
    END IF;
END $$;

-- التحقق من النتيجة النهائية
SELECT enumlabel as payment_methods
FROM pg_enum
WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'payment_method')
ORDER BY enumsortorder;

-- التحقق من بنية جدول sales وتحديث عمود payment_method إذا لزم الأمر
DO $$
BEGIN
    -- التحقق من نوع عمود payment_method في جدول sales
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'sales'
        AND column_name = 'payment_method'
        AND data_type = 'character varying'
    ) THEN
        -- تحديث نوع العمود من VARCHAR إلى enum payment_method
        ALTER TABLE sales ALTER COLUMN payment_method TYPE payment_method USING payment_method::payment_method;
        RAISE NOTICE 'تم تحديث نوع عمود payment_method في جدول sales إلى enum';
    ELSE
        RAISE NOTICE 'عمود payment_method في جدول sales يستخدم النوع الصحيح بالفعل';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'تحذير: لم يتم العثور على جدول sales أو عمود payment_method';
END $$;
