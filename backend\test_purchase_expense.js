const axios = require('axios');

const API_BASE = 'http://localhost:5002/api';

async function testPurchaseExpense() {
  try {
    console.log('🧪 اختبار إضافة مصروف عند إنشاء فاتورة مشتريات مدفوعة...');
    
    // 1️⃣ جلب قائمة الموردين
    console.log('\n1️⃣ جلب قائمة الموردين...');
    const suppliersResponse = await axios.get(`${API_BASE}/suppliers`);
    const suppliers = suppliersResponse.data;
    
    if (suppliers.length === 0) {
      console.log('❌ لا توجد موردين في النظام');
      return;
    }
    
    const supplier = suppliers[0];
    console.log(`✅ تم اختيار المورد: ${supplier.name} (ID: ${supplier.id})`);
    
    // 2️⃣ جلب قائمة المنتجات
    console.log('\n2️⃣ جلب قائمة المنتجات...');
    const productsResponse = await axios.get(`${API_BASE}/products`);
    const products = productsResponse.data;
    
    if (products.length === 0) {
      console.log('❌ لا توجد منتجات في النظام');
      return;
    }
    
    const product = products[0];
    console.log(`✅ تم اختيار المنتج: ${product.name} (ID: ${product.id})`);
    
    // 3️⃣ التحقق من المصروفات قبل إنشاء الفاتورة
    console.log('\n3️⃣ التحقق من المصروفات قبل إنشاء الفاتورة...');
    const expensesBefore = await axios.get(`${API_BASE}/financial/overview`);
    const totalExpensesBefore = expensesBefore.data.expenses?.total || 0;
    console.log(`💰 إجمالي المصروفات قبل الفاتورة: ${totalExpensesBefore} دج`);
    
    // 4️⃣ إنشاء فاتورة مشتريات مدفوعة بالكامل
    console.log('\n4️⃣ إنشاء فاتورة مشتريات مدفوعة بالكامل...');
    const purchaseData = {
      supplier_id: supplier.id,
      total_amount: 1000,
      amount_paid: 1000, // مدفوعة بالكامل
      invoice_date: new Date().toISOString().split('T')[0],
      notes: 'فاتورة اختبار للتحقق من إضافة المصروف',
      items: [
        {
          product_id: product.id,
          product_name: product.name,
          quantity: 10,
          unit_cost: 100,
          discount: 0,
          total_cost: 1000
        }
      ]
    };
    
    const purchaseResponse = await axios.post(`${API_BASE}/purchases`, purchaseData);
    const newPurchase = purchaseResponse.data;
    console.log(`✅ تم إنشاء الفاتورة: ${newPurchase.purchase.id}`);
    console.log(`💰 المبلغ المدفوع: ${purchaseData.amount_paid} دج`);
    
    // 5️⃣ التحقق من المصروفات بعد إنشاء الفاتورة
    console.log('\n5️⃣ التحقق من المصروفات بعد إنشاء الفاتورة...');
    await new Promise(resolve => setTimeout(resolve, 1000)); // انتظار ثانية واحدة
    
    const expensesAfter = await axios.get(`${API_BASE}/financial/overview`);
    const totalExpensesAfter = expensesAfter.data.expenses?.total || 0;
    console.log(`💰 إجمالي المصروفات بعد الفاتورة: ${totalExpensesAfter} دج`);
    
    const expenseDifference = totalExpensesAfter - totalExpensesBefore;
    console.log(`📊 الفرق في المصروفات: ${expenseDifference} دج`);
    
    // 6️⃣ التحقق من جدول المعاملات المالية
    console.log('\n6️⃣ التحقق من جدول المعاملات المالية...');
    try {
      const transactionsResponse = await axios.get(`${API_BASE}/financial/detailed-report`);
      const transactions = transactionsResponse.data;
      
      // البحث عن المعاملة المضافة
      const purchaseTransaction = transactions.financial_transactions?.find(t => 
        t.reference_id === newPurchase.purchase.id && t.type === 'expense'
      );
      
      if (purchaseTransaction) {
        console.log(`✅ تم العثور على المعاملة المالية:`);
        console.log(`   - رقم المعاملة: ${purchaseTransaction.transaction_number}`);
        console.log(`   - النوع: ${purchaseTransaction.type}`);
        console.log(`   - الفئة: ${purchaseTransaction.category}`);
        console.log(`   - المبلغ: ${purchaseTransaction.amount} دج`);
        console.log(`   - الوصف: ${purchaseTransaction.description}`);
      } else {
        console.log(`❌ لم يتم العثور على المعاملة المالية`);
      }
    } catch (error) {
      console.log(`⚠️ خطأ في جلب المعاملات المالية: ${error.message}`);
    }
    
    // 7️⃣ النتيجة النهائية
    console.log('\n🎯 النتيجة النهائية:');
    if (expenseDifference === purchaseData.amount_paid) {
      console.log(`✅ نجح الاختبار! تم إضافة المصروف بقيمة ${expenseDifference} دج`);
    } else {
      console.log(`❌ فشل الاختبار! المتوقع: ${purchaseData.amount_paid} دج، الفعلي: ${expenseDifference} دج`);
    }
    
  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
    if (error.response) {
      console.error('📋 تفاصيل الخطأ:', error.response.data);
    }
  }
}

// تشغيل الاختبار
testPurchaseExpense();
