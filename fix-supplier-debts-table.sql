-- إصلاح جدول ديون الموردين وتوحيد أسماء الأعمدة

-- التحقق من بنية الجدول الحالية
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_schema = 'pos_system' 
  AND table_name = 'supplier_debts'
ORDER BY ordinal_position;

-- إضافة العمود purchase_id إذا لم يكن موجوداً
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'pos_system' 
          AND table_name = 'supplier_debts' 
          AND column_name = 'purchase_id'
    ) THEN
        ALTER TABLE pos_system.supplier_debts 
        ADD COLUMN purchase_id UUID REFERENCES pos_system.purchase_invoices(id);
        RAISE NOTICE '✅ تم إضافة العمود purchase_id';
    ELSE
        RAISE NOTICE '✅ العمود purchase_id موجود بالفعل';
    END IF;
END $$;

-- إضافة العمود paid_amount إذا لم يكن موجوداً
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'pos_system' 
          AND table_name = 'supplier_debts' 
          AND column_name = 'paid_amount'
    ) THEN
        ALTER TABLE pos_system.supplier_debts 
        ADD COLUMN paid_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (paid_amount >= 0);
        RAISE NOTICE '✅ تم إضافة العمود paid_amount';
    ELSE
        RAISE NOTICE '✅ العمود paid_amount موجود بالفعل';
    END IF;
END $$;

-- إضافة العمود updated_at إذا لم يكن موجوداً
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'pos_system' 
          AND table_name = 'supplier_debts' 
          AND column_name = 'updated_at'
    ) THEN
        ALTER TABLE pos_system.supplier_debts 
        ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE '✅ تم إضافة العمود updated_at';
    ELSE
        RAISE NOTICE '✅ العمود updated_at موجود بالفعل';
    END IF;
END $$;

-- تحديث البيانات الموجودة إذا كان هناك عمود invoice_id
UPDATE pos_system.supplier_debts 
SET purchase_id = invoice_id 
WHERE purchase_id IS NULL AND invoice_id IS NOT NULL;

-- عرض النتائج النهائية
SELECT 'تم إصلاح جدول supplier_debts بنجاح' as message;
SELECT COUNT(*) as total_debts FROM pos_system.supplier_debts;
SELECT status, COUNT(*) as count FROM pos_system.supplier_debts GROUP BY status;
