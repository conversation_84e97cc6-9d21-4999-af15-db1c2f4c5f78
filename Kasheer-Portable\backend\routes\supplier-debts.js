const express = require('express');
const router = express.Router();
const pool = require('../database');

// 📋 جلب جميع ديون الموردين
router.get('/', async (req, res) => {
  try {
    console.log('📋 طلب جلب ديون الموردين...');

    const result = await pool.query(`
      SELECT
        sd.*,
        s.name as supplier_name,
        s.phone as supplier_phone
      FROM pos_system.supplier_debts sd
      LEFT JOIN pos_system.suppliers s ON sd.supplier_id = s.id
      ORDER BY sd.created_at DESC
    `);

    console.log(`✅ تم جلب ${result.rows.length} دين مورد`);
    res.json(result.rows);
  } catch (error) {
    console.error('❌ خطأ في جلب ديون الموردين:', error);
    res.status(500).json({ error: 'خطأ في جلب ديون الموردين' });
  }
});

// 💳 تحديث دين مورد لفاتورة محددة
router.put('/invoice/:invoiceId', async (req, res) => {
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    const { invoiceId } = req.params;
    const { amount_paid } = req.body;

    console.log(`💳 تحديث دين المورد للفاتورة: ${invoiceId} - مبلغ: ${amount_paid}`);

    if (!invoiceId || invoiceId === 'undefined') {
      return res.status(400).json({ error: 'معرف الفاتورة مطلوب' });
    }

    // جلب الفاتورة الحالية أولاً
    const currentInvoiceResult = await client.query(`
      SELECT * FROM pos_system.purchase_invoices WHERE id = $1
    `, [invoiceId]);

    if (currentInvoiceResult.rows.length === 0) {
      await client.query('ROLLBACK');
      return res.status(404).json({ error: 'الفاتورة غير موجودة' });
    }

    const currentInvoice = currentInvoiceResult.rows[0];
    const previousAmountPaid = parseFloat(currentInvoice.amount_paid) || 0;
    const newAmountPaid = parseFloat(amount_paid) || 0;
    const paymentAmount = newAmountPaid - previousAmountPaid;

    // تحديث الفاتورة
    const result = await client.query(`
      UPDATE pos_system.purchase_invoices
      SET amount_paid = $1, updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
      RETURNING *
    `, [amount_paid, invoiceId]);

    const invoice = result.rows[0];

    // جلب بيانات المورد
    const supplierResult = await client.query(`
      SELECT name FROM pos_system.suppliers WHERE id = $1
    `, [invoice.supplier_id]);

    const supplierName = supplierResult.rows[0]?.name || 'غير محدد';

    // تحديث دين المورد في جدول supplier_debts
    let debtUpdated = false;
    try {
      // محاولة أولى باستخدام purchase_id
      const debtUpdateResult = await client.query(`
        UPDATE pos_system.supplier_debts
        SET paid_amount = $1,
            status = CASE WHEN $1 >= amount THEN 'paid' ELSE 'partial' END,
            updated_at = CURRENT_TIMESTAMP
        WHERE purchase_id = $2
        RETURNING *
      `, [amount_paid, invoiceId]);

      if (debtUpdateResult.rowCount > 0) {
        debtUpdated = true;
      } else {
        // إذا لم يتم تحديث أي سجل، جرب باستخدام invoice_id
        const debtUpdateResult2 = await client.query(`
          UPDATE pos_system.supplier_debts
          SET paid_amount = $1,
              status = CASE WHEN $1 >= amount THEN 'paid' ELSE 'partial' END,
              updated_at = CURRENT_TIMESTAMP
          WHERE invoice_id = $2
          RETURNING *
        `, [amount_paid, invoiceId]);

        if (debtUpdateResult2.rowCount > 0) {
          debtUpdated = true;
        }
      }

      console.log(`💳 تم تحديث دين المورد للفاتورة: ${invoiceId}`);
    } catch (debtError) {
      console.error('⚠️ خطأ في تحديث جدول supplier_debts:', debtError.message);
      // لا نوقف العملية، فقط نسجل الخطأ
    }

    // إضافة المصروف تلقائياً عند دفع الفاتورة
    let expenseAdded = false;
    if (paymentAmount > 0) {
      try {
        const expenseDescription = `دفع فاتورة مشتريات للمورد ${supplierName}`;
        const expenseNotes = `دفع فاتورة رقم ${invoice.invoice_number || invoiceId} - مبلغ: ${paymentAmount} دج`;

        await client.query(`
          INSERT INTO pos_system.expenses (
            amount, description, category, payment_method, notes
          ) VALUES ($1, $2, $3, $4, $5)
        `, [
          paymentAmount,
          expenseDescription,
          'دفع فواتير مشتريات',
          'cash',
          expenseNotes
        ]);

        expenseAdded = true;
        console.log(`💰 تم إضافة مصروف بقيمة ${paymentAmount} دج للفاتورة ${invoiceId}`);
      } catch (expenseError) {
        console.error('⚠️ خطأ في إضافة المصروف:', expenseError);
        // لا نوقف العملية، فقط نسجل الخطأ
      }
    }

    await client.query('COMMIT');

    res.json({
      message: 'تم تحديث دين المورد وإضافة المصروف بنجاح',
      invoice: invoice,
      debt_updated: debtUpdated,
      expense_added: expenseAdded,
      payment_amount: paymentAmount
    });

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ خطأ في تحديث دين المورد:', error);
    res.status(500).json({ error: 'خطأ في تحديث دين المورد: ' + error.message });
  } finally {
    client.release();
  }
});

// 💰 إضافة دين جديد لمورد
router.post('/', async (req, res) => {
  try {
    const {
      supplier_id,
      amount,
      description,
      purchase_id = null,
      debt_date = new Date(),
      status = 'pending'
    } = req.body;

    console.log('💰 إضافة دين جديد لمورد');
    console.log('📤 البيانات المستلمة:', req.body);

    // التحقق من البيانات المطلوبة
    if (!supplier_id || !amount) {
      console.error('❌ بيانات ناقصة: supplier_id أو amount مفقود');
      return res.status(400).json({ error: 'معرف المورد والمبلغ مطلوبان' });
    }

    const result = await pool.query(`
      INSERT INTO pos_system.supplier_debts (
        supplier_id, purchase_id, amount, debt_date, paid_amount, status, notes
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `, [supplier_id, purchase_id, amount, debt_date, 0, status, description || '']);

    const newDebt = result.rows[0];

    console.log(`✅ تم إضافة دين جديد: ${newDebt.id} - المورد: ${supplier_id} - المبلغ: ${amount}`);
    res.status(201).json({
      message: 'تم إضافة الدين بنجاح',
      debt: newDebt
    });

  } catch (error) {
    console.error('❌ خطأ في إضافة دين المورد:', error);
    console.error('❌ تفاصيل الخطأ:', error.message);
    res.status(500).json({ error: 'خطأ في إضافة دين المورد: ' + error.message });
  }
});

// 💳 دفع دين مورد مباشرة
router.post('/:debtId/pay', async (req, res) => {
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    const { debtId } = req.params;
    const {
      amount,
      payment_method = 'cash',
      notes = ''
    } = req.body;

    console.log(`💳 دفع دين مورد: ${debtId} - مبلغ: ${amount}`);

    // التحقق من البيانات المطلوبة
    if (!amount || amount <= 0) {
      return res.status(400).json({ error: 'المبلغ مطلوب ويجب أن يكون أكبر من صفر' });
    }

    // جلب بيانات الدين والمورد
    const debtResult = await client.query(`
      SELECT
        sd.*,
        s.name as supplier_name,
        s.phone as supplier_phone
      FROM pos_system.supplier_debts sd
      LEFT JOIN pos_system.suppliers s ON sd.supplier_id = s.id
      WHERE sd.id = $1
    `, [debtId]);

    if (debtResult.rows.length === 0) {
      await client.query('ROLLBACK');
      return res.status(404).json({ error: 'الدين غير موجود' });
    }

    const debt = debtResult.rows[0];
    const cleanAmount = parseFloat(amount);
    const newPaidAmount = parseFloat(debt.paid_amount) + cleanAmount;
    const newStatus = newPaidAmount >= parseFloat(debt.amount) ? 'paid' : 'partial';

    // تحديث الدين
    await client.query(`
      UPDATE pos_system.supplier_debts
      SET paid_amount = $1, status = $2, updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
    `, [newPaidAmount, newStatus, debtId]);

    // تسجيل دفعة الدين
    await client.query(`
      INSERT INTO pos_system.supplier_debt_payments (
        debt_id, supplier_id, amount, payment_method, notes
      ) VALUES ($1, $2, $3, $4, $5)
    `, [debtId, debt.supplier_id, cleanAmount, payment_method, notes]);

    // إضافة المصروف تلقائياً
    const expenseDescription = `دفع دين للمورد ${debt.supplier_name}`;
    const expenseNotes = notes || `دفع دين بقيمة ${cleanAmount} دج للمورد ${debt.supplier_name}`;

    await client.query(`
      INSERT INTO pos_system.expenses (
        amount, description, category, payment_method, notes
      ) VALUES ($1, $2, $3, $4, $5)
    `, [
      cleanAmount,
      expenseDescription,
      'دفع ديون موردين',
      payment_method,
      expenseNotes
    ]);

    await client.query('COMMIT');

    console.log(`✅ تم دفع دين المورد بنجاح: ${debtId} - مبلغ: ${cleanAmount} - حالة جديدة: ${newStatus}`);

    res.json({
      message: 'تم دفع الدين بنجاح',
      debt_id: debtId,
      paid_amount: cleanAmount,
      new_paid_amount: newPaidAmount,
      new_status: newStatus,
      supplier_name: debt.supplier_name,
      expense_added: true
    });

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ خطأ في دفع دين المورد:', error);
    res.status(500).json({
      error: 'خطأ في دفع دين المورد',
      details: error.message
    });
  } finally {
    client.release();
  }
});

module.exports = router;
