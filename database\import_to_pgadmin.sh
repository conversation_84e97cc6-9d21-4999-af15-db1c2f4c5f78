#!/bin/bash

# سكريپت استيراد قاعدة البيانات إلى PostgreSQL
# Database Import Script for PostgreSQL

echo "========================================"
echo "       استيراد قاعدة بيانات نقطة البيع"
echo "       POS Database Import Script"
echo "========================================"

# إعداد متغيرات البيئة
export PGHOST=localhost
export PGPORT=5432
export PGUSER=postgres

# إعداد كلمة المرور
export PGPASSWORD=toossar

echo
echo "1. إنشاء قاعدة البيانات..."
echo "Creating database..."

# إنشاء قاعدة البيانات
psql -h $PGHOST -p $PGPORT -U $PGUSER -c "DROP DATABASE IF EXISTS pos_system_db;"
psql -h $PGHOST -p $PGPORT -U $PGUSER -c "CREATE DATABASE pos_system_db WITH OWNER = postgres ENCODING = 'UTF8';"

if [ $? -ne 0 ]; then
    echo "خطأ في إنشاء قاعدة البيانات!"
    echo "Error creating database!"
    exit 1
fi

echo "تم إنشاء قاعدة البيانات بنجاح!"
echo "Database created successfully!"

echo
echo "2. استيراد البيانات..."
echo "Importing data..."

# التحقق من وجود الملفات
files=("01_create_database.sql" "02_core_tables.sql" "03_sales_tables.sql" "04_triggers_functions.sql" "05_views_reports.sql" "06_security_permissions.sql")

for file in "${files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "خطأ: الملف $file غير موجود!"
        echo "Error: File $file not found!"
        exit 1
    fi
done

# تشغيل ملفات SQL بالترتيب
echo "- إنشاء الهيكل الأساسي..."
psql -h $PGHOST -p $PGPORT -U $PGUSER -d pos_system_db -f "01_create_database.sql"

echo "- إنشاء الجداول الأساسية..."
psql -h $PGHOST -p $PGPORT -U $PGUSER -d pos_system_db -f "02_core_tables.sql"

echo "- إنشاء جداول المبيعات..."
psql -h $PGHOST -p $PGPORT -U $PGUSER -d pos_system_db -f "03_sales_tables.sql"

echo "- إنشاء المشغلات والوظائف..."
psql -h $PGHOST -p $PGPORT -U $PGUSER -d pos_system_db -f "04_triggers_functions.sql"

echo "- إنشاء Views للتقارير..."
psql -h $PGHOST -p $PGPORT -U $PGUSER -d pos_system_db -f "05_views_reports.sql"

echo "- إعداد الأمان والصلاحيات..."
psql -h $PGHOST -p $PGPORT -U $PGUSER -d pos_system_db -f "06_security_permissions.sql"

if [ $? -ne 0 ]; then
    echo "خطأ في استيراد البيانات!"
    echo "Error importing data!"
    exit 1
fi

echo
echo "3. التحقق من الاستيراد..."
echo "Verifying import..."

# التحقق من الجداول
echo "عدد الجداول المنشأة:"
echo "Number of tables created:"
psql -h $PGHOST -p $PGPORT -U $PGUSER -d pos_system_db -c "SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = 'pos_system';"

# التحقق من البيانات التجريبية
echo
echo "البيانات التجريبية:"
echo "Sample data:"
psql -h $PGHOST -p $PGPORT -U $PGUSER -d pos_system_db -c "SELECT 'Products: ' || COUNT(*) FROM pos_system.products UNION ALL SELECT 'Categories: ' || COUNT(*) FROM pos_system.categories UNION ALL SELECT 'Customers: ' || COUNT(*) FROM pos_system.customers;"

echo
echo "========================================"
echo "تم استيراد قاعدة البيانات بنجاح!"
echo "Database imported successfully!"
echo "========================================"
echo
echo "يمكنك الآن فتح pgAdmin والاتصال بقاعدة البيانات:"
echo "You can now open pgAdmin and connect to the database:"
echo
echo "Host: localhost"
echo "Port: 5432"
echo "Database: pos_system_db"
echo "Username: postgres"
echo
echo "أو استخدام المستخدمين المخصصين:"
echo "Or use custom users:"
echo "- pos_admin_user (Admin)"
echo "- pos_manager_user (Manager)"
echo "- pos_cashier_user (Cashier)"
echo "- pos_readonly_user (ReadOnly)"
echo

# جعل الملف قابل للتنفيذ
chmod +x import_to_pgadmin.sh
