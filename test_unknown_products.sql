-- 🧪 اختبار نظام المنتجات غير المعروفة

-- 1. التحقق من تحديث جدول sale_items
SELECT 
    column_name,
    is_nullable,
    data_type,
    column_default
FROM information_schema.columns 
WHERE table_name = 'sale_items' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 2. اختبار إدراج منتج غير معروف
BEGIN;

-- إنشاء بيع تجريبي
INSERT INTO sales (
    sale_number,
    subtotal,
    total_amount,
    payment_method,
    amount_paid,
    cashier_name
) VALUES (
    'TEST-' || EXTRACT(EPOCH FROM NOW())::BIGINT,
    25.50,
    25.50,
    'cash',
    25.50,
    'اختبار النظام'
) RETURNING id as sale_id \gset

-- إدراج منتج غير معروف
INSERT INTO sale_items (
    sale_id,
    product_id,
    product_name,
    quantity,
    unit_price,
    total_price,
    is_unknown_product,
    unknown_product_code
) VALUES (
    :'sale_id',
    NULL, -- منتج غير معروف
    'منتج تجريبي غير معروف',
    2,
    12.75,
    25.50,
    TRUE,
    'TEST-UNK-' || EXTRACT(EPOCH FROM NOW())::BIGINT
);

-- التحقق من النتيجة
SELECT 
    si.product_name,
    si.is_unknown_product,
    si.unknown_product_code,
    si.quantity,
    si.unit_price,
    si.total_price,
    s.sale_number
FROM sale_items si
JOIN sales s ON si.sale_id = s.id
WHERE s.id = :'sale_id';

-- إلغاء البيع التجريبي
ROLLBACK;

-- 3. عرض إحصائيات المنتجات غير المعروفة
SELECT 
    'إجمالي المنتجات غير المعروفة' as النوع,
    COUNT(*) as العدد,
    COALESCE(SUM(total_price), 0) as إجمالي_المبلغ
FROM sale_items 
WHERE is_unknown_product = TRUE

UNION ALL

SELECT 
    'إجمالي المنتجات المعروفة' as النوع,
    COUNT(*) as العدد,
    COALESCE(SUM(total_price), 0) as إجمالي_المبلغ
FROM sale_items 
WHERE is_unknown_product = FALSE OR is_unknown_product IS NULL;

-- 4. عرض آخر 5 منتجات غير معروفة
SELECT 
    product_name,
    unknown_product_code,
    quantity,
    unit_price,
    total_price,
    created_at
FROM sale_items 
WHERE is_unknown_product = TRUE
ORDER BY created_at DESC
LIMIT 5;

-- 5. التحقق من وجود المنتج الافتراضي
SELECT 
    id,
    name,
    barcode,
    price,
    stock
FROM products 
WHERE id = '00000000-0000-0000-0000-000000000001'::UUID;

-- 6. التحقق من وجود الفئة الافتراضية
SELECT 
    id,
    name,
    description
FROM categories 
WHERE id = '00000000-0000-0000-0000-000000000001'::UUID;

-- رسالة النجاح
SELECT '🎉 اختبار نظام المنتجات غير المعروفة مكتمل!' as نتيجة_الاختبار;
