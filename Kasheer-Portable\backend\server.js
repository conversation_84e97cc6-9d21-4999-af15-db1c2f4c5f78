const express = require('express');
const cors = require('cors');
require('dotenv').config();

// اختبار الاتصال بقاعدة البيانات
const pool = require('./database');

const app = express();
const PORT = process.env.PORT || 5003;

// Middleware
app.use(cors());
app.use(express.json());

// Routes
app.use('/api/categories', require('./routes/categories'));
app.use('/api/products', require('./routes/products'));
app.use('/api/customers', require('./routes/customers'));
app.use('/api/suppliers', require('./routes/suppliers'));
app.use('/api/purchases', require('./routes/purchases'));
app.use('/api/sales', require('./routes/sales'));
app.use('/api/financial', require('./routes/financial'));
app.use('/api/debt', require('./routes/debt-management'));
app.use('/api/settings', require('./routes/settings'));
app.use('/api/supplier-debts', require('./routes/supplier-debts'));
app.use('/api/expenses', require('./routes/expenses'));

// اختبار الاتصال
app.get('/api/test', (req, res) => {
  res.json({
    message: '✅ Backend API يعمل بنجاح!',
    timestamp: new Date().toISOString()
  });
});

// تشغيل الخادم
app.listen(PORT, async () => {
  console.log(`🚀 Server يعمل على المنفذ ${PORT}`);
  console.log(`🔗 API متاح على: http://localhost:${PORT}/api`);

  // اختبار الاتصال بقاعدة البيانات
  try {
    const result = await pool.query('SELECT NOW()');
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
    
    // التحقق من المخطط الحالي
    const schemaResult = await pool.query('SELECT current_schema()');
    console.log('📊 المخطط الحالي:', schemaResult.rows[0].current_schema);
  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error.message);
  }
});
