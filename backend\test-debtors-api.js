const fetch = require('node-fetch');

async function testDebtorsAPI() {
  console.log('🧪 اختبار API المدينين...');
  
  try {
    const response = await fetch('http://localhost:5003/api/customers/debtors');
    const data = await response.json();
    
    console.log('📊 استجابة API:');
    console.log('Status:', response.status);
    console.log('Headers:', response.headers.get('content-type'));
    
    if (Array.isArray(data)) {
      console.log(`✅ تم العثور على ${data.length} مدين:`);
      
      data.forEach((debtor, index) => {
        console.log(`\n${index + 1}. مدين:`);
        console.log(`   - الاسم: ${debtor.name || 'غير محدد'}`);
        console.log(`   - الهاتف: ${debtor.phone || 'غير محدد'}`);
        console.log(`   - معرف العميل: ${debtor.id}`);
        console.log(`   - الرصيد: ${debtor.balance || 'غير محدد'} دج`);
        console.log(`   - إجمالي الدين: ${debtor.total_debt || 'غير محدد'} دج`);
        console.log(`   - المبلغ المدفوع: ${debtor.paid_amount || 'غير محدد'} دج`);
        console.log(`   - المبلغ المتبقي: ${debtor.remaining_debt || 'غير محدد'} دج`);
        console.log(`   - عدد الديون: ${debtor.debt_count || 'غير محدد'}`);
        
        // عرض جميع الخصائص
        console.log('   - جميع الخصائص:');
        Object.keys(debtor).forEach(key => {
          console.log(`     ${key}: ${JSON.stringify(debtor[key])}`);
        });
      });
      
      if (data.length === 0) {
        console.log('⚠️ لا توجد ديون معلقة حالياً');
      }
      
    } else {
      console.log('❌ الاستجابة ليست مصفوفة:', data);
    }
    
  } catch (error) {
    console.error('❌ خطأ في API:', error.message);
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testDebtorsAPI()
    .then(() => {
      console.log('\n🎉 انتهى اختبار API المدينين!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل اختبار API:', error);
      process.exit(1);
    });
}

module.exports = testDebtorsAPI;
