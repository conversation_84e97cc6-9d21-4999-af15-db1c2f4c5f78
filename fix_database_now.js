const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'pos_system',
  password: 'toossar',
  port: 5432,
});

async function fixDatabase() {
  const client = await pool.connect();
  try {
    console.log('🔧 بدء إصلاح قاعدة البيانات...');
    
    // 1. تعديل جدول sale_items
    await client.query('ALTER TABLE sale_items ALTER COLUMN product_id DROP NOT NULL');
    console.log('✅ تم تعديل product_id لقبول NULL');
    
    // 2. إضافة الأعمدة الجديدة
    await client.query('ALTER TABLE sale_items ADD COLUMN IF NOT EXISTS is_unknown_product BOOLEAN DEFAULT FALSE');
    await client.query('ALTER TABLE sale_items ADD COLUMN IF NOT EXISTS unknown_product_code VARCHAR(50)');
    console.log('✅ تم إضافة الأعمدة الجديدة');
    
    // 3. إنشاء فئة للمنتجات غير المعروفة
    await client.query(`
      INSERT INTO categories (id, name, description, is_active) 
      VALUES ('00000000-0000-0000-0000-000000000001'::UUID, 'منتجات غير معروفة', 'فئة خاصة للمنتجات غير المعروفة', true) 
      ON CONFLICT (id) DO NOTHING
    `);
    console.log('✅ تم إنشاء فئة المنتجات غير المعروفة');
    
    // 4. إنشاء منتج افتراضي
    await client.query(`
      INSERT INTO products (id, name, description, barcode, category_id, price, cost, stock, min_stock, unit, is_active) 
      VALUES ('00000000-0000-0000-0000-000000000001'::UUID, 'منتج غير معروف', 'منتج افتراضي للعناصر غير المعروفة', 'UNKNOWN_PRODUCT', '00000000-0000-0000-0000-000000000001'::UUID, 0.00, 0.00, 999999, 0, 'قطعة', true) 
      ON CONFLICT (id) DO NOTHING
    `);
    console.log('✅ تم إنشاء المنتج الافتراضي');
    
    // 5. إنشاء فهرس
    await client.query('CREATE INDEX IF NOT EXISTS idx_sale_items_unknown ON sale_items(is_unknown_product) WHERE is_unknown_product = TRUE');
    console.log('✅ تم إنشاء الفهرس');
    
    // 6. اختبار النتيجة
    const result = await client.query(`
      SELECT column_name, is_nullable, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'sale_items' 
      AND column_name IN ('product_id', 'is_unknown_product', 'unknown_product_code')
      ORDER BY column_name
    `);
    
    console.log('📊 نتيجة التحقق:');
    result.rows.forEach(row => {
      console.log(`   ${row.column_name}: ${row.data_type} (NULL: ${row.is_nullable})`);
    });
    
    console.log('\n🎉 تم إصلاح قاعدة البيانات بنجاح!');
    console.log('🔮 يمكنك الآن بيع المنتجات غير المعروفة!');
    console.log('💡 اكتب /25.50 في شاشة البيع لاختبار النظام');
    
  } catch (error) {
    console.error('❌ خطأ:', error.message);
    console.error('🔍 التفاصيل:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

fixDatabase();
