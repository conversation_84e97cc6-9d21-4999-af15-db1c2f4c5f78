const axios = require('axios');

async function testProfitsAPI() {
  try {
    console.log('🧪 اختبار API الأرباح...');
    
    // اختبار endpoint الأرباح
    const profitsResponse = await axios.get('http://localhost:5002/api/financial/profits');
    console.log('💰 نتائج API الأرباح:');
    console.log(JSON.stringify(profitsResponse.data, null, 2));
    
    console.log('\n📊 اختبار API إحصائيات لوحة التحكم...');
    
    // اختبار endpoint إحصائيات لوحة التحكم
    const dashboardResponse = await axios.get('http://localhost:5002/api/financial/dashboard-stats');
    console.log('📊 نتائج API إحصائيات لوحة التحكم:');
    console.log(JSON.stringify(dashboardResponse.data, null, 2));
    
    console.log('\n✅ تم اختبار جميع APIs بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في اختبار API:', error.message);
    if (error.response) {
      console.error('📋 تفاصيل الخطأ:', error.response.data);
    }
  }
}

testProfitsAPI();
