-- 🧠 الحل الذكي والبسيط - استخدام الجداول الموجودة فقط
-- انسخ والصق هذا الكود في pgAdmin Query Tool واضغط F5

-- تعيين المخطط
SET search_path TO pos_system, public;

-- 1. تعديل جدول sale_items لدعم المنتجات غير المعروفة
DO $$
BEGIN
    -- جعل product_id يقبل NULL (للمنتجات غير المعروفة)
    BEGIN
        ALTER TABLE sale_items ALTER COLUMN product_id DROP NOT NULL;
        RAISE NOTICE '✅ تم جعل product_id يقبل NULL';
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'ℹ️ product_id يقبل NULL مسبقاً';
    END;
    
    -- إضافة عمود is_unknown للتمييز
    BEGIN
        ALTER TABLE sale_items ADD COLUMN is_unknown BOOLEAN DEFAULT FALSE;
        RAISE NOTICE '✅ تم إضافة عمود is_unknown';
    EXCEPTION
        WHEN duplicate_column THEN
            RAISE NOTICE 'ℹ️ عمود is_unknown موجود مسبقاً';
    END;
    
    -- إضافة عمود unknown_product_code للتتبع
    BEGIN
        ALTER TABLE sale_items ADD COLUMN unknown_product_code VARCHAR(50);
        RAISE NOTICE '✅ تم إضافة عمود unknown_product_code';
    EXCEPTION
        WHEN duplicate_column THEN
            RAISE NOTICE 'ℹ️ عمود unknown_product_code موجود مسبقاً';
    END;
    
    -- إضافة عمود unknown_category للتصنيف
    BEGIN
        ALTER TABLE sale_items ADD COLUMN unknown_category VARCHAR(100) DEFAULT 'منتجات غير معروفة';
        RAISE NOTICE '✅ تم إضافة عمود unknown_category';
    EXCEPTION
        WHEN duplicate_column THEN
            RAISE NOTICE 'ℹ️ عمود unknown_category موجود مسبقاً';
    END;
END $$;

-- 2. إنشاء الفهارس للأداء الأمثل
CREATE INDEX IF NOT EXISTS idx_sale_items_unknown ON sale_items(is_unknown) WHERE is_unknown = TRUE;
CREATE INDEX IF NOT EXISTS idx_sale_items_unknown_code ON sale_items(unknown_product_code) WHERE unknown_product_code IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_sale_items_product_null ON sale_items(product_id) WHERE product_id IS NULL;

-- 3. إنشاء view ذكي للمنتجات غير المعروفة
CREATE OR REPLACE VIEW unknown_products_summary AS
SELECT 
    unknown_product_code,
    product_name,
    unknown_category,
    COUNT(*) as sales_count,
    SUM(quantity) as total_quantity,
    AVG(unit_price) as avg_price,
    SUM(total_price) as total_revenue,
    MIN(created_at) as first_sale,
    MAX(created_at) as last_sale
FROM sale_items 
WHERE is_unknown = TRUE 
GROUP BY unknown_product_code, product_name, unknown_category
ORDER BY total_revenue DESC;

-- 4. إنشاء view للمبيعات المختلطة
CREATE OR REPLACE VIEW mixed_sales_summary AS
SELECT 
    s.id as sale_id,
    s.sale_number,
    s.created_at,
    s.total_amount,
    s.payment_method,
    COUNT(si.id) as total_items,
    COUNT(CASE WHEN si.is_unknown = FALSE THEN 1 END) as regular_items,
    COUNT(CASE WHEN si.is_unknown = TRUE THEN 1 END) as unknown_items,
    SUM(CASE WHEN si.is_unknown = FALSE THEN si.total_price ELSE 0 END) as regular_revenue,
    SUM(CASE WHEN si.is_unknown = TRUE THEN si.total_price ELSE 0 END) as unknown_revenue,
    CASE 
        WHEN COUNT(CASE WHEN si.is_unknown = TRUE THEN 1 END) > 0 
        AND COUNT(CASE WHEN si.is_unknown = FALSE THEN 1 END) > 0 
        THEN 'مختلط'
        WHEN COUNT(CASE WHEN si.is_unknown = TRUE THEN 1 END) > 0 
        THEN 'غير معروف فقط'
        ELSE 'عادي فقط'
    END as sale_type
FROM sales s
LEFT JOIN sale_items si ON s.id = si.sale_id
GROUP BY s.id, s.sale_number, s.created_at, s.total_amount, s.payment_method
ORDER BY s.created_at DESC;

-- 5. إدراج بيانات تجريبية للاختبار
DO $$
DECLARE
    test_sale_id UUID;
BEGIN
    -- إنشاء بيع تجريبي
    INSERT INTO sales (
        subtotal, tax_amount, discount_amount, total_amount, 
        payment_method, amount_paid, change_amount, notes, cashier_name
    ) VALUES (
        350.00, 0, 0, 350.00, 'cash', 350.00, 0, 
        'بيع تجريبي مختلط (منتجات عادية + غير معروفة)', 'النظام'
    ) RETURNING id INTO test_sale_id;
    
    -- إضافة منتج غير معروف 1
    INSERT INTO sale_items (
        sale_id, product_id, product_name, quantity, unit_price, total_price,
        is_unknown, unknown_product_code, unknown_category
    ) VALUES (
        test_sale_id, NULL, '🔮 منتج غير معروف - قهوة خاصة', 2, 75.00, 150.00,
        TRUE, 'UNK-COFFEE-001', 'مشروبات'
    );
    
    -- إضافة منتج غير معروف 2
    INSERT INTO sale_items (
        sale_id, product_id, product_name, quantity, unit_price, total_price,
        is_unknown, unknown_product_code, unknown_category
    ) VALUES (
        test_sale_id, NULL, '🔮 منتج غير معروف - حلوى محلية', 1, 200.00, 200.00,
        TRUE, 'UNK-SWEET-001', 'حلويات'
    );
    
    RAISE NOTICE '✅ تم إنشاء بيع تجريبي مختلط برقم: %', test_sale_id;
END $$;

-- 6. إحصائيات سريعة
SELECT 
    'إجمالي المبيعات' as النوع,
    COUNT(*) as العدد,
    SUM(total_amount) as الإيرادات
FROM sales

UNION ALL

SELECT 
    'عناصر المبيعات العادية' as النوع,
    COUNT(*) as العدد,
    SUM(total_price) as الإيرادات
FROM sale_items 
WHERE is_unknown = FALSE

UNION ALL

SELECT 
    'عناصر المبيعات غير المعروفة' as النوع,
    COUNT(*) as العدد,
    SUM(total_price) as الإيرادات
FROM sale_items 
WHERE is_unknown = TRUE;

-- 7. عرض المبيعات المختلطة
SELECT * FROM mixed_sales_summary LIMIT 5;

-- 8. عرض ملخص المنتجات غير المعروفة
SELECT * FROM unknown_products_summary LIMIT 5;

-- رسالة نجاح
DO $$
BEGIN
    RAISE NOTICE '🎉 تم تطبيق الحل الذكي بنجاح!';
    RAISE NOTICE '✅ الآن يمكن بيع المنتجات العادية وغير المعروفة في فاتورة واحدة';
    RAISE NOTICE '📊 استخدم الـ Views الجديدة للإحصائيات المتقدمة';
    RAISE NOTICE '🔄 أعد تشغيل الخادم: npm run dev';
END $$;
