const pool = require('./database');

async function checkPurchasesTable() {
  try {
    console.log('🔍 فحص بنية جدول purchases...');
    
    const result = await pool.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_schema = 'pos_system' AND table_name = 'purchases' 
      ORDER BY ordinal_position
    `);
    
    console.log('📋 أعمدة جدول purchases:');
    result.rows.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });
    
    // فحص البيانات الموجودة
    const dataResult = await pool.query('SELECT * FROM pos_system.purchases LIMIT 3');
    console.log('\n📊 عينة من البيانات:');
    dataResult.rows.forEach((row, index) => {
      console.log(`  ${index + 1}. ID: ${row.id}, Total: ${row.total_amount}`);
    });
    
  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    process.exit(0);
  }
}

checkPurchasesTable();
