@echo off
chcp 65001 > nul
title 🚀 كشير توسار - تشغيل سريع
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🚀 كشير توسار                            ║
echo ║                  تشغيل سريع للنظام                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM التحقق من المتطلبات الأساسية
echo 🔍 التحقق من المتطلبات...

REM التحقق من Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo 📥 يرجى تحميل وتثبيت Node.js من: https://nodejs.org/
    echo.
    echo 🔧 بعد التثبيت، أعد تشغيل هذا الملف
    pause
    exit /b 1
)

REM التحقق من npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متوفر
    pause
    exit /b 1
)

echo ✅ Node.js و npm متوفران
echo.

REM التحقق من التبعيات
if not exist "node_modules" (
    echo 📦 تثبيت التبعيات لأول مرة...
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات
        goto error_exit
    )
    echo ✅ تم تثبيت التبعيات بنجاح
    echo.
)

REM التحقق من تبعيات Backend
if not exist "backend\node_modules" (
    echo 📦 تثبيت تبعيات Backend...
    cd backend
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت تبعيات Backend
        cd ..
        goto error_exit
    )
    cd ..
    echo ✅ تم تثبيت تبعيات Backend بنجاح
    echo.
)

echo 🎯 خيارات التشغيل:
echo.
echo 1. 🖥️  تشغيل تطبيق سطح المكتب (Electron)
echo 2. 🌐 تشغيل في المتصفح (Frontend + Backend)
echo 3. 🔧 تشغيل للتطوير (منفصل)
echo 4. 🧪 اختبار شامل
echo.
set /p mode="اختر وضع التشغيل (1-4): "

if "%mode%"=="1" goto electron_mode
if "%mode%"=="2" goto browser_mode
if "%mode%"=="3" goto dev_mode
if "%mode%"=="4" goto test_mode
goto invalid_choice

:electron_mode
echo.
echo 🖥️ تشغيل تطبيق سطح المكتب...
echo.

REM تثبيت Electron إذا لم يكن موجوداً
if not exist "node_modules\electron" (
    echo 📦 تثبيت Electron...
    call npm install electron --save-dev
    if %errorlevel% neq 0 goto error_exit
)

REM تحضير ملفات Electron
echo 🔧 تحضير التطبيق...
if exist "package-electron-fixed.json" (
    copy package-electron-fixed.json package.json > nul
) else (
    echo ⚠️  ملف package-electron-fixed.json غير موجود
    echo 🔧 استخدام test-electron.bat بدلاً من ذلك
    call test-electron.bat
    goto end
)

REM بدء Backend
echo 🚀 بدء الخادم الخلفي...
start /B cmd /c "cd backend && npm start"

REM انتظار قليل لبدء Backend
timeout /t 3 /nobreak > nul

REM بدء Frontend للتطوير
echo 🌐 بدء الواجهة الأمامية...
start /B cmd /c "npm run dev"

REM انتظار قليل لبدء Frontend
timeout /t 5 /nobreak > nul

REM تشغيل Electron
echo 🖥️ تشغيل التطبيق...
set NODE_ENV=development
call npx electron .

goto success

:browser_mode
echo.
echo 🌐 تشغيل في المتصفح...
echo.

REM بناء Frontend
echo 🏗️ بناء الواجهة الأمامية...
call npm run build
if %errorlevel% neq 0 goto error_exit

REM بدء Backend
echo 🚀 بدء الخادم...
start /B cmd /c "cd backend && npm start"

REM انتظار قليل
timeout /t 3 /nobreak > nul

REM فتح المتصفح
echo 🌐 فتح المتصفح...
start http://localhost:5003

echo.
echo ✅ النظام يعمل الآن!
echo 📋 الروابط:
echo - النظام: http://localhost:5003
echo - API: http://localhost:5003/api
echo.
echo 🛑 لإيقاف النظام: اضغط Ctrl+C أو أغلق هذه النافذة
echo.

REM انتظار إدخال المستخدم لإيقاف النظام
pause

goto success

:dev_mode
echo.
echo 🔧 تشغيل وضع التطوير...
echo.

echo 🚀 بدء الخادم الخلفي...
start cmd /k "title Backend Server && cd backend && npm start"

echo 🌐 بدء الواجهة الأمامية...
start cmd /k "title Frontend Dev && npm run dev"

echo.
echo ✅ تم تشغيل الخدمات في نوافذ منفصلة
echo 📋 الروابط:
echo - Frontend: http://localhost:3000
echo - Backend: http://localhost:5003
echo.
goto success

:test_mode
echo.
echo 🧪 تشغيل الاختبار الشامل...
call test-electron.bat
goto end

:invalid_choice
echo ❌ اختيار غير صحيح
pause
exit /b 1

:error_exit
echo.
echo ❌ حدث خطأ أثناء التشغيل!
echo.
echo 🔧 حلول مقترحة:
echo 1. تأكد من اتصال الإنترنت
echo 2. أغلق برامج مكافحة الفيروسات مؤقتاً
echo 3. شغل Command Prompt كمدير
echo 4. استخدم test-electron.bat للتشخيص
echo.
pause
exit /b 1

:success
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎉 تم التشغيل بنجاح!                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:end
echo 📋 ملفات مفيدة أخرى:
echo - test-electron.bat  (اختبار شامل)
echo - quick-build.bat    (بناء ملف التثبيت)
echo - دليل-الاستخدام-السريع.md (دليل مفصل)
echo.
pause
