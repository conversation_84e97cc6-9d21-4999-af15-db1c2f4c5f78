@echo off
echo 🚀 بدء تشغيل نظام نقطة البيع الكامل...
echo.

echo 📡 تشغيل الخادم الخلفي...
start "Backend Server" cmd /k "cd backend && npm start"

echo ⏳ انتظار تشغيل الخادم...
timeout /t 5 /nobreak > nul

echo 🌐 تشغيل الواجهة الأمامية...
start "Frontend Vite" cmd /k "npm run dev"

echo.
echo ✅ تم تشغيل النظام بنجاح!
echo 📡 الخادم الخلفي: http://localhost:5002
echo 🌐 الواجهة الأمامية: http://localhost:5173 (Vite)
echo.
echo 💡 نصائح:
echo - إذا لم تفتح الواجهة تلقائياً، اذهب إلى http://localhost:5173
echo - تأكد من تشغيل PostgreSQL قبل استخدام النظام
echo - استخدم Ctrl+C لإيقاف الخوادم
echo.
pause
