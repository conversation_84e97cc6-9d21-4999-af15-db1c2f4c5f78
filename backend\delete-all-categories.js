const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'pos_system_db',
  user: 'postgres',
  password: 'toossar',
});

async function deleteAllCategories() {
  try {
    console.log('🗑️ بدء حذف جميع الأقسام من قاعدة البيانات...');
    console.log('🔗 محاولة الاتصال بقاعدة البيانات...');

    // أولاً: عرض عدد الأقسام الحالية
    const countResult = await pool.query(
      'SELECT COUNT(*) as count FROM pos_system.categories WHERE is_active = true'
    );

    console.log('📊 عدد الأقسام النشطة حالياً:', countResult.rows[0].count);

    if (countResult.rows[0].count === '0') {
      console.log('✅ لا توجد أقسام نشطة للحذف');
      return;
    }

    // حذف جميع الأقسام (حذف منطقي)
    const result = await pool.query(
      'UPDATE pos_system.categories SET is_active = false WHERE is_active = true RETURNING name'
    );

    console.log('✅ تم حذف', result.rows.length, 'قسم بنجاح');

    // عرض الأقسام المحذوفة
    if (result.rows.length > 0) {
      console.log('📋 الأقسام المحذوفة:');
      result.rows.forEach((category, index) => {
        console.log(`${index + 1}. ${category.name}`);
      });
    }

    // التحقق من العدد المتبقي
    const finalCountResult = await pool.query(
      'SELECT COUNT(*) as count FROM pos_system.categories WHERE is_active = true'
    );

    console.log('📊 عدد الأقسام المتبقية النشطة:', finalCountResult.rows[0].count);

    console.log('🎉 تم حذف جميع الأقسام بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في حذف الأقسام:', error.message);
    console.error('تفاصيل الخطأ:', error);
  } finally {
    await pool.end();
    process.exit(0);
  }
}

deleteAllCategories();
