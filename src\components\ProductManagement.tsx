import React, { useState, useMemo, useCallback } from 'react';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Package,
  Barcode,
  DollarSign,
  Tag,
  Upload,
  Download,
  Camera,
  Save,
  X,
  AlertCircle,
  TrendingUp,
  TrendingDown,
  Eye,
  Filter,
  Grid3X3,
  List,
  BarChart3,
  PieChart,
  Target,
  Zap,
  Star,
  Award,
  ShoppingCart,
  Wallet,
  Calculator,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  Layers,
  Box,
  Sparkles,
  Crown,
  Gem,
  FileSpreadsheet,
  FileText
} from 'lucide-react';
import { useApp } from '../context/AppContext';

// تم نقل واجهة Product إلى AppContext

const ProductManagement: React.FC = () => {
  const { products, categories, addProduct, updateProduct, deleteProduct } = useApp();
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'stock' | 'profit' | 'value'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // إعادة تعيين الصفحة عند تغيير البحث أو الفلتر
  const handleSearchChange = useCallback((value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  }, []);

  const handleFilterChange = useCallback((value: string) => {
    setFilterCategory(value);
    setCurrentPage(1);
  }, []);
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [showStats, setShowStats] = useState(true);

  // 📄 إعدادات التحميل التدريجي
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(50);
  const [isLoading, setIsLoading] = useState(false);

  const [formData, setFormData] = useState({
    name: '',
    barcode: '',
    category: '',
    price: '',
    cost: '',
    stock: '',
    minStock: '',
    description: '',
    unit: 'قطعة',
    supplier: ''
  });

  const suppliers = ['مورد التقنية', 'مورد الملابس', 'دار النشر التقني', 'مورد الهواتف', 'مورد الإكسسوارات'];

  // 🧮 حسابات الإحصائيات المتقدمة
  const stats = useMemo(() => {
    const totalProducts = products.length;
    const totalValue = products.reduce((sum, p) => sum + (p.cost * p.stock), 0);
    const totalSellValue = products.reduce((sum, p) => sum + (p.price * p.stock), 0);
    const totalProfit = totalSellValue - totalValue;
    const avgProfit = totalProducts > 0 ? totalProfit / totalProducts : 0;
    const lowStockProducts = products.filter(p => p.stock <= p.minStock).length;
    const outOfStockProducts = products.filter(p => p.stock === 0).length;
    const highValueProducts = products.filter(p => (p.price * p.stock) > 10000).length;
    const topProfitProduct = products.reduce((max, p) =>
      ((p.price - p.cost) * p.stock) > ((max.price - max.cost) * max.stock) ? p : max,
      products[0] || { name: '', price: 0, cost: 0, stock: 0 }
    );

    return {
      totalProducts,
      totalValue,
      totalSellValue,
      totalProfit,
      avgProfit,
      lowStockProducts,
      outOfStockProducts,
      highValueProducts,
      topProfitProduct,
      profitMargin: totalValue > 0 ? (totalProfit / totalValue) * 100 : 0
    };
  }, [products]);

  // 🔍 تصفية وترتيب المنتجات مع التحميل التدريجي
  const { filteredProducts, paginatedProducts, totalPages, totalItems } = useMemo(() => {
    let filtered = products.filter(product => {
      const matchesSearch =
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.barcode.includes(searchTerm) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesCategory = filterCategory === 'all' || product.category === filterCategory;

      return product.isActive && matchesSearch && matchesCategory;
    });

    // ترتيب المنتجات
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'price':
          aValue = a.price;
          bValue = b.price;
          break;
        case 'stock':
          aValue = a.stock;
          bValue = b.stock;
          break;
        case 'profit':
          aValue = (a.price - a.cost) * a.stock;
          bValue = (b.price - b.cost) * b.stock;
          break;
        case 'value':
          aValue = a.price * a.stock;
          bValue = b.price * b.stock;
          break;
        default:
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    // التحميل التدريجي
    const totalItems = filtered.length;
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedProducts = filtered.slice(startIndex, endIndex);

    return {
      filteredProducts: filtered,
      paginatedProducts,
      totalPages,
      totalItems
    };
  }, [products, searchTerm, filterCategory, sortBy, sortOrder, currentPage, itemsPerPage]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const productData = {
      name: formData.name,
      barcode: formData.barcode,
      category: formData.category,
      price: parseFloat(formData.price),
      cost: parseFloat(formData.cost),
      stock: parseInt(formData.stock),
      minStock: parseInt(formData.minStock) || 0,
      description: formData.description,
      unit: formData.unit,
      supplier: formData.supplier,
      createdDate: new Date(),
      updatedDate: new Date(),
      isActive: true
    };

    try {
      if (editingProduct) {
        await updateProduct(editingProduct.id, productData);
      } else {
        await addProduct(productData);
      }

      setShowAddForm(false);
      setEditingProduct(null);
      setFormData({
        name: '',
        barcode: '',
        category: '',
        price: '',
        cost: '',
        stock: '',
        minStock: '',
        description: '',
        unit: 'قطعة',
        supplier: ''
      });
    } catch (error) {
      console.error('خطأ في حفظ المنتج:', error);
      alert('حدث خطأ في حفظ المنتج. يرجى المحاولة مرة أخرى.');
    }
  };

  const handleEdit = useCallback((product: any) => {
    setEditingProduct(product);
    const categoryId = categories.find(cat => cat.id === product.category)?.id || '';
    setFormData({
      name: product.name,
      barcode: product.barcode,
      category: categoryId,
      price: product.price.toString(),
      cost: product.cost.toString(),
      stock: product.stock.toString(),
      minStock: product.minStock.toString(),
      description: product.description,
      unit: product.unit,
      supplier: product.supplier
    });
    setShowAddForm(true);
  }, [categories]);

  const handleDelete = useCallback(async (productId: string) => {
    if (window.confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
      try {
        await deleteProduct(productId);
      } catch (error) {
        console.error('خطأ في حذف المنتج:', error);
        alert('حدث خطأ في حذف المنتج. يرجى المحاولة مرة أخرى.');
      }
    }
  }, [deleteProduct]);

  const generateBarcode = useCallback(() => {
    const barcode = Math.random().toString().slice(2, 14);
    setFormData(prev => ({ ...prev, barcode }));
  }, []);

  // 📤 تصدير المنتجات إلى CSV
  const handleExportCSV = () => {
    try {
      // إنشاء البيانات للتصدير (تصدير جميع المنتجات المفلترة، ليس فقط الصفحة الحالية)
      const csvData = filteredProducts.map(product => {
        const category = categories.find(cat => cat.id === product.category);
        return {
          'الباركود': product.barcode,
          'الاسم': product.name,
          'القسم': category?.name || '',
          'سعر الشراء': `${product.cost.toFixed(2)} دج`,
          'سعر البيع': `${product.price.toFixed(2)} دج`,
          'الكمية': product.stock,
          'حد التنبيه': product.minStock,
          'الوصف': product.description || ''
        };
      });

      // تحويل إلى CSV
      const headers = Object.keys(csvData[0] || {});
      const csvContent = [
        headers.map(header => `"${header}"`).join(','),
        ...csvData.map(row =>
          headers.map(header => `"${row[header] || ''}"`).join(',')
        )
      ].join('\n');

      // تحميل الملف
      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `products_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      alert('تم تصدير المنتجات بنجاح!');
    } catch (error) {
      console.error('خطأ في التصدير:', error);
      alert('حدث خطأ في تصدير المنتجات');
    }
  };

  // 📥 استيراد المنتجات من CSV
  const handleImportCSV = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const text = e.target?.result as string;
        const lines = text.split('\n').filter(line => line.trim());

        if (lines.length < 2) {
          alert('الملف فارغ أو لا يحتوي على بيانات صالحة');
          return;
        }

        // قراءة الرؤوس
        const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());

        // التحقق من الرؤوس المطلوبة (يدعم كلا التنسيقين)
        const requiredHeaders = ['الباركود', 'الاسم', 'سعر الشراء', 'سعر البيع', 'الكمية', 'حد التنبيه'];
        const categoryHeader = headers.includes('القسم') ? 'القسم' : 'category_id';

        const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));
        if (!headers.includes(categoryHeader)) {
          missingHeaders.push('القسم أو category_id');
        }

        if (missingHeaders.length > 0) {
          alert(`الملف لا يحتوي على الأعمدة المطلوبة: ${missingHeaders.join(', ')}`);
          return;
        }

        setIsLoading(true);
        let importedCount = 0;
        let errorCount = 0;
        const batchSize = 10; // معالجة 10 منتجات في كل دفعة

        // تحضير البيانات أولاً
        const productsToImport = [];
        for (let i = 1; i < lines.length; i++) {
          try {
            const values = lines[i].split(',').map(v => v.replace(/"/g, '').trim());
            const rowData: any = {};

            headers.forEach((header, index) => {
              rowData[header] = values[index] || '';
            });

            // البحث عن القسم
            const categoryValue = rowData[categoryHeader];
            let category;

            if (categoryHeader === 'القسم') {
              category = categories.find(cat => cat.name === categoryValue);
            } else {
              category = categories.find(cat => cat.name === categoryValue);
            }

            if (!category) {
              console.warn(`القسم غير موجود: ${categoryValue}`);
              errorCount++;
              continue;
            }

            // تنظيف الأسعار
            const cleanPrice = (priceStr: string) => {
              return parseFloat(priceStr.replace(/[^\d.]/g, '')) || 0;
            };

            const productData = {
              name: rowData['الاسم'],
              barcode: rowData['الباركود'],
              category: category.id,
              price: cleanPrice(rowData['سعر البيع']),
              cost: cleanPrice(rowData['سعر الشراء']),
              stock: parseInt(rowData['الكمية']) || 0,
              minStock: parseInt(rowData['حد التنبيه']) || 0,
              description: rowData['الوصف'] || '',
              unit: 'قطعة',
              supplier: '',
              createdDate: new Date(),
              updatedDate: new Date(),
              isActive: true
            };

            productsToImport.push(productData);
          } catch (error) {
            console.error(`خطأ في السطر ${i + 1}:`, error);
            errorCount++;
          }
        }

        // معالجة متدرجة
        for (let i = 0; i < productsToImport.length; i += batchSize) {
          const batch = productsToImport.slice(i, i + batchSize);

          // معالجة الدفعة الحالية
          const batchPromises = batch.map(async (productData) => {
            try {
              const existingProduct = products.find(p => p.barcode === productData.barcode);

              if (existingProduct) {
                await updateProduct(existingProduct.id, productData);
              } else {
                await addProduct(productData);
              }
              return true;
            } catch (error) {
              console.error('خطأ في إضافة المنتج:', error);
              return false;
            }
          });

          const results = await Promise.all(batchPromises);
          importedCount += results.filter(Boolean).length;

          // تحديث شريط التقدم (اختياري)
          const progress = Math.round(((i + batchSize) / productsToImport.length) * 100);
          console.log(`تقدم الاستيراد: ${progress}%`);

          // توقف قصير لتجنب إرهاق المتصفح
          await new Promise(resolve => setTimeout(resolve, 100));
        }

        setIsLoading(false);
        alert(`تم استيراد ${importedCount} منتج بنجاح${errorCount > 0 ? ` مع ${errorCount} أخطاء` : ''}`);

      } catch (error) {
        console.error('خطأ في قراءة الملف:', error);
        alert('حدث خطأ في قراءة الملف. تأكد من أن الملف بصيغة CSV صحيحة');
      }
    };

    reader.readAsText(file, 'UTF-8');
    // إعادة تعيين قيمة input لتمكين اختيار نفس الملف مرة أخرى
    event.target.value = '';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* 🎨 Header المبهر */}
        <div className="relative overflow-hidden bg-white/10 backdrop-blur-lg rounded-3xl border border-white/20 p-8">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20"></div>
          <div className="relative flex items-center justify-between">
            <div className="flex items-center space-x-6 space-x-reverse">
              <div className="relative">
                <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4 rounded-2xl">
                  <Package className="w-10 h-10 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 bg-yellow-400 rounded-full p-1">
                  <Crown className="w-4 h-4 text-yellow-900" />
                </div>
              </div>
              <div>
                <h1 className="text-4xl font-bold text-white mb-2 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  إدارة الأصناف المتقدمة
                </h1>
                <p className="text-blue-200 text-lg">نظام ذكي لإدارة المنتجات مع تحليلات متقدمة</p>
              </div>
            </div>

            <div className="flex items-center space-x-4 space-x-reverse">
              {/* أزرار الاستيراد والتصدير */}
              <div className="flex items-center space-x-2 space-x-reverse">
                {/* زر التصدير */}
                <button
                  onClick={handleExportCSV}
                  className="bg-green-500/20 hover:bg-green-500/30 backdrop-blur-lg border border-green-300/30 p-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse"
                  title="تصدير المنتجات إلى CSV"
                >
                  <Download className="w-5 h-5 text-green-300" />
                  <span className="text-green-300 text-sm hidden md:block">تصدير</span>
                </button>

                {/* زر الاستيراد */}
                <label className={`bg-blue-500/20 hover:bg-blue-500/30 backdrop-blur-lg border border-blue-300/30 p-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse cursor-pointer ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                       title="استيراد المنتجات من CSV">
                  {isLoading ? (
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-300"></div>
                  ) : (
                    <Upload className="w-5 h-5 text-blue-300" />
                  )}
                  <span className="text-blue-300 text-sm hidden md:block">
                    {isLoading ? 'جاري الاستيراد...' : 'استيراد'}
                  </span>
                  <input
                    type="file"
                    accept=".csv"
                    onChange={handleImportCSV}
                    className="hidden"
                    disabled={isLoading}
                  />
                </label>
              </div>

              <div className="w-px h-8 bg-white/20"></div>

              <button
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                className="bg-white/10 hover:bg-white/20 backdrop-blur-lg border border-white/20 p-3 rounded-xl transition-all duration-300 hover:scale-105"
              >
                {viewMode === 'grid' ? <List className="w-5 h-5 text-white" /> : <Grid3X3 className="w-5 h-5 text-white" />}
              </button>

              <button
                onClick={() => setShowStats(!showStats)}
                className="bg-white/10 hover:bg-white/20 backdrop-blur-lg border border-white/20 p-3 rounded-xl transition-all duration-300 hover:scale-105"
              >
                <BarChart3 className="w-5 h-5 text-white" />
              </button>

              {/* عدد العناصر في الصفحة */}
              <select
                value={itemsPerPage}
                onChange={(e) => {
                  setItemsPerPage(Number(e.target.value));
                  setCurrentPage(1);
                }}
                className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl px-3 py-2 text-white text-sm focus:outline-none focus:border-blue-400"
              >
                <option value={25} className="bg-slate-800">25 عنصر</option>
                <option value={50} className="bg-slate-800">50 عنصر</option>
                <option value={100} className="bg-slate-800">100 عنصر</option>
                <option value={200} className="bg-slate-800">200 عنصر</option>
              </select>

              <button
                onClick={() => setShowAddForm(true)}
                className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse shadow-lg"
              >
                <Plus className="w-5 h-5" />
                <span>إضافة صنف جديد</span>
                <Sparkles className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* 📊 لوحة الإحصائيات المبهرة */}
        {showStats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* إجمالي المنتجات */}
            <div className="relative overflow-hidden bg-gradient-to-br from-blue-500/20 to-cyan-500/20 backdrop-blur-lg rounded-2xl border border-blue-300/30 p-6 hover:scale-105 transition-all duration-300">
              <div className="absolute top-0 right-0 w-20 h-20 bg-blue-400/20 rounded-full -translate-y-10 translate-x-10"></div>
              <div className="relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="bg-blue-500/30 p-3 rounded-xl">
                    <Package className="w-6 h-6 text-blue-300" />
                  </div>
                  <div className="text-right">
                    <p className="text-blue-200 text-sm">إجمالي المنتجات</p>
                    <p className="text-3xl font-bold text-white">{stats.totalProducts}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <TrendingUp className="w-4 h-4 text-green-400" />
                  <span className="text-green-400 text-sm">نشط</span>
                </div>
              </div>
            </div>

            {/* إجمالي قيمة المخزون */}
            <div className="relative overflow-hidden bg-gradient-to-br from-purple-500/20 to-pink-500/20 backdrop-blur-lg rounded-2xl border border-purple-300/30 p-6 hover:scale-105 transition-all duration-300">
              <div className="absolute top-0 right-0 w-20 h-20 bg-purple-400/20 rounded-full -translate-y-10 translate-x-10"></div>
              <div className="relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="bg-purple-500/30 p-3 rounded-xl">
                    <Wallet className="w-6 h-6 text-purple-300" />
                  </div>
                  <div className="text-right">
                    <p className="text-purple-200 text-sm">قيمة المخزون</p>
                    <p className="text-2xl font-bold text-white">{stats.totalValue.toLocaleString()} دج</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <Calculator className="w-4 h-4 text-purple-400" />
                  <span className="text-purple-400 text-sm">التكلفة</span>
                </div>
              </div>
            </div>

            {/* إجمالي الربح المتوقع */}
            <div className="relative overflow-hidden bg-gradient-to-br from-green-500/20 to-emerald-500/20 backdrop-blur-lg rounded-2xl border border-green-300/30 p-6 hover:scale-105 transition-all duration-300">
              <div className="absolute top-0 right-0 w-20 h-20 bg-green-400/20 rounded-full -translate-y-10 translate-x-10"></div>
              <div className="relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="bg-green-500/30 p-3 rounded-xl">
                    <TrendingUp className="w-6 h-6 text-green-300" />
                  </div>
                  <div className="text-right">
                    <p className="text-green-200 text-sm">الربح المتوقع</p>
                    <p className="text-2xl font-bold text-white">{stats.totalProfit.toLocaleString()} دج</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <ArrowUpRight className="w-4 h-4 text-green-400" />
                  <span className="text-green-400 text-sm">{stats.profitMargin.toFixed(1)}% هامش</span>
                </div>
              </div>
            </div>

            {/* قيمة البيع المتوقعة */}
            <div className="relative overflow-hidden bg-gradient-to-br from-yellow-500/20 to-orange-500/20 backdrop-blur-lg rounded-2xl border border-yellow-300/30 p-6 hover:scale-105 transition-all duration-300">
              <div className="absolute top-0 right-0 w-20 h-20 bg-yellow-400/20 rounded-full -translate-y-10 translate-x-10"></div>
              <div className="relative">
                <div className="flex items-center justify-between mb-4">
                  <div className="bg-yellow-500/30 p-3 rounded-xl">
                    <DollarSign className="w-6 h-6 text-yellow-300" />
                  </div>
                  <div className="text-right">
                    <p className="text-yellow-200 text-sm">قيمة البيع المتوقعة</p>
                    <p className="text-2xl font-bold text-white">{stats.totalSellValue.toLocaleString()} دج</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <Target className="w-4 h-4 text-yellow-400" />
                  <span className="text-yellow-400 text-sm">إجمالي</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 🚨 تنبيهات المخزون */}
        {(stats.lowStockProducts > 0 || stats.outOfStockProducts > 0) && (
          <div className="bg-gradient-to-r from-red-500/20 to-orange-500/20 backdrop-blur-lg rounded-2xl border border-red-300/30 p-6">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="bg-red-500/30 p-3 rounded-xl">
                <AlertCircle className="w-6 h-6 text-red-300" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">تنبيهات المخزون</h3>
                <div className="flex items-center space-x-6 space-x-reverse">
                  {stats.outOfStockProducts > 0 && (
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <span className="text-red-300">{stats.outOfStockProducts} منتج نفد من المخزون</span>
                    </div>
                  )}
                  {stats.lowStockProducts > 0 && (
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <span className="text-yellow-300">{stats.lowStockProducts} منتج مخزون منخفض</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 📊 شريط المعلومات والتحكم */}
        <div className="bg-gradient-to-r from-slate-800/50 to-slate-700/50 backdrop-blur-lg rounded-2xl border border-slate-600/30 p-4">
          <div className="flex items-center justify-between flex-wrap gap-4">
            {/* معلومات العدد */}
            <div className="flex items-center space-x-6 space-x-reverse">
              <div className="text-white">
                <span className="text-sm text-slate-300">عرض </span>
                <span className="font-bold text-blue-400">{paginatedProducts.length}</span>
                <span className="text-sm text-slate-300"> من </span>
                <span className="font-bold text-purple-400">{totalItems}</span>
                <span className="text-sm text-slate-300"> منتج</span>
              </div>

              {isLoading && (
                <div className="flex items-center space-x-2 space-x-reverse">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-400"></div>
                  <span className="text-blue-400 text-sm">جاري التحميل...</span>
                </div>
              )}
            </div>

            {/* أزرار التنقل */}
            {totalPages > 1 && (
              <div className="flex items-center space-x-2 space-x-reverse">
                <button
                  onClick={() => setCurrentPage(1)}
                  disabled={currentPage === 1}
                  className="px-3 py-1 rounded-lg bg-slate-700 text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-slate-600 transition-colors"
                >
                  الأولى
                </button>

                <button
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 rounded-lg bg-slate-700 text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-slate-600 transition-colors"
                >
                  السابقة
                </button>

                <div className="flex items-center space-x-2 space-x-reverse">
                  <span className="text-slate-300 text-sm">صفحة</span>
                  <span className="font-bold text-white">{currentPage}</span>
                  <span className="text-slate-300 text-sm">من</span>
                  <span className="font-bold text-white">{totalPages}</span>
                </div>

                <button
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 rounded-lg bg-slate-700 text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-slate-600 transition-colors"
                >
                  التالية
                </button>

                <button
                  onClick={() => setCurrentPage(totalPages)}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 rounded-lg bg-slate-700 text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-slate-600 transition-colors"
                >
                  الأخيرة
                </button>
              </div>
            )}
          </div>
        </div>

        {/* 📋 معلومات تنسيق الاستيراد */}
        <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 backdrop-blur-lg rounded-2xl border border-blue-300/20 p-6">
          <div className="flex items-start space-x-4 space-x-reverse">
            <div className="bg-blue-500/20 p-3 rounded-xl">
              <FileSpreadsheet className="w-6 h-6 text-blue-300" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-white mb-3">تنسيق ملف الاستيراد (CSV)</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="text-blue-300 font-medium mb-2">الأعمدة المطلوبة:</h4>
                  <ul className="text-blue-200 text-sm space-y-1">
                    <li>• الباركود</li>
                    <li>• الاسم</li>
                    <li>• القسم (أو category_id)</li>
                    <li>• سعر الشراء (مثال: 100.00 دج)</li>
                    <li>• سعر البيع (مثال: 150.00 دج)</li>
                    <li>• الكمية</li>
                    <li>• حد التنبيه</li>
                  </ul>
                </div>
                <div>
                  <h4 className="text-purple-300 font-medium mb-2">ملاحظات مهمة:</h4>
                  <ul className="text-purple-200 text-sm space-y-1">
                    <li>• يجب أن تكون الأقسام موجودة مسبقاً</li>
                    <li>• الملف يجب أن يكون بترميز UTF-8</li>
                    <li>• سيتم تحديث المنتجات الموجودة بنفس الباركود</li>
                    <li>• يمكن تصدير المنتجات الحالية كمثال</li>
                    <li>• متوافق مع ملف products.csv الموجود</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

      {/* Add/Edit Form Modal */}
      {showAddForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-slate-800 rounded-xl border border-slate-700 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-slate-700 flex items-center justify-between">
              <h2 className="text-xl font-bold text-white">
                {editingProduct ? 'تعديل الصنف' : 'إضافة صنف جديد'}
              </h2>
              <button
                onClick={() => {
                  setShowAddForm(false);
                  setEditingProduct(null);
                }}
                className="text-slate-400 hover:text-white p-2 hover:bg-slate-700 rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* سيتم إضافة قسم الصورة لاحقاً */}

                {/* Product Name */}
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">اسم المنتج *</label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                    placeholder="أدخل اسم المنتج"
                  />
                </div>

                {/* Barcode */}
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">الباركود *</label>
                  <div className="flex space-x-reverse space-x-2">
                    <input
                      type="text"
                      name="barcode"
                      value={formData.barcode}
                      onChange={handleInputChange}
                      required
                      className="flex-1 bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                      placeholder="أدخل الباركود"
                    />
                    <button
                      type="button"
                      onClick={generateBarcode}
                      className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg flex items-center space-x-reverse space-x-2 transition-colors"
                    >
                      <Barcode className="w-4 h-4" />
                      <span>توليد</span>
                    </button>
                  </div>
                </div>

                {/* Category */}
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">القسم *</label>
                  <select
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    required
                    className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:border-blue-500 focus:outline-none"
                  >
                    <option value="">اختر القسم</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>{category.name}</option>
                    ))}
                  </select>
                </div>

                {/* Supplier */}
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">المورد</label>
                  <select
                    name="supplier"
                    value={formData.supplier}
                    onChange={handleInputChange}
                    className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:border-blue-500 focus:outline-none"
                  >
                    <option value="">اختر المورد</option>
                    {suppliers.map(supplier => (
                      <option key={supplier} value={supplier}>{supplier}</option>
                    ))}
                  </select>
                </div>

                {/* Price */}
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">سعر البيع *</label>
                  <div className="relative">
                    <input
                      type="number"
                      step="0.01"
                      name="price"
                      value={formData.price}
                      onChange={handleInputChange}
                      required
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 pr-12 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                      placeholder="0.00"
                    />
                    <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400">دج</span>
                  </div>
                </div>

                {/* Cost */}
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">سعر التكلفة *</label>
                  <div className="relative">
                    <input
                      type="number"
                      step="0.01"
                      name="cost"
                      value={formData.cost}
                      onChange={handleInputChange}
                      required
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 pr-12 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                      placeholder="0.00"
                    />
                    <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400">دج</span>
                  </div>
                </div>

                {/* Stock */}
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">الكمية الحالية *</label>
                  <input
                    type="number"
                    name="stock"
                    value={formData.stock}
                    onChange={handleInputChange}
                    required
                    className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                    placeholder="0"
                  />
                </div>

                {/* Min Stock */}
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">الحد الأدنى للمخزون</label>
                  <input
                    type="number"
                    name="minStock"
                    value={formData.minStock}
                    onChange={handleInputChange}
                    className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                    placeholder="0"
                  />
                </div>

                {/* Unit */}
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">وحدة القياس</label>
                  <select
                    name="unit"
                    value={formData.unit}
                    onChange={handleInputChange}
                    className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:border-blue-500 focus:outline-none"
                  >
                    <option value="قطعة">قطعة</option>
                    <option value="كيلو">كيلو</option>
                    <option value="لتر">لتر</option>
                    <option value="متر">متر</option>
                    <option value="علبة">علبة</option>
                    <option value="حبة">حبة</option>
                  </select>
                </div>

                {/* Description */}
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-slate-300 mb-2">الوصف</label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={3}
                    className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none resize-none"
                    placeholder="وصف المنتج..."
                  />
                </div>
              </div>

              {/* Profit Calculation */}
              {formData.price && formData.cost && (
                <div className="bg-slate-700 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-white mb-2">حساب الربح</h3>
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <p className="text-slate-400 text-sm">الربح لكل وحدة</p>
                      <p className="text-green-400 font-bold">
                        {(parseFloat(formData.price) - parseFloat(formData.cost)).toFixed(2)} دج
                      </p>
                    </div>
                    <div>
                      <p className="text-slate-400 text-sm">نسبة الربح</p>
                      <p className="text-blue-400 font-bold">
                        {(((parseFloat(formData.price) - parseFloat(formData.cost)) / parseFloat(formData.cost)) * 100).toFixed(1)}%
                      </p>
                    </div>
                    <div>
                      <p className="text-slate-400 text-sm">إجمالي قيمة المخزون</p>
                      <p className="text-purple-400 font-bold">
                        {formData.stock ? (parseFloat(formData.cost) * parseInt(formData.stock)).toFixed(2) : '0.00'} دج
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Form Actions */}
              <div className="flex items-center justify-end space-x-reverse space-x-4 pt-6 border-t border-slate-700">
                <button
                  type="button"
                  onClick={() => {
                    setShowAddForm(false);
                    setEditingProduct(null);
                  }}
                  className="bg-slate-600 hover:bg-slate-500 px-6 py-2 rounded-lg transition-colors"
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  className="bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded-lg flex items-center space-x-reverse space-x-2 transition-colors"
                >
                  <Save className="w-4 h-4" />
                  <span>{editingProduct ? 'حفظ التعديلات' : 'إضافة المنتج'}</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

        {/* 🔍 شريط البحث والتصفية المتقدم */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 p-6">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
            {/* البحث */}
            <div className="lg:col-span-2 relative">
              <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-blue-300" />
              <input
                type="text"
                placeholder="البحث عن المنتجات بالاسم أو الباركود..."
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="w-full bg-white/10 border border-white/20 rounded-xl px-12 py-3 text-white placeholder-blue-200 focus:border-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-400/50 transition-all"
              />
            </div>

            {/* تصفية القسم */}
            <div className="relative">
              <Filter className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-purple-300" />
              <select
                value={filterCategory}
                onChange={(e) => handleFilterChange(e.target.value)}
                className="w-full bg-white/10 border border-white/20 rounded-xl px-10 py-3 text-white focus:border-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400/50 transition-all appearance-none cursor-pointer"
              >
                <option value="all" className="bg-gray-800">جميع الأقسام</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id} className="bg-gray-800">
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* ترتيب */}
            <div className="relative">
              <BarChart3 className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-300" />
              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [newSortBy, newSortOrder] = e.target.value.split('-');
                  setSortBy(newSortBy as any);
                  setSortOrder(newSortOrder as any);
                }}
                className="w-full bg-white/10 border border-white/20 rounded-xl px-10 py-3 text-white focus:border-green-400 focus:outline-none focus:ring-2 focus:ring-green-400/50 transition-all appearance-none cursor-pointer"
              >
                <option value="name-asc" className="bg-gray-800">الاسم (أ-ي)</option>
                <option value="name-desc" className="bg-gray-800">الاسم (ي-أ)</option>
                <option value="price-desc" className="bg-gray-800">السعر (الأعلى)</option>
                <option value="price-asc" className="bg-gray-800">السعر (الأقل)</option>
                <option value="stock-desc" className="bg-gray-800">المخزون (الأكثر)</option>
                <option value="stock-asc" className="bg-gray-800">المخزون (الأقل)</option>
                <option value="profit-desc" className="bg-gray-800">الربح (الأعلى)</option>
                <option value="value-desc" className="bg-gray-800">القيمة (الأعلى)</option>
              </select>
            </div>
          </div>

          {/* إحصائيات البحث */}
          <div className="mt-4 flex items-center justify-between">
            <div className="flex items-center space-x-4 space-x-reverse">
              <span className="text-blue-200">
                عرض {paginatedProducts.length} من {totalItems} منتج
              </span>
              {searchTerm && (
                <span className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-lg text-sm">
                  البحث: "{searchTerm}"
                </span>
              )}
              {filterCategory !== 'all' && (
                <span className="bg-purple-500/20 text-purple-300 px-3 py-1 rounded-lg text-sm">
                  القسم: {categories.find(c => c.id === filterCategory)?.name}
                </span>
              )}
            </div>

            {(searchTerm || filterCategory !== 'all') && (
              <button
                onClick={() => {
                  handleSearchChange('');
                  handleFilterChange('all');
                }}
                className="text-red-300 hover:text-red-200 text-sm flex items-center space-x-1 space-x-reverse"
              >
                <X className="w-4 h-4" />
                <span>مسح الفلاتر</span>
              </button>
            )}
          </div>
        </div>

        {/* 📦 عرض المنتجات المبهر */}
        {viewMode === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {paginatedProducts.map((product) => {
              const categoryName = categories.find(cat => cat.id === product.category)?.name || 'غير محدد';
              const profit = product.price - product.cost;
              const profitMargin = product.cost > 0 ? (profit / product.cost) * 100 : 0;
              const totalValue = product.price * product.stock;
              const totalProfit = profit * product.stock;
              const isLowStock = product.stock <= product.minStock;
              const isOutOfStock = product.stock === 0;

              return (
                <div key={product.id} className="group relative overflow-hidden bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105 hover:shadow-2xl">
                  {/* شارة الحالة */}
                  <div className="absolute top-4 left-4 z-10">
                    {isOutOfStock ? (
                      <span className="bg-red-500 text-white px-2 py-1 rounded-lg text-xs font-bold flex items-center space-x-1 space-x-reverse">
                        <AlertCircle className="w-3 h-3" />
                        <span>نفد</span>
                      </span>
                    ) : isLowStock ? (
                      <span className="bg-yellow-500 text-white px-2 py-1 rounded-lg text-xs font-bold flex items-center space-x-1 space-x-reverse">
                        <AlertCircle className="w-3 h-3" />
                        <span>منخفض</span>
                      </span>
                    ) : (
                      <span className="bg-green-500 text-white px-2 py-1 rounded-lg text-xs font-bold flex items-center space-x-1 space-x-reverse">
                        <Activity className="w-3 h-3" />
                        <span>متوفر</span>
                      </span>
                    )}
                  </div>

                  {/* شارة القسم */}
                  <div className="absolute top-4 right-4 z-10">
                    <span className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-3 py-1 rounded-lg text-xs font-medium">
                      {categoryName}
                    </span>
                  </div>

                  {/* صورة المنتج */}
                  <div className="aspect-video bg-gradient-to-br from-blue-500/20 to-purple-500/20 relative flex items-center justify-center overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 to-purple-600/10"></div>
                    <Package className="w-20 h-20 text-white/60 relative z-10" />

                    {/* تأثير الهولوجرام */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -skew-x-12 translate-x-full group-hover:translate-x-[-200%] transition-transform duration-1000"></div>
                  </div>

                  {/* محتوى البطاقة */}
                  <div className="p-6">
                    {/* اسم المنتج */}
                    <div className="mb-4">
                      <h3 className="text-xl font-bold text-white mb-2 group-hover:text-blue-300 transition-colors">
                        {product.name}
                      </h3>
                      <p className="text-blue-200 text-sm line-clamp-2">{product.description}</p>
                    </div>

                    {/* الإحصائيات الرئيسية */}
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className="bg-white/5 rounded-xl p-3 text-center">
                        <p className="text-blue-200 text-xs mb-1">السعر</p>
                        <p className="text-green-400 font-bold text-lg">{product.price.toFixed(2)} دج</p>
                      </div>
                      <div className="bg-white/5 rounded-xl p-3 text-center">
                        <p className="text-blue-200 text-xs mb-1">المخزون</p>
                        <p className="text-white font-bold text-lg">{product.stock} {product.unit}</p>
                      </div>
                    </div>

                    {/* تفاصيل الربح */}
                    <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 rounded-xl p-4 mb-4">
                      <div className="grid grid-cols-2 gap-4 text-center">
                        <div>
                          <p className="text-green-200 text-xs mb-1">ربح الوحدة</p>
                          <p className="text-green-400 font-bold">{profit.toFixed(2)} دج</p>
                        </div>
                        <div>
                          <p className="text-blue-200 text-xs mb-1">هامش الربح</p>
                          <p className="text-blue-400 font-bold">{profitMargin.toFixed(1)}%</p>
                        </div>
                      </div>
                      <div className="mt-3 pt-3 border-t border-white/20">
                        <div className="grid grid-cols-2 gap-4 text-center">
                          <div>
                            <p className="text-purple-200 text-xs mb-1">قيمة المخزون</p>
                            <p className="text-purple-400 font-bold">{totalValue.toLocaleString()} دج</p>
                          </div>
                          <div>
                            <p className="text-yellow-200 text-xs mb-1">ربح متوقع</p>
                            <p className="text-yellow-400 font-bold">{totalProfit.toLocaleString()} دج</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* معلومات إضافية */}
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center justify-between">
                        <span className="text-blue-200 text-sm">الباركود:</span>
                        <span className="text-white font-mono text-sm bg-white/10 px-2 py-1 rounded">{product.barcode}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-blue-200 text-sm">التكلفة:</span>
                        <span className="text-orange-400 font-semibold">{product.cost.toFixed(2)} دج</span>
                      </div>
                    </div>

                    {/* أزرار الإجراءات */}
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <button
                        onClick={() => handleEdit(product)}
                        className="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 py-2 rounded-xl flex items-center justify-center space-x-2 space-x-reverse transition-all duration-300 hover:scale-105"
                      >
                        <Edit className="w-4 h-4" />
                        <span>تعديل</span>
                      </button>
                      <button
                        className="bg-white/10 hover:bg-white/20 p-2 rounded-xl transition-all duration-300 hover:scale-105"
                      >
                        <Eye className="w-4 h-4 text-blue-300" />
                      </button>
                      <button
                        onClick={() => handleDelete(product.id)}
                        className="bg-red-500/20 hover:bg-red-500/30 p-2 rounded-xl transition-all duration-300 hover:scale-105"
                      >
                        <Trash2 className="w-4 h-4 text-red-300" />
                      </button>
                    </div>
                  </div>

                  {/* تأثير الإضاءة */}
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/0 via-purple-500/5 to-pink-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                </div>
              );
            })}
          </div>
        ) : (
          /* عرض القائمة */
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-white/5">
                  <tr>
                    <th className="text-right py-4 px-6 text-blue-200 font-semibold">المنتج</th>
                    <th className="text-right py-4 px-6 text-blue-200 font-semibold">القسم</th>
                    <th className="text-right py-4 px-6 text-blue-200 font-semibold">السعر</th>
                    <th className="text-right py-4 px-6 text-blue-200 font-semibold">التكلفة</th>
                    <th className="text-right py-4 px-6 text-blue-200 font-semibold">المخزون</th>
                    <th className="text-right py-4 px-6 text-blue-200 font-semibold">الربح</th>
                    <th className="text-right py-4 px-6 text-blue-200 font-semibold">قيمة المخزون</th>
                    <th className="text-center py-4 px-6 text-blue-200 font-semibold">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {paginatedProducts.map((product, index) => {
                    const categoryName = categories.find(cat => cat.id === product.category)?.name || 'غير محدد';
                    const profit = product.price - product.cost;
                    const totalValue = product.price * product.stock;
                    const isLowStock = product.stock <= product.minStock;
                    const isOutOfStock = product.stock === 0;

                    return (
                      <tr
                        key={product.id}
                        className={`border-t border-white/10 hover:bg-white/5 transition-colors ${
                          index % 2 === 0 ? 'bg-white/2' : ''
                        }`}
                      >
                        <td className="py-4 px-6">
                          <div className="flex items-center space-x-4 space-x-reverse">
                            <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-2 rounded-lg">
                              <Package className="w-5 h-5 text-white" />
                            </div>
                            <div>
                              <p className="text-white font-semibold">{product.name}</p>
                              <p className="text-blue-200 text-sm">{product.barcode}</p>
                            </div>
                          </div>
                        </td>
                        <td className="py-4 px-6">
                          <span className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-lg text-sm">
                            {categoryName}
                          </span>
                        </td>
                        <td className="py-4 px-6">
                          <span className="text-green-400 font-bold">{product.price.toFixed(2)} دج</span>
                        </td>
                        <td className="py-4 px-6">
                          <span className="text-orange-400 font-semibold">{product.cost.toFixed(2)} دج</span>
                        </td>
                        <td className="py-4 px-6">
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <span className="text-white font-semibold">{product.stock} {product.unit}</span>
                            {isOutOfStock && <div className="w-2 h-2 bg-red-500 rounded-full"></div>}
                            {isLowStock && !isOutOfStock && <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>}
                          </div>
                        </td>
                        <td className="py-4 px-6">
                          <span className="text-blue-400 font-semibold">{profit.toFixed(2)} دج</span>
                        </td>
                        <td className="py-4 px-6">
                          <span className="text-purple-400 font-bold">{totalValue.toLocaleString()} دج</span>
                        </td>
                        <td className="py-4 px-6">
                          <div className="flex items-center justify-center space-x-2 space-x-reverse">
                            <button
                              onClick={() => handleEdit(product)}
                              className="bg-blue-500/20 text-blue-300 hover:bg-blue-500/30 p-2 rounded-lg transition-colors"
                            >
                              <Edit className="w-4 h-4" />
                            </button>
                            <button className="bg-purple-500/20 text-purple-300 hover:bg-purple-500/30 p-2 rounded-lg transition-colors">
                              <Eye className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleDelete(product.id)}
                              className="bg-red-500/20 text-red-300 hover:bg-red-500/30 p-2 rounded-lg transition-colors"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* 🎭 الحالة الفارغة المبهرة */}
        {totalItems === 0 && (
          <div className="text-center py-20">
            <div className="relative mb-8">
              <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full p-8 mx-auto w-32 h-32 flex items-center justify-center">
                <Package className="w-16 h-16 text-blue-300" />
              </div>
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full animate-ping"></div>
            </div>

            <h3 className="text-3xl font-bold text-white mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              {searchTerm || filterCategory !== 'all' ? 'لا توجد نتائج' : 'لا توجد منتجات'}
            </h3>

            <p className="text-blue-200 text-lg mb-8 max-w-md mx-auto">
              {searchTerm || filterCategory !== 'all'
                ? 'جرب تغيير معايير البحث أو الفلتر للعثور على المنتجات'
                : 'ابدأ رحلتك التجارية بإضافة منتجات مذهلة لمتجرك'
              }
            </p>

            <div className="flex items-center justify-center space-x-4 space-x-reverse">
              {searchTerm || filterCategory !== 'all' ? (
                <button
                  onClick={() => {
                    handleSearchChange('');
                    handleFilterChange('all');
                  }}
                  className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse shadow-lg"
                >
                  <X className="w-5 h-5" />
                  <span>مسح الفلاتر</span>
                </button>
              ) : (
                <button
                  onClick={() => setShowAddForm(true)}
                  className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse shadow-lg"
                >
                  <Plus className="w-6 h-6" />
                  <span>إضافة أول منتج</span>
                  <Sparkles className="w-5 h-5" />
                </button>
              )}
            </div>
          </div>
        )}

        {/* 🏆 منتج الشهر */}
        {stats.topProfitProduct && stats.topProfitProduct.name && totalItems > 0 && (
          <div className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-lg rounded-2xl border border-yellow-300/30 p-6">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="bg-yellow-500/30 p-3 rounded-xl">
                <Crown className="w-6 h-6 text-yellow-300" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">⭐ منتج الشهر - الأعلى ربحاً</h3>
                <p className="text-yellow-200">
                  <span className="font-bold">{stats.topProfitProduct.name}</span> -
                  ربح إجمالي: <span className="font-bold">{((stats.topProfitProduct.price - stats.topProfitProduct.cost) * stats.topProfitProduct.stock).toLocaleString()} دج</span>
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductManagement;