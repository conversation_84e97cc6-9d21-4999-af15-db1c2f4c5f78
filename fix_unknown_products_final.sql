-- الحل النهائي للمنتجات غير المعروفة
-- شغل هذا في pgAdmin Query Tool

-- 0. إصلاح مشكلة sale_number_seq
CREATE SEQUENCE IF NOT EXISTS pos_system.sale_number_seq START 1;

-- تحديث الدالة لتستخدم الـ schema الصحيح
CREATE OR REPLACE FUNCTION pos_system.generate_sale_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.sale_number IS NULL OR NEW.sale_number = '' THEN
        NEW.sale_number := 'SALE-' || TO_CHAR(CURRENT_DATE, 'YYYYMMDD') || '-' ||
                          LPAD(NEXTVAL('pos_system.sale_number_seq')::TEXT, 4, '0');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إعادة إنشاء الـ trigger
DROP TRIGGER IF EXISTS trigger_generate_sale_number ON pos_system.sales;
CREATE TRIGGER trigger_generate_sale_number
    BEFORE INSERT ON pos_system.sales
    FOR EACH ROW EXECUTE FUNCTION pos_system.generate_sale_number();

-- 1. إنشاء جدول منفصل للمنتجات غير المعروفة
CREATE TABLE IF NOT EXISTS pos_system.unknown_products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1,
    sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT DEFAULT 'منتج غير معروف'
);

-- 2. تعديل جدول sale_items ليقبل product_id كـ NULL
ALTER TABLE pos_system.sale_items
ALTER COLUMN product_id DROP NOT NULL;

-- 3. إضافة عمود للمنتجات غير المعروفة
ALTER TABLE pos_system.sale_items
ADD COLUMN IF NOT EXISTS is_unknown BOOLEAN DEFAULT FALSE;

-- 4. إنشاء فهرس للبحث السريع
CREATE INDEX IF NOT EXISTS idx_sale_items_unknown
ON pos_system.sale_items(is_unknown)
WHERE is_unknown = TRUE;

-- 5. إدراج بيع تجريبي للاختبار
INSERT INTO pos_system.sales (
    subtotal, tax_amount, discount_amount, total_amount,
    payment_method, amount_paid, change_amount, notes, cashier_name
) VALUES (
    50.00, 0.00, 0.00, 50.00,
    'cash', 50.00, 0.00, 'اختبار منتج غير معروف', 'النظام'
) RETURNING id;

-- 6. رسالة إتمام
SELECT '✅ تم إعداد نظام المنتجات غير المعروفة بنجاح!' AS message;
