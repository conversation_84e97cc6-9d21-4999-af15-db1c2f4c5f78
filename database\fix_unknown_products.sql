-- 🎯 إصلاح مشكلة المنتجات غير المعروفة في نظام البيع
-- هذا السكريبت يحل مشكلة بيع المنتجات غير المعروفة بطريقة سحرية

-- 1. تعديل جدول sale_items لجعل product_id يقبل NULL
ALTER TABLE sale_items 
ALTER COLUMN product_id DROP NOT NULL;

-- 2. إضافة أعمدة جديدة للمنتجات غير المعروفة
ALTER TABLE sale_items 
ADD COLUMN IF NOT EXISTS is_unknown_product BOOLEAN DEFAULT FALSE;

ALTER TABLE sale_items 
ADD COLUMN IF NOT EXISTS unknown_product_code VARCHAR(50);

-- 3. إنشاء منتج افتراضي للمنتجات غير المعروفة
INSERT INTO products (
    id,
    name,
    description,
    barcode,
    category_id,
    price,
    cost,
    stock,
    min_stock,
    unit,
    is_active
) VALUES (
    '00000000-0000-0000-0000-000000000001'::UUID,
    'منتج غير معروف',
    'منتج افتراضي للعناصر غير المعروفة في النظام',
    'UNKNOWN_PRODUCT',
    (SELECT id FROM categories LIMIT 1), -- أول فئة متاحة
    0.00,
    0.00,
    999999, -- مخزون كبير
    0,
    'قطعة',
    true
) ON CONFLICT (id) DO NOTHING;

-- 4. إنشاء فئة خاصة للمنتجات غير المعروفة إذا لم تكن موجودة
INSERT INTO categories (
    id,
    name,
    description,
    is_active
) VALUES (
    '00000000-0000-0000-0000-000000000001'::UUID,
    'منتجات غير معروفة',
    'فئة خاصة للمنتجات غير المعروفة في النظام',
    true
) ON CONFLICT (id) DO NOTHING;

-- 5. تحديث المنتج الافتراضي ليكون في الفئة الصحيحة
UPDATE products 
SET category_id = '00000000-0000-0000-0000-000000000001'::UUID
WHERE id = '00000000-0000-0000-0000-000000000001'::UUID;

-- 6. إنشاء فهرس للمنتجات غير المعروفة
CREATE INDEX IF NOT EXISTS idx_sale_items_unknown 
ON sale_items(is_unknown_product) 
WHERE is_unknown_product = TRUE;

-- 7. إنشاء view لإحصائيات المنتجات غير المعروفة
CREATE OR REPLACE VIEW unknown_products_stats AS
SELECT 
    COUNT(*) as total_unknown_sales,
    SUM(quantity) as total_unknown_quantity,
    SUM(total_price) as total_unknown_revenue,
    AVG(unit_price) as avg_unknown_price,
    DATE(created_at) as sale_date
FROM sale_items 
WHERE is_unknown_product = TRUE
GROUP BY DATE(created_at)
ORDER BY sale_date DESC;

-- 8. إنشاء function لمعالجة المنتجات غير المعروفة
CREATE OR REPLACE FUNCTION handle_unknown_product_sale(
    p_sale_id UUID,
    p_product_name VARCHAR(255),
    p_quantity INTEGER,
    p_unit_price DECIMAL(10,2),
    p_discount DECIMAL(10,2) DEFAULT 0.00
) RETURNS UUID AS $$
DECLARE
    v_item_id UUID;
    v_total_price DECIMAL(10,2);
    v_unknown_code VARCHAR(50);
BEGIN
    -- حساب السعر الإجمالي
    v_total_price := (p_quantity * p_unit_price) - p_discount;
    
    -- إنشاء كود خاص للمنتج غير المعروف
    v_unknown_code := 'UNK-' || EXTRACT(EPOCH FROM NOW())::BIGINT;
    
    -- إدراج عنصر البيع
    INSERT INTO sale_items (
        sale_id,
        product_id,
        product_name,
        quantity,
        unit_price,
        discount,
        total_price,
        is_unknown_product,
        unknown_product_code
    ) VALUES (
        p_sale_id,
        NULL, -- product_id = NULL للمنتجات غير المعروفة
        p_product_name,
        p_quantity,
        p_unit_price,
        p_discount,
        v_total_price,
        TRUE,
        v_unknown_code
    ) RETURNING id INTO v_item_id;
    
    RETURN v_item_id;
END;
$$ LANGUAGE plpgsql;

-- 9. إنشاء trigger لتسجيل المنتجات غير المعروفة
CREATE OR REPLACE FUNCTION log_unknown_product() 
RETURNS TRIGGER AS $$
BEGIN
    -- إذا كان المنتج غير معروف، سجل في جدول منفصل للتتبع
    IF NEW.is_unknown_product = TRUE THEN
        INSERT INTO financial_transactions (
            type,
            amount,
            description,
            reference_id,
            reference_type
        ) VALUES (
            'revenue',
            NEW.total_price,
            'بيع منتج غير معروف: ' || NEW.product_name,
            NEW.sale_id,
            'unknown_product_sale'
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء trigger
DROP TRIGGER IF EXISTS trigger_log_unknown_product ON sale_items;
CREATE TRIGGER trigger_log_unknown_product
    AFTER INSERT ON sale_items
    FOR EACH ROW
    EXECUTE FUNCTION log_unknown_product();

-- 10. تعليقات على التحديثات
COMMENT ON COLUMN sale_items.product_id IS 'معرف المنتج - يمكن أن يكون NULL للمنتجات غير المعروفة';
COMMENT ON COLUMN sale_items.is_unknown_product IS 'علامة تشير إلى أن هذا منتج غير معروف';
COMMENT ON COLUMN sale_items.unknown_product_code IS 'كود خاص للمنتجات غير المعروفة لتسهيل التتبع';

-- رسالة نجاح
SELECT '🎉 تم إصلاح مشكلة المنتجات غير المعروفة بنجاح! يمكنك الآن بيع أي منتج حتى لو لم يكن موجوداً في قاعدة البيانات.' as success_message;
