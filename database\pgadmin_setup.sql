-- إعد<PERSON> قاعدة البيانات الشامل لـ pgAdmin
-- Complete Database Setup for pgAdmin
-- تشغيل هذا الملف مباشرة في pgAdmin Query Tool

-- ===================================
-- 1. إنشاء قاعدة البيانات والإعدادات
-- ===================================

-- إنشاء امتدادات مفيدة
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- إنشاء schema للنظام
CREATE SCHEMA IF NOT EXISTS pos_system;

-- تعيين المسار الافتراضي
SET search_path TO pos_system, public;

-- إنشاء أنواع البيانات المخصصة (ENUMS)
DO $$
BEGIN
    -- التحقق من وجود الأنواع قبل إنشائها
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('admin', 'manager', 'cashier');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'payment_method') THEN
        CREATE TYPE payment_method AS ENUM ('cash', 'card', 'credit', 'mixed');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'sale_status') THEN
        CREATE TYPE sale_status AS ENUM ('completed', 'pending', 'cancelled', 'refunded');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'transaction_type') THEN
        CREATE TYPE transaction_type AS ENUM ('income', 'expense');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'connection_type') THEN
        CREATE TYPE connection_type AS ENUM ('usb', 'network', 'bluetooth');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'paper_type') THEN
        CREATE TYPE paper_type AS ENUM ('thermal', 'normal');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'print_density') THEN
        CREATE TYPE print_density AS ENUM ('light', 'medium', 'dark');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'print_speed') THEN
        CREATE TYPE print_speed AS ENUM ('slow', 'medium', 'fast');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'font_size') THEN
        CREATE TYPE font_size AS ENUM ('small', 'medium', 'large');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'line_spacing') THEN
        CREATE TYPE line_spacing AS ENUM ('tight', 'normal', 'loose');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'theme_type') THEN
        CREATE TYPE theme_type AS ENUM ('dark', 'light');
    END IF;
END $$;

-- ===================================
-- 2. الجداول الأساسية
-- ===================================

-- جدول الإعدادات العامة
CREATE TABLE IF NOT EXISTS settings (
    id SERIAL PRIMARY KEY,
    -- إعدادات المتجر
    store_name VARCHAR(255) NOT NULL DEFAULT '',
    store_address TEXT DEFAULT '',
    store_phone VARCHAR(50) DEFAULT '',
    store_email VARCHAR(255) DEFAULT '',
    store_tax_number VARCHAR(100) DEFAULT '',
    store_logo TEXT DEFAULT '',

    -- إعدادات المستخدم
    user_name VARCHAR(255) NOT NULL DEFAULT '',
    user_email VARCHAR(255) DEFAULT '',
    user_role user_role DEFAULT 'admin',

    -- إعدادات النظام
    currency VARCHAR(10) DEFAULT 'DZD',
    language VARCHAR(10) DEFAULT 'ar',
    timezone VARCHAR(50) DEFAULT 'Africa/Algiers',
    date_format VARCHAR(20) DEFAULT 'DD/MM/YYYY',

    -- إعدادات الضرائب والمبيعات
    tax_rate DECIMAL(5,2) DEFAULT 19.00,
    enable_tax BOOLEAN DEFAULT true,
    enable_discount BOOLEAN DEFAULT true,
    enable_barcode BOOLEAN DEFAULT true,

    -- إعدادات التنبيهات
    low_stock_alert BOOLEAN DEFAULT true,
    email_notifications BOOLEAN DEFAULT false,
    sound_notifications BOOLEAN DEFAULT true,

    -- إعدادات المظهر
    theme theme_type DEFAULT 'dark',
    primary_color VARCHAR(20) DEFAULT '#3b82f6',
    font_size font_size DEFAULT 'medium',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول إعدادات الطابعة
CREATE TABLE IF NOT EXISTS printer_settings (
    id SERIAL PRIMARY KEY,
    -- إعدادات الاتصال
    connection_type connection_type DEFAULT 'usb',
    printer_name VARCHAR(255) DEFAULT '',
    ip_address INET,
    port INTEGER DEFAULT 9100,
    bluetooth_address VARCHAR(50) DEFAULT '',

    -- إعدادات الطباعة
    paper_width INTEGER DEFAULT 58,
    paper_type paper_type DEFAULT 'thermal',
    print_density print_density DEFAULT 'medium',
    print_speed print_speed DEFAULT 'medium',

    -- إعدادات الفاتورة
    print_logo BOOLEAN DEFAULT true,
    print_header BOOLEAN DEFAULT true,
    print_footer BOOLEAN DEFAULT true,
    print_barcode BOOLEAN DEFAULT true,
    print_qr_code BOOLEAN DEFAULT false,

    -- إعدادات متقدمة
    auto_open_cash_drawer BOOLEAN DEFAULT false,
    print_sound BOOLEAN DEFAULT true,
    copies INTEGER DEFAULT 1,
    cut_paper BOOLEAN DEFAULT true,

    -- نص مخصص
    header_text TEXT DEFAULT '',
    footer_text TEXT DEFAULT 'شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى',

    -- إعدادات الخط
    font_size font_size DEFAULT 'medium',
    font_family VARCHAR(50) DEFAULT 'arial',
    line_spacing line_spacing DEFAULT 'normal',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الفئات/الأقسام
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    color VARCHAR(20) DEFAULT '#3b82f6',
    icon VARCHAR(50) DEFAULT 'Package',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المنتجات
CREATE TABLE IF NOT EXISTS products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    barcode VARCHAR(100) UNIQUE,
    category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    cost DECIMAL(10,2) NOT NULL CHECK (cost >= 0),
    stock INTEGER NOT NULL DEFAULT 0 CHECK (stock >= 0),
    min_stock INTEGER DEFAULT 0 CHECK (min_stock >= 0),
    unit VARCHAR(50) DEFAULT 'قطعة',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول العملاء
CREATE TABLE IF NOT EXISTS customers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    address TEXT,
    email VARCHAR(255),
    balance DECIMAL(10,2) DEFAULT 0.00,
    credit_limit DECIMAL(10,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الموردين
CREATE TABLE IF NOT EXISTS suppliers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    address TEXT,
    email VARCHAR(255),
    balance DECIMAL(10,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ===================================
-- 3. جداول المبيعات والمعاملات
-- ===================================

-- جدول المبيعات الرئيسي
CREATE TABLE IF NOT EXISTS sales (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sale_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,

    -- تفاصيل البيع
    subtotal DECIMAL(10,2) NOT NULL CHECK (subtotal >= 0),
    tax_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (tax_amount >= 0),
    discount_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (discount_amount >= 0),
    total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount >= 0),

    -- طريقة الدفع
    payment_method payment_method NOT NULL,
    amount_paid DECIMAL(10,2) NOT NULL CHECK (amount_paid >= 0),
    change_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (change_amount >= 0),

    -- حالة البيع
    status sale_status DEFAULT 'completed',
    notes TEXT,

    -- معلومات إضافية
    cashier_name VARCHAR(255),
    receipt_printed BOOLEAN DEFAULT false,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول عناصر المبيعات
CREATE TABLE IF NOT EXISTS sale_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sale_id UUID NOT NULL REFERENCES sales(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE RESTRICT,

    -- تفاصيل العنصر
    product_name VARCHAR(255) NOT NULL,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(10,2) NOT NULL CHECK (unit_price >= 0),
    discount DECIMAL(10,2) DEFAULT 0.00 CHECK (discount >= 0),
    total_price DECIMAL(10,2) NOT NULL CHECK (total_price >= 0),

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول فواتير المشتريات
CREATE TABLE IF NOT EXISTS purchase_invoices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    supplier_id UUID REFERENCES suppliers(id) ON DELETE SET NULL,

    -- تفاصيل الفاتورة
    subtotal DECIMAL(10,2) NOT NULL CHECK (subtotal >= 0),
    tax_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (tax_amount >= 0),
    discount_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (discount_amount >= 0),
    total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount >= 0),

    -- حالة الدفع
    amount_paid DECIMAL(10,2) DEFAULT 0.00 CHECK (amount_paid >= 0),
    remaining_amount DECIMAL(10,2) GENERATED ALWAYS AS (total_amount - amount_paid) STORED,
    is_paid BOOLEAN GENERATED ALWAYS AS (amount_paid >= total_amount) STORED,

    -- تواريخ
    invoice_date DATE NOT NULL DEFAULT CURRENT_DATE,
    due_date DATE,

    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول عناصر فواتير المشتريات
CREATE TABLE IF NOT EXISTS purchase_invoice_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID NOT NULL REFERENCES purchase_invoices(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE RESTRICT,

    -- تفاصيل العنصر
    product_name VARCHAR(255) NOT NULL,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_cost DECIMAL(10,2) NOT NULL CHECK (unit_cost >= 0),
    discount DECIMAL(10,2) DEFAULT 0.00 CHECK (discount >= 0),
    total_cost DECIMAL(10,2) NOT NULL CHECK (total_cost >= 0),

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المعاملات المالية العامة
CREATE TABLE IF NOT EXISTS financial_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_number VARCHAR(50) UNIQUE NOT NULL,

    -- نوع المعاملة
    type transaction_type NOT NULL,
    category VARCHAR(100) NOT NULL,

    -- تفاصيل المعاملة
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    description TEXT NOT NULL,
    reference_id UUID,
    reference_type VARCHAR(50),

    -- معلومات إضافية
    payment_method payment_method,
    notes TEXT,

    transaction_date DATE NOT NULL DEFAULT CURRENT_DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول حركة المخزون
CREATE TABLE IF NOT EXISTS inventory_movements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,

    -- تفاصيل الحركة
    movement_type VARCHAR(20) NOT NULL CHECK (movement_type IN ('in', 'out', 'adjustment')),
    quantity INTEGER NOT NULL,
    previous_stock INTEGER NOT NULL,
    new_stock INTEGER NOT NULL,

    -- سبب الحركة
    reason VARCHAR(100) NOT NULL,
    reference_id UUID,
    reference_type VARCHAR(50),

    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول ديون العملاء
CREATE TABLE IF NOT EXISTS customer_debts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
    sale_id UUID REFERENCES sales(id) ON DELETE SET NULL,

    -- تفاصيل الدين
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    paid_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (paid_amount >= 0),
    remaining_amount DECIMAL(10,2) GENERATED ALWAYS AS (amount - paid_amount) STORED,
    is_paid BOOLEAN GENERATED ALWAYS AS (paid_amount >= amount) STORED,

    -- تواريخ
    debt_date DATE NOT NULL DEFAULT CURRENT_DATE,
    due_date DATE,

    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول مدفوعات ديون العملاء
CREATE TABLE IF NOT EXISTS customer_debt_payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    debt_id UUID NOT NULL REFERENCES customer_debts(id) ON DELETE CASCADE,

    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    payment_method payment_method NOT NULL,
    payment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    notes TEXT,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- رسالة تأكيد
DO $$
BEGIN
    RAISE NOTICE 'تم إنشاء جداول المبيعات والمعاملات بنجاح!';
    RAISE NOTICE 'Sales and transaction tables created successfully!';
END $$;
