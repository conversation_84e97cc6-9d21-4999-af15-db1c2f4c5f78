// اختبار بسيط للخادم
console.log('🚀 بدء اختبار الخادم...');

try {
  const express = require('express');
  console.log('✅ Express تم تحميله بنجاح');
  
  const cors = require('cors');
  console.log('✅ CORS تم تحميله بنجاح');
  
  require('dotenv').config();
  console.log('✅ dotenv تم تحميله بنجاح');
  
  const pool = require('./database');
  console.log('✅ Database تم تحميله بنجاح');
  
  const app = express();
  const PORT = 5003;
  
  app.use(cors());
  app.use(express.json());
  
  app.get('/test', (req, res) => {
    res.json({ message: 'الخادم يعمل بنجاح!' });
  });
  
  app.listen(PORT, () => {
    console.log(`🚀 الخادم يعمل على المنفذ ${PORT}`);
  });
  
} catch (error) {
  console.error('❌ خطأ في تشغيل الخادم:', error);
}
