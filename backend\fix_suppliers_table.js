const pool = require('./database');

async function fixSuppliersTable() {
  try {
    console.log('🔧 إصلاح جدول الموردين...');

    const client = await pool.connect();

    // التحقق من الحقول الموجودة في جدول الموردين
    const columnsResult = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_schema = 'pos_system' 
      AND table_name = 'suppliers'
      ORDER BY ordinal_position
    `);

    console.log('📋 الحقول الموجودة في جدول الموردين:');
    const existingColumns = columnsResult.rows.map(row => row.column_name);
    existingColumns.forEach(col => {
      console.log(`  ✓ ${col}`);
    });

    // إضافة الحقول المفقودة
    const requiredColumns = [
      { name: 'company', type: 'VARCHAR(255)', description: 'اسم الشركة' },
      { name: 'notes', type: 'TEXT', description: 'ملاحظات' },
      { name: 'email', type: 'VARCHAR(255)', description: 'البريد الإلكتروني' }
    ];

    for (const column of requiredColumns) {
      if (!existingColumns.includes(column.name)) {
        console.log(`➕ إضافة حقل ${column.name} (${column.description})...`);
        
        try {
          await client.query(`
            ALTER TABLE pos_system.suppliers 
            ADD COLUMN ${column.name} ${column.type}
          `);
          console.log(`✅ تم إضافة حقل ${column.name} بنجاح`);
        } catch (error) {
          console.log(`⚠️ حقل ${column.name} موجود مسبقاً أو حدث خطأ:`, error.message);
        }
      } else {
        console.log(`✅ حقل ${column.name} موجود مسبقاً`);
      }
    }

    // التحقق من الحقول بعد الإضافة
    const updatedColumnsResult = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_schema = 'pos_system' 
      AND table_name = 'suppliers'
      ORDER BY ordinal_position
    `);

    console.log('\n📋 الحقول بعد التحديث:');
    updatedColumnsResult.rows.forEach(row => {
      console.log(`  ✓ ${row.column_name} (${row.data_type})`);
    });

    // عرض عدد الموردين الموجودين
    const countResult = await client.query('SELECT COUNT(*) as count FROM pos_system.suppliers');
    console.log(`\n📊 عدد الموردين في قاعدة البيانات: ${countResult.rows[0].count}`);

    // عرض الموردين الموجودين
    if (parseInt(countResult.rows[0].count) > 0) {
      const suppliersResult = await client.query(`
        SELECT id, name, company, phone, address, notes, is_active, created_at
        FROM pos_system.suppliers 
        ORDER BY created_at DESC 
        LIMIT 10
      `);

      console.log('\n📋 الموردين الموجودين:');
      suppliersResult.rows.forEach((supplier, index) => {
        console.log(`${index + 1}. ${supplier.name}`);
        console.log(`   الشركة: ${supplier.company || 'غير محدد'}`);
        console.log(`   الهاتف: ${supplier.phone || 'غير محدد'}`);
        console.log(`   العنوان: ${supplier.address || 'غير محدد'}`);
        console.log(`   نشط: ${supplier.is_active ? 'نعم' : 'لا'}`);
        console.log(`   تاريخ الإضافة: ${supplier.created_at}`);
        console.log('   ' + '-'.repeat(50));
      });
    }

    client.release();
    console.log('\n🎉 تم إصلاح جدول الموردين بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في إصلاح جدول الموردين:', error.message);
    console.error('تفاصيل الخطأ:', error.stack);
  } finally {
    process.exit();
  }
}

fixSuppliersTable();
