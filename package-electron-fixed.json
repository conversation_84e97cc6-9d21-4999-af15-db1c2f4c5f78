{"name": "kashe<PERSON>-<PERSON><PERSON>", "productName": "كشير توسار - نظام نقاط البيع", "version": "1.0.0", "description": "نظام نقاط بيع متكامل باللغة العربية", "main": "electron-main.js", "author": {"name": "فريق توسار", "email": "<EMAIL>"}, "license": "MIT", "homepage": "./", "scripts": {"electron": "electron .", "electron-dev": "NODE_ENV=development electron .", "build-electron": "npm run build && electron-builder", "dist": "electron-builder --publish=never", "pack": "electron-builder --dir"}, "devDependencies": {"electron": "^32.2.6", "electron-builder": "^25.1.8"}, "build": {"appId": "com.toosar.kasheer", "productName": "كشير توسار - نظام نقاط البيع", "directories": {"output": "dist"}, "files": ["dist/**/*", "backend/**/*", "electron-main.js", "assets/**/*", "!node_modules/**/*", "!src/**/*", "!public/**/*", "!*.md", "!*.bat"], "extraResources": [{"from": "backend", "to": "backend", "filter": ["**/*", "!node_modules/**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "msi", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "installerIcon": "assets/icon.ico", "uninstallerIcon": "assets/icon.ico", "installerHeaderIcon": "assets/icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "كشير توسار", "include": "installer.nsh", "language": "1025"}, "msi": {"oneClick": false, "upgradeCode": "12345678-1234-1234-1234-123456789012", "language": "1025"}, "compression": "maximum", "publish": null}}