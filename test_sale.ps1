$headers = @{
    "Content-Type" = "application/json"
}

$body = @{
    customer_id = $null
    items = @(
        @{
            product_id = $null
            product_name = "منتج تجريبي غير معروف - 25.50 دج"
            quantity = 1
            unit_price = 25.50
            discount = 0
            total_price = 25.50
        }
    )
    subtotal = 25.50
    tax_amount = 0
    discount_amount = 0
    total_amount = 25.50
    payment_method = "cash"
    amount_paid = 25.50
    change_amount = 0
    notes = "اختبار منتج غير معروف"
    cashier_name = "اختبار النظام"
} | ConvertTo-Json -Depth 3

Write-Host "🧪 اختبار بيع منتج غير معروف..." -ForegroundColor Yellow
Write-Host "📤 إرسال طلب البيع..." -ForegroundColor Cyan

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5002/api/sales" -Method POST -Headers $headers -Body $body
    
    Write-Host "✅ نجح البيع!" -ForegroundColor Green
    Write-Host "📊 رقم البيع: $($response.sale_number)" -ForegroundColor Green
    Write-Host "💰 المبلغ الإجمالي: $($response.total_amount)" -ForegroundColor Green
    Write-Host "🔮 تم بيع منتج غير معروف بنجاح!" -ForegroundColor Magenta
    
} catch {
    Write-Host "❌ فشل البيع:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    if ($_.ErrorDetails) {
        Write-Host "🔍 التفاصيل: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
}
