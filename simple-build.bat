@echo off
title Building Kasheer Toosar Installer

echo Building Kasheer Toosar Installer...
echo.

echo Step 1: Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo Failed to install dependencies
    pause
    exit /b 1
)

echo Step 2: Installing Electron...
call npm install electron electron-builder --save-dev
if %errorlevel% neq 0 (
    echo Failed to install Electron
    pause
    exit /b 1
)

echo Step 3: Building frontend...
call npm run build
if %errorlevel% neq 0 (
    echo Failed to build frontend
    pause
    exit /b 1
)

echo Step 4: Preparing package.json...
copy /Y package-electron-fixed.json package.json

echo Step 5: Building installer...
call npx electron-builder --win
if %errorlevel% neq 0 (
    echo Build failed
    pause
    exit /b 1
)

echo.
echo SUCCESS: Installer built!
echo Check the dist folder for installer files.
pause
