const axios = require('axios');

async function testUnknownProductSale() {
  try {
    console.log('🧪 اختبار بيع منتج غير معروف...');
    
    // بيانات البيع مع منتج غير معروف
    const saleData = {
      customer_id: null,
      items: [
        {
          product_id: null, // منتج غير معروف
          product_name: "منتج تجريبي غير معروف - 25.50 دج",
          quantity: 1,
          unit_price: 25.50,
          discount: 0,
          total_price: 25.50
        }
      ],
      subtotal: 25.50,
      tax_amount: 0,
      discount_amount: 0,
      total_amount: 25.50,
      payment_method: 'cash',
      amount_paid: 25.50,
      change_amount: 0,
      notes: 'اختبار منتج غير معروف',
      cashier_name: 'اختبار النظام'
    };
    
    console.log('📤 إرسال طلب البيع...');
    const response = await axios.post('http://localhost:5002/api/sales', saleData);
    
    console.log('✅ نجح البيع!');
    console.log('📊 رقم البيع:', response.data.sale_number);
    console.log('💰 المبلغ الإجمالي:', response.data.total_amount);
    console.log('🔮 تم بيع منتج غير معروف بنجاح!');
    
  } catch (error) {
    console.error('❌ فشل البيع:', error.response?.data || error.message);
    console.error('🔍 التفاصيل:', error.response?.data?.details || 'لا توجد تفاصيل');
  }
}

testUnknownProductSale();
