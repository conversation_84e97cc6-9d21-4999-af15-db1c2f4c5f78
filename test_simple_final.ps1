# اختبار بسيط للحل النهائي
Write-Host "Testing final solution..." -ForegroundColor Green

$data = '{"name":"Test Product","price":100}'

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5002/api/sales/quick-unknown" -Method POST -Body $data -ContentType "application/json"
    Write-Host "SUCCESS: Sale created!" -ForegroundColor Green
    Write-Host "ID: $($response.product.id)" -ForegroundColor Yellow
    Write-Host "Name: $($response.product.name)" -ForegroundColor Yellow
    Write-Host "Price: $($response.product.price)" -ForegroundColor Yellow
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

# Test viewing unknown products
try {
    $products = Invoke-RestMethod -Uri "http://localhost:5002/api/sales/unknown-products" -Method GET
    Write-Host "Found $($products.Count) unknown products" -ForegroundColor Green
} catch {
    Write-Host "ERROR viewing products: $($_.Exception.Message)" -ForegroundColor Red
}
