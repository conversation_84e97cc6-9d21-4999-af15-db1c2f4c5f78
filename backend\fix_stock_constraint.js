const pool = require('./database');

async function fixStockConstraint() {
  try {
    console.log('🔧 إصلاح مشكلة قيد المخزون...');

    // 1. التحقق من وجود المنتجات
    const productsResult = await pool.query('SELECT COUNT(*) as count FROM products');
    console.log(`📦 عدد المنتجات في قاعدة البيانات: ${productsResult.rows[0].count}`);

    if (productsResult.rows[0].count === '0') {
      console.log('⚠️ لا توجد منتجات في قاعدة البيانات. سيتم إضافة منتجات تجريبية...');
      
      // إضافة منتجات تجريبية
      await pool.query(`
        INSERT INTO products (name, description, price, cost, stock, min_stock, unit, is_active)
        VALUES 
        ('منتج تجريبي 1', 'وصف المنتج الأول', 100.00, 50.00, 1000, 10, 'قطعة', true),
        ('منتج تجريبي 2', 'وصف المنتج الثاني', 200.00, 100.00, 500, 5, 'قطعة', true),
        ('منتج تجريبي 3', 'وصف المنتج الثالث', 50.00, 25.00, 2000, 20, 'قطعة', true)
      `);
      console.log('✅ تم إضافة منتجات تجريبية');
    }

    // 2. التحقق من قيود المخزون
    const constraintsResult = await pool.query(`
      SELECT conname, contype 
      FROM pg_constraint 
      WHERE conrelid = 'products'::regclass 
      AND contype = 'c'
    `);

    console.log('📋 قيود جدول المنتجات:');
    constraintsResult.rows.forEach(constraint => {
      console.log(`  - ${constraint.conname} (${constraint.contype})`);
    });

    // 3. عرض المنتجات الحالية
    const currentProducts = await pool.query(`
      SELECT id, name, stock, min_stock, price 
      FROM products 
      ORDER BY stock ASC 
      LIMIT 10
    `);

    console.log('\n📦 المنتجات الحالية:');
    currentProducts.rows.forEach((product, index) => {
      console.log(`${index + 1}. ${product.name} - المخزون: ${product.stock} - السعر: ${product.price} دج`);
    });

    // 4. تحديث المنتجات التي لديها مخزون صفر أو سالب
    const updateResult = await pool.query(`
      UPDATE products 
      SET stock = 100 
      WHERE stock <= 0
      RETURNING name, stock
    `);

    if (updateResult.rows.length > 0) {
      console.log('\n🔄 تم تحديث المنتجات التي لديها مخزون منخفض:');
      updateResult.rows.forEach(product => {
        console.log(`  - ${product.name}: المخزون الجديد = ${product.stock}`);
      });
    }

    console.log('\n✅ تم إصلاح مشكلة المخزون بنجاح');

  } catch (error) {
    console.error('❌ خطأ في إصلاح مشكلة المخزون:', error);
    console.error('تفاصيل الخطأ:', error.message);
  } finally {
    process.exit();
  }
}

fixStockConstraint();
