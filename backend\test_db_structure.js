const pool = require('./database');

async function testDatabaseStructure() {
  try {
    console.log('🔍 فحص بنية قاعدة البيانات...');
    
    // فحص جدول customer_debts
    const result = await pool.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_schema = 'pos_system' 
      AND table_name = 'customer_debts'
      ORDER BY ordinal_position
    `);
    
    console.log('📋 أعمدة جدول customer_debts:');
    result.rows.forEach(row => {
      console.log(`  - ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });
    
    // اختبار إدراج بيانات تجريبية
    console.log('\n🧪 اختبار إدراج دين تجريبي...');
    
    // أولاً، التحقق من وجود عميل
    const customerCheck = await pool.query('SELECT id FROM customers LIMIT 1');
    if (customerCheck.rows.length === 0) {
      console.log('⚠️ لا يوجد عملاء في قاعدة البيانات');
      return;
    }
    
    const customerId = customerCheck.rows[0].id;
    console.log(`📝 استخدام العميل: ${customerId}`);
    
    // محاولة إدراج دين تجريبي
    const insertResult = await pool.query(`
      INSERT INTO customer_debts (
        customer_id, amount, debt_date, notes, status
      )
      VALUES ($1, $2, CURRENT_DATE, $3, $4)
      RETURNING *
    `, [customerId, 100.00, 'اختبار دين', 'pending']);
    
    console.log('✅ تم إدراج الدين التجريبي بنجاح:');
    console.log(insertResult.rows[0]);
    
    // حذف الدين التجريبي
    await pool.query('DELETE FROM customer_debts WHERE id = $1', [insertResult.rows[0].id]);
    console.log('🗑️ تم حذف الدين التجريبي');
    
    console.log('\n🎉 جدول customer_debts يعمل بشكل صحيح!');
    
  } catch (error) {
    console.error('❌ خطأ في فحص قاعدة البيانات:', error.message);
    console.error('📋 تفاصيل الخطأ:', error);
  } finally {
    await pool.end();
  }
}

testDatabaseStructure();
