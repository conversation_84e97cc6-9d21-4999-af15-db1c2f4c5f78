# 🧪 تعليمات اختبار النظام المحدث

## 🚀 خطوات الاختبار

### 1. إعداد قاعدة البيانات
```sql
-- في pgAdmin Query Tool
-- انسخ والصق محتوى ملف quick_setup_unknown.sql
-- اضغط F5 لتنفيذ الكود
```

### 2. إعادة تشغيل الخادم
```bash
# في مجلد backend
npm run dev
```

### 3. إعادة تشغيل Frontend
```bash
# في المجلد الرئيسي
npm start
```

## 🔮 اختبار المنتجات غير المعروفة

### في شاشة البيع:
1. اكتب `/100` واضغط Enter
2. اكتب `/250` واضغط Enter  
3. اكتب `/75.50` واضغط Enter
4. أكمل عملية البيع

### النتائج المتوقعة:
- ✅ حفظ في جدول `unknown_sales`
- ✅ ظهور في لوحة التحكم
- ✅ ظهور في صفحة الطلبات
- ✅ تحديث الإحصائيات

## 📊 فحص النتائج

### في لوحة التحكم:
- تحقق من تحديث "مبيعات اليوم"
- تحقق من تحديث "إجمالي الطلبات"
- تحقق من ظهور إحصائيات المنتجات غير المعروفة

### في صفحة الطلبات:
- تحقق من ظهور الطلبات الجديدة
- تحقق من عرض "منتج غير معروف" كعميل
- تحقق من رقم الطلب بصيغة UNK-

### في pgAdmin:
```sql
-- فحص المبيعات غير المعروفة
SELECT * FROM pos_system.unknown_sales ORDER BY sale_date DESC;

-- فحص الإحصائيات
SELECT 
  COUNT(*) as total_unknown_sales,
  SUM(total_amount) as total_revenue
FROM pos_system.unknown_sales;
```

## 🎯 نقاط الفحص الرئيسية

- [ ] المنتجات غير المعروفة تُحفظ في قاعدة البيانات
- [ ] لوحة التحكم تعرض الإحصائيات المحدثة
- [ ] صفحة الطلبات تعرض المبيعات الجديدة
- [ ] الأرقام متطابقة بين الواجهة وقاعدة البيانات
- [ ] لا توجد أخطاء في وحدة التحكم

## 🔧 استكشاف الأخطاء

### إذا لم تظهر الإحصائيات:
1. تحقق من تشغيل SQL في pgAdmin
2. أعد تشغيل الخادم
3. تحقق من وحدة التحكم للأخطاء

### إذا لم تظهر في الطلبات:
1. تحقق من API endpoint `/sales/unknown-sales`
2. تحقق من تحديث AppContext
3. أعد تحميل الصفحة

## ✅ النجاح المتوقع

عند النجاح ستجد:
- 🔮 منتجات غير معروفة في شاشة البيع
- 📊 إحصائيات محدثة في لوحة التحكم  
- 📋 طلبات جديدة في صفحة الطلبات
- 💾 بيانات محفوظة في قاعدة البيانات
