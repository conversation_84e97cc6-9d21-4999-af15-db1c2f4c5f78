import React, { useState, useEffect } from 'react';
import {
  X,
  Printer,
  Download,
  Share2,
  Calendar,
  Clock,
  User,
  Package,
  DollarSign,
  CreditCard,
  Phone,
  Mail,
  MapPin,
  FileText,
  Hash,
  Building
} from 'lucide-react';
import BarcodeGenerator from './BarcodeGenerator';
import { useApp } from '../context/AppContext';
import '../styles/professional-invoice-print.css';

interface ProfessionalInvoiceProps {
  isOpen: boolean;
  onClose: () => void;
  saleData: any; // نستخدم any لأن البيانات تأتي من قاعدة البيانات بأشكال مختلفة
}

const ProfessionalInvoice: React.FC<ProfessionalInvoiceProps> = ({
  isOpen,
  onClose,
  saleData
}) => {
  const { settings } = useApp();
  const [fullSaleData, setFullSaleData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  // معلومات المتجر من الإعدادات
  const storeInfo = {
    name: settings?.storeName || "متجر توسار",
    address: settings?.storeAddress || "الجزائر - الجزائر العاصمة",
    phone: settings?.storePhone || "+213 XXX XXX XXX",
    email: settings?.storeEmail || "<EMAIL>",
    taxNumber: settings?.storeTaxNumber || "*********",
    logo: settings?.storeLogo || ""
  };

  // جلب البيانات الكاملة للمبيعة مع الأصناف
  useEffect(() => {
    const fetchFullSaleData = async () => {
      if (!saleData?.id) {
        setFullSaleData(saleData);
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        console.log('🔄 جلب البيانات الكاملة للمبيعة:', saleData.id);

        const response = await fetch(`/api/sales/${saleData.id}`);
        if (response.ok) {
          const data = await response.json();
          console.log('✅ تم جلب البيانات الكاملة:', data);
          setFullSaleData(data);
        } else {
          console.error('❌ خطأ في جلب البيانات:', response.status);
          setFullSaleData(saleData);
        }
      } catch (error) {
        console.error('❌ خطأ في الشبكة:', error);
        setFullSaleData(saleData);
      } finally {
        setLoading(false);
      }
    };

    if (isOpen) {
      fetchFullSaleData();
    }
  }, [isOpen, saleData]);

  // استخدام البيانات الكاملة أو البيانات الأصلية
  const currentSaleData = fullSaleData || saleData;

  // تحويل البيانات إلى التنسيق المطلوب
  const processedSaleData = {
    id: currentSaleData?.id || '',
    saleNumber: currentSaleData?.sale_number || currentSaleData?.saleNumber || `SALE-${Date.now()}`,
    date: currentSaleData?.created_at ? new Date(currentSaleData.created_at) : (currentSaleData?.date ? new Date(currentSaleData.date) : new Date()),
    customerId: currentSaleData?.customer_id || currentSaleData?.customerId,
    customerName: currentSaleData?.customer_name || currentSaleData?.customerName || 'عميل عادي',
    items: currentSaleData?.items || [],
    subtotal: parseFloat(currentSaleData?.subtotal || currentSaleData?.total_amount || currentSaleData?.total || 0),
    tax: parseFloat(currentSaleData?.tax_amount || currentSaleData?.tax || 0),
    discount: parseFloat(currentSaleData?.discount_amount || currentSaleData?.discount || 0),
    total: parseFloat(currentSaleData?.total_amount || currentSaleData?.total || 0),
    paymentMethod: currentSaleData?.payment_method || currentSaleData?.paymentMethod || 'cash',
    status: currentSaleData?.status || 'completed',
    paid: parseFloat(currentSaleData?.amount_paid || currentSaleData?.paid || 0),
    change: parseFloat(currentSaleData?.change_amount || currentSaleData?.change || 0)
  };

  console.log('🔍 بيانات المبيعة الأصلية:', saleData);
  console.log('🔍 البيانات الكاملة المجلبة:', fullSaleData);
  console.log('🔍 بيانات المبيعة المعالجة:', processedSaleData);

  // تحويل الأرقام إلى كلمات بالعربية
  const numberToWords = (num: number): string => {
    const ones = ['', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'];
    const tens = ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];
    const teens = ['عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'];
    const hundreds = ['', 'مائة', 'مائتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'];
    const thousands = ['', 'ألف', 'ألفان', 'ثلاثة آلاف', 'أربعة آلاف', 'خمسة آلاف', 'ستة آلاف', 'سبعة آلاف', 'ثمانية آلاف', 'تسعة آلاف'];

    if (num === 0) return 'صفر';
    if (num < 0) return 'سالب ' + numberToWords(-num);

    let result = '';
    const integerPart = Math.floor(num);
    const decimalPart = Math.round((num - integerPart) * 100);

    // معالجة الجزء الصحيح
    if (integerPart >= 1000000) {
      const millions = Math.floor(integerPart / 1000000);
      result += numberToWords(millions) + ' مليون ';
      const remainder = integerPart % 1000000;
      if (remainder > 0) result += numberToWords(remainder) + ' ';
    } else if (integerPart >= 1000) {
      const thousandsNum = Math.floor(integerPart / 1000);
      if (thousandsNum < 10) {
        result += thousands[thousandsNum] + ' ';
      } else {
        result += numberToWords(thousandsNum) + ' ألف ';
      }
      const remainder = integerPart % 1000;
      if (remainder > 0) result += numberToWords(remainder) + ' ';
    } else if (integerPart >= 100) {
      const hundredsNum = Math.floor(integerPart / 100);
      result += hundreds[hundredsNum] + ' ';
      const remainder = integerPart % 100;
      if (remainder > 0) result += numberToWords(remainder) + ' ';
    } else if (integerPart >= 20) {
      const tensNum = Math.floor(integerPart / 10);
      result += tens[tensNum] + ' ';
      const remainder = integerPart % 10;
      if (remainder > 0) result += ones[remainder] + ' ';
    } else if (integerPart >= 10) {
      result += teens[integerPart - 10] + ' ';
    } else if (integerPart > 0) {
      result += ones[integerPart] + ' ';
    }

    result += 'دينار جزائري';

    // معالجة الجزء العشري
    if (decimalPart > 0) {
      result += ' و ' + numberToWords(decimalPart) + ' سنتيم';
    }

    return result.trim();
  };

  const handlePrint = () => {
    const printContent = document.getElementById('professional-invoice-content');
    if (printContent) {
      // الحصول على ملف CSS للطباعة
      const printCSS = `
        @page {
          size: A4;
          margin: 20mm;
        }
        body {
          margin: 0;
          padding: 0;
          font-family: 'Arial', sans-serif;
          color: #000 !important;
          background: #fff !important;
          line-height: 1.5;
          font-size: 14px;
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
        * {
          box-sizing: border-box;
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }

        /* إعدادات خاصة للباركود */
        canvas {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
          max-width: 100% !important;
          height: auto !important;
          margin: 0 auto !important;
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }

        svg {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
          max-width: 100% !important;
          height: auto !important;
          margin: 0 auto !important;
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }

        .barcode-container {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
          text-align: center !important;
          margin: 16px 0 !important;
          page-break-inside: avoid !important;
          break-inside: avoid !important;
        }

        table {
          page-break-inside: avoid;
          break-inside: avoid;
          border-collapse: collapse !important;
        }
        tr {
          page-break-inside: avoid;
          break-inside: avoid;
        }
        h1 { font-size: 24px !important; margin: 0 0 4px 0 !important; font-weight: bold !important; }
        h2 { font-size: 18px !important; margin: 0 !important; font-weight: bold !important; }
        p { margin: 0 0 8px 0 !important; line-height: 1.5 !important; }
        .text-sm { font-size: 12px !important; }
        .text-lg { font-size: 16px !important; }
        .text-xl { font-size: 18px !important; }
        .text-2xl { font-size: 24px !important; }
        .font-bold { font-weight: bold !important; }
        .font-semibold { font-weight: 600 !important; }
        .font-medium { font-weight: 500 !important; }
        .text-center { text-align: center !important; }
        .text-left { text-align: left !important; }
        .text-right { text-align: right !important; }
        .text-gray-900 { color: #111827 !important; }
        .text-gray-800 { color: #1f2937 !important; }
        .text-gray-700 { color: #374151 !important; }
        .text-gray-600 { color: #4b5563 !important; }
        .text-blue-600 { color: #2563eb !important; }
        .text-blue-700 { color: #1d4ed8 !important; }
        .text-green-600 { color: #059669 !important; }
        .border-b-2 { border-bottom: 2px solid #d1d5db !important; }
        .border-t-2 { border-top: 2px solid #d1d5db !important; }
        .border-b { border-bottom: 1px solid #e5e7eb !important; }
        .border { border: 1px solid #e5e7eb !important; }
        .border-blue-200 { border-color: #bfdbfe !important; }
        .py-3 { padding-top: 12px !important; padding-bottom: 12px !important; }
        .py-4 { padding-top: 16px !important; padding-bottom: 16px !important; }
        .py-6 { padding-top: 24px !important; padding-bottom: 24px !important; }
        .px-4 { padding-left: 16px !important; padding-right: 16px !important; }
        .p-3 { padding: 12px !important; }
        .mb-1 { margin-bottom: 4px !important; }
        .mb-2 { margin-bottom: 8px !important; }
        .mb-4 { margin-bottom: 16px !important; }
        .mb-6 { margin-bottom: 24px !important; }
        .mb-8 { margin-bottom: 32px !important; }
        .mt-2 { margin-top: 8px !important; }
        .mt-4 { margin-top: 16px !important; }
        .pt-6 { padding-top: 24px !important; }
        .grid { display: grid !important; }
        .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
        .gap-8 { gap: 32px !important; }
        .flex { display: flex !important; }
        .items-center { align-items: center !important; }
        .justify-between { justify-content: space-between !important; }
        .justify-end { justify-content: flex-end !important; }
        .w-5 { width: 20px !important; }
        .h-5 { height: 20px !important; }
        .ml-2 { margin-left: 8px !important; }
        .max-w-2xl { max-width: 672px !important; }
        .mx-auto { margin-left: auto !important; margin-right: auto !important; }
        .bg-gradient-to-r { background: #f3f4f6 !important; }
        .from-blue-50 { background-color: #eff6ff !important; }
        .to-green-50 { background-color: #f0fdf4 !important; }
        .rounded-lg { border-radius: 8px !important; }
      `;

      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <!DOCTYPE html>
          <html dir="rtl">
            <head>
              <title>فاتورة ${processedSaleData.saleNumber}</title>
              <meta charset="UTF-8">
              <style>${printCSS}</style>
            </head>
            <body>
              ${printContent.innerHTML}
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.focus();
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);
      }
    }
  };

  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case 'cash': return 'نقداً';
      case 'card': return 'بطاقة ائتمان';
      case 'credit': return 'آجل';
      case 'mixed': return 'مختلط';
      default: return 'غير محدد';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'مكتملة';
      case 'pending': return 'معلقة';
      case 'cancelled': return 'ملغية';
      case 'refunded': return 'مسترد';
      default: return 'غير محدد';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="fixed inset-4 bg-white rounded-2xl shadow-2xl overflow-hidden">
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-4 flex items-center justify-between">
            <div className="flex items-center space-x-reverse space-x-3">
              <FileText className="w-6 h-6 text-white" />
              <h2 className="text-xl font-bold text-white">فاتورة احترافية</h2>
            </div>
            <div className="flex items-center space-x-reverse space-x-2">
              {!loading && (
                <button
                  onClick={handlePrint}
                  className="bg-white/20 hover:bg-white/30 p-2 rounded-lg transition-colors"
                >
                  <Printer className="w-5 h-5 text-white" />
                </button>
              )}
              <button
                onClick={onClose}
                className="bg-white/20 hover:bg-white/30 p-2 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-white" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">جاري تحميل بيانات الفاتورة...</p>
                </div>
              </div>
            ) : (
              <div
                id="professional-invoice-content"
                className="w-full max-w-2xl mx-auto bg-white"
                dir="rtl"
                style={{
                  fontFamily: 'Arial, sans-serif',
                  color: '#000',
                  lineHeight: '1.5',
                  fontSize: '14px',
                  padding: '40px',
                  border: '1px solid #e5e7eb'
                }}
              >
              {/* Invoice Header */}
              <div className="flex items-center justify-between mb-8">
                {/* Store Info */}
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 mb-1">
                    {storeInfo.name}
                  </h1>
                  <p className="text-sm text-gray-600">فاتورة مبيعات</p>
                  <div className="text-xs text-gray-500 mt-1">
                    <div>📍 {storeInfo.address}</div>
                    <div>📞 {storeInfo.phone}</div>
                  </div>
                </div>

                {/* Invoice Number */}
                <div className="text-left">
                  <div className="text-2xl font-bold text-gray-900">
                    #{processedSaleData.saleNumber}
                  </div>
                </div>
              </div>

              {/* Barcode */}
              <div className="barcode-container text-center mb-8" style={{ pageBreakInside: 'avoid' }}>
                <div style={{
                  display: 'inline-block',
                  padding: '10px',
                  backgroundColor: '#ffffff',
                  border: '1px solid #e5e7eb',
                  borderRadius: '4px',
                  minHeight: '80px'
                }}>
                  <BarcodeGenerator
                    value={processedSaleData.saleNumber}
                    width={250}
                    height={60}
                    displayValue={true}
                    fontSize={14}
                    textAlign="center"
                    textPosition="bottom"
                    background="#ffffff"
                    lineColor="#000000"
                  />
                  {/* نسخة احتياطية نصية للطباعة */}
                  <div style={{
                    marginTop: '5px',
                    fontSize: '12px',
                    fontFamily: 'monospace',
                    letterSpacing: '2px',
                    color: '#000000'
                  }}>
                    {processedSaleData.saleNumber}
                  </div>
                </div>
              </div>

              {/* Invoice Info */}
              <div className="grid grid-cols-2 gap-8 mb-8">
                {/* Left Side - Customer Info */}
                <div>
                  <div className="flex items-center mb-4">
                    <User className="w-5 h-5 ml-2 text-gray-600" />
                    <span className="font-semibold text-gray-800">اسم العميل:</span>
                  </div>
                  <div className="text-lg font-bold text-gray-900 mb-6">
                    {processedSaleData.customerName}
                  </div>
                </div>

                {/* Right Side - Date & Time */}
                <div className="text-left">
                  <div className="flex items-center justify-end mb-2">
                    <span className="font-semibold text-gray-800 ml-2">وقت الطلب:</span>
                    <Calendar className="w-5 h-5 text-gray-600" />
                  </div>
                  <div className="text-sm text-gray-600 mb-1">
                    {processedSaleData.date.toLocaleDateString('fr-FR')} - {processedSaleData.date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}
                  </div>
                </div>
              </div>

              {/* Items Table */}
              <div className="mb-8">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b-2 border-gray-300">
                      <th className="text-center py-3 px-4 font-semibold text-gray-700">المنتج</th>
                      <th className="text-center py-3 px-4 font-semibold text-gray-700">السعر</th>
                      <th className="text-center py-3 px-4 font-semibold text-gray-700">الكمية</th>
                      <th className="text-center py-3 px-4 font-semibold text-gray-700">الإجمالي</th>
                    </tr>
                  </thead>
                  <tbody>
                    {processedSaleData.items && processedSaleData.items.length > 0 ? (
                      processedSaleData.items.map((item: any, index: number) => {
                        const productName = item.product_name || item.productName || item.name || 'منتج غير محدد';
                        const unitPrice = parseFloat(item.unit_price || item.unitPrice || item.price || 0);
                        const quantity = parseInt(item.quantity || 0);
                        const totalPrice = parseFloat(item.total_price || item.totalPrice || (unitPrice * quantity) || 0);

                        return (
                          <tr key={item.id || index} className="border-b border-gray-200">
                            <td className="text-center py-4 px-4 font-medium text-gray-900">
                              {productName}
                            </td>
                            <td className="text-center py-4 px-4 text-gray-700">
                              {unitPrice.toFixed(2)}
                            </td>
                            <td className="text-center py-4 px-4 font-semibold text-gray-900">
                              {quantity}
                            </td>
                            <td className="text-center py-4 px-4 font-bold text-gray-900">
                              {totalPrice.toFixed(2)}
                            </td>
                          </tr>
                        );
                      })
                    ) : (
                      <tr>
                        <td colSpan={4} className="text-center py-8 text-gray-500">
                          لا توجد أصناف في هذه الفاتورة
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              {/* Total Section */}
              <div className="border-t-2 border-gray-300 pt-6 mb-8">
                <div className="flex justify-between items-center">
                  <span className="text-xl font-bold text-gray-900">الإجمالي:</span>
                  <span className="text-2xl font-bold text-gray-900">
                    {processedSaleData.total.toFixed(2)} دينار
                  </span>
                </div>
              </div>

              {/* Footer */}
              <div className="text-center text-sm text-gray-600">
                <p className="mb-2">
                  {processedSaleData.date.toLocaleDateString('fr-FR')} {processedSaleData.date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}
                </p>
                <div className="mt-4 p-3 bg-gradient-to-r from-blue-50 to-green-50 rounded-lg border border-blue-200">
                  <p className="text-blue-700 font-bold text-base mb-1">
                    🌟 شكراً لثقتكم بنا 🌟
                  </p>
                  <p className="text-green-600 font-medium text-sm">
                    نتطلع لخدمتكم مرة أخرى ونسعد بزيارتكم القادمة
                  </p>
                  <p className="text-blue-500 text-xs mt-2">
                    💙 رضاكم هو هدفنا الأول 💙
                  </p>
                </div>
              </div>
            </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfessionalInvoice;
