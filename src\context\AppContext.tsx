import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import apiService from '../services/api';

// واجهات البيانات الأساسية
export interface Product {
  id: string;
  name: string;
  price: number;
  cost: number;
  stock: number;
  minStock: number;
  barcode: string;
  category: string;
  description: string;
  supplier: string;
  unit: string;
  createdDate: Date;
  updatedDate: Date;
  isActive: boolean;
}

export interface Category {
  id: string;
  name: string;
  description: string;
  color: string;
  icon: string;
  createdDate: Date;
  isActive: boolean;
}

export interface Customer {
  id: string;
  name: string;
  phone: string;
  address: string;
  email: string;
  balance: number;
  credit_limit: number;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface CartItem {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  discount: number;
}

export interface Sale {
  id: string;
  saleNumber: string;
  date: Date;
  customerId?: string;
  items: CartItem[];
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
  paymentMethod: 'cash' | 'card' | 'credit' | 'balance' | 'mixed';
  status: 'completed' | 'pending' | 'cancelled' | 'refunded';
  // حقول نظام الدين الجديد
  debtStatus?: 'paid' | 'pending' | 'partial';
  isDebtSale?: boolean;
  debtInvoiceNumber?: string;
  customerName?: string;
}

export interface DashboardStats {
  todaySales: number;
  todayOrders: number;
  totalProducts: number;
  lowStockProducts: number;
  totalRevenue: number;
  totalProfit: number;
  topSellingProducts: Array<{
    productId: string;
    productName: string;
    quantity: number;
    revenue: number;
  }>;
  salesByCategory: Array<{
    categoryId: string;
    categoryName: string;
    sales: number;
    revenue: number;
  }>;
  recentSales: Sale[];
}

interface Settings {
  // إعدادات المتجر
  storeName: string;
  storeAddress: string;
  storePhone: string;
  storeEmail: string;
  storeTaxNumber: string;
  storeLogo: string;

  // إعدادات المستخدم
  userName: string;
  userEmail: string;
  userRole: string;

  // إعدادات النظام
  currency: string;
  language: string;
  timezone: string;
  dateFormat: string;

  // إعدادات الضرائب والمبيعات
  taxRate: number;
  enableTax: boolean;
  enableDiscount: boolean;
  enableBarcode: boolean;

  // إعدادات التنبيهات
  lowStockAlert: boolean;
  emailNotifications: boolean;
  soundNotifications: boolean;

  // إعدادات المظهر
  theme: string;
  primaryColor: string;
  fontSize: string;
}

interface PrinterSettings {
  // إعدادات الاتصال
  connectionType: string;
  printerName: string;
  ipAddress: string;
  port: number;
  bluetoothAddress: string;

  // إعدادات الطباعة
  paperWidth: number;
  paperType: string;
  printDensity: string;
  printSpeed: string;

  // إعدادات الفاتورة
  printLogo: boolean;
  printHeader: boolean;
  printFooter: boolean;
  printBarcode: boolean;
  printQRCode: boolean;

  // إعدادات متقدمة
  autoOpenCashDrawer: boolean;
  printSound: boolean;
  copies: number;
  cutPaper: boolean;

  // نص مخصص
  headerText: string;
  footerText: string;

  // إعدادات الخط
  fontSize: string;
  fontFamily: string;
  lineSpacing: string;
}

interface AppContextType {
  // البيانات
  products: Product[];
  categories: Category[];
  customers: Customer[];
  sales: Sale[];
  cart: CartItem[];
  dashboardStats: DashboardStats;

  // وظائف إدارة المنتجات
  addProduct: (product: Omit<Product, 'id' | 'createdDate' | 'updatedDate'>) => Promise<Product>;
  updateProduct: (id: string, updates: Partial<Product>) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;
  getProduct: (id: string) => Product | undefined;
  loadProducts: () => Promise<void>;

  // وظائف إدارة الفئات
  addCategory: (category: Omit<Category, 'id' | 'createdDate'>) => Promise<Category>;
  updateCategory: (id: string, updates: Partial<Category>) => Promise<void>;
  deleteCategory: (id: string) => Promise<void>;
  deleteAllCategories: () => Promise<void>;
  loadCategories: () => Promise<void>;

  // وظائف إدارة العملاء
  addCustomer: (customer: Omit<Customer, 'id' | 'created_at' | 'updated_at'>) => Promise<Customer>;
  updateCustomer: (id: string, updates: Partial<Customer>) => Promise<void>;
  deleteCustomer: (id: string) => Promise<void>;
  getCustomer: (id: string) => Customer | undefined;
  loadCustomers: () => Promise<void>;
  getDebtorCustomers: () => Customer[];
  getCreditorCustomers: () => Customer[];
  updateCustomerBalance: (customerId: string, amount: number, description: string) => Promise<number>;

  // وظائف إدارة السلة
  addToCart: (product: Product, quantity?: number) => void;
  removeFromCart: (productId: string) => void;
  updateCartQuantity: (productId: string, quantity: number) => void;
  clearCart: () => void;
  getCartTotal: () => number;

  // وظائف إدارة المبيعات
  completeSale: (saleData: any) => Promise<any>;
  loadSales: () => Promise<void>;
  updateSale: (id: string, updates: Partial<Sale>) => void;
  getSalesByDateRange: (startDate: Date, endDate: Date) => Sale[];

  // وظائف الإحصائيات
  refreshDashboardStats: () => void;
  getProductsByCategory: (categoryId: string) => Product[];
  getLowStockProducts: () => Product[];
  getTopSellingProducts: (limit?: number) => Product[];

  // الإعدادات
  settings: Settings;
  loadSettings: () => Promise<void>;
  updateSettings: (settings: Partial<Settings>) => void;

  // إعدادات الطابعة
  printerSettings: PrinterSettings;
  updatePrinterSettings: (settings: Partial<PrinterSettings>) => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export const AppProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // البيانات الأساسية (فارغة - ستأتي من قاعدة البيانات)
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [sales, setSales] = useState<Sale[]>([]);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [dashboardStats, setDashboardStats] = useState<DashboardStats>({
    todaySales: 0,
    todayOrders: 0,
    totalProducts: 0,
    lowStockProducts: 0,
    totalRevenue: 0,
    totalProfit: 0,
    topSellingProducts: [],
    salesByCategory: [],
    recentSales: []
  });

  // الإعدادات الافتراضية
  const [settings, setSettings] = useState<Settings>({
    // إعدادات المتجر
    storeName: '',
    storeAddress: '',
    storePhone: '',
    storeEmail: '',
    storeTaxNumber: '',
    storeLogo: '',

    // إعدادات المستخدم
    userName: '',
    userEmail: '',
    userRole: 'admin',

    // إعدادات النظام
    currency: 'DZD',
    language: 'ar',
    timezone: 'Africa/Algiers',
    dateFormat: 'DD/MM/YYYY',

    // إعدادات الضرائب والمبيعات
    taxRate: 19,
    enableTax: true,
    enableDiscount: true,
    enableBarcode: true,

    // إعدادات التنبيهات
    lowStockAlert: true,
    emailNotifications: false,
    soundNotifications: true,

    // إعدادات المظهر
    theme: 'dark',
    primaryColor: '#3b82f6',
    fontSize: 'medium'
  });

  const [printerSettings, setPrinterSettings] = useState<PrinterSettings>({
    // إعدادات الاتصال
    connectionType: 'usb',
    printerName: '',
    ipAddress: '',
    port: 9100,
    bluetoothAddress: '',

    // إعدادات الطباعة
    paperWidth: 58,
    paperType: 'thermal',
    printDensity: 'medium',
    printSpeed: 'medium',

    // إعدادات الفاتورة
    printLogo: true,
    printHeader: true,
    printFooter: true,
    printBarcode: true,
    printQRCode: false,

    // إعدادات متقدمة
    autoOpenCashDrawer: false,
    printSound: true,
    copies: 1,
    cutPaper: true,

    // نص مخصص
    headerText: '',
    footerText: 'شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى',

    // إعدادات الخط
    fontSize: 'medium',
    fontFamily: 'arial',
    lineSpacing: 'normal'
  });

  // وظائف إدارة المنتجات
  const addProduct = async (productData: Omit<Product, 'id' | 'createdDate' | 'updatedDate'>) => {
    try {
      // تحويل البيانات لتتوافق مع API
      const apiProductData = {
        name: productData.name,
        description: productData.description,
        barcode: productData.barcode,
        category_id: productData.category, // تحويل category إلى category_id
        price: productData.price,
        cost: productData.cost,
        stock: productData.stock,
        min_stock: productData.minStock, // تحويل minStock إلى min_stock
        unit: productData.unit
      };

      const newProduct = await apiService.createProduct(apiProductData);

      // تحويل البيانات من API إلى تنسيق React
      const reactProduct: Product = {
        id: newProduct.id,
        name: newProduct.name,
        description: newProduct.description || '',
        barcode: newProduct.barcode || '',
        category: newProduct.category_id || '',
        price: parseFloat(newProduct.price),
        cost: parseFloat(newProduct.cost),
        stock: newProduct.stock,
        minStock: newProduct.min_stock,
        unit: newProduct.unit,
        supplier: '', // مؤقت
        createdDate: new Date(newProduct.created_at),
        updatedDate: new Date(newProduct.updated_at),
        isActive: newProduct.is_active
      };

      setProducts(prev => [...prev, reactProduct]);
      refreshDashboardStats();
      return reactProduct;
    } catch (error) {
      console.error('خطأ في إضافة المنتج:', error);
      throw error;
    }
  };

  const updateProduct = async (id: string, updates: Partial<Product>) => {
    try {
      // تحويل البيانات لتتوافق مع API
      const apiUpdates = {
        name: updates.name,
        description: updates.description,
        barcode: updates.barcode,
        category_id: updates.category,
        price: updates.price,
        cost: updates.cost,
        stock: updates.stock,
        min_stock: updates.minStock,
        unit: updates.unit
      };

      const updatedProduct = await apiService.updateProduct(id, apiUpdates);

      // تحديث البيانات المحلية
      setProducts(prev => prev.map(product =>
        product.id === id
          ? {
              ...product,
              name: updatedProduct.name,
              description: updatedProduct.description || '',
              barcode: updatedProduct.barcode || '',
              category: updatedProduct.category_id || '',
              price: parseFloat(updatedProduct.price),
              cost: parseFloat(updatedProduct.cost),
              stock: updatedProduct.stock,
              minStock: updatedProduct.min_stock,
              unit: updatedProduct.unit,
              updatedDate: new Date(updatedProduct.updated_at),
              isActive: updatedProduct.is_active
            }
          : product
      ));
      refreshDashboardStats();
    } catch (error) {
      console.error('خطأ في تعديل المنتج:', error);
      throw error;
    }
  };

  const deleteProduct = async (id: string) => {
    try {
      await apiService.deleteProduct(id);
      setProducts(prev => prev.filter(product => product.id !== id));
      // حذف من السلة أيضاً
      setCart(prev => prev.filter(item => item.productId !== id));
      refreshDashboardStats();
    } catch (error) {
      console.error('خطأ في حذف المنتج:', error);
      throw error;
    }
  };

  const getProduct = (id: string) => {
    return products.find(product => product.id === id);
  };

  // تحميل المنتجات من API
  const loadProducts = async () => {
    try {
      const apiProducts = await apiService.getProducts();
      const reactProducts: Product[] = apiProducts.map((product: any) => ({
        id: product.id,
        name: product.name,
        description: product.description || '',
        barcode: product.barcode || '',
        category: product.category_id || '',
        price: parseFloat(product.price),
        cost: parseFloat(product.cost),
        stock: product.stock,
        minStock: product.min_stock,
        unit: product.unit,
        supplier: '', // مؤقت
        createdDate: new Date(product.created_at),
        updatedDate: new Date(product.updated_at),
        isActive: product.is_active
      }));
      setProducts(reactProducts);
    } catch (error) {
      console.error('خطأ في تحميل المنتجات:', error);
    }
  };

  // وظائف إدارة الفئات
  const addCategory = async (categoryData: Omit<Category, 'id' | 'createdDate'>) => {
    try {
      const newCategory = await apiService.createCategory(categoryData);
      const reactCategory: Category = {
        id: newCategory.id,
        name: newCategory.name,
        description: newCategory.description || '',
        color: newCategory.color,
        icon: newCategory.icon,
        createdDate: new Date(newCategory.created_at),
        isActive: newCategory.is_active
      };
      setCategories(prev => [...prev, reactCategory]);
      return reactCategory;
    } catch (error) {
      console.error('خطأ في إضافة الفئة:', error);
      throw error;
    }
  };

  const updateCategory = async (id: string, updates: Partial<Category>) => {
    try {
      const updatedCategory = await apiService.updateCategory(id, updates);
      setCategories(prev => prev.map(category =>
        category.id === id ? {
          ...category,
          name: updatedCategory.name,
          description: updatedCategory.description || '',
          color: updatedCategory.color,
          icon: updatedCategory.icon,
          isActive: updatedCategory.is_active
        } : category
      ));
    } catch (error) {
      console.error('خطأ في تعديل الفئة:', error);
      throw error;
    }
  };

  const deleteCategory = async (id: string) => {
    try {
      await apiService.deleteCategory(id);
      setCategories(prev => prev.filter(category => category.id !== id));
    } catch (error) {
      console.error('خطأ في حذف الفئة:', error);
      throw error;
    }
  };

  const deleteAllCategories = async () => {
    try {
      await apiService.deleteAllCategories();
      setCategories([]);
    } catch (error) {
      console.error('خطأ في حذف جميع الأقسام:', error);
      throw error;
    }
  };

  // تحميل الفئات من API
  const loadCategories = async () => {
    try {
      const apiCategories = await apiService.getCategories();
      const reactCategories: Category[] = apiCategories.map((category: any) => ({
        id: category.id,
        name: category.name,
        description: category.description || '',
        color: category.color,
        icon: category.icon,
        createdDate: new Date(category.created_at),
        isActive: category.is_active
      }));
      setCategories(reactCategories);
    } catch (error) {
      console.error('خطأ في تحميل الفئات:', error);
    }
  };

  // وظائف إدارة العملاء
  const addCustomer = async (customerData: Omit<Customer, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      const newCustomer = await apiService.createCustomer(customerData);
      const reactCustomer: Customer = {
        id: newCustomer.id,
        name: newCustomer.name,
        phone: newCustomer.phone || '',
        address: newCustomer.address || '',
        email: newCustomer.email || '',
        balance: parseFloat(newCustomer.balance) || 0,
        credit_limit: parseFloat(newCustomer.credit_limit) || 0,
        is_active: newCustomer.is_active,
        created_at: new Date(newCustomer.created_at),
        updated_at: new Date(newCustomer.updated_at)
      };
      setCustomers(prev => [...prev, reactCustomer]);
      return reactCustomer;
    } catch (error) {
      console.error('خطأ في إضافة العميل:', error);
      throw error;
    }
  };

  const updateCustomer = async (id: string, updates: Partial<Customer>) => {
    try {
      const updatedCustomer = await apiService.updateCustomer(id, updates);
      setCustomers(prev => prev.map(customer =>
        customer.id === id ? {
          ...customer,
          name: updatedCustomer.name,
          phone: updatedCustomer.phone || '',
          address: updatedCustomer.address || '',
          email: updatedCustomer.email || '',
          balance: parseFloat(updatedCustomer.balance) || 0,
          credit_limit: parseFloat(updatedCustomer.credit_limit) || 0,
          is_active: updatedCustomer.is_active,
          updated_at: new Date(updatedCustomer.updated_at)
        } : customer
      ));
    } catch (error) {
      console.error('خطأ في تعديل العميل:', error);
      throw error;
    }
  };

  const deleteCustomer = async (id: string) => {
    try {
      await apiService.deleteCustomer(id);
      setCustomers(prev => prev.filter(customer => customer.id !== id));
    } catch (error) {
      console.error('خطأ في حذف العميل:', error);
      throw error;
    }
  };

  const getCustomer = (id: string) => {
    return customers.find(customer => customer.id === id);
  };

  // تحميل العملاء من API
  const loadCustomers = async () => {
    try {
      const apiCustomers = await apiService.getCustomers();
      const reactCustomers: Customer[] = apiCustomers.map((customer: any) => ({
        id: customer.id,
        name: customer.name,
        phone: customer.phone || '',
        address: customer.address || '',
        email: customer.email || '',
        balance: parseFloat(customer.balance) || 0,
        credit_limit: parseFloat(customer.credit_limit) || 0,
        is_active: customer.is_active,
        created_at: new Date(customer.created_at),
        updated_at: new Date(customer.updated_at)
      }));
      setCustomers(reactCustomers);
    } catch (error) {
      console.error('خطأ في تحميل العملاء:', error);
    }
  };

  // وظائف حسابات العملاء
  const getDebtorCustomers = () => {
    return customers.filter(customer => customer.balance < 0);
  };

  const getCreditorCustomers = () => {
    return customers.filter(customer => customer.balance > 0);
  };

  const updateCustomerBalance = async (customerId: string, amount: number, description: string) => {
    try {
      const response = await apiService.updateCustomerBalance(customerId, amount, description);

      // تحديث البيانات المحلية
      setCustomers(prev => prev.map(customer =>
        customer.id === customerId ? {
          ...customer,
          balance: parseFloat(response.customer.balance),
          updated_at: new Date(response.customer.updated_at)
        } : customer
      ));

      return response.transaction.newBalance;
    } catch (error) {
      console.error('خطأ في تحديث رصيد العميل:', error);
      throw error;
    }
  };

  // وظائف إدارة السلة
  const addToCart = (product: Product, quantity: number = 1) => {
    const existingItem = cart.find(item => item.productId === product.id);

    if (existingItem) {
      setCart(prev => prev.map(item =>
        item.productId === product.id
          ? {
              ...item,
              quantity: item.quantity + quantity,
              totalPrice: (item.quantity + quantity) * item.unitPrice
            }
          : item
      ));
    } else {
      const newCartItem: CartItem = {
        id: Date.now().toString(),
        productId: product.id,
        productName: product.name,
        quantity,
        unitPrice: product.price,
        totalPrice: product.price * quantity,
        discount: 0
      };
      setCart(prev => [...prev, newCartItem]);
    }
  };

  const removeFromCart = (productId: string) => {
    setCart(prev => prev.filter(item => item.productId !== productId));
  };

  const updateCartQuantity = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(productId);
      return;
    }

    setCart(prev => prev.map(item =>
      item.productId === productId
        ? {
            ...item,
            quantity,
            totalPrice: quantity * item.unitPrice
          }
        : item
    ));
  };

  const clearCart = () => {
    setCart([]);
  };

  const getCartTotal = () => {
    return cart.reduce((total, item) => total + item.totalPrice, 0);
  };

  // وظائف إدارة المبيعات
  const completeSale = async (saleData: any) => {
    try {
      console.log('🔄 بدء عملية حفظ البيع...', saleData);

      let savedSale;

      // التحقق من نوع البيع - إذا كان آجل، استخدم endpoint خاص
      if (saleData.payment_method === 'credit') {
        console.log('💳 بيع آجل - سيتم إنشاء سجل دين');

        // التحقق من وجود عميل للبيع الآجل
        if (!saleData.customer_id) {
          throw new Error('يجب اختيار عميل للبيع الآجل');
        }

        // استخدام endpoint خاص للمبيعات الآجلة
        savedSale = await apiService.createCreditSale(saleData);
        console.log('✅ تم حفظ البيع الآجل وإنشاء سجل الدين:', savedSale);
      } else {
        // بيع عادي
        savedSale = await apiService.createSale(saleData);
        console.log('✅ تم حفظ البيع العادي:', savedSale);
      }

      // تحويل البيانات من API إلى تنسيق React
      const reactSale: Sale = {
        id: savedSale.id,
        saleNumber: savedSale.sale_number,
        date: new Date(savedSale.created_at),
        customerId: saleData.customer_id || undefined,
        items: [...cart],
        subtotal: parseFloat(savedSale.subtotal),
        tax: parseFloat(savedSale.tax_amount),
        discount: parseFloat(savedSale.discount_amount),
        total: parseFloat(savedSale.total_amount),
        paymentMethod: savedSale.payment_method,
        status: savedSale.status || 'completed',
        // إضافة معلومات الدين
        debtStatus: saleData.payment_method === 'credit' ? 'pending' : 'paid',
        isDebtSale: saleData.payment_method === 'credit',
        debtInvoiceNumber: savedSale.debt_invoice_number || null,
        customerName: savedSale.customer_name || null
      };

      setSales(prev => [reactSale, ...prev]);

      // تحديث رصيد العميل محلياً إذا تم الدفع بالرصيد
      if (saleData.customer_id && (saleData.payment_method === 'balance' || saleData.balance_amount > 0)) {
        const balanceUsed = saleData.payment_method === 'balance' ? saleData.total_amount : (saleData.balance_amount || 0);
        setCustomers(prev => prev.map(customer =>
          customer.id === saleData.customer_id ? {
            ...customer,
            balance: customer.balance - balanceUsed,
            updated_at: new Date()
          } : customer
        ));
        console.log(`💰 تم تحديث رصيد العميل محلياً: خصم ${balanceUsed} دج`);
      }

      clearCart();
      refreshDashboardStats();

      return savedSale;
    } catch (error) {
      console.error('خطأ في إتمام البيع:', error);
      throw error;
    }
  };

  // تحميل المبيعات من API مع دعم نظام الدين الجديد
  const loadSales = async () => {
    try {
      const response = await apiService.getSales();
      const apiSales = response.success ? response.sales : response;

      const reactSales: Sale[] = apiSales.map((sale: any) => ({
        id: sale.id,
        saleNumber: sale.sale_number,
        date: new Date(sale.created_at),
        customerId: sale.customer_id,
        items: [], // سيتم تحميلها عند الحاجة
        subtotal: parseFloat(sale.subtotal),
        tax: parseFloat(sale.tax_amount),
        discount: parseFloat(sale.discount_amount),
        total: parseFloat(sale.total_amount),
        paymentMethod: sale.payment_method || sale.paymentMethod,
        status: sale.status,
        // إضافة معلومات الدين الجديدة
        debtStatus: sale.debtStatus || (sale.is_debt_sale ? (sale.debt_status || 'pending') : 'paid'),
        isDebtSale: sale.isDebtSale || sale.is_debt_sale || false,
        debtInvoiceNumber: sale.debtInvoiceNumber || sale.debt_invoice_number || null,
        customerName: sale.customerName || sale.customer_name || null
      }));
      setSales(reactSales);
    } catch (error) {
      console.error('خطأ في تحميل المبيعات:', error);
    }
  };

  const updateSale = (id: string, updates: Partial<Sale>) => {
    setSales(prev => prev.map(sale =>
      sale.id === id ? { ...sale, ...updates } : sale
    ));
    refreshDashboardStats();
  };

  const getSalesByDateRange = (startDate: Date, endDate: Date) => {
    return sales.filter(sale => {
      const saleDate = new Date(sale.date);
      return saleDate >= startDate && saleDate <= endDate;
    });
  };

  // وظائف مساعدة
  const getProductsByCategory = (categoryId: string) => {
    return products.filter(product => product.category === categoryId);
  };

  const getLowStockProducts = () => {
    return products.filter(product => product.stock <= product.minStock);
  };

  const getTopSellingProducts = (limit: number = 5) => {
    const productSales = new Map<string, number>();

    sales.forEach(sale => {
      sale.items.forEach(item => {
        const current = productSales.get(item.productId) || 0;
        productSales.set(item.productId, current + item.quantity);
      });
    });

    return Array.from(productSales.entries())
      .map(([productId, quantity]) => {
        const product = getProduct(productId);
        return product ? { ...product, soldQuantity: quantity } : null;
      })
      .filter(Boolean)
      .sort((a, b) => (b?.soldQuantity || 0) - (a?.soldQuantity || 0))
      .slice(0, limit) as (Product & { soldQuantity: number })[];
  };

  // تحديث إحصائيات لوحة التحكم
  const refreshDashboardStats = async () => {
    try {
      console.log('🔄 بدء تحميل إحصائيات لوحة التحكم من API...');

      // تحميل الإحصائيات من API
      const dashboardResponse = await apiService.request('/financial/dashboard-stats');

      if (dashboardResponse.success) {
        console.log('✅ تم تحميل الإحصائيات من API بنجاح');
        const apiStats = dashboardResponse.stats;

        // استخدام البيانات من API
        const lowStockProducts = getLowStockProducts();
        const topSellingProducts = getTopSellingProducts();

        // مبيعات حسب الفئة (محلي)
        const salesByCategory = categories.map(category => {
          const categoryProducts = getProductsByCategory(category.id);
          let totalCategoryItems = 0;
          const categorySales = sales.reduce((sum, sale) => {
            const categoryItems = sale.items.filter(item =>
              categoryProducts.some(p => p.id === item.productId)
            );
            totalCategoryItems += categoryItems.length;
            return sum + categoryItems.reduce((itemSum, item) => itemSum + item.totalPrice, 0);
          }, 0);

          return {
            categoryId: category.id,
            categoryName: category.name,
            sales: totalCategoryItems,
            revenue: categorySales
          };
        });

        setDashboardStats({
          todaySales: apiStats.today.sales,
          todayOrders: apiStats.today.orders,
          totalProducts: apiStats.total.products,
          lowStockProducts: apiStats.total.low_stock_products,
          totalRevenue: apiStats.total.revenue,
          totalProfit: apiStats.total.profit,
          topSellingProducts: apiStats.top_products.map(product => ({
            productId: product.product_id,
            productName: product.product_name,
            quantity: product.quantity,
            revenue: product.revenue
          })),
          salesByCategory,
          recentSales: sales.slice(0, 10)
        });

        console.log('📊 تم تحديث إحصائيات لوحة التحكم من API:', {
          totalProfit: apiStats.total.profit,
          todaySales: apiStats.today.sales,
          totalRevenue: apiStats.total.revenue
        });

        return;
      }
    } catch (error) {
      console.error('❌ خطأ في تحميل الإحصائيات من API:', error);

      // تحقق من نوع الخطأ
      if (error instanceof Error && error.message.includes('خطأ في الاتصال بالخادم')) {
        console.warn('⚠️ الخادم الخلفي غير متاح - سيتم استخدام الحساب المحلي');
      } else {
        console.warn('⚠️ خطأ في API - سيتم استخدام الحساب المحلي');
      }
    }

    // Fallback: حساب محلي في حالة فشل API
    console.log('🔄 استخدام الحساب المحلي للإحصائيات...');

    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);

    const todaySalesData = getSalesByDateRange(startOfDay, endOfDay);
    const lowStockProducts = getLowStockProducts();
    const topSellingProducts = getTopSellingProducts();

    const todayRevenue = todaySalesData.reduce((sum, sale) => sum + sale.total, 0);
    const totalRevenue = sales.reduce((sum, sale) => sum + sale.total, 0);

    // حساب الربح محلياً
    const totalProfit = sales.reduce((sum, sale) => {
      const saleProfit = sale.items.reduce((itemSum, item) => {
        const product = getProduct(item.productId);
        const profit = product ? (item.unitPrice - product.cost) * item.quantity : 0;
        return itemSum + profit;
      }, 0);
      return sum + saleProfit;
    }, 0);

    // مبيعات حسب الفئة
    const salesByCategory = categories.map(category => {
      const categoryProducts = getProductsByCategory(category.id);
      const categorySales = sales.reduce((sum, sale) => {
        const categoryItems = sale.items.filter(item =>
          categoryProducts.some(p => p.id === item.productId)
        );
        return sum + categoryItems.reduce((itemSum, item) => itemSum + item.totalPrice, 0);
      }, 0);

      return {
        categoryId: category.id,
        categoryName: category.name,
        sales: categoryProducts.reduce((sum, product) => {
          const productSales = sales.reduce((saleSum, sale) => {
            const productItems = sale.items.filter(item => item.productId === product.id);
            return saleSum + productItems.reduce((itemSum, item) => itemSum + item.quantity, 0);
          }, 0);
          return sum + productSales;
        }, 0),
        revenue: categorySales
      };
    });

    setDashboardStats({
      todaySales: todayRevenue,
      todayOrders: todaySalesData.length,
      totalProducts: products.length,
      lowStockProducts: lowStockProducts.length,
      totalRevenue,
      totalProfit,
      topSellingProducts: topSellingProducts.map(product => ({
        productId: product.id,
        productName: product.name,
        quantity: product.soldQuantity,
        revenue: product.soldQuantity * product.price
      })),
      salesByCategory,
      recentSales: sales.slice(0, 10)
    });
  };

  // وظائف الإعدادات
  const loadSettings = async () => {
    try {
      const response = await fetch('/api/settings');
      if (response.ok) {
        const dbData = await response.json();
        if (dbData && Object.keys(dbData).length > 0) {
          // تحويل أسماء الحقول من snake_case إلى camelCase
          const settingsData = {
            storeName: dbData.store_name || '',
            storeAddress: dbData.store_address || '',
            storePhone: dbData.store_phone || '',
            storeEmail: dbData.store_email || '',
            storeTaxNumber: dbData.store_tax_number || '',
            storeLogo: dbData.store_logo || '',
            userName: dbData.user_name || '',
            userEmail: dbData.user_email || '',
            userRole: dbData.user_role || 'admin',
            currency: dbData.currency || 'DZD',
            language: dbData.language || 'ar',
            timezone: dbData.timezone || 'Africa/Algiers',
            dateFormat: dbData.date_format || 'DD/MM/YYYY',
            taxRate: dbData.tax_rate || 19,
            enableTax: dbData.enable_tax !== undefined ? dbData.enable_tax : true,
            enableDiscount: dbData.enable_discount !== undefined ? dbData.enable_discount : true,
            enableBarcode: dbData.enable_barcode !== undefined ? dbData.enable_barcode : true,
            lowStockAlert: dbData.low_stock_alert !== undefined ? dbData.low_stock_alert : true,
            emailNotifications: dbData.email_notifications !== undefined ? dbData.email_notifications : false,
            soundNotifications: dbData.sound_notifications !== undefined ? dbData.sound_notifications : true,
            theme: dbData.theme || 'dark',
            primaryColor: dbData.primary_color || '#3b82f6',
            fontSize: dbData.font_size || 'medium'
          };
          setSettings(prev => ({ ...prev, ...settingsData }));
        }
      }
    } catch (error) {
      console.error('خطأ في تحميل الإعدادات:', error);
    }
  };

  const updateSettings = (newSettings: Partial<Settings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  const updatePrinterSettings = (newSettings: Partial<PrinterSettings>) => {
    setPrinterSettings(prev => ({ ...prev, ...newSettings }));
  };

  // تحميل البيانات عند بدء التطبيق
  useEffect(() => {
    const initializeData = async () => {
      try {
        await Promise.all([
          loadSettings(),
          loadCategories(),
          loadProducts(),
          loadCustomers(),
          loadSales()
        ]);
      } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
      }
    };

    initializeData();
  }, []);

  // تحديث الإحصائيات عند تغيير البيانات
  useEffect(() => {
    refreshDashboardStats().catch(error => {
      console.error('خطأ في تحديث إحصائيات لوحة التحكم:', error);
    });
  }, [products, sales, categories]);

  const value: AppContextType = {
    // البيانات
    products,
    categories,
    customers,
    sales,
    cart,
    dashboardStats,

    // وظائف إدارة المنتجات
    addProduct,
    updateProduct,
    deleteProduct,
    getProduct,
    loadProducts,

    // وظائف إدارة الفئات
    addCategory,
    updateCategory,
    deleteCategory,
    deleteAllCategories,
    loadCategories,

    // وظائف إدارة العملاء
    addCustomer,
    updateCustomer,
    deleteCustomer,
    getCustomer,
    loadCustomers,
    getDebtorCustomers,
    getCreditorCustomers,
    updateCustomerBalance,

    // وظائف إدارة السلة
    addToCart,
    removeFromCart,
    updateCartQuantity,
    clearCart,
    getCartTotal,

    // وظائف إدارة المبيعات
    completeSale,
    loadSales,
    updateSale,
    getSalesByDateRange,

    // وظائف الإحصائيات
    refreshDashboardStats,
    getProductsByCategory,
    getLowStockProducts,
    getTopSellingProducts,

    // الإعدادات
    settings,
    loadSettings,
    updateSettings,

    // إعدادات الطابعة
    printerSettings,
    updatePrinterSettings
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

export const useApp = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};