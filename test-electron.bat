@echo off
chcp 65001 > nul
title 🧪 اختبار Electron - كشير توسار
color 0B

cd /d "%~dp0"

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🧪 اختبار Electron                       ║
echo ║                     كشير توسار                             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM التحقق من المتطلبات
echo 🔍 التحقق من المتطلبات...

REM التحقق من Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo 📥 يرجى تحميل وتثبيت Node.js من: https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js متوفر

REM التحقق من npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متوفر
    pause
    exit /b 1
)
echo ✅ npm متوفر

echo.
echo 📋 خيارات الاختبار:
echo.
echo 1. 🧪 اختبار Electron (وضع التطوير)
echo 2. 🏗️ بناء واختبار التطبيق
echo 3. 🔧 تثبيت Electron فقط
echo 4. 🖥️ تشغيل Frontend + Backend منفصلين
echo 5. 🔍 فحص حالة النظام
echo 6. 🧹 تنظيف وإعادة تثبيت
echo 7. 📊 عرض معلومات المشروع
echo.
set /p choice=🔢 اختر الرقم المطلوب (1-7): 

if "%choice%"=="1" goto test_electron
if "%choice%"=="2" goto build_and_test
if "%choice%"=="3" goto install_electron
if "%choice%"=="4" goto run_separate
if "%choice%"=="5" goto system_check
if "%choice%"=="6" goto clean_install
if "%choice%"=="7" goto project_info
goto invalid_choice

:test_electron
echo.
echo 🧪 اختبار Electron في وضع التطوير...
echo.

if not exist "node_modules\electron" (
    echo 📦 تثبيت Electron...
    call npm install electron --save-dev
    if %errorlevel% neq 0 goto error_exit
)

echo 🔧 تحضير ملفات Electron...
copy "package-electron-fixed.json" "package.json" > nul

echo 🚀 بدء تشغيل Backend...
start /B cmd /c "cd backend && npm start"
timeout /t 3 /nobreak > nul

echo 🌐 بدء تشغيل Frontend...
start /B cmd /c "npm run dev"
timeout /t 5 /nobreak > nul

echo 🖥️ تشغيل Electron...
set NODE_ENV=development
call npx electron .

goto success

:build_and_test
echo.
echo 🏗️ بناء واختبار التطبيق...
echo.

echo 📦 بناء Frontend...
call npm run build
if %errorlevel% neq 0 goto error_exit

if not exist "node_modules\electron" (
    echo 📦 تثبيت Electron...
    call npm install electron --save-dev
    if %errorlevel% neq 0 goto error_exit
)

echo 🔧 تحضير ملفات Electron...
copy "package-electron-fixed.json" "package.json" > nul

echo 🚀 بدء تشغيل Backend...
start /B cmd /c "cd backend && npm start"
timeout /t 3 /nobreak > nul

echo 🖥️ تشغيل Electron...
call npx electron .

goto success

:install_electron
echo.
echo 📦 تثبيت Electron...
call npm install electron electron-builder --save-dev
if %errorlevel% neq 0 goto error_exit

echo ✅ تم تثبيت Electron بنجاح
echo.
echo 🚀 يمكنك الآن:
echo - تشغيل: test-electron.bat
echo - بناء: quick-build.bat
goto success

:run_separate
echo.
echo 🖥️ تشغيل Frontend + Backend منفصلين...
echo.

start cmd /k "cd backend && npm start"
start cmd /k "npm run dev"

echo.
echo ✅ تم تشغيل الخدمات منفصلة
echo 📋 الروابط:
echo - Frontend: http://localhost:3000
echo - Backend: http://localhost:5003
goto success

:system_check
echo.
echo 🔍 فحص حالة النظام...
echo.

echo 📋 معلومات Node.js:
node --version
echo.

echo 📋 معلومات npm:
npm --version
echo.

echo 🔌 فحص المنافذ:
netstat -an | findstr ":3000" >nul
if %errorlevel% equ 0 (echo ⚠️  المنفذ 3000 مستخدم) else (echo ✅ المنفذ 3000 متاح)
netstat -an | findstr ":5003" >nul
if %errorlevel% equ 0 (echo ⚠️  المنفذ 5003 مستخدم) else (echo ✅ المنفذ 5003 متاح)

echo.
echo 📁 فحص الملفات المهمة:
for %%f in ("package.json" "electron-main.js" "backend\server.js" "src\main.tsx") do (
    if exist "%%f" (echo ✅ %%~nxf موجود) else (echo ❌ %%~nxf مفقود)
)

echo.
echo 📦 فحص التبعيات:
if exist "node_modules" (
    echo ✅ node_modules موجود
    if exist "node_modules\electron" (echo ✅ Electron مثبت) else (echo ❌ Electron غير مثبت)
    if exist "node_modules\react" (echo ✅ React مثبت) else (echo ❌ React غير مثبت)
) else (
    echo ❌ node_modules مفقود
)

goto success

:clean_install
echo.
echo 🧹 تنظيف وإعادة تثبيت...
echo.

echo ⚠️  هذا سيحذف node_modules ويعيد تثبيت كل شيء
set /p confirm="هل أنت متأكد؟ (y/n): "
if /i not "%confirm%"=="y" goto success

if exist "node_modules" rmdir /s /q "node_modules"
if exist "package-lock.json" del "package-lock.json"

echo 📦 إعادة تثبيت التبعيات...
call npm install
if %errorlevel% neq 0 goto error_exit

echo 📦 تثبيت Electron...
call npm install electron electron-builder --save-dev
if %errorlevel% neq 0 goto error_exit

echo ✅ تم التنظيف وإعادة التثبيت بنجاح!
goto success

:project_info
echo.
echo 📊 معلومات المشروع...
echo.

echo 📋 اسم المشروع: كشير توسار - نظام نقاط البيع
echo 🏷️  الإصدار: 1.0.0
echo 👥 المطور: فريق توسار
echo.
echo 🏗️ التقنيات المستخدمة:
echo - Frontend: React + TypeScript + Vite
echo - Backend: Node.js + Express + PostgreSQL
echo - Desktop: Electron
echo - Styling: Tailwind CSS
echo.
echo 📁 هيكل المشروع:
echo ├── src/
echo ├── backend/
echo ├── assets/
echo ├── database/
echo └── dist/
echo.
echo 🔗 المنافذ:
echo - Frontend: http://localhost:3000
echo - Backend API: http://localhost:5003
echo.
echo 📋 الأوامر المتاحة:
echo - npm run dev
echo - npm run build
echo - test-electron.bat
echo - quick-build.bat
goto success

:invalid_choice
echo ❌ اختيار غير صحيح
pause
exit /b 1

:error_exit
echo.
echo ❌ حدث خطأ أثناء الاختبار!
echo.
echo 🔧 حلول مقترحة:
echo 1. تأكد من اتصال الإنترنت
echo 2. أغلق مضاد الفيروسات مؤقتًا
echo 3. شغّل CMD كمسؤول
echo 4. تأكد من عدم تشغيل خدمات أخرى على نفس المنافذ
pause
exit /b 1

:success
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      🎉 نجح الاختبار!                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📋 ملاحظات:
echo - إذا ظهر التطبيق بنجاح، فكل شيء يعمل بشكل صحيح
echo - للإغلاق: أغلق نافذة Electron ونافذة CMD المفتوحة
pause
