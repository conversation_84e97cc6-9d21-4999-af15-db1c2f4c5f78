@echo off
chcp 65001 > nul
title 🧪 اختبار Electron - كشير توسار
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🧪 اختبار Electron                       ║
echo ║                     كشير توسار                             ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM التحقق من المتطلبات
echo 🔍 التحقق من المتطلبات...

REM التحقق من Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo 📥 يرجى تحميل وتثبيت Node.js من: https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js متوفر

REM التحقق من npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متوفر
    pause
    exit /b 1
)
echo ✅ npm متوفر

echo.
echo 📋 خيارات الاختبار:
echo.
echo 1. 🧪 اختبار Electron (وضع التطوير)
echo 2. 🏗️ بناء واختبار التطبيق
echo 3. 🔧 تثبيت Electron فقط
echo 4. 🖥️ تشغيل Frontend + Backend منفصلين
echo.
set /p choice="اختر الرقم المطلوب (1-4): "

if "%choice%"=="1" goto test_electron
if "%choice%"=="2" goto build_and_test
if "%choice%"=="3" goto install_electron
if "%choice%"=="4" goto run_separate
goto invalid_choice

:test_electron
echo.
echo 🧪 اختبار Electron في وضع التطوير...
echo.

REM تثبيت Electron إذا لم يكن موجوداً
if not exist "node_modules\electron" (
    echo 📦 تثبيت Electron...
    call npm install electron --save-dev
    if %errorlevel% neq 0 goto error_exit
)

REM تحضير ملفات Electron
echo 🔧 تحضير ملفات Electron...
copy package-electron-fixed.json package.json > nul

REM بدء تشغيل Backend في الخلفية
echo 🚀 بدء تشغيل Backend...
start /B cmd /c "cd backend && npm start"

REM انتظار قليل لبدء Backend
timeout /t 3 /nobreak > nul

REM بدء تشغيل Frontend في الخلفية
echo 🌐 بدء تشغيل Frontend...
start /B cmd /c "npm run dev"

REM انتظار قليل لبدء Frontend
timeout /t 5 /nobreak > nul

REM تشغيل Electron
echo 🖥️ تشغيل Electron...
set NODE_ENV=development
call npx electron .

goto success

:build_and_test
echo.
echo 🏗️ بناء واختبار التطبيق...
echo.

REM بناء Frontend
echo 📦 بناء Frontend...
call npm run build
if %errorlevel% neq 0 goto error_exit

REM تثبيت Electron
if not exist "node_modules\electron" (
    echo 📦 تثبيت Electron...
    call npm install electron --save-dev
    if %errorlevel% neq 0 goto error_exit
)

REM تحضير ملفات Electron
echo 🔧 تحضير ملفات Electron...
copy package-electron-fixed.json package.json > nul

REM بدء تشغيل Backend
echo 🚀 بدء تشغيل Backend...
start /B cmd /c "cd backend && npm start"

REM انتظار قليل
timeout /t 3 /nobreak > nul

REM تشغيل Electron
echo 🖥️ تشغيل Electron...
call npx electron .

goto success

:install_electron
echo.
echo 📦 تثبيت Electron...
call npm install electron electron-builder --save-dev
if %errorlevel% neq 0 goto error_exit

echo ✅ تم تثبيت Electron بنجاح
echo.
echo 🚀 يمكنك الآن:
echo 1. تشغيل: test-electron.bat
echo 2. بناء: quick-build.bat
echo.
goto success

:run_separate
echo.
echo 🖥️ تشغيل Frontend + Backend منفصلين...
echo.

echo 🚀 بدء تشغيل Backend...
start cmd /k "cd backend && npm start"

echo 🌐 بدء تشغيل Frontend...
start cmd /k "npm run dev"

echo.
echo ✅ تم تشغيل الخدمات منفصلة
echo 📋 الروابط:
echo - Frontend: http://localhost:3000
echo - Backend: http://localhost:5003
echo.
goto success

:invalid_choice
echo ❌ اختيار غير صحيح
pause
exit /b 1

:error_exit
echo.
echo ❌ حدث خطأ أثناء الاختبار!
echo.
echo 🔧 حلول مقترحة:
echo 1. تأكد من اتصال الإنترنت
echo 2. أغلق برامج مكافحة الفيروسات مؤقتاً
echo 3. شغل Command Prompt كمدير
echo 4. تأكد من عدم تشغيل خدمات أخرى على نفس المنافذ
echo.
pause
exit /b 1

:success
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      🎉 نجح الاختبار!                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📋 ملاحظات:
echo - إذا ظهر التطبيق بنجاح، فكل شيء يعمل بشكل صحيح
echo - يمكنك الآن بناء ملف التثبيت باستخدام: quick-build.bat
echo - للإغلاق: أغلق نافذة Electron وجميع نوافذ Command Prompt
echo.
pause
