const pool = require('./database');

async function removeStockConstraint() {
  try {
    console.log('🔧 إزالة قيد فحص المخزون...');

    const client = await pool.connect();

    // البحث عن قيود المخزون
    const constraintsResult = await client.query(`
      SELECT conname, contype 
      FROM pg_constraint 
      WHERE conrelid = 'products'::regclass 
      AND contype = 'c'
      AND conname LIKE '%stock%'
    `);

    console.log('📋 قيود المخزون الموجودة:');
    constraintsResult.rows.forEach(constraint => {
      console.log(`  - ${constraint.conname}`);
    });

    // إزالة قيد فحص المخزون إذا كان موجوداً
    try {
      await client.query('ALTER TABLE products DROP CONSTRAINT IF EXISTS products_stock_check');
      console.log('✅ تم إزالة قيد products_stock_check');
    } catch (error) {
      console.log('⚠️ قيد products_stock_check غير موجود أو تم إزالته مسبقاً');
    }

    // إزالة أي قيود أخرى للمخزون
    try {
      await client.query('ALTER TABLE products DROP CONSTRAINT IF EXISTS products_stock_check_constraint');
      console.log('✅ تم إزالة قيد products_stock_check_constraint');
    } catch (error) {
      console.log('⚠️ قيد products_stock_check_constraint غير موجود');
    }

    // التحقق من القيود المتبقية
    const remainingConstraints = await client.query(`
      SELECT conname, contype 
      FROM pg_constraint 
      WHERE conrelid = 'products'::regclass 
      AND contype = 'c'
    `);

    console.log('\n📋 القيود المتبقية في جدول products:');
    remainingConstraints.rows.forEach(constraint => {
      console.log(`  - ${constraint.conname}`);
    });

    // تحديث المنتجات التي لديها مخزون سالب إلى صفر
    const updateResult = await client.query(`
      UPDATE products 
      SET stock = 0 
      WHERE stock < 0
      RETURNING name, stock
    `);

    if (updateResult.rows.length > 0) {
      console.log('\n🔄 تم تحديث المنتجات التي لديها مخزون سالب:');
      updateResult.rows.forEach(product => {
        console.log(`  - ${product.name}: المخزون = ${product.stock}`);
      });
    } else {
      console.log('\n✅ جميع المنتجات لديها مخزون صحيح');
    }

    client.release();
    console.log('\n🎉 تم إصلاح مشكلة قيد المخزون بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في إزالة قيد المخزون:', error.message);
    console.error('تفاصيل الخطأ:', error.stack);
  } finally {
    process.exit();
  }
}

removeStockConstraint();
