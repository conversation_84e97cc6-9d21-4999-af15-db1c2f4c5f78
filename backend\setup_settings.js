const { Pool } = require('pg');

const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'pos_system_db',
  user: 'postgres',
  password: 'toossar',
  schema: 'pos_system'
});

async function setupSettings() {
  try {
    console.log('🔧 إنشاء جدول الإعدادات...');
    
    // إنشاء جدول الإعدادات
    await pool.query(`
      CREATE TABLE IF NOT EXISTS pos_system.settings (
        id SERIAL PRIMARY KEY,
        store_name VARCHAR(255) NOT NULL DEFAULT 'متجر توسار',
        store_address TEXT,
        store_phone VARCHAR(50),
        store_email VARCHAR(255),
        store_tax_number VARCHAR(100),
        store_logo TEXT,
        user_name VARCHAR(255) NOT NULL DEFAULT 'admin',
        user_email VARCHAR(255),
        user_role VARCHAR(50) DEFAULT 'admin',
        currency VARCHAR(10) DEFAULT 'DZD',
        language VARCHAR(10) DEFAULT 'ar',
        timezone VARCHAR(50) DEFAULT 'Africa/Algiers',
        date_format VARCHAR(20) DEFAULT 'DD/MM/YYYY',
        tax_rate NUMERIC(5,2) DEFAULT 19.00,
        enable_tax BOOLEAN DEFAULT true,
        enable_discount BOOLEAN DEFAULT true,
        enable_barcode BOOLEAN DEFAULT true,
        low_stock_alert INTEGER DEFAULT 10,
        email_notifications BOOLEAN DEFAULT false,
        sound_notifications BOOLEAN DEFAULT true,
        theme VARCHAR(20) DEFAULT 'light',
        primary_color VARCHAR(7) DEFAULT '#4CAF50',
        font_size VARCHAR(10) DEFAULT 'medium',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    console.log('✅ تم إنشاء جدول الإعدادات');
    
    // التحقق من وجود بيانات
    const existingData = await pool.query('SELECT COUNT(*) FROM pos_system.settings');
    
    if (existingData.rows[0].count === '0') {
      console.log('📝 إدراج البيانات الافتراضية...');
      
      await pool.query(`
        INSERT INTO pos_system.settings (
          store_name, store_address, store_phone, store_email, 
          user_name, user_email, currency, language
        ) VALUES (
          'متجر توسار', 'الجزائر', '+213', '<EMAIL>',
          'admin', '<EMAIL>', 'DZD', 'ar'
        )
      `);
      
      console.log('✅ تم إدراج البيانات الافتراضية');
    } else {
      console.log('ℹ️ البيانات موجودة مسبقاً');
    }
    
    // إنشاء جدول إعدادات الطابعة
    await pool.query(`
      CREATE TABLE IF NOT EXISTS pos_system.printer_settings (
        id SERIAL PRIMARY KEY,
        printer_name VARCHAR(255) DEFAULT 'Default Printer',
        paper_size VARCHAR(50) DEFAULT 'A4',
        print_header BOOLEAN DEFAULT true,
        print_footer BOOLEAN DEFAULT true,
        auto_print BOOLEAN DEFAULT false,
        copies INTEGER DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    console.log('✅ تم إنشاء جدول إعدادات الطابعة');
    
    // التحقق من وجود إعدادات الطابعة
    const existingPrinter = await pool.query('SELECT COUNT(*) FROM pos_system.printer_settings');
    
    if (existingPrinter.rows[0].count === '0') {
      await pool.query(`
        INSERT INTO pos_system.printer_settings (
          printer_name, paper_size, print_header, print_footer
        ) VALUES (
          'Default Printer', 'A4', true, true
        )
      `);
      
      console.log('✅ تم إدراج إعدادات الطابعة الافتراضية');
    }
    
    console.log('🎉 تم إعداد جداول الإعدادات بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في إعداد الإعدادات:', error);
  } finally {
    await pool.end();
  }
}

setupSettings();
