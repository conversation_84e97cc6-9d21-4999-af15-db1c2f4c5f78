@echo off
echo إنشاء enum payment_method...
echo.

set PGPASSWORD=toossar
psql -h localhost -p 5432 -U postgres -d pos_system -c "CREATE TYPE payment_method AS ENUM ('cash', 'card', 'credit', 'mixed', 'balance');"

echo.
echo التحقق من النتيجة...
psql -h localhost -p 5432 -U postgres -d pos_system -c "SELECT enumlabel as payment_methods FROM pg_enum WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'payment_method') ORDER BY enumsortorder;"

echo.
echo تم الانتهاء! اضغط أي مفتاح للخروج...
pause > nul
