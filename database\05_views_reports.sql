-- Views للتقارير والإحصائيات
-- Views for reports and statistics

-- عرض إحصائيات المبيعات اليومية
CREATE OR REPLACE VIEW daily_sales_stats AS
SELECT 
    DATE(created_at) as sale_date,
    COUNT(*) as total_orders,
    SUM(total_amount) as total_revenue,
    SUM(total_amount - (
        SELECT SUM(si.quantity * p.cost) 
        FROM sale_items si 
        JOIN products p ON si.product_id = p.id 
        WHERE si.sale_id = s.id
    )) as total_profit,
    AVG(total_amount) as average_order_value,
    COUNT(DISTINCT customer_id) as unique_customers
FROM sales s
WHERE status = 'completed'
GROUP BY DATE(created_at)
ORDER BY sale_date DESC;

-- عرض المنتجات الأكثر مبيعاً
CREATE OR REPLACE VIEW top_selling_products AS
SELECT 
    p.id,
    p.name,
    p.barcode,
    c.name as category_name,
    SUM(si.quantity) as total_sold,
    SUM(si.total_price) as total_revenue,
    SUM(si.quantity * (si.unit_price - p.cost)) as total_profit,
    COUNT(DISTINCT si.sale_id) as order_count
FROM products p
JOIN sale_items si ON p.id = si.product_id
JOIN sales s ON si.sale_id = s.id
LEFT JOIN categories c ON p.category_id = c.id
WHERE s.status = 'completed'
GROUP BY p.id, p.name, p.barcode, c.name
ORDER BY total_sold DESC;

-- عرض المنتجات منخفضة المخزون
CREATE OR REPLACE VIEW low_stock_products AS
SELECT 
    p.id,
    p.name,
    p.barcode,
    c.name as category_name,
    p.stock,
    p.min_stock,
    (p.min_stock - p.stock) as shortage_quantity,
    p.price,
    (p.min_stock - p.stock) * p.cost as reorder_cost
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
WHERE p.stock <= p.min_stock AND p.is_active = true
ORDER BY (p.min_stock - p.stock) DESC;

-- عرض إحصائيات العملاء
CREATE OR REPLACE VIEW customer_statistics AS
SELECT 
    c.id,
    c.name,
    c.phone,
    c.balance,
    COUNT(s.id) as total_orders,
    COALESCE(SUM(s.total_amount), 0) as total_spent,
    COALESCE(AVG(s.total_amount), 0) as average_order_value,
    MAX(s.created_at) as last_order_date,
    COALESCE(SUM(cd.remaining_amount), 0) as outstanding_debt
FROM customers c
LEFT JOIN sales s ON c.id = s.customer_id AND s.status = 'completed'
LEFT JOIN customer_debts cd ON c.id = cd.customer_id AND cd.is_paid = false
GROUP BY c.id, c.name, c.phone, c.balance
ORDER BY total_spent DESC;

-- عرض المبيعات حسب الفئات
CREATE OR REPLACE VIEW sales_by_category AS
SELECT 
    c.id as category_id,
    c.name as category_name,
    c.color,
    COUNT(DISTINCT si.sale_id) as total_orders,
    SUM(si.quantity) as total_quantity_sold,
    SUM(si.total_price) as total_revenue,
    SUM(si.quantity * (si.unit_price - p.cost)) as total_profit,
    COUNT(DISTINCT si.product_id) as products_sold
FROM categories c
JOIN products p ON c.id = p.category_id
JOIN sale_items si ON p.id = si.product_id
JOIN sales s ON si.sale_id = s.id
WHERE s.status = 'completed'
GROUP BY c.id, c.name, c.color
ORDER BY total_revenue DESC;

-- عرض الملخص المالي
CREATE OR REPLACE VIEW financial_summary AS
SELECT 
    'اليوم' as period,
    CURRENT_DATE as date_from,
    CURRENT_DATE as date_to,
    COALESCE(SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END), 0) as total_income,
    COALESCE(SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END), 0) as total_expenses,
    COALESCE(SUM(CASE WHEN type = 'income' THEN amount ELSE -amount END), 0) as net_profit
FROM financial_transactions
WHERE transaction_date = CURRENT_DATE

UNION ALL

SELECT 
    'هذا الشهر' as period,
    DATE_TRUNC('month', CURRENT_DATE) as date_from,
    CURRENT_DATE as date_to,
    COALESCE(SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END), 0) as total_income,
    COALESCE(SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END), 0) as total_expenses,
    COALESCE(SUM(CASE WHEN type = 'income' THEN amount ELSE -amount END), 0) as net_profit
FROM financial_transactions
WHERE transaction_date >= DATE_TRUNC('month', CURRENT_DATE)

UNION ALL

SELECT 
    'هذا العام' as period,
    DATE_TRUNC('year', CURRENT_DATE) as date_from,
    CURRENT_DATE as date_to,
    COALESCE(SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END), 0) as total_income,
    COALESCE(SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END), 0) as total_expenses,
    COALESCE(SUM(CASE WHEN type = 'income' THEN amount ELSE -amount END), 0) as net_profit
FROM financial_transactions
WHERE transaction_date >= DATE_TRUNC('year', CURRENT_DATE);

-- عرض حركة المخزون
CREATE OR REPLACE VIEW inventory_movement_summary AS
SELECT 
    p.id as product_id,
    p.name as product_name,
    p.barcode,
    c.name as category_name,
    p.stock as current_stock,
    COALESCE(SUM(CASE WHEN im.movement_type = 'in' THEN im.quantity ELSE 0 END), 0) as total_in,
    COALESCE(SUM(CASE WHEN im.movement_type = 'out' THEN im.quantity ELSE 0 END), 0) as total_out,
    COALESCE(SUM(CASE WHEN im.movement_type = 'adjustment' THEN im.quantity ELSE 0 END), 0) as total_adjustments
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN inventory_movements im ON p.id = im.product_id
WHERE p.is_active = true
GROUP BY p.id, p.name, p.barcode, c.name, p.stock
ORDER BY p.name;

-- عرض الديون المستحقة
CREATE OR REPLACE VIEW outstanding_debts AS
SELECT 
    cd.id,
    c.name as customer_name,
    c.phone as customer_phone,
    cd.amount as debt_amount,
    cd.paid_amount,
    cd.remaining_amount,
    cd.debt_date,
    cd.due_date,
    CASE 
        WHEN cd.due_date < CURRENT_DATE THEN 'متأخر'
        WHEN cd.due_date <= CURRENT_DATE + INTERVAL '7 days' THEN 'مستحق قريباً'
        ELSE 'عادي'
    END as status,
    CURRENT_DATE - cd.due_date as days_overdue
FROM customer_debts cd
JOIN customers c ON cd.customer_id = c.id
WHERE cd.is_paid = false
ORDER BY cd.due_date ASC;

-- عرض أداء المبيعات الشهري
CREATE OR REPLACE VIEW monthly_sales_performance AS
SELECT 
    DATE_TRUNC('month', created_at) as month,
    TO_CHAR(DATE_TRUNC('month', created_at), 'YYYY-MM') as month_label,
    COUNT(*) as total_orders,
    SUM(total_amount) as total_revenue,
    AVG(total_amount) as average_order_value,
    COUNT(DISTINCT customer_id) as unique_customers,
    SUM(total_amount) / COUNT(*) as revenue_per_order
FROM sales
WHERE status = 'completed'
GROUP BY DATE_TRUNC('month', created_at)
ORDER BY month DESC;

-- عرض تحليل الربحية
CREATE OR REPLACE VIEW profitability_analysis AS
SELECT 
    p.id,
    p.name,
    p.barcode,
    c.name as category_name,
    p.cost,
    p.price,
    (p.price - p.cost) as profit_per_unit,
    CASE 
        WHEN p.cost > 0 THEN ROUND(((p.price - p.cost) / p.cost * 100)::numeric, 2)
        ELSE 0
    END as profit_margin_percentage,
    COALESCE(SUM(si.quantity), 0) as total_sold,
    COALESCE(SUM(si.quantity * (si.unit_price - p.cost)), 0) as total_profit_generated
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN sale_items si ON p.id = si.product_id
LEFT JOIN sales s ON si.sale_id = s.id AND s.status = 'completed'
WHERE p.is_active = true
GROUP BY p.id, p.name, p.barcode, c.name, p.cost, p.price
ORDER BY total_profit_generated DESC;

-- تعليقات على الـ Views
COMMENT ON VIEW daily_sales_stats IS 'إحصائيات المبيعات اليومية';
COMMENT ON VIEW top_selling_products IS 'المنتجات الأكثر مبيعاً';
COMMENT ON VIEW low_stock_products IS 'المنتجات منخفضة المخزون';
COMMENT ON VIEW customer_statistics IS 'إحصائيات العملاء';
COMMENT ON VIEW sales_by_category IS 'المبيعات حسب الفئات';
COMMENT ON VIEW financial_summary IS 'الملخص المالي';
COMMENT ON VIEW inventory_movement_summary IS 'ملخص حركة المخزون';
COMMENT ON VIEW outstanding_debts IS 'الديون المستحقة';
COMMENT ON VIEW monthly_sales_performance IS 'أداء المبيعات الشهري';
COMMENT ON VIEW profitability_analysis IS 'تحليل الربحية';
