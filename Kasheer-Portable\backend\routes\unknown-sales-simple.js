const express = require('express');
const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');

const router = express.Router();

// مسار ملف البيانات
const DATA_FILE = path.join(__dirname, '../data/unknown-sales.json');

// التأكد من وجود مجلد البيانات
async function ensureDataDir() {
  const dataDir = path.dirname(DATA_FILE);
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

// قراءة البيانات من الملف
async function readData() {
  try {
    await ensureDataDir();
    const data = await fs.readFile(DATA_FILE, 'utf8');
    return JSON.parse(data);
  } catch {
    return [];
  }
}

// كتابة البيانات إلى الملف
async function writeData(data) {
  await ensureDataDir();
  await fs.writeFile(DATA_FILE, JSON.stringify(data, null, 2));
}

// إنشاء رقم بيع
function generateSaleNumber() {
  const date = new Date();
  const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
  const timeStr = Date.now().toString().slice(-4);
  return `UNK-${dateStr}-${timeStr}`;
}

// 🔮 بيع منتج غير معروف
router.post('/unknown-sale', async (req, res) => {
  try {
    const { productName, price, quantity = 1, paymentMethod = 'cash' } = req.body;
    
    if (!productName || !price) {
      return res.status(400).json({ error: 'اسم المنتج والسعر مطلوبان' });
    }
    
    const totalAmount = parseFloat(price) * parseInt(quantity);
    
    const sale = {
      id: uuidv4(),
      sale_number: generateSaleNumber(),
      product_name: productName,
      quantity: parseInt(quantity),
      unit_price: parseFloat(price),
      total_amount: totalAmount,
      payment_method: paymentMethod,
      notes: 'منتج غير معروف',
      cashier_name: 'النظام',
      sale_date: new Date().toISOString(),
      created_at: new Date().toISOString()
    };
    
    console.log('🔮 بيع منتج غير معروف:', sale);
    
    // قراءة البيانات الحالية
    const data = await readData();
    
    // إضافة البيع الجديد
    data.push(sale);
    
    // حفظ البيانات
    await writeData(data);
    
    console.log('✅ تم حفظ البيع غير المعروف:', sale.sale_number);
    
    res.status(201).json({
      success: true,
      sale: sale,
      message: 'تم بيع المنتج غير المعروف بنجاح'
    });
    
  } catch (error) {
    console.error('❌ خطأ في بيع المنتج غير المعروف:', error);
    res.status(500).json({ error: error.message });
  }
});

// 📋 عرض جميع المبيعات غير المعروفة
router.get('/unknown-sales', async (req, res) => {
  try {
    const data = await readData();
    
    // ترتيب حسب التاريخ (الأحدث أولاً)
    data.sort((a, b) => new Date(b.sale_date) - new Date(a.sale_date));
    
    console.log(`📦 تم العثور على ${data.length} مبيعة غير معروفة`);
    res.json(data);
    
  } catch (error) {
    console.error('❌ خطأ في عرض المبيعات غير المعروفة:', error);
    res.status(500).json({ error: error.message });
  }
});

// 📊 إحصائيات المبيعات غير المعروفة
router.get('/unknown-sales/stats', async (req, res) => {
  try {
    const data = await readData();
    
    const today = new Date().toISOString().slice(0, 10);
    const todaySales = data.filter(sale => sale.sale_date.slice(0, 10) === today);
    
    const stats = {
      total_sales: data.length,
      total_revenue: data.reduce((sum, sale) => sum + sale.total_amount, 0),
      average_sale: data.length > 0 ? data.reduce((sum, sale) => sum + sale.total_amount, 0) / data.length : 0,
      today_sales: todaySales.length,
      today_revenue: todaySales.reduce((sum, sale) => sum + sale.total_amount, 0)
    };
    
    res.json(stats);
    
  } catch (error) {
    console.error('❌ خطأ في إحصائيات المبيعات غير المعروفة:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
