@echo off
title Final Build - Kasheer Toosar

echo ========================================
echo    Final Build - Kasheer Toosar
echo ========================================
echo.

echo Cleaning old build files...
if exist "dist" (
    timeout /t 2 /nobreak >nul
    rmdir /s /q "dist" 2>nul
)

echo Installing dependencies...
call npm install --silent

echo Installing Electron and Builder...
call npm install electron electron-builder --save-dev --silent

echo Preparing package.json for Electron...
copy /Y "package-electron-fixed.json" "package.json" >nul

echo Building frontend...
call npm run build

echo Building Electron installer...
call npx electron-builder --win --publish=never

echo.
echo Build completed!
echo Check the dist folder for installer files.
echo.

if exist "dist\*.exe" (
    echo Installer files created:
    for %%f in (dist\*.exe) do echo   - %%f
)

pause
