const express = require('express');
const router = express.Router();
const pool = require('../database');

// إنشاء جدول الإعدادات (مؤقت)
router.get('/create-table', async (req, res) => {
  try {
    await pool.query(`
      CREATE TABLE IF NOT EXISTS settings (
        id SERIAL PRIMARY KEY,
        store_name VARCHAR(255) DEFAULT '',
        store_address TEXT DEFAULT '',
        store_phone VARCHAR(50) DEFAULT '',
        store_email VARCHAR(255) DEFAULT '',
        store_tax_number VARCHAR(100) DEFAULT '',
        store_logo TEXT DEFAULT '',
        user_name VARCHAR(255) DEFAULT '',
        user_email VARCHAR(255) DEFAULT '',
        user_role VARCHAR(50) DEFAULT 'admin',
        currency VARCHAR(10) DEFAULT 'DZD',
        language VARCHAR(10) DEFAULT 'ar',
        timezone VARCHAR(50) DEFAULT 'Africa/Algiers',
        date_format VARCHAR(20) DEFAULT 'DD/MM/YYYY',
        tax_rate DECIMAL(5,2) DEFAULT 19.00,
        enable_tax BOOLEAN DEFAULT true,
        enable_discount BOOLEAN DEFAULT true,
        enable_barcode BOOLEAN DEFAULT true,
        low_stock_alert BOOLEAN DEFAULT true,
        email_notifications BOOLEAN DEFAULT false,
        sound_notifications BOOLEAN DEFAULT true,
        theme VARCHAR(20) DEFAULT 'dark',
        primary_color VARCHAR(20) DEFAULT '#3b82f6',
        font_size VARCHAR(20) DEFAULT 'medium',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // إدراج إعدادات افتراضية
    const result = await pool.query('SELECT COUNT(*) FROM settings');
    if (parseInt(result.rows[0].count) === 0) {
      await pool.query(`
        INSERT INTO settings (store_name, store_address, store_phone, store_email, user_name)
        VALUES ('متجر توسار الإلكتروني', 'الجزائر - الجزائر العاصمة', '+213 XXX XXX XXX', '<EMAIL>', 'المدير')
      `);
    }

    res.json({ success: true, message: 'تم إنشاء جدول الإعدادات بنجاح' });
  } catch (error) {
    console.error('خطأ في إنشاء جدول الإعدادات:', error);
    res.status(500).json({ error: 'خطأ في إنشاء جدول الإعدادات' });
  }
});

// عرض الإعدادات العامة
router.get('/', async (req, res) => {
  try {
    // التحقق من وجود الجدول أولاً
    const tableCheck = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'pos_system'
        AND table_name = 'settings'
      );
    `);

    if (!tableCheck.rows[0].exists) {
      // إرجاع إعدادات افتراضية إذا لم يكن الجدول موجوداً
      console.log('⚠️ جدول الإعدادات غير موجود، إرجاع إعدادات افتراضية');
      return res.json({
        store_name: 'متجر توسار الإلكتروني',
        store_address: 'الجزائر - الجزائر العاصمة',
        store_phone: '+213 XXX XXX XXX',
        store_email: '<EMAIL>',
        user_name: 'المدير',
        currency: 'DZD',
        language: 'ar',
        theme: 'dark'
      });
    }

    const result = await pool.query('SELECT * FROM pos_system.settings LIMIT 1');
    res.json(result.rows[0] || {
      store_name: 'متجر توسار الإلكتروني',
      store_address: 'الجزائر - الجزائر العاصمة',
      store_phone: '+213 XXX XXX XXX',
      store_email: '<EMAIL>',
      user_name: 'المدير',
      currency: 'DZD',
      language: 'ar',
      theme: 'dark'
    });
  } catch (error) {
    console.error('خطأ في عرض الإعدادات:', error);
    // إرجاع إعدادات افتراضية بدلاً من خطأ 500
    res.json({
      store_name: 'متجر توسار الإلكتروني',
      store_address: 'الجزائر - الجزائر العاصمة',
      store_phone: '+213 XXX XXX XXX',
      store_email: '<EMAIL>',
      user_name: 'المدير',
      currency: 'DZD',
      language: 'ar',
      theme: 'dark'
    });
  }
});

// تحديث الإعدادات العامة
router.put('/', async (req, res) => {
  try {
    const settings = req.body;

    // التحقق من وجود إعدادات
    const existingResult = await pool.query('SELECT id FROM pos_system.settings LIMIT 1');

    let result;
    if (existingResult.rows.length === 0) {
      // إنشاء إعدادات جديدة
      const columns = Object.keys(settings).join(', ');
      const values = Object.values(settings);
      const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');

      result = await pool.query(
        `INSERT INTO pos_system.settings (${columns}) VALUES (${placeholders}) RETURNING *`,
        values
      );
    } else {
      // تحديث الإعدادات الموجودة
      const setClause = Object.keys(settings)
        .map((key, index) => `${key} = $${index + 1}`)
        .join(', ');
      const values = Object.values(settings);

      result = await pool.query(
        `UPDATE pos_system.settings SET ${setClause}, updated_at = CURRENT_TIMESTAMP RETURNING *`,
        values
      );
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('خطأ في تحديث الإعدادات:', error);
    res.status(500).json({ error: 'خطأ في تحديث الإعدادات' });
  }
});

// عرض إعدادات الطابعة
router.get('/printer', async (req, res) => {
  try {
    // التحقق من وجود جدول إعدادات الطابعة
    const tableCheck = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'pos_system'
        AND table_name = 'printer_settings'
      );
    `);

    if (!tableCheck.rows[0].exists) {
      // إرجاع إعدادات طابعة افتراضية
      console.log('⚠️ جدول إعدادات الطابعة غير موجود، إرجاع إعدادات افتراضية');
      return res.json({
        printer_name: 'Default Printer',
        paper_size: 'A4',
        print_logo: true,
        print_header: true,
        print_footer: true
      });
    }

    const result = await pool.query('SELECT * FROM pos_system.printer_settings LIMIT 1');
    res.json(result.rows[0] || {
      printer_name: 'Default Printer',
      paper_size: 'A4',
      print_logo: true,
      print_header: true,
      print_footer: true
    });
  } catch (error) {
    console.error('خطأ في عرض إعدادات الطابعة:', error);
    // إرجاع إعدادات افتراضية بدلاً من خطأ
    res.json({
      printer_name: 'Default Printer',
      paper_size: 'A4',
      print_logo: true,
      print_header: true,
      print_footer: true
    });
  }
});

// تحديث إعدادات الطابعة
router.put('/printer', async (req, res) => {
  try {
    const settings = req.body;

    // التحقق من وجود إعدادات الطابعة
    const existingResult = await pool.query('SELECT id FROM pos_system.printer_settings LIMIT 1');

    let result;
    if (existingResult.rows.length === 0) {
      // إنشاء إعدادات طابعة جديدة
      const columns = Object.keys(settings).join(', ');
      const values = Object.values(settings);
      const placeholders = values.map((_, index) => `$${index + 1}`).join(', ');

      result = await pool.query(
        `INSERT INTO pos_system.printer_settings (${columns}) VALUES (${placeholders}) RETURNING *`,
        values
      );
    } else {
      // تحديث إعدادات الطابعة الموجودة
      const setClause = Object.keys(settings)
        .map((key, index) => `${key} = $${index + 1}`)
        .join(', ');
      const values = Object.values(settings);

      result = await pool.query(
        `UPDATE printer_settings SET ${setClause}, updated_at = CURRENT_TIMESTAMP RETURNING *`,
        values
      );
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('خطأ في تحديث إعدادات الطابعة:', error);
    res.status(500).json({ error: 'خطأ في تحديث إعدادات الطابعة' });
  }
});

module.exports = router;
