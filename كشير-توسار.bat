@echo off
title Kasheer Toosar - POS System
color 0A
cd /d "%~dp0"

echo.
echo ==========================================
echo     Kasheer Toosar - POS System
echo ==========================================
echo.

REM Check if server is already running
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:5003' -TimeoutSec 2 -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorlevel% equ 0 (
    echo [!] System is already running!
    echo [!] Opening browser...
    start http://localhost:5003
    echo.
    echo System URL: http://localhost:5003
    echo.
    pause
    exit /b 0
)

REM Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [X] ERROR: Node.js not found!
    echo [!] Please install Node.js from: https://nodejs.org
    echo.
    pause
    exit /b 1
)

echo [✓] Node.js found

REM Install dependencies if needed
if not exist "node_modules\vite" (
    echo [!] Installing dependencies...
    npm install --silent
    if %errorlevel% neq 0 (
        echo [X] Failed to install dependencies!
        pause
        exit /b 1
    )
    echo [✓] Dependencies installed
)

REM Build project if needed
if not exist "dist\index.html" (
    echo [!] Building project...
    npm run build --silent
    if %errorlevel% neq 0 (
        echo [X] Build failed!
        pause
        exit /b 1
    )
    echo [✓] Project built successfully
)

REM Important reminder
echo [!] IMPORTANT: Make sure PostgreSQL is running!
echo.

REM Start server in background
echo [!] Starting server...
start /min "Kasheer-Server" node backend\server.js

REM Wait for server to start
echo [!] Waiting for server to start...
timeout /t 5 /nobreak >nul

REM Check if server is running
:check_server
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:5003' -TimeoutSec 3 -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorlevel% equ 0 (
    echo [✓] Server is running successfully!
    goto success
)

echo [!] Server still starting... (checking again)
timeout /t 3 /nobreak >nul
goto check_server

:success
REM Open browser
echo [!] Opening browser...
start http://localhost:5003

echo.
echo ==========================================
echo   SUCCESS! System is now running
echo   URL: http://localhost:5003
echo ==========================================
echo.
echo INSTRUCTIONS:
echo - Keep this window open to monitor the system
echo - Close this window to stop the server
echo - Make sure PostgreSQL is running
echo - The system will auto-restart if it stops
echo.
echo Press any key to continue monitoring...
pause >nul

REM Monitor system and auto-restart if needed
:monitor
cls
echo ==========================================
echo   Kasheer Toosar - System Monitor
echo ==========================================
echo.
echo System URL: http://localhost:5003
echo Status: Monitoring...
echo.
echo Press Ctrl+C to stop the system
echo.

timeout /t 30 /nobreak >nul

REM Check if server is still running
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:5003' -TimeoutSec 3 -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorlevel% neq 0 (
    echo [!] WARNING: Server stopped! Restarting...
    start /min "Kasheer-Server" node backend\server.js
    timeout /t 5 /nobreak >nul
    echo [✓] Server restarted
)

goto monitor
