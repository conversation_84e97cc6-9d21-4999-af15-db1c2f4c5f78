-- المشغلات والوظائف للنظام
-- Triggers and Functions for the system

-- وظيفة تحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- تطبيق المشغل على الجداول المطلوبة
CREATE TRIGGER update_settings_updated_at BEFORE UPDATE ON settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_printer_settings_updated_at BEFORE UPDATE ON printer_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customers_updated_at BEFORE UPDATE ON customers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_suppliers_updated_at BEFORE UPDATE ON suppliers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sales_updated_at BEFORE UPDATE ON sales
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_purchase_invoices_updated_at BEFORE UPDATE ON purchase_invoices
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_financial_transactions_updated_at BEFORE UPDATE ON financial_transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customer_debts_updated_at BEFORE UPDATE ON customer_debts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- وظيفة تحديث المخزون عند البيع
CREATE OR REPLACE FUNCTION update_inventory_on_sale()
RETURNS TRIGGER AS $$
BEGIN
    -- تحديث مخزون المنتج
    UPDATE products 
    SET stock = stock - NEW.quantity
    WHERE id = NEW.product_id;
    
    -- إضافة حركة مخزون
    INSERT INTO inventory_movements (
        product_id, movement_type, quantity, 
        previous_stock, new_stock, reason, 
        reference_id, reference_type
    )
    SELECT 
        NEW.product_id, 'out', NEW.quantity,
        p.stock + NEW.quantity, p.stock, 'sale',
        NEW.sale_id, 'sale'
    FROM products p WHERE p.id = NEW.product_id;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- مشغل تحديث المخزون عند إضافة عنصر بيع
CREATE TRIGGER trigger_update_inventory_on_sale
    AFTER INSERT ON sale_items
    FOR EACH ROW EXECUTE FUNCTION update_inventory_on_sale();

-- وظيفة تحديث المخزون عند الشراء
CREATE OR REPLACE FUNCTION update_inventory_on_purchase()
RETURNS TRIGGER AS $$
BEGIN
    -- تحديث مخزون المنتج
    UPDATE products 
    SET stock = stock + NEW.quantity
    WHERE id = NEW.product_id;
    
    -- إضافة حركة مخزون
    INSERT INTO inventory_movements (
        product_id, movement_type, quantity, 
        previous_stock, new_stock, reason, 
        reference_id, reference_type
    )
    SELECT 
        NEW.product_id, 'in', NEW.quantity,
        p.stock - NEW.quantity, p.stock, 'purchase',
        NEW.invoice_id, 'purchase'
    FROM products p WHERE p.id = NEW.product_id;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- مشغل تحديث المخزون عند إضافة عنصر شراء
CREATE TRIGGER trigger_update_inventory_on_purchase
    AFTER INSERT ON purchase_invoice_items
    FOR EACH ROW EXECUTE FUNCTION update_inventory_on_purchase();

-- وظيفة إنشاء معاملة مالية عند البيع
CREATE OR REPLACE FUNCTION create_financial_transaction_on_sale()
RETURNS TRIGGER AS $$
BEGIN
    -- إضافة معاملة مالية للبيع
    INSERT INTO financial_transactions (
        transaction_number, type, category, amount, 
        description, reference_id, reference_type, 
        payment_method, transaction_date
    ) VALUES (
        'TXN-' || NEW.sale_number,
        'income',
        'مبيعات',
        NEW.total_amount,
        'بيع رقم: ' || NEW.sale_number,
        NEW.id,
        'sale',
        NEW.payment_method,
        CURRENT_DATE
    );
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- مشغل إنشاء معاملة مالية عند البيع
CREATE TRIGGER trigger_create_financial_transaction_on_sale
    AFTER INSERT ON sales
    FOR EACH ROW EXECUTE FUNCTION create_financial_transaction_on_sale();

-- وظيفة إنشاء معاملة مالية عند الشراء
CREATE OR REPLACE FUNCTION create_financial_transaction_on_purchase()
RETURNS TRIGGER AS $$
BEGIN
    -- إضافة معاملة مالية للشراء
    INSERT INTO financial_transactions (
        transaction_number, type, category, amount, 
        description, reference_id, reference_type, 
        transaction_date
    ) VALUES (
        'TXN-' || NEW.invoice_number,
        'expense',
        'مشتريات',
        NEW.total_amount,
        'شراء رقم: ' || NEW.invoice_number,
        NEW.id,
        'purchase',
        CURRENT_DATE
    );
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- مشغل إنشاء معاملة مالية عند الشراء
CREATE TRIGGER trigger_create_financial_transaction_on_purchase
    AFTER INSERT ON purchase_invoices
    FOR EACH ROW EXECUTE FUNCTION create_financial_transaction_on_purchase();

-- وظيفة تحديث رصيد العميل
CREATE OR REPLACE FUNCTION update_customer_balance()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- زيادة الرصيد عند إضافة دين جديد
        UPDATE customers 
        SET balance = balance + NEW.amount
        WHERE id = NEW.customer_id;
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        -- تحديث الرصيد عند تحديث الدين
        UPDATE customers 
        SET balance = balance - OLD.amount + NEW.amount
        WHERE id = NEW.customer_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- تقليل الرصيد عند حذف الدين
        UPDATE customers 
        SET balance = balance - OLD.amount
        WHERE id = OLD.customer_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

-- مشغلات تحديث رصيد العميل
CREATE TRIGGER trigger_update_customer_balance_insert
    AFTER INSERT ON customer_debts
    FOR EACH ROW EXECUTE FUNCTION update_customer_balance();

CREATE TRIGGER trigger_update_customer_balance_update
    AFTER UPDATE ON customer_debts
    FOR EACH ROW EXECUTE FUNCTION update_customer_balance();

CREATE TRIGGER trigger_update_customer_balance_delete
    AFTER DELETE ON customer_debts
    FOR EACH ROW EXECUTE FUNCTION update_customer_balance();

-- وظيفة تحديث الدين عند الدفع
CREATE OR REPLACE FUNCTION update_debt_on_payment()
RETURNS TRIGGER AS $$
BEGIN
    -- تحديث المبلغ المدفوع في الدين
    UPDATE customer_debts 
    SET paid_amount = paid_amount + NEW.amount
    WHERE id = NEW.debt_id;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- مشغل تحديث الدين عند الدفع
CREATE TRIGGER trigger_update_debt_on_payment
    AFTER INSERT ON customer_debt_payments
    FOR EACH ROW EXECUTE FUNCTION update_debt_on_payment();

-- وظيفة توليد رقم البيع التلقائي
CREATE OR REPLACE FUNCTION generate_sale_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.sale_number IS NULL OR NEW.sale_number = '' THEN
        NEW.sale_number := 'SALE-' || TO_CHAR(CURRENT_DATE, 'YYYYMMDD') || '-' || 
                          LPAD(NEXTVAL('sale_number_seq')::TEXT, 4, '0');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إنشاء sequence لأرقام البيع
CREATE SEQUENCE IF NOT EXISTS sale_number_seq START 1;

-- مشغل توليد رقم البيع
CREATE TRIGGER trigger_generate_sale_number
    BEFORE INSERT ON sales
    FOR EACH ROW EXECUTE FUNCTION generate_sale_number();

-- وظيفة توليد رقم فاتورة الشراء التلقائي
CREATE OR REPLACE FUNCTION generate_purchase_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.invoice_number IS NULL OR NEW.invoice_number = '' THEN
        NEW.invoice_number := 'PUR-' || TO_CHAR(CURRENT_DATE, 'YYYYMMDD') || '-' || 
                             LPAD(NEXTVAL('purchase_number_seq')::TEXT, 4, '0');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إنشاء sequence لأرقام الشراء
CREATE SEQUENCE IF NOT EXISTS purchase_number_seq START 1;

-- مشغل توليد رقم فاتورة الشراء
CREATE TRIGGER trigger_generate_purchase_number
    BEFORE INSERT ON purchase_invoices
    FOR EACH ROW EXECUTE FUNCTION generate_purchase_number();
