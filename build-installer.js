// 🏗️ سكريبت بناء ملف التثبيت
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 بدء عملية بناء ملف التثبيت...');

// 1. بناء الواجهة الأمامية
console.log('📦 بناء الواجهة الأمامية...');
try {
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ تم بناء الواجهة الأمامية بنجاح');
} catch (error) {
    console.error('❌ خطأ في بناء الواجهة الأمامية:', error.message);
    process.exit(1);
}

// 2. نسخ ملفات الخادم
console.log('🔧 تحضير ملفات الخادم...');
const distDir = path.join(__dirname, 'dist-app');
if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true });
}

// نسخ الملفات الأساسية
const filesToCopy = [
    'backend/server.js',
    'backend/database.js',
    'backend/package.json',
    'backend/routes',
    'backend/migrations',
    'dist' // الواجهة الأمامية المبنية
];

console.log('📁 نسخ الملفات...');
filesToCopy.forEach(file => {
    const src = path.join(__dirname, file);
    const dest = path.join(distDir, file);
    
    if (fs.existsSync(src)) {
        if (fs.statSync(src).isDirectory()) {
            copyDir(src, dest);
        } else {
            fs.copyFileSync(src, dest);
        }
        console.log(`✅ تم نسخ: ${file}`);
    }
});

// 3. إنشاء ملف التشغيل
console.log('🎯 إنشاء ملف التشغيل...');
const startScript = `
@echo off
title كشير توسار - نظام نقاط البيع
echo 🏪 مرحباً بك في كشير توسار
echo ============================
echo.
echo 🚀 بدء تشغيل النظام...

cd /d "%~dp0"
cd backend

echo 📦 تثبيت التبعيات...
call npm install --production --silent

echo 🔧 بدء تشغيل الخادم...
start "كشير توسار - الخادم" /min node server.js

echo ⏳ انتظار تشغيل الخادم...
timeout /t 5 /nobreak > nul

echo 🌐 فتح التطبيق...
start "" "http://localhost:5003"

echo.
echo ✅ تم تشغيل النظام بنجاح!
echo 📱 يمكنك الوصول للنظام على: http://localhost:5003
echo.
echo لإغلاق النظام، أغلق هذه النافذة
pause
`;

fs.writeFileSync(path.join(distDir, 'start-pos.bat'), startScript);

console.log('✅ تم إنشاء ملف التثبيت بنجاح!');
console.log('📁 الملفات موجودة في مجلد: dist-app');
console.log('🎉 يمكنك الآن إنشاء ملف .exe باستخدام أدوات التحويل');

// دالة مساعدة لنسخ المجلدات
function copyDir(src, dest) {
    if (!fs.existsSync(dest)) {
        fs.mkdirSync(dest, { recursive: true });
    }
    
    const files = fs.readdirSync(src);
    files.forEach(file => {
        const srcFile = path.join(src, file);
        const destFile = path.join(dest, file);
        
        if (fs.statSync(srcFile).isDirectory()) {
            copyDir(srcFile, destFile);
        } else {
            fs.copyFileSync(srcFile, destFile);
        }
    });
}
