import React, { useState, useEffect } from 'react';
import {
  Settings as SettingsIcon,
  Store,
  User,
  Bell,
  Shield,
  Palette,
  Globe,
  Save,
  RefreshCw,
  Upload,
  Download,
  Trash2,
  Eye,
  EyeOff,
  Check,
  X,
  AlertTriangle,
  Info
} from 'lucide-react';
import { useApp } from '../context/AppContext';

const Settings: React.FC = () => {
  const { settings, updateSettings, loadSettings } = useApp();
  const [activeTab, setActiveTab] = useState('store');
  const [showPassword, setShowPassword] = useState(false);
  const [unsavedChanges, setUnsavedChanges] = useState(false);

  const [formData, setFormData] = useState({
    // إعدادات المتجر
    storeName: '',
    storeAddress: '',
    storePhone: '',
    storeEmail: '',
    storeTaxNumber: '',
    storeLogo: '',

    // إعدادات المستخدم
    userName: '',
    userEmail: '',
    userPassword: '',
    userRole: 'admin',

    // إعدادات النظام
    currency: 'DZD',
    language: 'ar',
    timezone: 'Africa/Algiers',
    dateFormat: 'DD/MM/YYYY',

    // إعدادات الضرائب والمبيعات
    taxRate: 19,
    enableTax: true,
    enableDiscount: true,
    enableBarcode: true,

    // إعدادات التنبيهات
    lowStockAlert: true,
    emailNotifications: false,
    soundNotifications: true,

    // إعدادات المظهر
    theme: 'dark',
    primaryColor: '#3b82f6',
    fontSize: 'medium'
  });

  // تحديث البيانات عند تغيير الإعدادات
  useEffect(() => {
    if (settings) {
      setFormData({
        // إعدادات المتجر
        storeName: settings.storeName || '',
        storeAddress: settings.storeAddress || '',
        storePhone: settings.storePhone || '',
        storeEmail: settings.storeEmail || '',
        storeTaxNumber: settings.storeTaxNumber || '',
        storeLogo: settings.storeLogo || '',

        // إعدادات المستخدم
        userName: settings.userName || '',
        userEmail: settings.userEmail || '',
        userPassword: '',
        userRole: settings.userRole || 'admin',

        // إعدادات النظام
        currency: settings.currency || 'DZD',
        language: settings.language || 'ar',
        timezone: settings.timezone || 'Africa/Algiers',
        dateFormat: settings.dateFormat || 'DD/MM/YYYY',

        // إعدادات الضرائب والمبيعات
        taxRate: settings.taxRate || 19,
        enableTax: settings.enableTax !== undefined ? settings.enableTax : true,
        enableDiscount: settings.enableDiscount !== undefined ? settings.enableDiscount : true,
        enableBarcode: settings.enableBarcode !== undefined ? settings.enableBarcode : true,

        // إعدادات التنبيهات
        lowStockAlert: settings.lowStockAlert !== undefined ? settings.lowStockAlert : true,
        emailNotifications: settings.emailNotifications !== undefined ? settings.emailNotifications : false,
        soundNotifications: settings.soundNotifications !== undefined ? settings.soundNotifications : true,

        // إعدادات المظهر
        theme: settings.theme || 'dark',
        primaryColor: settings.primaryColor || '#3b82f6',
        fontSize: settings.fontSize || 'medium'
      });
    }
  }, [settings]);

  // تحميل الإعدادات عند فتح الصفحة
  useEffect(() => {
    loadSettings();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    const newValue = type === 'checkbox' ? (e.target as HTMLInputElement).checked : value;

    setFormData(prev => ({ ...prev, [name]: newValue }));
    setUnsavedChanges(true);
  };

  const handleSave = async () => {
    try {
      // تحويل أسماء الحقول من camelCase إلى snake_case
      const dbData = {
        store_name: formData.storeName,
        store_address: formData.storeAddress,
        store_phone: formData.storePhone,
        store_email: formData.storeEmail,
        store_tax_number: formData.storeTaxNumber,
        store_logo: formData.storeLogo,
        user_name: formData.userName,
        user_email: formData.userEmail,
        user_role: formData.userRole,
        currency: formData.currency,
        language: formData.language,
        timezone: formData.timezone,
        date_format: formData.dateFormat,
        tax_rate: formData.taxRate,
        enable_tax: formData.enableTax,
        enable_discount: formData.enableDiscount,
        enable_barcode: formData.enableBarcode,
        low_stock_alert: formData.lowStockAlert,
        email_notifications: formData.emailNotifications,
        sound_notifications: formData.soundNotifications,
        theme: formData.theme,
        primary_color: formData.primaryColor,
        font_size: formData.fontSize
      };

      // حفظ الإعدادات في قاعدة البيانات
      const response = await fetch('/api/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dbData),
      });

      if (response.ok) {
        updateSettings(formData);
        setUnsavedChanges(false);

        // إظهار رسالة نجاح مع تأثير بصري
        const successMsg = document.createElement('div');
        successMsg.textContent = '✅ تم حفظ الإعدادات بنجاح!';
        successMsg.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: linear-gradient(135deg, #10B981, #059669);
          color: white;
          padding: 16px 24px;
          border-radius: 12px;
          font-weight: bold;
          z-index: 9999;
          box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
          animation: slideIn 0.3s ease-out;
        `;
        document.body.appendChild(successMsg);

        setTimeout(() => {
          successMsg.remove();
        }, 3000);
      } else {
        throw new Error('فشل في حفظ الإعدادات');
      }
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات:', error);
      alert('حدث خطأ في حفظ الإعدادات. يرجى المحاولة مرة أخرى.');
    }
  };

  const handleReset = () => {
    if (window.confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
      setFormData({
        storeName: '',
        storeAddress: '',
        storePhone: '',
        storeEmail: '',
        storeTaxNumber: '',
        storeLogo: '',
        userName: '',
        userEmail: '',
        userPassword: '',
        userRole: 'admin',
        currency: 'DZD',
        language: 'ar',
        timezone: 'Africa/Algiers',
        dateFormat: 'DD/MM/YYYY',
        taxRate: 19,
        enableTax: true,
        enableDiscount: true,
        enableBarcode: true,
        lowStockAlert: true,
        emailNotifications: false,
        soundNotifications: true,
        theme: 'dark',
        primaryColor: '#3b82f6',
        fontSize: 'medium'
      });
      setUnsavedChanges(true);
    }
  };

  const tabs = [
    { id: 'store', name: 'المتجر', icon: Store },
    { id: 'user', name: 'المستخدم', icon: User },
    { id: 'system', name: 'النظام', icon: SettingsIcon },
    { id: 'sales', name: 'المبيعات', icon: Globe },
    { id: 'notifications', name: 'التنبيهات', icon: Bell },
    { id: 'appearance', name: 'المظهر', icon: Palette }
  ];

  return (
    <>
      {/* CSS للرسائل المنبثقة */}
      <style>{`
        @keyframes slideIn {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
      `}</style>

    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white mb-2">الإعدادات</h1>
          <p className="text-slate-400">إدارة إعدادات النظام والمتجر</p>
        </div>

        <div className="flex items-center space-x-reverse space-x-4">
          {unsavedChanges && (
            <div className="flex items-center space-x-reverse space-x-2 text-yellow-400">
              <AlertTriangle className="w-4 h-4" />
              <span className="text-sm">تغييرات غير محفوظة</span>
            </div>
          )}

          <button
            onClick={handleReset}
            className="bg-slate-600 hover:bg-slate-500 px-4 py-2 rounded-lg flex items-center space-x-reverse space-x-2 transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
            <span>إعادة تعيين</span>
          </button>

          <button
            onClick={handleSave}
            disabled={!unsavedChanges}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-slate-600 disabled:cursor-not-allowed px-6 py-2 rounded-lg flex items-center space-x-reverse space-x-2 transition-colors"
          >
            <Save className="w-4 h-4" />
            <span>حفظ التغييرات</span>
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-slate-800 rounded-xl border border-slate-700 p-4">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-reverse space-x-3 px-4 py-3 rounded-lg transition-colors ${
                      activeTab === tab.id
                        ? 'bg-blue-600 text-white'
                        : 'text-slate-300 hover:bg-slate-700 hover:text-white'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{tab.name}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          <div className="bg-slate-800 rounded-xl border border-slate-700 p-6">
            {/* Store Settings */}
            {activeTab === 'store' && (
              <div className="space-y-6">
                <div className="flex items-center space-x-reverse space-x-3 mb-6">
                  <Store className="w-6 h-6 text-blue-400" />
                  <h2 className="text-xl font-bold text-white">إعدادات المتجر</h2>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">اسم المتجر *</label>
                    <input
                      type="text"
                      name="storeName"
                      value={formData.storeName}
                      onChange={handleInputChange}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                      placeholder="أدخل اسم المتجر"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">رقم الهاتف</label>
                    <input
                      type="tel"
                      name="storePhone"
                      value={formData.storePhone}
                      onChange={handleInputChange}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                      placeholder="+213 XXX XXX XXX"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">البريد الإلكتروني</label>
                    <input
                      type="email"
                      name="storeEmail"
                      value={formData.storeEmail}
                      onChange={handleInputChange}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">الرقم الضريبي</label>
                    <input
                      type="text"
                      name="storeTaxNumber"
                      value={formData.storeTaxNumber}
                      onChange={handleInputChange}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                      placeholder="رقم التسجيل الضريبي"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-slate-300 mb-2">عنوان المتجر</label>
                    <textarea
                      name="storeAddress"
                      value={formData.storeAddress}
                      onChange={handleInputChange}
                      rows={3}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none resize-none"
                      placeholder="العنوان الكامل للمتجر"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-slate-300 mb-2">شعار المتجر</label>
                    <div className="flex items-center space-x-reverse space-x-4">
                      <div className="w-16 h-16 bg-slate-700 rounded-lg flex items-center justify-center border-2 border-dashed border-slate-600">
                        {formData.storeLogo ? (
                          <img src={formData.storeLogo} alt="Logo" className="w-full h-full object-cover rounded-lg" />
                        ) : (
                          <Upload className="w-6 h-6 text-slate-400" />
                        )}
                      </div>
                      <div className="flex-1">
                        <input
                          type="url"
                          name="storeLogo"
                          value={formData.storeLogo}
                          onChange={handleInputChange}
                          className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                          placeholder="رابط الشعار"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* User Settings */}
            {activeTab === 'user' && (
              <div className="space-y-6">
                <div className="flex items-center space-x-reverse space-x-3 mb-6">
                  <User className="w-6 h-6 text-green-400" />
                  <h2 className="text-xl font-bold text-white">إعدادات المستخدم</h2>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">اسم المستخدم *</label>
                    <input
                      type="text"
                      name="userName"
                      value={formData.userName}
                      onChange={handleInputChange}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                      placeholder="أدخل اسم المستخدم"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">البريد الإلكتروني</label>
                    <input
                      type="email"
                      name="userEmail"
                      value={formData.userEmail}
                      onChange={handleInputChange}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">كلمة المرور الجديدة</label>
                    <div className="relative">
                      <input
                        type={showPassword ? 'text' : 'password'}
                        name="userPassword"
                        value={formData.userPassword}
                        onChange={handleInputChange}
                        className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 pr-12 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                        placeholder="اتركها فارغة للاحتفاظ بالحالية"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-white"
                      >
                        {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">الدور</label>
                    <select
                      name="userRole"
                      value={formData.userRole}
                      onChange={handleInputChange}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:border-blue-500 focus:outline-none"
                    >
                      <option value="admin">مدير</option>
                      <option value="cashier">كاشير</option>
                      <option value="manager">مدير متجر</option>
                    </select>
                  </div>
                </div>
              </div>
            )}

            {/* System Settings */}
            {activeTab === 'system' && (
              <div className="space-y-6">
                <div className="flex items-center space-x-reverse space-x-3 mb-6">
                  <SettingsIcon className="w-6 h-6 text-purple-400" />
                  <h2 className="text-xl font-bold text-white">إعدادات النظام</h2>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">العملة</label>
                    <select
                      name="currency"
                      value={formData.currency}
                      onChange={handleInputChange}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:border-blue-500 focus:outline-none"
                    >
                      <option value="DZD">دينار جزائري (DZD)</option>
                      <option value="USD">دولار أمريكي (USD)</option>
                      <option value="EUR">يورو (EUR)</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">اللغة</label>
                    <select
                      name="language"
                      value={formData.language}
                      onChange={handleInputChange}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:border-blue-500 focus:outline-none"
                    >
                      <option value="ar">العربية</option>
                      <option value="en">English</option>
                      <option value="fr">Français</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">المنطقة الزمنية</label>
                    <select
                      name="timezone"
                      value={formData.timezone}
                      onChange={handleInputChange}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:border-blue-500 focus:outline-none"
                    >
                      <option value="Africa/Algiers">الجزائر</option>
                      <option value="Africa/Tunis">تونس</option>
                      <option value="Africa/Casablanca">المغرب</option>
                      <option value="Europe/Paris">باريس</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">تنسيق التاريخ</label>
                    <select
                      name="dateFormat"
                      value={formData.dateFormat}
                      onChange={handleInputChange}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:border-blue-500 focus:outline-none"
                    >
                      <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                      <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                      <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                    </select>
                  </div>
                </div>
              </div>
            )}

            {/* Sales Settings */}
            {activeTab === 'sales' && (
              <div className="space-y-6">
                <div className="flex items-center space-x-reverse space-x-3 mb-6">
                  <Globe className="w-6 h-6 text-orange-400" />
                  <h2 className="text-xl font-bold text-white">إعدادات المبيعات</h2>
                </div>

                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">معدل الضريبة (%)</label>
                      <input
                        type="number"
                        step="0.01"
                        name="taxRate"
                        value={formData.taxRate}
                        onChange={handleInputChange}
                        className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                        placeholder="19"
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-slate-700 rounded-lg">
                      <div>
                        <h3 className="text-white font-medium">تفعيل الضريبة</h3>
                        <p className="text-slate-400 text-sm">إضافة الضريبة تلقائياً على المبيعات</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="enableTax"
                          checked={formData.enableTax}
                          onChange={handleInputChange}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-slate-700 rounded-lg">
                      <div>
                        <h3 className="text-white font-medium">تفعيل الخصومات</h3>
                        <p className="text-slate-400 text-sm">السماح بإضافة خصومات على المنتجات</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="enableDiscount"
                          checked={formData.enableDiscount}
                          onChange={handleInputChange}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-slate-700 rounded-lg">
                      <div>
                        <h3 className="text-white font-medium">تفعيل الباركود</h3>
                        <p className="text-slate-400 text-sm">استخدام الباركود في البحث والمبيعات</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="enableBarcode"
                          checked={formData.enableBarcode}
                          onChange={handleInputChange}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Notifications Settings */}
            {activeTab === 'notifications' && (
              <div className="space-y-6">
                <div className="flex items-center space-x-reverse space-x-3 mb-6">
                  <Bell className="w-6 h-6 text-yellow-400" />
                  <h2 className="text-xl font-bold text-white">إعدادات التنبيهات</h2>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-slate-700 rounded-lg">
                    <div>
                      <h3 className="text-white font-medium">تنبيه المخزون المنخفض</h3>
                      <p className="text-slate-400 text-sm">إشعار عند انخفاض مخزون المنتجات</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        name="lowStockAlert"
                        checked={formData.lowStockAlert}
                        onChange={handleInputChange}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-slate-700 rounded-lg">
                    <div>
                      <h3 className="text-white font-medium">إشعارات البريد الإلكتروني</h3>
                      <p className="text-slate-400 text-sm">إرسال التقارير والتنبيهات عبر البريد</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        name="emailNotifications"
                        checked={formData.emailNotifications}
                        onChange={handleInputChange}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-slate-700 rounded-lg">
                    <div>
                      <h3 className="text-white font-medium">الأصوات التنبيهية</h3>
                      <p className="text-slate-400 text-sm">تشغيل أصوات عند العمليات المهمة</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        name="soundNotifications"
                        checked={formData.soundNotifications}
                        onChange={handleInputChange}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                </div>
              </div>
            )}

            {/* Appearance Settings */}
            {activeTab === 'appearance' && (
              <div className="space-y-6">
                <div className="flex items-center space-x-reverse space-x-3 mb-6">
                  <Palette className="w-6 h-6 text-pink-400" />
                  <h2 className="text-xl font-bold text-white">إعدادات المظهر</h2>
                </div>

                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">المظهر</label>
                    <div className="grid grid-cols-2 gap-4">
                      <label className="relative">
                        <input
                          type="radio"
                          name="theme"
                          value="dark"
                          checked={formData.theme === 'dark'}
                          onChange={handleInputChange}
                          className="sr-only peer"
                        />
                        <div className="p-4 bg-slate-700 border-2 border-slate-600 rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-500/10">
                          <div className="text-center">
                            <div className="w-12 h-8 bg-slate-900 rounded mb-2 mx-auto"></div>
                            <span className="text-white text-sm">المظهر الداكن</span>
                          </div>
                        </div>
                      </label>

                      <label className="relative">
                        <input
                          type="radio"
                          name="theme"
                          value="light"
                          checked={formData.theme === 'light'}
                          onChange={handleInputChange}
                          className="sr-only peer"
                        />
                        <div className="p-4 bg-slate-700 border-2 border-slate-600 rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-500/10">
                          <div className="text-center">
                            <div className="w-12 h-8 bg-white rounded mb-2 mx-auto"></div>
                            <span className="text-white text-sm">المظهر الفاتح</span>
                          </div>
                        </div>
                      </label>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">اللون الأساسي</label>
                    <div className="grid grid-cols-6 gap-3">
                      {[
                        '#3b82f6', '#ef4444', '#10b981', '#f59e0b',
                        '#8b5cf6', '#06b6d4', '#84cc16', '#f97316',
                        '#ec4899', '#6366f1', '#14b8a6', '#eab308'
                      ].map(color => (
                        <button
                          key={color}
                          type="button"
                          onClick={() => setFormData(prev => ({ ...prev, primaryColor: color }))}
                          className={`w-12 h-12 rounded-lg border-2 transition-all ${
                            formData.primaryColor === color ? 'border-white scale-110' : 'border-slate-600'
                          }`}
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">حجم الخط</label>
                    <select
                      name="fontSize"
                      value={formData.fontSize}
                      onChange={handleInputChange}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:border-blue-500 focus:outline-none"
                    >
                      <option value="small">صغير</option>
                      <option value="medium">متوسط</option>
                      <option value="large">كبير</option>
                    </select>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
    </>
  );
};

export default Settings;