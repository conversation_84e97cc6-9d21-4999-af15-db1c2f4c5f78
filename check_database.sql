-- 🔍 فحص قاعدة البيانات للمنتجات غير المعروفة

-- تعيين المخطط
SET search_path TO pos_system, public;

-- 1. فحص وجود الجداول
SELECT 
    table_name,
    CASE 
        WHEN table_name IN (
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'pos_system'
        ) THEN '✅ موجود'
        ELSE '❌ غير موجود'
    END as status
FROM (
    VALUES 
        ('unknown_products'),
        ('unknown_sales'),
        ('sales'),
        ('products')
) AS t(table_name);

-- 2. فحص محتوى الجداول (إذا كانت موجودة)
DO $$
BEGIN
    -- فحص unknown_sales
    BEGIN
        PERFORM 1 FROM pos_system.unknown_sales LIMIT 1;
        RAISE NOTICE '📊 جدول unknown_sales: موجود';
        
        -- عرض عدد السجلات
        DECLARE
            count_unknown_sales INTEGER;
        BEGIN
            SELECT COUNT(*) INTO count_unknown_sales FROM pos_system.unknown_sales;
            RAISE NOTICE '📦 عدد المبيعات غير المعروفة: %', count_unknown_sales;
        END;
        
    EXCEPTION
        WHEN undefined_table THEN
            RAISE NOTICE '❌ جدول unknown_sales: غير موجود';
        WHEN OTHERS THEN
            RAISE NOTICE '⚠️ جدول unknown_sales: خطأ في الوصول';
    END;
    
    -- فحص unknown_products
    BEGIN
        PERFORM 1 FROM pos_system.unknown_products LIMIT 1;
        RAISE NOTICE '📊 جدول unknown_products: موجود';
        
        -- عرض عدد السجلات
        DECLARE
            count_unknown_products INTEGER;
        BEGIN
            SELECT COUNT(*) INTO count_unknown_products FROM pos_system.unknown_products;
            RAISE NOTICE '📦 عدد المنتجات غير المعروفة: %', count_unknown_products;
        END;
        
    EXCEPTION
        WHEN undefined_table THEN
            RAISE NOTICE '❌ جدول unknown_products: غير موجود';
        WHEN OTHERS THEN
            RAISE NOTICE '⚠️ جدول unknown_products: خطأ في الوصول';
    END;
    
    -- فحص sales العادية
    BEGIN
        DECLARE
            count_sales INTEGER;
        BEGIN
            SELECT COUNT(*) INTO count_sales FROM pos_system.sales;
            RAISE NOTICE '📦 عدد المبيعات العادية: %', count_sales;
        END;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE '❌ خطأ في قراءة جدول sales';
    END;
END $$;

-- 3. إنشاء الجداول إذا لم تكن موجودة
CREATE TABLE IF NOT EXISTS pos_system.unknown_products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    description TEXT DEFAULT 'منتج غير معروف',
    barcode VARCHAR(100) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS pos_system.unknown_sales (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sale_number VARCHAR(50) UNIQUE NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1 CHECK (quantity > 0),
    unit_price DECIMAL(10,2) NOT NULL CHECK (unit_price >= 0),
    total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount >= 0),
    payment_method VARCHAR(20) DEFAULT 'cash',
    notes TEXT DEFAULT 'منتج غير معروف',
    cashier_name VARCHAR(255) DEFAULT 'النظام',
    sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. إنشاء الفهارس
CREATE INDEX IF NOT EXISTS idx_unknown_products_name ON pos_system.unknown_products(name);
CREATE INDEX IF NOT EXISTS idx_unknown_sales_date ON pos_system.unknown_sales(sale_date);

-- 5. اختبار إدراج بيانات تجريبية
INSERT INTO pos_system.unknown_sales (
    sale_number, product_name, quantity, unit_price, total_amount, payment_method
) VALUES (
    'TEST-' || EXTRACT(EPOCH FROM NOW())::TEXT,
    'منتج تجريبي',
    1,
    100.00,
    100.00,
    'cash'
) ON CONFLICT (sale_number) DO NOTHING;

-- 6. فحص نهائي
SELECT 
    'unknown_sales' as table_name,
    COUNT(*) as record_count,
    MAX(sale_date) as latest_sale
FROM pos_system.unknown_sales

UNION ALL

SELECT 
    'sales' as table_name,
    COUNT(*) as record_count,
    MAX(created_at) as latest_sale
FROM pos_system.sales;

RAISE NOTICE '✅ تم الانتهاء من فحص قاعدة البيانات';
