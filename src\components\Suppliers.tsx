import React, { useState } from 'react';
import {
  Truck,
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  Phone,
  MapPin,
  Building,
  CreditCard,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle,
  BarChart3,
  Calculator,
  Users,
  RefreshCw,
  Star,
  Award,
  Receipt,
  Calendar,
  Target,
  Banknote,
  Crown,
  Download
} from 'lucide-react';
// @ts-ignore
import apiService from '../services/api';

interface SupplierDebt {
  id: string;
  amount: number;
  description: string;
  dueDate: Date;
  invoiceNumber?: string;
  status: 'pending' | 'overdue' | 'paid' | 'partial';
  createdDate: Date;
}

interface Supplier {
  id: string;
  name: string;
  company: string;
  phone: string;
  address: string;
  totalPurchases: number;
  totalDebt: number;
  paidAmount: number;
  lastPurchaseDate?: Date;
  status: 'active' | 'inactive' | 'blocked';
  rating: number; // من 1 إلى 5
  notes: string;
  createdDate: Date;
  joinDate: Date;
  debts: SupplierDebt[];
}

interface SupplierStats {
  totalSuppliers: number;
  activeSuppliers: number;
  totalDebt: number;
  overdueDebt: number;
  totalPurchases: number;
  averageDebt: number;
  topSupplier: string;
  riskSuppliers: number;
}

const Suppliers: React.FC = () => {
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'active' | 'inactive' | 'blocked'>('all');
  const [debtFilter, setDebtFilter] = useState<'all' | 'with_debt' | 'no_debt' | 'overdue'>('all');
  const [sortBy, setSortBy] = useState<'name' | 'debt' | 'purchases' | 'rating'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showDebtModal, setShowDebtModal] = useState(false);
  const [showPayDebtModal, setShowPayDebtModal] = useState(false);
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);
  const [selectedDebt, setSelectedDebt] = useState<SupplierDebt | null>(null);

  const [newSupplier, setNewSupplier] = useState({
    name: '',
    company: '',
    phone: '',
    address: '',
    notes: ''
  });

  const [newDebt, setNewDebt] = useState({
    amount: '',
    description: '',
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 يوم من اليوم
    invoiceNumber: ''
  });

  // حساب الإحصائيات
  const stats: SupplierStats = {
    totalSuppliers: suppliers.length,
    activeSuppliers: suppliers.filter(s => s.status === 'active').length,
    totalDebt: suppliers.reduce((sum, supplier) => sum + supplier.totalDebt, 0),
    overdueDebt: suppliers.reduce((sum, supplier) => {
      const overdueAmount = supplier.debts
        .filter(debt => debt.status === 'overdue')
        .reduce((debtSum, debt) => debtSum + debt.amount, 0);
      return sum + overdueAmount;
    }, 0),
    totalPurchases: suppliers.reduce((sum, supplier) => sum + supplier.totalPurchases, 0),
    averageDebt: suppliers.length > 0 ? suppliers.reduce((sum, supplier) => sum + supplier.totalDebt, 0) / suppliers.length : 0,
    topSupplier: suppliers.length > 0 ? suppliers.sort((a, b) => b.totalPurchases - a.totalPurchases)[0]?.name || 'غير محدد' : 'غير محدد',
    riskSuppliers: suppliers.filter(s => s.totalDebt > 100000 || s.debts.some(d => d.status === 'overdue')).length
  };

  // فلترة الموردين
  const filteredSuppliers = suppliers
    .filter(supplier => {
      const matchesSearch = supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           supplier.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           supplier.phone.includes(searchTerm);

      const matchesStatus = selectedStatus === 'all' || supplier.status === selectedStatus;

      let matchesDebt = true;
      switch (debtFilter) {
        case 'with_debt':
          matchesDebt = supplier.totalDebt > 0;
          break;
        case 'no_debt':
          matchesDebt = supplier.totalDebt === 0;
          break;
        case 'overdue':
          matchesDebt = supplier.debts.some(debt => debt.status === 'overdue');
          break;
        default:
          matchesDebt = true;
      }

      return matchesSearch && matchesStatus && matchesDebt;
    })
    .sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'debt':
          aValue = a.totalDebt;
          bValue = b.totalDebt;
          break;
        case 'purchases':
          aValue = a.totalPurchases;
          bValue = b.totalPurchases;
          break;
        case 'rating':
          aValue = a.rating;
          bValue = b.rating;
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

  const formatCurrency = (amount: number) => `${amount.toLocaleString()} دج`;
  const formatDate = (date: Date) => date.toLocaleDateString('ar-DZ');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400 bg-green-500/20 border-green-400/30';
      case 'inactive': return 'text-gray-400 bg-gray-500/20 border-gray-400/30';
      case 'blocked': return 'text-red-400 bg-red-500/20 border-red-400/30';
      default: return 'text-gray-400 bg-gray-500/20 border-gray-400/30';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'inactive': return 'غير نشط';
      case 'blocked': return 'محظور';
      default: return 'غير محدد';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4" />;
      case 'inactive': return <Clock className="w-4 h-4" />;
      case 'blocked': return <XCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const getDebtStatusColor = (debt: SupplierDebt) => {
    switch (debt.status) {
      case 'paid': return 'text-green-400 bg-green-500/20 border-green-400/30';
      case 'partial': return 'text-blue-400 bg-blue-500/20 border-blue-400/30';
      case 'pending': return 'text-yellow-400 bg-yellow-500/20 border-yellow-400/30';
      case 'overdue': return 'text-red-400 bg-red-500/20 border-red-400/30';
      default: return 'text-gray-400 bg-gray-500/20 border-gray-400/30';
    }
  };

  const getRatingStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-400'}`}
      />
    ));
  };

  const getRiskLevel = (supplier: Supplier) => {
    const hasOverdue = supplier.debts.some(debt => debt.status === 'overdue');
    const highDebt = supplier.totalDebt > 100000;

    if (hasOverdue && highDebt) return { level: 'عالي', color: 'text-red-400', icon: <AlertTriangle className="w-4 h-4" /> };
    if (hasOverdue || highDebt) return { level: 'متوسط', color: 'text-yellow-400', icon: <Clock className="w-4 h-4" /> };
    return { level: 'منخفض', color: 'text-green-400', icon: <CheckCircle className="w-4 h-4" /> };
  };

  // إضافة مورد جديد
  const addSupplier = async () => {
    if (!newSupplier.name.trim() || !newSupplier.phone.trim()) {
      alert('يرجى ملء الاسم ورقم الهاتف على الأقل');
      return;
    }

    try {
      const supplierData = {
        name: newSupplier.name.trim(),
        phone: newSupplier.phone.trim(),
        address: newSupplier.address.trim() || null
      };

      const response = await fetch('/api/suppliers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(supplierData),
      });

      if (response.ok) {
        const savedSupplier = await response.json();

        // تحديث القائمة من قاعدة البيانات
        await fetchSuppliers();
        setNewSupplier({
          name: '',
          company: '',
          phone: '',
          address: '',
          notes: ''
        });
        setShowAddModal(false);
        alert('✅ تم إضافة المورد بنجاح وحفظه في قاعدة البيانات!');
      } else {
        throw new Error('فشل في حفظ المورد');
      }
    } catch (error) {
      console.error('خطأ في إضافة المورد:', error);
      alert('❌ حدث خطأ في إضافة المورد. يرجى المحاولة مرة أخرى.');
    }
  };

  // إضافة دين جديد
  const addDebt = () => {
    if (!selectedSupplier || !newDebt.amount.trim() || !newDebt.description.trim()) {
      alert('يرجى ملء جميع البيانات المطلوبة');
      return;
    }

    const debt: SupplierDebt = {
      id: Date.now().toString(),
      amount: parseFloat(newDebt.amount),
      description: newDebt.description.trim(),
      dueDate: newDebt.dueDate,
      invoiceNumber: newDebt.invoiceNumber.trim(),
      status: 'pending',
      createdDate: new Date()
    };

    const updatedSupplier = {
      ...selectedSupplier,
      debts: [debt, ...selectedSupplier.debts],
      totalDebt: selectedSupplier.totalDebt + debt.amount
    };

    setSuppliers(suppliers.map(s => s.id === selectedSupplier.id ? updatedSupplier : s));
    setSelectedSupplier(updatedSupplier);

    setNewDebt({
      amount: '',
      description: '',
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      invoiceNumber: ''
    });

    setShowDebtModal(false);
    alert('تم إضافة الدين بنجاح!');
  };

  // تسديد دين
  const payDebt = async (paymentAmount: number, paymentMethod: string) => {
    if (!selectedSupplier || !selectedDebt) return;

    try {
      console.log(`💳 بدء دفع دين المورد: ${selectedDebt.id} - مبلغ: ${paymentAmount}`);

      // استدعاء API لدفع الدين
      const paymentData = {
        amount: paymentAmount,
        payment_method: paymentMethod,
        notes: `دفع دين للمورد ${selectedSupplier.name} - ${selectedDebt.description}`
      };

      const response = await apiService.paySupplierDebt(selectedDebt.id, paymentData);

      if (response) {
        console.log('✅ تم دفع الدين بنجاح:', response);

        // تحديث البيانات المحلية
        const updatedDebt = {
          ...selectedDebt,
          status: response.new_status === 'paid' ? 'paid' as const : 'partial' as const
        };

        const updatedSupplier = {
          ...selectedSupplier,
          debts: selectedSupplier.debts.map(debt =>
            debt.id === selectedDebt.id ? updatedDebt : debt
          ),
          totalDebt: selectedSupplier.totalDebt - paymentAmount,
          paidAmount: selectedSupplier.paidAmount + paymentAmount
        };

        setSuppliers(suppliers.map(s => s.id === selectedSupplier.id ? updatedSupplier : s));
        setSelectedSupplier(updatedSupplier);

        setShowPayDebtModal(false);
        setSelectedDebt(null);

        alert(`✅ تم تسديد الدين بنجاح!\n\n💰 المبلغ: ${formatCurrency(paymentAmount)}\n🏢 المورد: ${selectedSupplier.name}\n💳 طريقة الدفع: ${paymentMethod}\n\n🔗 تم إضافة المصروف تلقائياً إلى النظام\n📊 تم تحديث حالة الدين إلى: ${response.new_status === 'paid' ? 'مدفوع' : 'مدفوع جزئياً'}`);

        // إعادة تحميل البيانات للتأكد من التحديث
        fetchSuppliers();
        fetchGlobalStats();
      }
    } catch (error) {
      console.error('❌ خطأ في دفع دين المورد:', error);
      alert('❌ حدث خطأ أثناء دفع الدين. يرجى المحاولة مرة أخرى.');
    }
  };

  // تحديث حالة الديون المتأخرة
  const updateOverdueDebts = () => {
    const now = new Date();
    suppliers.forEach(supplier => {
      const hasOverdueDebts = supplier.debts.some(debt => {
        if (debt.status === 'pending' && debt.dueDate < now) {
          debt.status = 'overdue';
          return true;
        }
        return false;
      });

      if (hasOverdueDebts) {
        setSuppliers(prev => prev.map(s => s.id === supplier.id ? supplier : s));
      }
    });
  };

  // جلب الموردين من قاعدة البيانات مع الإحصائيات الحقيقية
  const fetchSuppliers = async () => {
    try {
      setLoading(true);
      console.log('🔄 جلب الموردين مع الإحصائيات...');

      const response = await fetch('/api/suppliers');
      if (response.ok) {
        const data = await response.json();
        console.log('📊 بيانات الموردين المستلمة:', data);

        // تحويل البيانات من قاعدة البيانات إلى تنسيق المكون مع الإحصائيات الحقيقية
        const formattedSuppliers = data.map((supplier: any) => ({
          id: supplier.id.toString(),
          name: supplier.name,
          company: '', // سيتم إضافته لاحقاً
          phone: supplier.phone || '',
          address: supplier.address || '',
          notes: '', // سيتم إضافته لاحقاً
          status: supplier.is_active ? 'active' as const : 'inactive' as const,
          rating: 4, // تقييم افتراضي

          // 💰 الإحصائيات الحقيقية من قاعدة البيانات
          totalPurchases: parseFloat(supplier.total_purchases) || 0,
          totalDebt: parseFloat(supplier.total_debt) || 0,
          paidAmount: parseFloat(supplier.total_paid) || 0,

          // 📊 إحصائيات إضافية
          totalInvoices: parseInt(supplier.total_invoices) || 0,
          averageInvoiceAmount: parseFloat(supplier.average_invoice_amount) || 0,
          unpaidInvoicesCount: parseInt(supplier.unpaid_invoices_count) || 0,
          outstandingDebt: parseFloat(supplier.outstanding_debt) || 0,

          // 📅 تواريخ
          lastPurchaseDate: supplier.last_purchase_date ? new Date(supplier.last_purchase_date) : null,
          createdDate: new Date(supplier.created_at),
          joinDate: new Date(supplier.created_at),

          // 🔍 ديون وهمية للتوافق مع النظام الحالي (سيتم استبدالها بديون حقيقية)
          debts: [] // سيتم جلب الديون الحقيقية من API منفصل
        }));

        setSuppliers(formattedSuppliers);
        console.log(`✅ تم تحويل ${formattedSuppliers.length} مورد مع الإحصائيات`);
      } else {
        console.error('❌ خطأ في الاستجابة:', response.status);
      }
    } catch (error) {
      console.error('❌ خطأ في جلب الموردين:', error);
    } finally {
      setLoading(false);
    }
  };

  // 🎯 حالة الإحصائيات العامة
  const [globalStats, setGlobalStats] = useState<any>(null);

  // جلب الإحصائيات العامة من الباك إند
  const fetchGlobalStats = async () => {
    try {
      console.log('📈 جلب الإحصائيات العامة...');
      const response = await fetch('/api/suppliers/stats/overview');
      if (response.ok) {
        const data = await response.json();
        setGlobalStats(data);
        console.log('✅ تم جلب الإحصائيات العامة:', data);
      }
    } catch (error) {
      console.error('❌ خطأ في جلب الإحصائيات العامة:', error);
    }
  };

  // تشغيل فحص الديون المتأخرة عند تحميل الصفحة
  React.useEffect(() => {
    fetchSuppliers(); // جلب الموردين من قاعدة البيانات
    fetchGlobalStats(); // جلب الإحصائيات العامة
    updateOverdueDebts();
  }, []);



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="bg-gradient-to-r from-blue-500 to-cyan-600 p-3 rounded-2xl shadow-lg animate-pulse">
              <Truck className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">🚛 إدارة الموردين</h1>
              <p className="text-gray-300">إدارة الموردين والديون المستحقة عليهم</p>
              <div className="mt-3 p-3 bg-blue-500/10 border border-blue-400/20 rounded-lg">
                <p className="text-blue-300 text-sm">
                  💡 <strong>إدارة ذكية:</strong> تتبع المشتريات والديون المستحقة لكل مورد مع تقييم المخاطر
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-4 space-x-reverse">
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-gradient-to-r from-blue-500 to-cyan-600 hover:scale-105 transform transition-all duration-300 px-6 py-3 rounded-xl font-bold text-white shadow-2xl hover:shadow-blue-500/25 flex items-center space-x-2 space-x-reverse"
            >
              <Plus className="w-5 h-5" />
              <span>إضافة مورد</span>
            </button>

            <button className="bg-white/10 backdrop-blur-lg border border-white/20 hover:border-white/40 px-4 py-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse text-white">
              <Download className="w-5 h-5" />
              <span>تصدير</span>
            </button>

            <button
              onClick={() => {
                fetchSuppliers();
                fetchGlobalStats();
              }}
              className="bg-white/10 backdrop-blur-lg border border-white/20 hover:border-white/40 px-4 py-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse text-white"
            >
              <RefreshCw className="w-5 h-5" />
              <span>تحديث</span>
            </button>
          </div>
        </div>

        {/* إحصائيات الموردين */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-blue-400/50 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">إجمالي الموردين</p>
                <p className="text-3xl font-bold text-white">
                  {globalStats?.total_suppliers || stats.totalSuppliers}
                </p>
                <div className="flex items-center space-x-1 space-x-reverse mt-2">
                  <Users className="w-4 h-4 text-blue-400" />
                  <span className="text-blue-400 text-sm font-semibold">مورد</span>
                </div>
              </div>
              <div className="bg-blue-500/20 p-3 rounded-xl">
                <Calculator className="w-6 h-6 text-blue-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-blue-400/50 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">إجمالي الديون</p>
                <p className="text-3xl font-bold text-white">
                  {formatCurrency(globalStats?.total_outstanding_debt || stats.totalDebt)}
                </p>
                <div className="flex items-center space-x-1 space-x-reverse mt-2">
                  <CreditCard className="w-4 h-4 text-red-400" />
                  <span className="text-red-400 text-sm font-semibold">مستحق</span>
                </div>
              </div>
              <div className="bg-red-500/20 p-3 rounded-xl">
                <DollarSign className="w-6 h-6 text-red-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-blue-400/50 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">ديون متأخرة</p>
                <p className="text-3xl font-bold text-white">{formatCurrency(stats.overdueDebt)}</p>
                <div className="flex items-center space-x-1 space-x-reverse mt-2">
                  <AlertTriangle className="w-4 h-4 text-orange-400" />
                  <span className="text-orange-400 text-sm font-semibold">متأخر</span>
                </div>
              </div>
              <div className="bg-orange-500/20 p-3 rounded-xl">
                <AlertTriangle className="w-6 h-6 text-orange-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-blue-400/50 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">موردين عالي المخاطر</p>
                <p className="text-3xl font-bold text-white">{stats.riskSuppliers}</p>
                <div className="flex items-center space-x-1 space-x-reverse mt-2">
                  <Target className="w-4 h-4 text-yellow-400" />
                  <span className="text-yellow-400 text-sm font-semibold">يحتاج متابعة</span>
                </div>
              </div>
              <div className="bg-yellow-500/20 p-3 rounded-xl">
                <Target className="w-6 h-6 text-yellow-400" />
              </div>
            </div>
          </div>
        </div>

        {/* إحصائيات إضافية */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <h3 className="text-xl font-bold text-white mb-4 flex items-center space-x-2 space-x-reverse">
              <BarChart3 className="w-6 h-6 text-green-400" />
              <span>إجمالي المشتريات</span>
            </h3>
            <div className="text-center">
              <p className="text-3xl font-bold text-green-400 mb-2">
                {formatCurrency(globalStats?.total_purchases_amount || stats.totalPurchases)}
              </p>
              <p className="text-gray-300 text-sm">من جميع الموردين</p>
              {globalStats && (
                <div className="mt-3 space-y-1">
                  <p className="text-xs text-gray-400">
                    📊 {globalStats.total_invoices} فاتورة مشتريات
                  </p>
                  <p className="text-xs text-gray-400">
                    📈 متوسط الفاتورة: {formatCurrency(globalStats.average_invoice_amount)}
                  </p>
                </div>
              )}
              <div className="w-full bg-gray-700 rounded-full h-2 mt-4">
                <div className="bg-gradient-to-r from-green-500 to-emerald-600 h-2 rounded-full transition-all duration-1000 w-full"></div>
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <h3 className="text-xl font-bold text-white mb-4 flex items-center space-x-2 space-x-reverse">
              <Award className="w-6 h-6 text-purple-400" />
              <span>متوسط الدين</span>
            </h3>
            <div className="text-center">
              <p className="text-3xl font-bold text-purple-400 mb-2">
                {formatCurrency(globalStats?.average_debt_per_supplier || stats.averageDebt)}
              </p>
              <p className="text-gray-300 text-sm">لكل مورد</p>
              {globalStats && (
                <div className="mt-3 space-y-1">
                  <p className="text-xs text-gray-400">
                    💰 إجمالي الديون: {formatCurrency(globalStats.total_outstanding_debt)}
                  </p>
                  <p className="text-xs text-gray-400">
                    📊 نسبة الديون: {globalStats.debt_to_purchases_ratio}%
                  </p>
                </div>
              )}
              <div className="flex items-center justify-center space-x-2 space-x-reverse mt-4">
                <Star className="w-5 h-5 text-yellow-400" />
                <span className="text-yellow-400 text-sm font-semibold">
                  {globalStats?.debt_to_purchases_ratio < 20 ? 'متوسط ممتاز' :
                   globalStats?.debt_to_purchases_ratio < 40 ? 'متوسط جيد' : 'يحتاج متابعة'}
                </span>
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <h3 className="text-xl font-bold text-white mb-4 flex items-center space-x-2 space-x-reverse">
              <Crown className="w-6 h-6 text-yellow-400" />
              <span>أفضل مورد</span>
            </h3>
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-400 mb-2">
                {globalStats?.top_supplier_by_purchases || stats.topSupplier}
              </p>
              <p className="text-gray-300 text-sm">أعلى مشتريات</p>
              {globalStats && (
                <div className="mt-3 space-y-1">
                  <p className="text-xs text-gray-400">
                    💰 مشتريات: {formatCurrency(globalStats.top_supplier_purchases_amount)}
                  </p>
                  <p className="text-xs text-gray-400">
                    📊 معدل الدفع: {globalStats.payment_completion_rate}%
                  </p>
                </div>
              )}
              <div className="flex items-center justify-center space-x-2 space-x-reverse mt-4">
                <Award className="w-5 h-5 text-yellow-400" />
                <span className="text-yellow-400 text-sm font-semibold">
                  {globalStats?.payment_completion_rate > 80 ? 'مورد ممتاز' : 'مورد مميز'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* شريط البحث والفلاتر */}
      <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 mb-8">
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-4">
          {/* البحث */}
          <div className="lg:col-span-2">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="البحث في الموردين..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-white/10 border border-white/20 rounded-xl px-12 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 transition-all duration-300"
              />
            </div>
          </div>

          {/* فلتر الحالة */}
          <div>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value as any)}
              className="w-full bg-blue-900/30 border border-blue-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-blue-400 transition-all duration-300 cursor-pointer hover:bg-blue-800/40"
              style={{
                backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233b82f6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'left 12px center',
                backgroundSize: '16px'
              }}
            >
              <option value="all" className="bg-blue-900 text-white">جميع الحالات</option>
              <option value="active" className="bg-blue-900 text-white">نشط</option>
              <option value="inactive" className="bg-blue-900 text-white">غير نشط</option>
              <option value="blocked" className="bg-blue-900 text-white">محظور</option>
            </select>
          </div>

          {/* فلتر الديون */}
          <div>
            <select
              value={debtFilter}
              onChange={(e) => setDebtFilter(e.target.value as any)}
              className="w-full bg-blue-900/30 border border-blue-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-blue-400 transition-all duration-300 cursor-pointer hover:bg-blue-800/40"
              style={{
                backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233b82f6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'left 12px center',
                backgroundSize: '16px'
              }}
            >
              <option value="all" className="bg-blue-900 text-white">جميع الديون</option>
              <option value="with_debt" className="bg-blue-900 text-white">عليه ديون</option>
              <option value="no_debt" className="bg-blue-900 text-white">بدون ديون</option>
              <option value="overdue" className="bg-blue-900 text-white">ديون متأخرة</option>
            </select>
          </div>

          {/* الترتيب */}
          <div>
            <select
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => {
                const [field, order] = e.target.value.split('-');
                setSortBy(field as any);
                setSortOrder(order as any);
              }}
              className="w-full bg-blue-900/30 border border-blue-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-blue-400 transition-all duration-300 cursor-pointer hover:bg-blue-800/40"
              style={{
                backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%233b82f6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'left 12px center',
                backgroundSize: '16px'
              }}
            >
              <option value="name-asc" className="bg-blue-900 text-white">الاسم (أ-ي)</option>
              <option value="name-desc" className="bg-blue-900 text-white">الاسم (ي-أ)</option>
              <option value="debt-desc" className="bg-blue-900 text-white">الدين (الأعلى)</option>
              <option value="debt-asc" className="bg-blue-900 text-white">الدين (الأقل)</option>
              <option value="purchases-desc" className="bg-blue-900 text-white">المشتريات (الأعلى)</option>
              <option value="rating-desc" className="bg-blue-900 text-white">التقييم (الأعلى)</option>
            </select>
          </div>
        </div>
      </div>

      {/* قائمة الموردين */}
      <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 overflow-hidden">
        {loading ? (
          <div className="text-center py-16">
            <RefreshCw className="w-20 h-20 text-blue-400 mx-auto mb-6 animate-spin" />
            <h3 className="text-2xl font-bold text-white mb-4">جاري تحميل الموردين...</h3>
            <p className="text-gray-400">يتم جلب البيانات من قاعدة البيانات</p>
          </div>
        ) : filteredSuppliers.length === 0 ? (
          <div className="text-center py-16">
            <Truck className="w-20 h-20 text-gray-400 mx-auto mb-6" />
            <h3 className="text-2xl font-bold text-white mb-4">
              {suppliers.length === 0 ? 'لا يوجد موردين مسجلين' : 'لا توجد نتائج للبحث'}
            </h3>
            <p className="text-gray-400 mb-4">
              {suppliers.length === 0
                ? 'ابدأ بإضافة أول مورد للشركة'
                : 'جرب تغيير معايير البحث أو الفلترة'
              }
            </p>
            {suppliers.length === 0 && (
              <div className="text-center">
                <p className="text-gray-500 text-sm mb-6">
                  إدارة الموردين مع تتبع الديون والمشتريات والتقييمات
                </p>
                <button
                  onClick={() => setShowAddModal(true)}
                  className="bg-gradient-to-r from-blue-500 to-cyan-600 hover:scale-105 transform transition-all duration-300 px-8 py-4 rounded-xl font-bold text-white shadow-2xl hover:shadow-blue-500/25 flex items-center space-x-2 space-x-reverse mx-auto"
                >
                  <Plus className="w-6 h-6" />
                  <span>إضافة أول مورد</span>
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
            {filteredSuppliers.map((supplier) => {
              const risk = getRiskLevel(supplier);
              return (
                <div
                  key={supplier.id}
                  className="bg-white/5 rounded-xl p-6 border border-white/10 hover:border-blue-400/50 transition-all duration-300 hover:scale-105"
                >
                  {/* رأس البطاقة */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-white mb-1">{supplier.name}</h3>
                      {supplier.company && (
                        <p className="text-gray-400 text-sm mb-2">{supplier.company}</p>
                      )}
                      <div className="flex items-center space-x-1 space-x-reverse">
                        {getRatingStars(supplier.rating)}
                      </div>
                    </div>
                    <div className={`inline-flex items-center space-x-2 space-x-reverse px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(supplier.status)}`}>
                      {getStatusIcon(supplier.status)}
                      <span>{getStatusText(supplier.status)}</span>
                    </div>
                  </div>

                  {/* معلومات الاتصال */}
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center space-x-2 space-x-reverse text-gray-300">
                      <Phone className="w-4 h-4 text-blue-400" />
                      <span className="text-sm">{supplier.phone}</span>
                    </div>
                    {supplier.address && (
                      <div className="flex items-center space-x-2 space-x-reverse text-gray-300">
                        <MapPin className="w-4 h-4 text-green-400" />
                        <span className="text-sm">{supplier.address}</span>
                      </div>
                    )}
                  </div>

                  {/* الإحصائيات المالية */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="bg-white/5 rounded-lg p-3">
                      <p className="text-gray-400 text-xs mb-1">إجمالي المشتريات</p>
                      <p className="text-white font-bold">{formatCurrency(supplier.totalPurchases)}</p>
                    </div>
                    <div className="bg-white/5 rounded-lg p-3">
                      <p className="text-gray-400 text-xs mb-1">الدين المستحق</p>
                      <p className={`font-bold ${supplier.totalDebt > 0 ? 'text-red-400' : 'text-green-400'}`}>
                        {formatCurrency(supplier.totalDebt)}
                      </p>
                    </div>
                  </div>

                  {/* مستوى المخاطر */}
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-gray-400 text-sm">مستوى المخاطر:</span>
                    <div className={`flex items-center space-x-1 space-x-reverse ${risk.color}`}>
                      {risk.icon}
                      <span className="text-sm font-semibold">{risk.level}</span>
                    </div>
                  </div>

                  {/* الإجراءات */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between space-x-2 space-x-reverse">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <button
                          onClick={() => {
                            setSelectedSupplier(supplier);
                            setShowDetailsModal(true);
                          }}
                          className="bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 p-2 rounded-lg transition-all duration-300 hover:scale-110"
                          title="عرض التفاصيل"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => {
                            setSelectedSupplier(supplier);
                            setShowDetailsModal(true);
                          }}
                          className="bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-400 p-2 rounded-lg transition-all duration-300 hover:scale-110"
                          title="تعديل"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          className="bg-red-500/20 hover:bg-red-500/30 text-red-400 p-2 rounded-lg transition-all duration-300 hover:scale-110"
                          title="حذف"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                      <button
                        onClick={() => {
                          setSelectedSupplier(supplier);
                          setShowDebtModal(true);
                        }}
                        className="bg-gradient-to-r from-orange-500 to-red-600 hover:scale-105 transform transition-all duration-300 px-3 py-2 rounded-lg font-bold text-white text-xs flex items-center space-x-1 space-x-reverse"
                      >
                        <CreditCard className="w-4 h-4" />
                        <span>إضافة دين</span>
                      </button>
                    </div>

                    {/* أزرار تسديد الديون */}
                    {supplier.totalDebt > 0 && (
                      <div className="grid grid-cols-2 gap-2">
                        <button
                          onClick={() => {
                            setSelectedSupplier(supplier);
                            setShowDetailsModal(true);
                          }}
                          className="bg-gradient-to-r from-green-500 to-emerald-600 hover:scale-105 transform transition-all duration-300 px-3 py-2 rounded-lg font-bold text-white text-xs flex items-center justify-center space-x-1 space-x-reverse"
                        >
                          <Banknote className="w-4 h-4" />
                          <span>عرض الديون</span>
                        </button>
                        {supplier.debts.filter(d => d.status === 'overdue').length > 0 && (
                          <button
                            className="bg-gradient-to-r from-red-500 to-pink-600 hover:scale-105 transform transition-all duration-300 px-3 py-2 rounded-lg font-bold text-white text-xs flex items-center justify-center space-x-1 space-x-reverse animate-pulse"
                          >
                            <AlertTriangle className="w-4 h-4" />
                            <span>ديون متأخرة</span>
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* 📊 إحصائيات متقدمة إضافية */}
      {globalStats && (
        <div className="mt-8 bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 p-6">
          <h3 className="text-2xl font-bold text-white mb-6 flex items-center space-x-2 space-x-reverse">
            <BarChart3 className="w-7 h-7 text-purple-400" />
            <span>📈 إحصائيات متقدمة</span>
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* إحصائيات الفواتير */}
            <div className="bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-xl p-4 border border-blue-400/30">
              <div className="flex items-center justify-between mb-3">
                <div className="bg-blue-500/30 p-2 rounded-lg">
                  <Receipt className="w-5 h-5 text-blue-400" />
                </div>
                <span className="text-blue-400 text-xs font-semibold">الفواتير</span>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-300 text-sm">إجمالي الفواتير:</span>
                  <span className="text-white font-bold">{globalStats.total_invoices}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300 text-sm">مدفوعة:</span>
                  <span className="text-green-400 font-bold">{globalStats.paid_invoices_count}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300 text-sm">غير مدفوعة:</span>
                  <span className="text-red-400 font-bold">{globalStats.unpaid_invoices_count}</span>
                </div>
              </div>
            </div>

            {/* إحصائيات زمنية */}
            <div className="bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-xl p-4 border border-green-400/30">
              <div className="flex items-center justify-between mb-3">
                <div className="bg-green-500/30 p-2 rounded-lg">
                  <Calendar className="w-5 h-5 text-green-400" />
                </div>
                <span className="text-green-400 text-xs font-semibold">آخر 30 يوم</span>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-300 text-sm">فواتير جديدة:</span>
                  <span className="text-white font-bold">{globalStats.invoices_last_30_days}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300 text-sm">مشتريات:</span>
                  <span className="text-green-400 font-bold">{formatCurrency(globalStats.purchases_last_30_days)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300 text-sm">آخر 7 أيام:</span>
                  <span className="text-yellow-400 font-bold">{globalStats.invoices_last_7_days}</span>
                </div>
              </div>
            </div>

            {/* نسب الأداء */}
            <div className="bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-xl p-4 border border-purple-400/30">
              <div className="flex items-center justify-between mb-3">
                <div className="bg-purple-500/30 p-2 rounded-lg">
                  <Target className="w-5 h-5 text-purple-400" />
                </div>
                <span className="text-purple-400 text-xs font-semibold">الأداء</span>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-300 text-sm">معدل الدفع:</span>
                  <span className="text-green-400 font-bold">{globalStats.payment_completion_rate}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300 text-sm">نسبة الديون:</span>
                  <span className="text-red-400 font-bold">{globalStats.debt_to_purchases_ratio}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300 text-sm">متوسط الفاتورة:</span>
                  <span className="text-blue-400 font-bold">{formatCurrency(globalStats.average_invoice_amount)}</span>
                </div>
              </div>
            </div>

            {/* ملخص مالي */}
            <div className="bg-gradient-to-br from-yellow-500/20 to-orange-500/20 rounded-xl p-4 border border-yellow-400/30">
              <div className="flex items-center justify-between mb-3">
                <div className="bg-yellow-500/30 p-2 rounded-lg">
                  <Banknote className="w-5 h-5 text-yellow-400" />
                </div>
                <span className="text-yellow-400 text-xs font-semibold">المالية</span>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-300 text-sm">إجمالي المدفوع:</span>
                  <span className="text-green-400 font-bold">{formatCurrency(globalStats.total_paid_amount)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300 text-sm">إجمالي الديون:</span>
                  <span className="text-red-400 font-bold">{formatCurrency(globalStats.total_outstanding_debt)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-300 text-sm">متوسط/مورد:</span>
                  <span className="text-purple-400 font-bold">{formatCurrency(globalStats.average_purchases_per_supplier)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* شريط التقدم للأداء العام */}
          <div className="mt-6 bg-white/5 rounded-xl p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-white font-semibold">الأداء العام للموردين</span>
              <span className="text-green-400 font-bold">{globalStats.payment_completion_rate}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-3">
              <div
                className="bg-gradient-to-r from-green-500 to-emerald-600 h-3 rounded-full transition-all duration-1000"
                style={{ width: `${globalStats.payment_completion_rate}%` }}
              ></div>
            </div>
            <div className="flex justify-between text-xs text-gray-400 mt-2">
              <span>ضعيف</span>
              <span>متوسط</span>
              <span>ممتاز</span>
            </div>
          </div>
        </div>
      )}

      {/* نموذج إضافة مورد جديد */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 rounded-2xl border border-white/20 w-full max-w-2xl max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b border-white/20">
              <div className="flex items-center justify-between">
                <h3 className="text-2xl font-bold text-white flex items-center space-x-2 space-x-reverse">
                  <Plus className="w-6 h-6 text-blue-400" />
                  <span>إضافة مورد جديد</span>
                </h3>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="text-gray-400 hover:text-white transition-colors duration-300"
                >
                  <XCircle className="w-8 h-8" />
                </button>
              </div>
            </div>

            <div className="p-6 overflow-y-auto max-h-[70vh]">
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      اسم المورد *
                    </label>
                    <input
                      type="text"
                      value={newSupplier.name}
                      onChange={(e) => setNewSupplier({...newSupplier, name: e.target.value})}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 transition-all duration-300"
                      placeholder="أدخل اسم المورد..."
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      اسم الشركة (اختياري)
                    </label>
                    <input
                      type="text"
                      value={newSupplier.company}
                      onChange={(e) => setNewSupplier({...newSupplier, company: e.target.value})}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 transition-all duration-300"
                      placeholder="أدخل اسم الشركة..."
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-gray-300 text-sm font-semibold mb-2">
                    رقم الهاتف *
                  </label>
                  <input
                    type="tel"
                    value={newSupplier.phone}
                    onChange={(e) => setNewSupplier({...newSupplier, phone: e.target.value})}
                    className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 transition-all duration-300"
                    placeholder="أدخل رقم الهاتف..."
                  />
                </div>

                <div>
                  <label className="block text-gray-300 text-sm font-semibold mb-2">
                    العنوان (اختياري)
                  </label>
                  <textarea
                    value={newSupplier.address}
                    onChange={(e) => setNewSupplier({...newSupplier, address: e.target.value})}
                    rows={3}
                    className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 transition-all duration-300 resize-none"
                    placeholder="أدخل العنوان..."
                  />
                </div>

                <div>
                  <label className="block text-gray-300 text-sm font-semibold mb-2">
                    ملاحظات
                  </label>
                  <textarea
                    value={newSupplier.notes}
                    onChange={(e) => setNewSupplier({...newSupplier, notes: e.target.value})}
                    rows={3}
                    className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 transition-all duration-300 resize-none"
                    placeholder="ملاحظات إضافية..."
                  />
                </div>
              </div>

              <div className="flex space-x-4 space-x-reverse pt-6 mt-6 border-t border-white/20">
                <button
                  onClick={addSupplier}
                  disabled={!newSupplier.name.trim() || !newSupplier.phone.trim()}
                  className="flex-1 bg-gradient-to-r from-blue-500 to-cyan-600 hover:scale-105 transform transition-all duration-300 py-3 rounded-xl font-bold text-white shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2 space-x-reverse"
                >
                  <Plus className="w-5 h-5" />
                  <span>إضافة المورد</span>
                </button>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="flex-1 bg-white/10 border border-white/20 hover:border-white/40 py-3 rounded-xl font-bold text-white transition-all duration-300 hover:scale-105"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* نموذج إضافة دين */}
      {showDebtModal && selectedSupplier && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gradient-to-br from-slate-900 via-red-900 to-slate-900 rounded-2xl border border-white/20 w-full max-w-2xl max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b border-white/20">
              <div className="flex items-center justify-between">
                <h3 className="text-2xl font-bold text-white flex items-center space-x-2 space-x-reverse">
                  <CreditCard className="w-6 h-6 text-red-400" />
                  <span>إضافة دين جديد</span>
                </h3>
                <button
                  onClick={() => setShowDebtModal(false)}
                  className="text-gray-400 hover:text-white transition-colors duration-300"
                >
                  <XCircle className="w-8 h-8" />
                </button>
              </div>
              <div className="mt-4 p-3 bg-blue-500/10 border border-blue-400/20 rounded-lg">
                <p className="text-blue-300 text-sm">
                  <strong>المورد:</strong> {selectedSupplier.name} - {selectedSupplier.company}
                </p>
              </div>
            </div>

            <div className="p-6 overflow-y-auto max-h-[70vh]">
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      مبلغ الدين (دج) *
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      value={newDebt.amount}
                      onChange={(e) => setNewDebt({...newDebt, amount: e.target.value})}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-red-400 transition-all duration-300"
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      تاريخ الاستحقاق
                    </label>
                    <input
                      type="date"
                      value={newDebt.dueDate.toISOString().split('T')[0]}
                      onChange={(e) => setNewDebt({...newDebt, dueDate: new Date(e.target.value)})}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-red-400 transition-all duration-300"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-gray-300 text-sm font-semibold mb-2">
                    وصف الدين *
                  </label>
                  <input
                    type="text"
                    value={newDebt.description}
                    onChange={(e) => setNewDebt({...newDebt, description: e.target.value})}
                    className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-red-400 transition-all duration-300"
                    placeholder="مثل: فاتورة مشتريات، مواد خام، etc."
                  />
                </div>

                <div>
                  <label className="block text-gray-300 text-sm font-semibold mb-2">
                    رقم الفاتورة
                  </label>
                  <input
                    type="text"
                    value={newDebt.invoiceNumber}
                    onChange={(e) => setNewDebt({...newDebt, invoiceNumber: e.target.value})}
                    className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-red-400 transition-all duration-300"
                    placeholder="رقم الفاتورة (اختياري)"
                  />
                </div>
              </div>

              <div className="flex space-x-4 space-x-reverse pt-6 mt-6 border-t border-white/20">
                <button
                  onClick={addDebt}
                  disabled={!newDebt.amount.trim() || !newDebt.description.trim()}
                  className="flex-1 bg-gradient-to-r from-red-500 to-orange-600 hover:scale-105 transform transition-all duration-300 py-3 rounded-xl font-bold text-white shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2 space-x-reverse"
                >
                  <CreditCard className="w-5 h-5" />
                  <span>إضافة الدين</span>
                </button>
                <button
                  onClick={() => setShowDebtModal(false)}
                  className="flex-1 bg-white/10 border border-white/20 hover:border-white/40 py-3 rounded-xl font-bold text-white transition-all duration-300 hover:scale-105"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* نموذج تفاصيل المورد مع إدارة الديون */}
      {showDetailsModal && selectedSupplier && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 rounded-2xl border border-white/20 w-full max-w-4xl max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b border-white/20">
              <div className="flex items-center justify-between">
                <h3 className="text-2xl font-bold text-white flex items-center space-x-2 space-x-reverse">
                  <Building className="w-6 h-6 text-blue-400" />
                  <span>تفاصيل المورد - {selectedSupplier.name}</span>
                </h3>
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="text-gray-400 hover:text-white transition-colors duration-300"
                >
                  <XCircle className="w-8 h-8" />
                </button>
              </div>
            </div>

            <div className="p-6 overflow-y-auto max-h-[75vh]">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* معلومات المورد */}
                <div className="space-y-6">
                  <h4 className="text-xl font-bold text-white mb-4 flex items-center space-x-2 space-x-reverse">
                    <Users className="w-6 h-6 text-green-400" />
                    <span>معلومات المورد</span>
                  </h4>

                  <div className="bg-white/5 rounded-xl p-4 space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-gray-400 text-sm">الاسم</p>
                        <p className="text-white font-semibold">{selectedSupplier.name}</p>
                      </div>
                      <div>
                        <p className="text-gray-400 text-sm">الشركة</p>
                        <p className="text-white font-semibold">{selectedSupplier.company || 'غير محدد'}</p>
                      </div>
                      <div>
                        <p className="text-gray-400 text-sm">الهاتف</p>
                        <p className="text-white font-semibold">{selectedSupplier.phone}</p>
                      </div>
                      <div>
                        <p className="text-gray-400 text-sm">التقييم</p>
                        <div className="flex items-center space-x-1 space-x-reverse">
                          {getRatingStars(selectedSupplier.rating)}
                        </div>
                      </div>
                    </div>

                    {selectedSupplier.address && (
                      <div>
                        <p className="text-gray-400 text-sm">العنوان</p>
                        <p className="text-white font-semibold">{selectedSupplier.address}</p>
                      </div>
                    )}

                    <div className="grid grid-cols-3 gap-4 pt-4 border-t border-white/10">
                      <div className="text-center">
                        <p className="text-gray-400 text-xs">إجمالي المشتريات</p>
                        <p className="text-green-400 font-bold text-lg">{formatCurrency(selectedSupplier.totalPurchases)}</p>
                      </div>
                      <div className="text-center">
                        <p className="text-gray-400 text-xs">الدين المستحق</p>
                        <p className={`font-bold text-lg ${selectedSupplier.totalDebt > 0 ? 'text-red-400' : 'text-green-400'}`}>
                          {formatCurrency(selectedSupplier.totalDebt)}
                        </p>
                      </div>
                      <div className="text-center">
                        <p className="text-gray-400 text-xs">المبلغ المدفوع</p>
                        <p className="text-blue-400 font-bold text-lg">{formatCurrency(selectedSupplier.paidAmount)}</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* إدارة الديون */}
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h4 className="text-xl font-bold text-white flex items-center space-x-2 space-x-reverse">
                      <CreditCard className="w-6 h-6 text-red-400" />
                      <span>إدارة الديون ({selectedSupplier.debts.length})</span>
                    </h4>
                    <button
                      onClick={() => {
                        setShowDebtModal(true);
                        setShowDetailsModal(false);
                      }}
                      className="bg-gradient-to-r from-orange-500 to-red-600 hover:scale-105 transform transition-all duration-300 px-4 py-2 rounded-lg font-bold text-white text-sm flex items-center space-x-1 space-x-reverse"
                    >
                      <Plus className="w-4 h-4" />
                      <span>إضافة دين</span>
                    </button>
                  </div>

                  <div className="bg-white/5 rounded-xl max-h-96 overflow-y-auto">
                    {selectedSupplier.debts.length === 0 ? (
                      <div className="text-center py-8">
                        <CheckCircle className="w-12 h-12 text-green-400 mx-auto mb-4" />
                        <p className="text-green-400 font-semibold">لا توجد ديون مستحقة</p>
                        <p className="text-gray-400 text-sm">هذا المورد ليس عليه أي ديون</p>
                      </div>
                    ) : (
                      <div className="space-y-3 p-4">
                        {selectedSupplier.debts.map((debt) => (
                          <div
                            key={debt.id}
                            className={`p-4 rounded-lg border transition-all duration-300 ${getDebtStatusColor(debt)}`}
                          >
                            <div className="flex items-center justify-between mb-2">
                              <div>
                                <p className="font-semibold">{debt.description}</p>
                                <p className="text-sm opacity-75">
                                  {debt.invoiceNumber && `فاتورة: ${debt.invoiceNumber} • `}
                                  تاريخ الإنشاء: {formatDate(debt.createdDate)}
                                </p>
                              </div>
                              <div className="text-right">
                                <p className="font-bold text-lg">{formatCurrency(debt.amount)}</p>
                                <p className="text-sm opacity-75">
                                  استحقاق: {formatDate(debt.dueDate)}
                                </p>
                              </div>
                            </div>

                            {debt.status === 'pending' && (
                              <div className="flex space-x-2 space-x-reverse mt-3">
                                <button
                                  onClick={() => {
                                    setSelectedDebt(debt);
                                    setShowPayDebtModal(true);
                                  }}
                                  className="flex-1 bg-gradient-to-r from-green-500 to-emerald-600 hover:scale-105 transform transition-all duration-300 py-2 rounded-lg font-bold text-white text-sm flex items-center justify-center space-x-1 space-x-reverse"
                                >
                                  <Banknote className="w-4 h-4" />
                                  <span>تسديد</span>
                                </button>
                                <button
                                  className="bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-400 px-3 py-2 rounded-lg transition-all duration-300 hover:scale-110"
                                  title="تعديل"
                                >
                                  <Edit className="w-4 h-4" />
                                </button>
                              </div>
                            )}

                            {debt.status === 'overdue' && (
                              <div className="flex items-center space-x-2 space-x-reverse mt-3 p-2 bg-red-500/20 rounded-lg">
                                <AlertTriangle className="w-5 h-5 text-red-400" />
                                <span className="text-red-400 font-semibold text-sm">دين متأخر - يحتاج تسديد فوري</span>
                              </div>
                            )}

                            {debt.status === 'paid' && (
                              <div className="flex items-center space-x-2 space-x-reverse mt-3 p-2 bg-green-500/20 rounded-lg">
                                <CheckCircle className="w-5 h-5 text-green-400" />
                                <span className="text-green-400 font-semibold text-sm">تم التسديد</span>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* نموذج تسديد دين */}
      {showPayDebtModal && selectedDebt && selectedSupplier && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gradient-to-br from-slate-900 via-green-900 to-slate-900 rounded-2xl border border-white/20 w-full max-w-2xl max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b border-white/20">
              <div className="flex items-center justify-between">
                <h3 className="text-2xl font-bold text-white flex items-center space-x-2 space-x-reverse">
                  <Banknote className="w-6 h-6 text-green-400" />
                  <span>تسديد دين</span>
                </h3>
                <button
                  onClick={() => setShowPayDebtModal(false)}
                  className="text-gray-400 hover:text-white transition-colors duration-300"
                >
                  <XCircle className="w-8 h-8" />
                </button>
              </div>
              <div className="mt-4 p-4 bg-blue-500/10 border border-blue-400/20 rounded-lg">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-400">المورد:</p>
                    <p className="text-white font-semibold">{selectedSupplier.name}</p>
                  </div>
                  <div>
                    <p className="text-gray-400">المبلغ:</p>
                    <p className="text-green-400 font-bold">{formatCurrency(selectedDebt.amount)}</p>
                  </div>
                  <div>
                    <p className="text-gray-400">الوصف:</p>
                    <p className="text-white">{selectedDebt.description}</p>
                  </div>
                  <div>
                    <p className="text-gray-400">تاريخ الاستحقاق:</p>
                    <p className="text-white">{formatDate(selectedDebt.dueDate)}</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="p-6">
              <div className="space-y-6">
                <div>
                  <label className="block text-gray-300 text-sm font-semibold mb-2">
                    طريقة الدفع
                  </label>
                  <select
                    className="w-full bg-green-900/30 border border-green-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-green-400 transition-all duration-300 cursor-pointer"
                  >
                    <option value="cash">نقدي</option>
                    <option value="transfer">تحويل بنكي</option>
                    <option value="card">بطاقة ائتمان</option>
                    <option value="check">شيك</option>
                  </select>
                </div>

                <div className="bg-green-500/10 border border-green-400/20 rounded-lg p-4">
                  <h4 className="text-green-400 font-semibold mb-2">تأكيد التسديد</h4>
                  <p className="text-gray-300 text-sm mb-4">
                    سيتم تسديد دين بقيمة <span className="text-green-400 font-bold">{formatCurrency(selectedDebt.amount)}</span> للمورد <span className="text-white font-semibold">{selectedSupplier.name}</span>
                  </p>
                  <div className="text-xs text-gray-400 space-y-1">
                    <p>• سيتم تحديث رصيد المورد تلقائياً</p>
                    <p>• سيتم إضافة المصروف إلى النظام</p>
                    <p>• سيتم تحديث الإحصائيات</p>
                  </div>
                </div>
              </div>

              <div className="flex space-x-4 space-x-reverse pt-6 mt-6 border-t border-white/20">
                <button
                  onClick={() => payDebt(selectedDebt.amount, 'cash')}
                  className="flex-1 bg-gradient-to-r from-green-500 to-emerald-600 hover:scale-105 transform transition-all duration-300 py-3 rounded-xl font-bold text-white shadow-lg flex items-center justify-center space-x-2 space-x-reverse"
                >
                  <CheckCircle className="w-5 h-5" />
                  <span>تأكيد التسديد</span>
                </button>
                <button
                  onClick={() => setShowPayDebtModal(false)}
                  className="flex-1 bg-white/10 border border-white/20 hover:border-white/40 py-3 rounded-xl font-bold text-white transition-all duration-300 hover:scale-105"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Suppliers;
