const pool = require('./database');

async function testDebtors() {
  try {
    console.log('🔍 اختبار استعلام المدينون...');
    
    // اختبار الاستعلام الذي يسبب المشكلة
    const result = await pool.query(`
      SELECT
        c.id,
        c.name,
        c.phone,
        c.email,
        c.address,
        c.balance,
        c.credit_limit,
        c.created_at,
        c.updated_at,
        COALESCE(debt_summary.total_debt, 0) as total_debt,
        COALESCE(debt_summary.paid_amount, 0) as paid_amount,
        COALESCE(debt_summary.remaining_debt, 0) as remaining_debt,
        COALESCE(debt_summary.debt_count, 0) as debt_count
      FROM pos_system.customers c
      LEFT JOIN (
        SELECT
          customer_id,
          SUM(amount) as total_debt,
          SUM(paid_amount) as paid_amount,
          SUM(amount - paid_amount) as remaining_debt,
          COUNT(*) as debt_count
        FROM pos_system.customer_debts
        WHERE status IN ('pending', 'partial')
        GROUP BY customer_id
      ) debt_summary ON c.id = debt_summary.customer_id
      WHERE (c.balance < 0 OR debt_summary.remaining_debt > 0)
        AND c.is_active = true
      ORDER BY debt_summary.remaining_debt DESC, c.balance ASC
    `);
    
    console.log('✅ نجح الاستعلام!');
    console.log('📊 عدد المدينين:', result.rows.length);
    
    if (result.rows.length > 0) {
      console.log('👤 أول مدين:', result.rows[0]);
    }
    
  } catch (error) {
    console.error('❌ خطأ في الاستعلام:');
    console.error('رسالة:', error.message);
    console.error('كود:', error.code);
    console.error('تفاصيل:', error.detail);
  }
  
  process.exit(0);
}

testDebtors();
