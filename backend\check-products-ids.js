const pool = require('./database');

async function checkProductIds() {
  try {
    console.log('🔍 فحص معرفات المنتجات...');
    
    const result = await pool.query(`
      SELECT id, name, price, stock 
      FROM pos_system.products 
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    
    console.log(`📦 تم العثور على ${result.rows.length} منتج:`);
    
    result.rows.forEach((product, index) => {
      console.log(`${index + 1}. ID: ${product.id}`);
      console.log(`   الاسم: ${product.name}`);
      console.log(`   السعر: ${product.price} دج`);
      console.log(`   المخزون: ${product.stock}`);
      console.log('   ---');
    });
    
  } catch (error) {
    console.error('❌ خطأ في فحص المنتجات:', error);
  } finally {
    process.exit(0);
  }
}

checkProductIds();
