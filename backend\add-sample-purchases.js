const pool = require('./database');

async function addSamplePurchases() {
  try {
    console.log('🔄 إضافة فواتير مشتريات تجريبية...');

    // الحصول على معرفات الموردين
    const suppliersResult = await pool.query('SELECT id, name FROM pos_system.suppliers LIMIT 2');
    
    if (suppliersResult.rows.length < 2) {
      console.log('❌ نحتاج على الأقل مورديْن في قاعدة البيانات');
      return;
    }

    const supplier1 = suppliersResult.rows[0];
    const supplier2 = suppliersResult.rows[1];

    console.log(`📋 الموردين: ${supplier1.name} و ${supplier2.name}`);

    // إضافة فاتورة مشتريات للمورد الأول (مدفوعة جزئياً - يعني فيها دين)
    await pool.query(`
      INSERT INTO pos_system.purchases (
        invoice_number, supplier_id, total_amount, amount_paid, 
        invoice_date, notes
      ) VALUES ($1, $2, $3, $4, $5, $6)
    `, [
      'PUR-001', 
      supplier1.id, 
      50000, // إجمالي 50,000 دج
      30000, // مدفوع 30,000 دج (يعني دين 20,000 دج)
      new Date(), 
      'فاتورة مشتريات تجريبية - مدفوعة جزئياً'
    ]);

    // إضافة فاتورة مشتريات للمورد الثاني (غير مدفوعة - كلها دين)
    await pool.query(`
      INSERT INTO pos_system.purchases (
        invoice_number, supplier_id, total_amount, amount_paid, 
        invoice_date, notes
      ) VALUES ($1, $2, $3, $4, $5, $6)
    `, [
      'PUR-002', 
      supplier2.id, 
      75000, // إجمالي 75,000 دج
      0,     // مدفوع 0 دج (يعني دين 75,000 دج)
      new Date(), 
      'فاتورة مشتريات تجريبية - غير مدفوعة'
    ]);

    // إضافة فاتورة أخرى للمورد الأول (مدفوعة كاملة)
    await pool.query(`
      INSERT INTO pos_system.purchases (
        invoice_number, supplier_id, total_amount, amount_paid, 
        invoice_date, notes
      ) VALUES ($1, $2, $3, $4, $5, $6)
    `, [
      'PUR-003', 
      supplier1.id, 
      25000, // إجمالي 25,000 دج
      25000, // مدفوع 25,000 دج (مدفوعة كاملة)
      new Date(), 
      'فاتورة مشتريات تجريبية - مدفوعة كاملة'
    ]);

    console.log('✅ تم إضافة فواتير المشتريات التجريبية بنجاح!');
    
    // عرض النتائج
    const result = await pool.query(`
      SELECT 
        s.name as supplier_name,
        COUNT(p.id) as total_invoices,
        SUM(p.total_amount) as total_purchases,
        SUM(p.amount_paid) as total_paid,
        SUM(p.total_amount - p.amount_paid) as total_debt
      FROM pos_system.suppliers s
      LEFT JOIN pos_system.purchases p ON s.id = p.supplier_id
      GROUP BY s.id, s.name
      ORDER BY s.name
    `);

    console.log('\n📊 إحصائيات الموردين بعد إضافة الفواتير:');
    result.rows.forEach(row => {
      console.log(`- ${row.supplier_name}:`);
      console.log(`  📋 فواتير: ${row.total_invoices}`);
      console.log(`  💰 إجمالي المشتريات: ${row.total_purchases} دج`);
      console.log(`  ✅ مدفوع: ${row.total_paid} دج`);
      console.log(`  ❌ دين: ${row.total_debt} دج`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ خطأ في إضافة فواتير المشتريات:', error.message);
  } finally {
    process.exit(0);
  }
}

addSamplePurchases();
