import React, { useState, useMemo } from 'react';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Tag,
  Package,
  MoreVertical,
  Save,
  X,
  AlertCircle,
  Palette,
  Grid,
  List,
  Filter,
  BarChart3,
  PieChart,
  TrendingUp,

  Eye,
  Star,
  Award,
  Target,
  Zap,
  Activity,
  Layers,
  Box,
  Sparkles,
  Crown,
  Gem,
  Heart,
  Flame,
  Coffee,
  ShoppingBag,
  Home,
  Car,

  Camera,
  Gamepad2,
  Shirt,
  Monitor,
  Smartphone,
  Headphones,
  Watch,
  Gift,
  Utensils,
  Dumbbell,
  Book,
  Briefcase,
  Scissors,
  Paintbrush,

  Stethoscope,
  GraduationCap,
  Plane,
  TreePine,
  Sun,
  Moon,
  Cloud,
  Umbrella,
  Snowflake,
  Flower,
  Leaf,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>
} from 'lucide-react';
import { useApp } from '../context/AppContext';

// تم نقل واجهة Category إلى AppContext

const Categories: React.FC = () => {
  const { categories, products, addCategory, updateCategory, deleteCategory, deleteAllCategories } = useApp();
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'name' | 'products' | 'created'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [filterBy, setFilterBy] = useState<'all' | 'used' | 'empty'>('all');
  const [showStats, setShowStats] = useState(true);
  const [showDeleteAllConfirm, setShowDeleteAllConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    color: '#3b82f6',
    icon: 'Package'
  });

  const colorOptions = [
    { name: 'أزرق', value: '#3b82f6', gradient: 'from-blue-500 to-blue-600' },
    { name: 'أحمر', value: '#ef4444', gradient: 'from-red-500 to-red-600' },
    { name: 'أخضر', value: '#10b981', gradient: 'from-green-500 to-green-600' },
    { name: 'أصفر', value: '#f59e0b', gradient: 'from-yellow-500 to-yellow-600' },
    { name: 'بنفسجي', value: '#8b5cf6', gradient: 'from-purple-500 to-purple-600' },
    { name: 'سماوي', value: '#06b6d4', gradient: 'from-cyan-500 to-cyan-600' },
    { name: 'ليموني', value: '#84cc16', gradient: 'from-lime-500 to-lime-600' },
    { name: 'برتقالي', value: '#f97316', gradient: 'from-orange-500 to-orange-600' },
    { name: 'وردي', value: '#ec4899', gradient: 'from-pink-500 to-pink-600' },
    { name: 'نيلي', value: '#6366f1', gradient: 'from-indigo-500 to-indigo-600' },
    { name: 'تركوازي', value: '#14b8a6', gradient: 'from-teal-500 to-teal-600' },
    { name: 'ذهبي', value: '#eab308', gradient: 'from-amber-500 to-amber-600' }
  ];

  const iconCategories = {
    'عام': ['Package', 'Tag', 'Grid', 'Box', 'Layers'],
    'تجاري': ['ShoppingBag', 'Briefcase', 'Target', 'TrendingUp', 'Award'],
    'منزلي': ['Home', 'Coffee', 'Utensils', 'Gift', 'Heart'],
    'تقني': ['Monitor', 'Smartphone', 'Headphones', 'Camera', 'Gamepad2'],
    'أزياء': ['Shirt', 'Watch', 'Crown', 'Gem', 'Sparkles'],
    'رياضة': ['Dumbbell', 'Activity', 'Zap', 'Star', 'Flame'],
    'تعليم': ['Book', 'GraduationCap', 'Palette', 'Paintbrush', 'Scissors'],
    'صحة': ['Stethoscope', 'Plus', 'AlertCircle', 'Eye', 'Leaf'],
    'سفر': ['Plane', 'Car', 'Umbrella', 'Sun', 'Cloud'],
    'طبيعة': ['TreePine', 'Flower', 'Butterfly', 'Bird', 'Fish'],
    'حيوانات': ['Cat', 'Dog', 'Rabbit', 'Fish', 'Bird'],
    'طقس': ['Sun', 'Moon', 'Cloud', 'Umbrella', 'Snowflake']
  };

  // حساب عدد المنتجات لكل فئة
  const getCategoryProductCount = (categoryId: string) => {
    return products.filter(product => product.category === categoryId && product.isActive).length;
  };

  // 🧮 حسابات الإحصائيات المتقدمة
  const stats = useMemo(() => {
    const totalCategories = categories.length;
    const usedCategories = categories.filter(cat => getCategoryProductCount(cat.id) > 0).length;
    const emptyCategories = totalCategories - usedCategories;
    const totalProducts = products.filter(p => p.isActive).length;
    const avgProductsPerCategory = totalCategories > 0 ? totalProducts / totalCategories : 0;

    // أكثر الأقسام استخدام<|im_start|>
    const mostUsedCategory = categories.reduce((max, cat) => {
      const catCount = getCategoryProductCount(cat.id);
      const maxCount = getCategoryProductCount(max.id || '');
      return catCount > maxCount ? cat : max;
    }, categories[0] || { id: '', name: '', color: '#3b82f6' });

    // قيمة المخزون لكل قسم
    const categoryValues = categories.map(cat => {
      const categoryProducts = products.filter(p => p.category === cat.id && p.isActive);
      const totalValue = categoryProducts.reduce((sum, p) => sum + (p.price * p.stock), 0);
      const totalCost = categoryProducts.reduce((sum, p) => sum + (p.cost * p.stock), 0);
      const profit = totalValue - totalCost;

      return {
        ...cat,
        productCount: categoryProducts.length,
        totalValue,
        totalCost,
        profit,
        profitMargin: totalCost > 0 ? (profit / totalCost) * 100 : 0
      };
    });

    const topValueCategory = categoryValues.reduce((max, cat) =>
      cat.totalValue > max.totalValue ? cat : max,
      categoryValues[0] || { totalValue: 0, name: '', color: '#3b82f6' }
    );

    return {
      totalCategories,
      usedCategories,
      emptyCategories,
      totalProducts,
      avgProductsPerCategory,
      mostUsedCategory,
      topValueCategory,
      categoryValues
    };
  }, [categories, products, getCategoryProductCount]);

  // 🔍 تصفية وترتيب الأقسام
  const filteredAndSortedCategories = useMemo(() => {
    let filtered = categories.filter(category => {
      const matchesSearch =
        category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        category.description.toLowerCase().includes(searchTerm.toLowerCase());

      const productCount = getCategoryProductCount(category.id);
      const matchesFilter =
        filterBy === 'all' ||
        (filterBy === 'used' && productCount > 0) ||
        (filterBy === 'empty' && productCount === 0);

      return matchesSearch && matchesFilter;
    });

    // ترتيب الأقسام
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'products':
          aValue = getCategoryProductCount(a.id);
          bValue = getCategoryProductCount(b.id);
          break;
        case 'created':
          aValue = new Date(a.createdDate).getTime();
          bValue = new Date(b.createdDate).getTime();
          break;
        default:
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [categories, searchTerm, filterBy, sortBy, sortOrder]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const categoryData = {
      name: formData.name,
      description: formData.description,
      color: formData.color,
      icon: formData.icon,
      isActive: true
    };

    if (editingCategory) {
      updateCategory(editingCategory.id, categoryData);
    } else {
      addCategory(categoryData);
    }

    setShowAddForm(false);
    setEditingCategory(null);
    setFormData({
      name: '',
      description: '',
      color: '#3b82f6',
      icon: 'Package'
    });
  };

  const handleEdit = (category: any) => {
    setEditingCategory(category);
    setFormData({
      name: category.name,
      description: category.description,
      color: category.color,
      icon: category.icon
    });
    setShowAddForm(true);
  };

  const handleDelete = (categoryId: string) => {
    const productCount = getCategoryProductCount(categoryId);
    if (productCount > 0) {
      alert(`لا يمكن حذف هذا القسم لأنه يحتوي على ${productCount} منتج. يرجى نقل المنتجات إلى قسم آخر أولاً.`);
      return;
    }

    if (window.confirm('هل أنت متأكد من حذف هذا القسم؟')) {
      deleteCategory(categoryId);
    }
  };

  // 🗑️ حذف جميع الأقسام
  const handleDeleteAllCategories = async () => {
    setIsDeleting(true);
    try {
      await deleteAllCategories();
      setShowDeleteAllConfirm(false);
    } catch (error) {
      console.error('خطأ في حذف جميع الأقسام:', error);
      alert('حدث خطأ أثناء حذف جميع الأقسام');
    } finally {
      setIsDeleting(false);
    }
  };

  const getIconComponent = (iconName: string, size: string = "w-6 h-6") => {
    const icons: { [key: string]: React.ReactNode } = {
      // عام
      Package: <Package className={size} />,
      Tag: <Tag className={size} />,
      Grid: <Grid className={size} />,
      Box: <Box className={size} />,
      Layers: <Layers className={size} />,

      // تجاري
      ShoppingBag: <ShoppingBag className={size} />,
      Briefcase: <Briefcase className={size} />,
      Target: <Target className={size} />,
      TrendingUp: <TrendingUp className={size} />,
      Award: <Award className={size} />,

      // منزلي
      Home: <Home className={size} />,
      Coffee: <Coffee className={size} />,
      Utensils: <Utensils className={size} />,
      Gift: <Gift className={size} />,
      Heart: <Heart className={size} />,

      // تقني
      Monitor: <Monitor className={size} />,
      Smartphone: <Smartphone className={size} />,
      Headphones: <Headphones className={size} />,
      Camera: <Camera className={size} />,
      Gamepad2: <Gamepad2 className={size} />,

      // أزياء
      Shirt: <Shirt className={size} />,
      Watch: <Watch className={size} />,
      Crown: <Crown className={size} />,
      Gem: <Gem className={size} />,
      Sparkles: <Sparkles className={size} />,

      // رياضة
      Dumbbell: <Dumbbell className={size} />,
      Activity: <Activity className={size} />,
      Zap: <Zap className={size} />,
      Star: <Star className={size} />,
      Flame: <Flame className={size} />,

      // تعليم
      Book: <Book className={size} />,
      GraduationCap: <GraduationCap className={size} />,
      Palette: <Palette className={size} />,
      Paintbrush: <Paintbrush className={size} />,
      Scissors: <Scissors className={size} />,

      // صحة
      Stethoscope: <Stethoscope className={size} />,
      Plus: <Plus className={size} />,
      AlertCircle: <AlertCircle className={size} />,
      Eye: <Eye className={size} />,
      Leaf: <Leaf className={size} />,

      // سفر
      Plane: <Plane className={size} />,
      Car: <Car className={size} />,
      Umbrella: <Umbrella className={size} />,
      Sun: <Sun className={size} />,
      Cloud: <Cloud className={size} />,

      // طبيعة
      TreePine: <TreePine className={size} />,
      Flower: <Flower className={size} />,
      Bird: <Bird className={size} />,
      Fish: <Fish className={size} />,

      // حيوانات
      Cat: <Cat className={size} />,
      Dog: <Dog className={size} />,
      Rabbit: <Rabbit className={size} />,

      // طقس
      Moon: <Moon className={size} />,
      Snowflake: <Snowflake className={size} />,

      // أساسية
      Search: <Search className={size} />,
      Edit: <Edit className={size} />,
      Trash2: <Trash2 className={size} />,
      Save: <Save className={size} />,
      X: <X className={size} />,
      MoreVertical: <MoreVertical className={size} />
    };
    return icons[iconName] || <Package className={size} />;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6 relative overflow-hidden">
      {/* خلفية متحركة مذهلة */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-indigo-500/10 rounded-full blur-3xl animate-spin-slow"></div>
      </div>

      <div className="max-w-7xl mx-auto space-y-8 relative z-10">
        {/* 🎨 Header المبهر الجديد */}
        <div className="relative overflow-hidden bg-gradient-to-r from-purple-900/40 via-blue-900/40 to-indigo-900/40 backdrop-blur-xl rounded-3xl border border-purple-500/30 p-8 shadow-2xl">
          {/* تأثيرات الخلفية المتحركة */}
          <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 via-blue-600/10 to-indigo-600/10 animate-gradient-x"></div>
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 via-blue-500 to-indigo-500 animate-shimmer"></div>

          <div className="relative flex items-center justify-between">
            <div className="flex items-center space-x-6 space-x-reverse">
              <div className="relative group">
                <div className="bg-gradient-to-r from-purple-500 via-blue-500 to-indigo-500 p-4 rounded-2xl shadow-2xl transform group-hover:scale-110 transition-all duration-500">
                  <Tag className="w-12 h-12 text-white" />
                </div>
                <div className="absolute -top-3 -right-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full p-2 animate-bounce">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/50 to-blue-500/50 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>
              <div>
                <h1 className="text-5xl font-black text-transparent bg-gradient-to-r from-purple-400 via-blue-400 to-indigo-400 bg-clip-text mb-3 animate-text-shimmer">
                  إدارة الأقسام الاحترافية
                </h1>
                <p className="text-purple-200 text-xl font-medium">نظام ذكي متطور لتصنيف وإدارة أقسام المنتجات بأحدث التقنيات</p>
                <div className="flex items-center space-x-4 space-x-reverse mt-3">
                  <div className="flex items-center space-x-2 space-x-reverse bg-green-500/20 px-3 py-1 rounded-full">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-green-300 text-sm font-medium">نظام نشط</span>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse bg-blue-500/20 px-3 py-1 rounded-full">
                    <Activity className="w-4 h-4 text-blue-300" />
                    <span className="text-blue-300 text-sm font-medium">تحديث مباشر</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4 space-x-reverse">
              <button
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                className="group relative bg-white/10 hover:bg-white/20 backdrop-blur-lg border border-white/20 p-4 rounded-xl transition-all duration-300 hover:scale-110 hover:shadow-2xl"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                {viewMode === 'grid' ? <List className="w-6 h-6 text-white relative z-10" /> : <Grid className="w-6 h-6 text-white relative z-10" />}
              </button>

              <button
                onClick={() => setShowStats(!showStats)}
                className="group relative bg-white/10 hover:bg-white/20 backdrop-blur-lg border border-white/20 p-4 rounded-xl transition-all duration-300 hover:scale-110 hover:shadow-2xl"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <BarChart3 className="w-6 h-6 text-white relative z-10" />
              </button>

              {categories.length > 0 && (
                <button
                  onClick={() => setShowDeleteAllConfirm(true)}
                  className="group relative bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white px-6 py-4 rounded-xl font-bold transition-all duration-300 hover:scale-110 flex items-center space-x-2 space-x-reverse shadow-2xl hover:shadow-red-500/25"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-red-400/20 to-pink-400/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <Trash2 className="w-5 h-5 relative z-10" />
                  <span className="relative z-10">حذف جميع الأقسام</span>
                </button>
              )}

              <button
                onClick={() => setShowAddForm(true)}
                className="group relative bg-gradient-to-r from-purple-500 via-blue-500 to-indigo-500 hover:from-purple-600 hover:via-blue-600 hover:to-indigo-600 text-white px-8 py-4 rounded-xl font-bold transition-all duration-300 hover:scale-110 flex items-center space-x-2 space-x-reverse shadow-2xl hover:shadow-purple-500/25"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-purple-400/20 via-blue-400/20 to-indigo-400/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <Plus className="w-6 h-6 relative z-10" />
                <span className="relative z-10">إضافة قسم جديد</span>
                <Crown className="w-5 h-5 relative z-10 animate-pulse" />
              </button>
            </div>
          </div>
        </div>

        {/* 📊 لوحة الإحصائيات المبهرة المحسنة */}
        {showStats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* إجمالي الأقسام */}
            <div className="group relative overflow-hidden bg-gradient-to-br from-purple-500/15 via-purple-600/10 to-blue-500/15 backdrop-blur-xl rounded-3xl border border-purple-300/30 p-8 hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/25 transition-all duration-500 transform-gpu">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-600/5 via-blue-600/5 to-indigo-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="absolute top-0 right-0 w-24 h-24 bg-purple-400/20 rounded-full -translate-y-12 translate-x-12 group-hover:scale-150 transition-transform duration-500"></div>

              <div className="relative z-10">
                <div className="flex items-center justify-between mb-6">
                  <div className="bg-gradient-to-br from-purple-500/40 to-blue-500/40 p-4 rounded-2xl group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <Tag className="w-8 h-8 text-purple-200 group-hover:text-white transition-colors duration-300" />
                  </div>
                  <div className="text-right">
                    <p className="text-purple-200/80 text-sm font-medium mb-1">إجمالي الأقسام</p>
                    <p className="text-4xl font-black text-transparent bg-gradient-to-r from-white via-purple-200 to-blue-200 bg-clip-text group-hover:from-purple-300 group-hover:via-blue-300 group-hover:to-indigo-300 transition-all duration-500">
                      {stats.totalCategories}
                    </p>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 space-x-reverse bg-green-500/20 px-3 py-2 rounded-xl">
                    <TrendingUp className="w-4 h-4 text-green-300" />
                    <span className="text-green-300 text-sm font-medium">نشط</span>
                  </div>
                  <div className="text-purple-200/60 text-xs">
                    {new Date().toLocaleDateString('ar-SA')}
                  </div>
                </div>

                {/* شريط التقدم */}
                <div className="mt-4 w-full bg-purple-500/20 rounded-full h-2">
                  <div className="bg-gradient-to-r from-purple-400 to-blue-400 h-2 rounded-full transition-all duration-1000" style={{ width: '100%' }}></div>
                </div>
              </div>
            </div>

            {/* الأقسام المستخدمة */}
            <div className="group relative overflow-hidden bg-gradient-to-br from-green-500/15 via-emerald-600/10 to-teal-500/15 backdrop-blur-xl rounded-3xl border border-green-300/30 p-8 hover:scale-105 hover:shadow-2xl hover:shadow-green-500/25 transition-all duration-500 transform-gpu">
              <div className="absolute inset-0 bg-gradient-to-br from-green-600/5 via-emerald-600/5 to-teal-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="absolute top-0 right-0 w-24 h-24 bg-green-400/20 rounded-full -translate-y-12 translate-x-12 group-hover:scale-150 transition-transform duration-500"></div>

              <div className="relative z-10">
                <div className="flex items-center justify-between mb-6">
                  <div className="bg-gradient-to-br from-green-500/40 to-emerald-500/40 p-4 rounded-2xl group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <Package className="w-8 h-8 text-green-200 group-hover:text-white transition-colors duration-300" />
                  </div>
                  <div className="text-right">
                    <p className="text-green-200/80 text-sm font-medium mb-1">الأقسام المستخدمة</p>
                    <p className="text-4xl font-black text-transparent bg-gradient-to-r from-white via-green-200 to-emerald-200 bg-clip-text group-hover:from-green-300 group-hover:via-emerald-300 group-hover:to-teal-300 transition-all duration-500">
                      {stats.usedCategories}
                    </p>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 space-x-reverse bg-blue-500/20 px-3 py-2 rounded-xl">
                    <Activity className="w-4 h-4 text-blue-300" />
                    <span className="text-blue-300 text-sm font-medium">تحتوي منتجات</span>
                  </div>
                  <div className="text-green-200/60 text-xs">
                    {((stats.usedCategories / stats.totalCategories) * 100).toFixed(0)}% مستخدمة
                  </div>
                </div>

                {/* شريط التقدم */}
                <div className="mt-4 w-full bg-green-500/20 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-green-400 to-emerald-400 h-2 rounded-full transition-all duration-1000"
                    style={{ width: `${(stats.usedCategories / Math.max(stats.totalCategories, 1)) * 100}%` }}
                  ></div>
                </div>
              </div>
            </div>

            {/* إجمالي المنتجات */}
            <div className="group relative overflow-hidden bg-gradient-to-br from-blue-500/15 via-cyan-600/10 to-sky-500/15 backdrop-blur-xl rounded-3xl border border-blue-300/30 p-8 hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/25 transition-all duration-500 transform-gpu">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 via-cyan-600/5 to-sky-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="absolute top-0 right-0 w-24 h-24 bg-blue-400/20 rounded-full -translate-y-12 translate-x-12 group-hover:scale-150 transition-transform duration-500"></div>

              <div className="relative z-10">
                <div className="flex items-center justify-between mb-6">
                  <div className="bg-gradient-to-br from-blue-500/40 to-cyan-500/40 p-4 rounded-2xl group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <Box className="w-8 h-8 text-blue-200 group-hover:text-white transition-colors duration-300" />
                  </div>
                  <div className="text-right">
                    <p className="text-blue-200/80 text-sm font-medium mb-1">إجمالي المنتجات</p>
                    <p className="text-4xl font-black text-transparent bg-gradient-to-r from-white via-blue-200 to-cyan-200 bg-clip-text group-hover:from-blue-300 group-hover:via-cyan-300 group-hover:to-sky-300 transition-all duration-500">
                      {stats.totalProducts}
                    </p>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 space-x-reverse bg-purple-500/20 px-3 py-2 rounded-xl">
                    <Target className="w-4 h-4 text-purple-300" />
                    <span className="text-purple-300 text-sm font-medium">موزعة</span>
                  </div>
                  <div className="text-blue-200/60 text-xs">
                    في {stats.totalCategories} قسم
                  </div>
                </div>

                {/* شريط التقدم */}
                <div className="mt-4 w-full bg-blue-500/20 rounded-full h-2">
                  <div className="bg-gradient-to-r from-blue-400 to-cyan-400 h-2 rounded-full transition-all duration-1000 animate-pulse" style={{ width: '85%' }}></div>
                </div>
              </div>
            </div>

            {/* متوسط المنتجات */}
            <div className="group relative overflow-hidden bg-gradient-to-br from-orange-500/15 via-amber-600/10 to-yellow-500/15 backdrop-blur-xl rounded-3xl border border-orange-300/30 p-8 hover:scale-105 hover:shadow-2xl hover:shadow-orange-500/25 transition-all duration-500 transform-gpu">
              <div className="absolute inset-0 bg-gradient-to-br from-orange-600/5 via-amber-600/5 to-yellow-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="absolute top-0 right-0 w-24 h-24 bg-orange-400/20 rounded-full -translate-y-12 translate-x-12 group-hover:scale-150 transition-transform duration-500"></div>

              <div className="relative z-10">
                <div className="flex items-center justify-between mb-6">
                  <div className="bg-gradient-to-br from-orange-500/40 to-amber-500/40 p-4 rounded-2xl group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <BarChart3 className="w-8 h-8 text-orange-200 group-hover:text-white transition-colors duration-300" />
                  </div>
                  <div className="text-right">
                    <p className="text-orange-200/80 text-sm font-medium mb-1">متوسط المنتجات</p>
                    <p className="text-4xl font-black text-transparent bg-gradient-to-r from-white via-orange-200 to-amber-200 bg-clip-text group-hover:from-orange-300 group-hover:via-amber-300 group-hover:to-yellow-300 transition-all duration-500">
                      {Math.round(stats.avgProductsPerCategory)}
                    </p>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 space-x-reverse bg-indigo-500/20 px-3 py-2 rounded-xl">
                    <PieChart className="w-4 h-4 text-indigo-300" />
                    <span className="text-indigo-300 text-sm font-medium">لكل قسم</span>
                  </div>
                  <div className="text-orange-200/60 text-xs">
                    توزيع متوازن
                  </div>
                </div>

                {/* شريط التقدم */}
                <div className="mt-4 w-full bg-orange-500/20 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-orange-400 to-amber-400 h-2 rounded-full transition-all duration-1000"
                    style={{ width: `${Math.min((stats.avgProductsPerCategory / 10) * 100, 100)}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 🏆 أقسام مميزة محسنة */}
        {stats.mostUsedCategory && stats.mostUsedCategory.name && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* القسم الأكثر استخداماً */}
            <div className="group relative overflow-hidden bg-gradient-to-br from-yellow-500/15 via-orange-500/10 to-amber-500/15 backdrop-blur-xl rounded-3xl border border-yellow-300/30 p-8 hover:scale-105 hover:shadow-2xl hover:shadow-yellow-500/25 transition-all duration-500">
              <div className="absolute inset-0 bg-gradient-to-br from-yellow-600/5 via-orange-600/5 to-amber-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="absolute top-0 right-0 w-32 h-32 bg-yellow-400/20 rounded-full -translate-y-16 translate-x-16 group-hover:scale-125 transition-transform duration-500"></div>

              {/* تأثير الجزيئات الذهبية */}
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                <div className="absolute top-4 left-4 w-2 h-2 bg-yellow-400 rounded-full animate-bounce-gentle"></div>
                <div className="absolute top-8 right-6 w-1 h-1 bg-orange-400 rounded-full animate-bounce-gentle delay-300"></div>
                <div className="absolute bottom-6 left-8 w-1.5 h-1.5 bg-amber-400 rounded-full animate-bounce-gentle delay-700"></div>
              </div>

              <div className="relative z-10">
                <div className="flex items-center space-x-6 space-x-reverse">
                  <div className="relative">
                    <div className="bg-gradient-to-br from-yellow-500/40 to-orange-500/40 p-5 rounded-2xl group-hover:scale-110 transition-transform duration-300 shadow-2xl">
                      <Crown className="w-10 h-10 text-yellow-200 group-hover:text-yellow-100 transition-colors duration-300" />
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-br from-yellow-400/30 to-orange-400/30 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  </div>

                  <div className="flex-1">
                    <div className="flex items-center space-x-2 space-x-reverse mb-3">
                      <h3 className="text-2xl font-black text-transparent bg-gradient-to-r from-yellow-300 via-orange-300 to-amber-300 bg-clip-text">
                        القسم الأكثر استخداماً
                      </h3>
                      <div className="bg-yellow-500/20 px-2 py-1 rounded-lg">
                        <span className="text-yellow-300 text-xs font-bold">👑 الأول</span>
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-2xl p-4 border border-yellow-400/20">
                      <p className="text-yellow-100 font-bold text-xl mb-2">
                        {stats.mostUsedCategory.name}
                      </p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <Package className="w-5 h-5 text-yellow-300" />
                          <span className="text-yellow-200 font-semibold">
                            {getCategoryProductCount(stats.mostUsedCategory.id)} منتج
                          </span>
                        </div>
                        <div className="bg-yellow-500/20 px-3 py-1 rounded-xl">
                          <span className="text-yellow-300 text-sm font-bold">
                            {((getCategoryProductCount(stats.mostUsedCategory.id) / stats.totalProducts) * 100).toFixed(1)}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* القسم الأعلى قيمة */}
            <div className="group relative overflow-hidden bg-gradient-to-br from-emerald-500/15 via-teal-500/10 to-green-500/15 backdrop-blur-xl rounded-3xl border border-emerald-300/30 p-8 hover:scale-105 hover:shadow-2xl hover:shadow-emerald-500/25 transition-all duration-500">
              <div className="absolute inset-0 bg-gradient-to-br from-emerald-600/5 via-teal-600/5 to-green-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="absolute top-0 right-0 w-32 h-32 bg-emerald-400/20 rounded-full -translate-y-16 translate-x-16 group-hover:scale-125 transition-transform duration-500"></div>

              {/* تأثير الجزيئات الزمردية */}
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                <div className="absolute top-4 left-4 w-2 h-2 bg-emerald-400 rounded-full animate-bounce-gentle"></div>
                <div className="absolute top-8 right-6 w-1 h-1 bg-teal-400 rounded-full animate-bounce-gentle delay-300"></div>
                <div className="absolute bottom-6 left-8 w-1.5 h-1.5 bg-green-400 rounded-full animate-bounce-gentle delay-700"></div>
              </div>

              <div className="relative z-10">
                <div className="flex items-center space-x-6 space-x-reverse">
                  <div className="relative">
                    <div className="bg-gradient-to-br from-emerald-500/40 to-teal-500/40 p-5 rounded-2xl group-hover:scale-110 transition-transform duration-300 shadow-2xl">
                      <Gem className="w-10 h-10 text-emerald-200 group-hover:text-emerald-100 transition-colors duration-300" />
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-br from-emerald-400/30 to-teal-400/30 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  </div>

                  <div className="flex-1">
                    <div className="flex items-center space-x-2 space-x-reverse mb-3">
                      <h3 className="text-2xl font-black text-transparent bg-gradient-to-r from-emerald-300 via-teal-300 to-green-300 bg-clip-text">
                        القسم الأعلى قيمة
                      </h3>
                      <div className="bg-emerald-500/20 px-2 py-1 rounded-lg">
                        <span className="text-emerald-300 text-xs font-bold">💎 الأغلى</span>
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-emerald-500/10 to-teal-500/10 rounded-2xl p-4 border border-emerald-400/20">
                      <p className="text-emerald-100 font-bold text-xl mb-2">
                        {stats.topValueCategory.name}
                      </p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <TrendingUp className="w-5 h-5 text-emerald-300" />
                          <span className="text-emerald-200 font-semibold">
                            {stats.topValueCategory.totalValue ? `${(stats.topValueCategory.totalValue / 1000).toFixed(1)}ك دج` : '0 دج'}
                          </span>
                        </div>
                        <div className="bg-emerald-500/20 px-3 py-1 rounded-xl">
                          <span className="text-emerald-300 text-sm font-bold">
                            {stats.topValueCategory.totalValue && stats.categoryValues.reduce((sum, cat) => sum + cat.totalValue, 0) > 0
                              ? ((stats.topValueCategory.totalValue / stats.categoryValues.reduce((sum, cat) => sum + cat.totalValue, 0)) * 100).toFixed(1)
                              : '0'
                            }%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 🎨 شريط البحث والتصفية المتقدم */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 p-6">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
            {/* البحث */}
            <div className="lg:col-span-2 relative">
              <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-purple-300" />
              <input
                type="text"
                placeholder="البحث في الأقسام بالاسم أو الوصف..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-white/10 border border-white/20 rounded-xl px-12 py-3 text-white placeholder-purple-200 focus:border-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400/50 transition-all"
              />
            </div>

            {/* تصفية */}
            <div className="relative">
              <Filter className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-blue-300" />
              <select
                value={filterBy}
                onChange={(e) => setFilterBy(e.target.value as any)}
                className="w-full bg-white/10 border border-white/20 rounded-xl px-10 py-3 text-white focus:border-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-400/50 transition-all appearance-none cursor-pointer"
              >
                <option value="all" className="bg-gray-800">جميع الأقسام</option>
                <option value="used" className="bg-gray-800">الأقسام المستخدمة</option>
                <option value="empty" className="bg-gray-800">الأقسام الفارغة</option>
              </select>
            </div>

            {/* ترتيب */}
            <div className="relative">
              <BarChart3 className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-green-300" />
              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [newSortBy, newSortOrder] = e.target.value.split('-');
                  setSortBy(newSortBy as any);
                  setSortOrder(newSortOrder as any);
                }}
                className="w-full bg-white/10 border border-white/20 rounded-xl px-10 py-3 text-white focus:border-green-400 focus:outline-none focus:ring-2 focus:ring-green-400/50 transition-all appearance-none cursor-pointer"
              >
                <option value="name-asc" className="bg-gray-800">الاسم (أ-ي)</option>
                <option value="name-desc" className="bg-gray-800">الاسم (ي-أ)</option>
                <option value="products-desc" className="bg-gray-800">المنتجات (الأكثر)</option>
                <option value="products-asc" className="bg-gray-800">المنتجات (الأقل)</option>
                <option value="created-desc" className="bg-gray-800">الأحدث</option>
                <option value="created-asc" className="bg-gray-800">الأقدم</option>
              </select>
            </div>
          </div>

          {/* إحصائيات البحث */}
          <div className="mt-4 flex items-center justify-between">
            <div className="flex items-center space-x-4 space-x-reverse">
              <span className="text-purple-200">
                عرض {filteredAndSortedCategories.length} من {stats.totalCategories} قسم
              </span>
              {searchTerm && (
                <span className="bg-purple-500/20 text-purple-300 px-3 py-1 rounded-lg text-sm">
                  البحث: "{searchTerm}"
                </span>
              )}
              {filterBy !== 'all' && (
                <span className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-lg text-sm">
                  الفلتر: {filterBy === 'used' ? 'مستخدمة' : 'فارغة'}
                </span>
              )}
            </div>

            {(searchTerm || filterBy !== 'all') && (
              <button
                onClick={() => {
                  setSearchTerm('');
                  setFilterBy('all');
                }}
                className="text-red-300 hover:text-red-200 text-sm flex items-center space-x-1 space-x-reverse"
              >
                <X className="w-4 h-4" />
                <span>مسح الفلاتر</span>
              </button>
            )}
          </div>
        </div>

      {/* Add/Edit Form Modal */}
      {showAddForm && (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white/10 backdrop-blur-lg rounded-3xl border border-white/20 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="p-8 border-b border-white/20 flex items-center justify-between">
              <div className="flex items-center space-x-4 space-x-reverse">
                <div className="bg-gradient-to-r from-purple-500 to-blue-600 p-3 rounded-xl">
                  {editingCategory ? <Edit className="w-6 h-6 text-white" /> : <Plus className="w-6 h-6 text-white" />}
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-white">
                    {editingCategory ? 'تعديل القسم' : 'إضافة قسم جديد'}
                  </h2>
                  <p className="text-purple-200">
                    {editingCategory ? 'قم بتعديل بيانات القسم' : 'أضف قسم جديد لتصنيف منتجاتك'}
                  </p>
                </div>
              </div>
              <button
                onClick={() => {
                  setShowAddForm(false);
                  setEditingCategory(null);
                }}
                className="text-white/60 hover:text-white p-3 hover:bg-white/10 rounded-xl transition-all duration-300"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="p-8 space-y-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* الجانب الأيسر - البيانات الأساسية */}
                <div className="space-y-6">
                  {/* اسم القسم */}
                  <div>
                    <label className="block text-sm font-semibold text-white mb-3 flex items-center space-x-2 space-x-reverse">
                      <Tag className="w-4 h-4 text-purple-400" />
                      <span>اسم القسم *</span>
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-purple-200 focus:border-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400/50 transition-all"
                      placeholder="أدخل اسم القسم"
                    />
                  </div>

                  {/* الوصف */}
                  <div>
                    <label className="block text-sm font-semibold text-white mb-3 flex items-center space-x-2 space-x-reverse">
                      <Edit className="w-4 h-4 text-blue-400" />
                      <span>الوصف</span>
                    </label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows={4}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-purple-200 focus:border-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-400/50 transition-all resize-none"
                      placeholder="وصف مفصل للقسم..."
                    />
                  </div>

                  {/* اختيار اللون */}
                  <div>
                    <label className="block text-sm font-semibold text-white mb-3 flex items-center space-x-2 space-x-reverse">
                      <Palette className="w-4 h-4 text-pink-400" />
                      <span>اللون المميز</span>
                    </label>
                    <div className="grid grid-cols-6 gap-3">
                      {colorOptions.map(colorOption => (
                        <button
                          key={colorOption.value}
                          type="button"
                          onClick={() => setFormData(prev => ({ ...prev, color: colorOption.value }))}
                          className={`group relative w-12 h-12 rounded-xl border-2 transition-all duration-300 hover:scale-110 ${
                            formData.color === colorOption.value
                              ? 'border-white scale-110 shadow-lg'
                              : 'border-white/30 hover:border-white/60'
                          }`}
                          style={{ backgroundColor: colorOption.value }}
                          title={colorOption.name}
                        >
                          {formData.color === colorOption.value && (
                            <div className="absolute inset-0 flex items-center justify-center">
                              <Star className="w-5 h-5 text-white" />
                            </div>
                          )}
                        </button>
                      ))}
                    </div>
                    <p className="text-purple-200 text-sm mt-2">
                      اللون المختار: <span className="font-semibold">{colorOptions.find(c => c.value === formData.color)?.name}</span>
                    </p>
                  </div>
                </div>

                {/* الجانب الأيمن - الأيقونة والمعاينة */}
                <div className="space-y-6">
                  {/* اختيار الأيقونة */}
                  <div>
                    <label className="block text-sm font-semibold text-white mb-3 flex items-center space-x-2 space-x-reverse">
                      <Sparkles className="w-4 h-4 text-yellow-400" />
                      <span>الأيقونة</span>
                    </label>

                    {/* أيقونات مصنفة */}
                    <div className="space-y-4 max-h-64 overflow-y-auto bg-white/5 rounded-xl p-4">
                      {Object.entries(iconCategories).map(([categoryName, icons]) => (
                        <div key={categoryName}>
                          <h4 className="text-white font-medium mb-2 text-sm">{categoryName}</h4>
                          <div className="grid grid-cols-5 gap-2">
                            {icons.map(iconName => (
                              <button
                                key={iconName}
                                type="button"
                                onClick={() => setFormData(prev => ({ ...prev, icon: iconName }))}
                                className={`p-2 rounded-lg border transition-all duration-300 hover:scale-110 ${
                                  formData.icon === iconName
                                    ? 'border-white bg-white/20 scale-110'
                                    : 'border-white/20 hover:border-white/40 hover:bg-white/10'
                                }`}
                                title={iconName}
                              >
                                {getIconComponent(iconName, "w-5 h-5 text-white")}
                              </button>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* 🎭 معاينة مباشرة */}
              <div className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-lg rounded-2xl border border-white/20 p-6">
                <label className="block text-sm font-semibold text-white mb-4 flex items-center space-x-2 space-x-reverse">
                  <Eye className="w-4 h-4 text-green-400" />
                  <span>معاينة مباشرة</span>
                </label>

                {/* معاينة بطاقة القسم */}
                <div className="relative overflow-hidden bg-white/10 backdrop-blur-lg rounded-xl border border-white/20 p-6 hover:scale-105 transition-all duration-300">
                  <div className="absolute top-0 right-0 w-16 h-16 bg-white/10 rounded-full -translate-y-8 translate-x-8"></div>
                  <div className="relative">
                    <div className="flex items-center justify-between mb-4">
                      <div
                        className={`w-14 h-14 rounded-xl flex items-center justify-center text-white shadow-lg bg-gradient-to-br ${
                          colorOptions.find(c => c.value === formData.color)?.gradient || 'from-blue-500 to-blue-600'
                        }`}
                      >
                        {getIconComponent(formData.icon, "w-7 h-7")}
                      </div>
                      <div className="text-right">
                        <span className="bg-green-500/20 text-green-300 px-2 py-1 rounded-lg text-xs font-medium">
                          نشط
                        </span>
                      </div>
                    </div>

                    <div className="mb-4">
                      <h3 className="text-xl font-bold text-white mb-2">
                        {formData.name || 'اسم القسم'}
                      </h3>
                      <p className="text-purple-200 text-sm">
                        {formData.description || 'وصف القسم سيظهر هنا...'}
                      </p>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-blue-400">0</p>
                        <p className="text-purple-200 text-xs">منتج</p>
                      </div>
                      <div className="text-center">
                        <p className="text-purple-200 text-xs">تاريخ الإنشاء</p>
                        <p className="text-white text-sm">{new Date().toLocaleDateString('ar-SA')}</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* معلومات إضافية */}
                <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
                  <div className="bg-white/5 rounded-lg p-3">
                    <p className="text-purple-200">اللون المختار</p>
                    <div className="flex items-center space-x-2 space-x-reverse mt-1">
                      <div
                        className="w-4 h-4 rounded-full border border-white/30"
                        style={{ backgroundColor: formData.color }}
                      ></div>
                      <span className="text-white font-medium">
                        {colorOptions.find(c => c.value === formData.color)?.name}
                      </span>
                    </div>
                  </div>
                  <div className="bg-white/5 rounded-lg p-3">
                    <p className="text-purple-200">الأيقونة المختارة</p>
                    <div className="flex items-center space-x-2 space-x-reverse mt-1">
                      {getIconComponent(formData.icon, "w-4 h-4 text-white")}
                      <span className="text-white font-medium">{formData.icon}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 🎯 أزرار الإجراءات */}
              <div className="flex items-center justify-end space-x-4 space-x-reverse pt-8 border-t border-white/20">
                <button
                  type="button"
                  onClick={() => {
                    setShowAddForm(false);
                    setEditingCategory(null);
                  }}
                  className="bg-white/10 hover:bg-white/20 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse"
                >
                  <X className="w-5 h-5" />
                  <span>إلغاء</span>
                </button>
                <button
                  type="submit"
                  className="bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse shadow-lg"
                >
                  <Save className="w-5 h-5" />
                  <span>{editingCategory ? 'حفظ التعديلات' : 'إضافة القسم'}</span>
                  <Sparkles className="w-4 h-4" />
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

        {/* 📦 عرض الأقسام المبهر - أفضل بطاقات في العالم */}
        {viewMode === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {filteredAndSortedCategories.length > 0 ? (
              filteredAndSortedCategories.map((category) => {
                const productCount = getCategoryProductCount(category.id);
                const categoryValue = stats.categoryValues.find(cv => cv.id === category.id);
                const isEmpty = productCount === 0;
                const isTopUsed = stats.mostUsedCategory.id === category.id;
                const isTopValue = stats.topValueCategory.id === category.id;

                return (
                  <div key={category.id} className="group relative overflow-hidden bg-gradient-to-br from-white/5 via-white/10 to-white/5 backdrop-blur-xl rounded-3xl border border-white/20 hover:border-purple-400/50 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/25 transform-gpu">
                    {/* تأثيرات الخلفية المتحركة */}
                    <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 via-blue-500/5 to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-x-12 translate-x-full group-hover:translate-x-[-200%] transition-transform duration-1000"></div>

                    {/* شارات مميزة محسنة */}
                    <div className="absolute top-4 left-4 z-20 flex flex-col space-y-2">
                      {isTopUsed && (
                        <div className="relative">
                          <span className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-3 py-2 rounded-xl text-xs font-bold flex items-center space-x-1 space-x-reverse shadow-lg animate-pulse">
                            <Crown className="w-4 h-4" />
                            <span>الأكثر استخداماً</span>
                          </span>
                          <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/50 to-orange-400/50 rounded-xl blur-md opacity-50"></div>
                        </div>
                      )}
                      {isTopValue && (
                        <div className="relative">
                          <span className="bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-3 py-2 rounded-xl text-xs font-bold flex items-center space-x-1 space-x-reverse shadow-lg animate-pulse">
                            <Gem className="w-4 h-4" />
                            <span>الأعلى قيمة</span>
                          </span>
                          <div className="absolute inset-0 bg-gradient-to-r from-emerald-400/50 to-teal-400/50 rounded-xl blur-md opacity-50"></div>
                        </div>
                      )}
                      {isEmpty && (
                        <span className="bg-gradient-to-r from-gray-500 to-slate-500 text-white px-3 py-2 rounded-xl text-xs font-bold flex items-center space-x-1 space-x-reverse shadow-lg">
                          <AlertCircle className="w-4 h-4" />
                          <span>فارغ</span>
                        </span>
                      )}
                    </div>

                    {/* شارة الحالة محسنة */}
                    <div className="absolute top-4 right-4 z-20">
                      <div className="relative">
                        <span className="bg-gradient-to-r from-green-500/30 to-emerald-500/30 text-green-300 px-4 py-2 rounded-xl text-sm font-bold backdrop-blur-sm border border-green-400/30 flex items-center space-x-2 space-x-reverse">
                          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                          <span>نشط</span>
                        </span>
                      </div>
                    </div>

                    {/* أيقونة القسم المحسنة */}
                    <div className="aspect-video bg-gradient-to-br from-slate-800/50 via-purple-900/30 to-blue-900/50 relative flex items-center justify-center overflow-hidden">
                      {/* خلفية متحركة */}
                      <div className="absolute inset-0 bg-gradient-to-br from-purple-600/10 via-blue-600/10 to-indigo-600/10 animate-gradient-xy"></div>

                      {/* الأيقونة الرئيسية */}
                      <div className="relative z-10 group-hover:scale-110 transition-transform duration-500">
                        <div
                          className={`w-24 h-24 rounded-3xl flex items-center justify-center text-white shadow-2xl relative bg-gradient-to-br ${
                            colorOptions.find(c => c.value === category.color)?.gradient || 'from-blue-500 to-blue-600'
                          } group-hover:shadow-3xl transition-all duration-500`}
                        >
                          {getIconComponent(category.icon, "w-12 h-12")}

                          {/* تأثير الإضاءة الداخلية */}
                          <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-transparent rounded-3xl"></div>
                          <div className="absolute inset-0 bg-gradient-to-tl from-white/10 via-transparent to-transparent rounded-3xl"></div>
                        </div>

                        {/* هالة مضيئة */}
                        <div className={`absolute inset-0 rounded-3xl blur-xl opacity-0 group-hover:opacity-60 transition-opacity duration-500 bg-gradient-to-br ${
                          colorOptions.find(c => c.value === category.color)?.gradient || 'from-blue-500 to-blue-600'
                        }`}></div>
                      </div>

                      {/* تأثير الهولوجرام المحسن */}
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12 translate-x-full group-hover:translate-x-[-200%] transition-transform duration-1000"></div>

                      {/* جزيئات متحركة */}
                      <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                        <div className="absolute top-4 left-4 w-1 h-1 bg-white rounded-full animate-ping"></div>
                        <div className="absolute top-8 right-6 w-1 h-1 bg-purple-400 rounded-full animate-ping delay-300"></div>
                        <div className="absolute bottom-6 left-8 w-1 h-1 bg-blue-400 rounded-full animate-ping delay-700"></div>
                      </div>
                    </div>

                    {/* محتوى البطاقة المحسن */}
                    <div className="p-8 relative z-10">
                      {/* اسم القسم والوصف */}
                      <div className="mb-6">
                        <h3 className="text-2xl font-black text-transparent bg-gradient-to-r from-white via-purple-200 to-blue-200 bg-clip-text mb-3 group-hover:from-purple-300 group-hover:via-blue-300 group-hover:to-indigo-300 transition-all duration-500">
                          {category.name}
                        </h3>
                        <p className="text-purple-200/80 text-sm leading-relaxed line-clamp-2 group-hover:text-purple-100 transition-colors duration-300">
                          {category.description || 'لا يوجد وصف متاح لهذا القسم'}
                        </p>
                      </div>

                      {/* الإحصائيات المحسنة */}
                      <div className="grid grid-cols-2 gap-4 mb-6">
                        <div className="relative overflow-hidden bg-gradient-to-br from-blue-500/10 to-cyan-500/10 backdrop-blur-sm rounded-2xl p-4 border border-blue-400/20 group-hover:border-blue-400/40 transition-all duration-300">
                          <div className="absolute top-0 right-0 w-8 h-8 bg-blue-400/20 rounded-full -translate-y-4 translate-x-4"></div>
                          <div className="relative text-center">
                            <div className="flex items-center justify-center mb-2">
                              <Package className="w-5 h-5 text-blue-400 mr-2" />
                              <p className="text-blue-200 text-xs font-medium">المنتجات</p>
                            </div>
                            <p className="text-blue-300 font-black text-2xl">{productCount}</p>
                            <div className="w-full bg-blue-500/20 rounded-full h-1 mt-2">
                              <div
                                className="bg-gradient-to-r from-blue-400 to-cyan-400 h-1 rounded-full transition-all duration-1000"
                                style={{ width: `${Math.min((productCount / Math.max(...filteredAndSortedCategories.map(c => getCategoryProductCount(c.id)))) * 100, 100)}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>

                        <div className="relative overflow-hidden bg-gradient-to-br from-green-500/10 to-emerald-500/10 backdrop-blur-sm rounded-2xl p-4 border border-green-400/20 group-hover:border-green-400/40 transition-all duration-300">
                          <div className="absolute top-0 right-0 w-8 h-8 bg-green-400/20 rounded-full -translate-y-4 translate-x-4"></div>
                          <div className="relative text-center">
                            <div className="flex items-center justify-center mb-2">
                              <TrendingUp className="w-5 h-5 text-green-400 mr-2" />
                              <p className="text-green-200 text-xs font-medium">القيمة</p>
                            </div>
                            <p className="text-green-300 font-black text-lg">
                              {categoryValue?.totalValue ? `${(categoryValue.totalValue / 1000).toFixed(1)}ك دج` : '0 دج'}
                            </p>
                            <div className="w-full bg-green-500/20 rounded-full h-1 mt-2">
                              <div
                                className="bg-gradient-to-r from-green-400 to-emerald-400 h-1 rounded-full transition-all duration-1000"
                                style={{ width: `${Math.min(((categoryValue?.totalValue || 0) / Math.max(...stats.categoryValues.map(c => c.totalValue))) * 100, 100)}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* معلومات إضافية */}
                      <div className="mb-6 bg-gradient-to-r from-white/5 to-white/10 rounded-2xl p-4 border border-white/10">
                        <div className="grid grid-cols-3 gap-4 text-center">
                          <div>
                            <p className="text-purple-200/60 text-xs mb-1">الربح</p>
                            <p className="text-purple-300 font-bold text-sm">
                              {categoryValue?.profit ? `${(categoryValue.profit / 1000).toFixed(1)}ك دج` : '0 دج'}
                            </p>
                          </div>
                          <div>
                            <p className="text-blue-200/60 text-xs mb-1">هامش الربح</p>
                            <p className="text-blue-300 font-bold text-sm">
                              {categoryValue?.profitMargin ? `${categoryValue.profitMargin.toFixed(1)}%` : '0%'}
                            </p>
                          </div>
                          <div>
                            <p className="text-indigo-200/60 text-xs mb-1">تاريخ الإنشاء</p>
                            <p className="text-indigo-300 font-bold text-sm">
                              {new Date(category.createdDate).toLocaleDateString('ar-SA', { month: 'short', day: 'numeric' })}
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* أزرار الإجراءات المحسنة */}
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <button
                          onClick={() => handleEdit(category)}
                          className="group/btn flex-1 relative overflow-hidden bg-gradient-to-r from-purple-500 via-blue-500 to-indigo-500 hover:from-purple-600 hover:via-blue-600 hover:to-indigo-600 py-3 rounded-2xl flex items-center justify-center space-x-2 space-x-reverse transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-purple-500/25"
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-white/10 via-white/5 to-white/10 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"></div>
                          <Edit className="w-5 h-5 relative z-10" />
                          <span className="font-bold relative z-10">تعديل</span>
                        </button>
                        <button
                          onClick={() => handleDelete(category.id)}
                          className="group/btn relative overflow-hidden bg-gradient-to-r from-red-500/20 to-pink-500/20 hover:from-red-500/30 hover:to-pink-500/30 p-3 rounded-2xl transition-all duration-300 hover:scale-110 border border-red-400/20 hover:border-red-400/40"
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-red-400/10 to-pink-400/10 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"></div>
                          <Trash2 className="w-5 h-5 text-red-300 group-hover/btn:text-red-200 relative z-10" />
                        </button>
                      </div>
                    </div>

                    {/* تأثيرات الإضاءة المحسنة */}
                    <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-purple-500/0 via-blue-500/10 to-indigo-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
                    <div className="absolute inset-0 rounded-3xl border border-purple-400/0 group-hover:border-purple-400/30 transition-all duration-500 pointer-events-none"></div>
                  </div>
                );
              })
            ) : (
              /* الحالة الفارغة المبهرة المحسنة */
              <div className="col-span-full text-center py-24 relative">
                {/* خلفية متحركة */}
                <div className="absolute inset-0 overflow-hidden">
                  <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
                  <div className="absolute top-3/4 right-1/4 w-24 h-24 bg-blue-500/10 rounded-full blur-2xl animate-pulse delay-1000"></div>
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-indigo-500/5 rounded-full blur-3xl animate-spin-slow"></div>
                </div>

                <div className="relative z-10">
                  <div className="relative mb-12">
                    <div className="bg-gradient-to-r from-purple-500/20 via-blue-500/20 to-indigo-500/20 rounded-full p-12 mx-auto w-40 h-40 flex items-center justify-center relative overflow-hidden group">
                      <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 via-blue-600/10 to-indigo-600/10 animate-gradient-xy"></div>
                      <Tag className="w-20 h-20 text-purple-300 relative z-10 group-hover:scale-110 transition-transform duration-500" />

                      {/* جزيئات متحركة */}
                      <div className="absolute top-4 left-4 w-2 h-2 bg-purple-400 rounded-full animate-bounce-gentle"></div>
                      <div className="absolute top-8 right-6 w-1 h-1 bg-blue-400 rounded-full animate-bounce-gentle delay-500"></div>
                      <div className="absolute bottom-6 left-8 w-1.5 h-1.5 bg-indigo-400 rounded-full animate-bounce-gentle delay-1000"></div>
                      <div className="absolute bottom-4 right-4 w-1 h-1 bg-pink-400 rounded-full animate-bounce-gentle delay-1500"></div>
                    </div>

                    {/* هالات مضيئة */}
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-full animate-scale-pulse"></div>
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-blue-500/5 rounded-full animate-scale-pulse delay-1000"></div>
                  </div>

                  <h3 className="text-4xl font-black text-transparent bg-gradient-to-r from-purple-400 via-blue-400 to-indigo-400 bg-clip-text mb-6 animate-text-shimmer">
                    {searchTerm || filterBy !== 'all' ? '🔍 لا توجد نتائج مطابقة' : '📦 ابدأ رحلتك مع الأقسام'}
                  </h3>

                  <p className="text-purple-200/80 text-xl mb-4 max-w-2xl mx-auto leading-relaxed">
                    {searchTerm || filterBy !== 'all'
                      ? 'لم نتمكن من العثور على أقسام تطابق معايير البحث الحالية'
                      : 'أنشئ أقسام ذكية لتنظيم منتجاتك وإدارة مخزونك بكفاءة عالية'
                    }
                  </p>

                  <p className="text-purple-300/60 text-lg mb-12 max-w-xl mx-auto">
                    {searchTerm || filterBy !== 'all'
                      ? 'جرب تعديل كلمات البحث أو إزالة الفلاتر للحصول على نتائج أفضل'
                      : 'نظام الأقسام يساعدك في تصنيف المنتجات وتتبع الأداء والمبيعات'
                    }
                  </p>

                  <div className="flex items-center justify-center space-x-6 space-x-reverse">
                    {searchTerm || filterBy !== 'all' ? (
                      <div className="flex items-center space-x-4 space-x-reverse">
                        <button
                          onClick={() => {
                            setSearchTerm('');
                            setFilterBy('all');
                          }}
                          className="group relative bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-500 hover:from-blue-600 hover:via-purple-600 hover:to-indigo-600 text-white px-10 py-4 rounded-2xl font-bold transition-all duration-300 hover:scale-110 flex items-center space-x-3 space-x-reverse shadow-2xl hover:shadow-blue-500/25"
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-white/10 via-white/5 to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>
                          <X className="w-6 h-6 relative z-10" />
                          <span className="relative z-10">مسح جميع الفلاتر</span>
                          <Sparkles className="w-5 h-5 relative z-10 animate-pulse" />
                        </button>

                        <button
                          onClick={() => setShowAddForm(true)}
                          className="group relative bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white px-8 py-4 rounded-2xl font-bold transition-all duration-300 hover:scale-110 flex items-center space-x-2 space-x-reverse shadow-2xl hover:shadow-green-500/25"
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>
                          <Plus className="w-5 h-5 relative z-10" />
                          <span className="relative z-10">إضافة قسم</span>
                        </button>
                      </div>
                    ) : (
                      <button
                        onClick={() => setShowAddForm(true)}
                        className="group relative bg-gradient-to-r from-purple-500 via-blue-500 to-indigo-500 hover:from-purple-600 hover:via-blue-600 hover:to-indigo-600 text-white px-12 py-5 rounded-2xl font-black text-lg transition-all duration-300 hover:scale-110 flex items-center space-x-4 space-x-reverse shadow-2xl hover:shadow-purple-500/30"
                      >
                        <div className="absolute inset-0 bg-gradient-to-r from-white/10 via-white/5 to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>
                        <div className="relative z-10 bg-white/20 p-2 rounded-xl">
                          <Plus className="w-8 h-8" />
                        </div>
                        <span className="relative z-10">إنشاء أول قسم احترافي</span>
                        <Crown className="w-6 h-6 relative z-10 animate-bounce-gentle text-yellow-300" />
                      </button>
                    )}
                  </div>

                  {/* معلومات إضافية */}
                  {!searchTerm && filterBy === 'all' && (
                    <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
                      <div className="bg-gradient-to-br from-purple-500/10 to-blue-500/10 backdrop-blur-sm rounded-2xl p-6 border border-purple-400/20">
                        <div className="bg-purple-500/20 p-3 rounded-xl w-fit mx-auto mb-4">
                          <Target className="w-8 h-8 text-purple-300" />
                        </div>
                        <h4 className="text-white font-bold text-lg mb-2">تنظيم ذكي</h4>
                        <p className="text-purple-200/70 text-sm">صنف منتجاتك في أقسام منطقية لسهولة الإدارة والبحث</p>
                      </div>

                      <div className="bg-gradient-to-br from-blue-500/10 to-indigo-500/10 backdrop-blur-sm rounded-2xl p-6 border border-blue-400/20">
                        <div className="bg-blue-500/20 p-3 rounded-xl w-fit mx-auto mb-4">
                          <BarChart3 className="w-8 h-8 text-blue-300" />
                        </div>
                        <h4 className="text-white font-bold text-lg mb-2">تحليل متقدم</h4>
                        <p className="text-blue-200/70 text-sm">احصل على إحصائيات مفصلة لكل قسم ومتابعة الأداء</p>
                      </div>

                      <div className="bg-gradient-to-br from-indigo-500/10 to-purple-500/10 backdrop-blur-sm rounded-2xl p-6 border border-indigo-400/20">
                        <div className="bg-indigo-500/20 p-3 rounded-xl w-fit mx-auto mb-4">
                          <Zap className="w-8 h-8 text-indigo-300" />
                        </div>
                        <h4 className="text-white font-bold text-lg mb-2">إدارة سريعة</h4>
                        <p className="text-indigo-200/70 text-sm">تحكم كامل في الأقسام مع إمكانيات التعديل والحذف</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        ) : (
          /* عرض القائمة المتطور */
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-white/5">
                  <tr>
                    <th className="text-right py-4 px-6 text-purple-200 font-semibold">القسم</th>
                    <th className="text-right py-4 px-6 text-purple-200 font-semibold">الوصف</th>
                    <th className="text-center py-4 px-6 text-purple-200 font-semibold">المنتجات</th>
                    <th className="text-center py-4 px-6 text-purple-200 font-semibold">القيمة</th>
                    <th className="text-center py-4 px-6 text-purple-200 font-semibold">الحالة</th>
                    <th className="text-center py-4 px-6 text-purple-200 font-semibold">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredAndSortedCategories.length > 0 ? (
                    filteredAndSortedCategories.map((category, index) => {
                      const productCount = getCategoryProductCount(category.id);
                      const categoryValue = stats.categoryValues.find(cv => cv.id === category.id);
                      const isEmpty = productCount === 0;

                      return (
                        <tr
                          key={category.id}
                          className={`border-t border-white/10 hover:bg-white/5 transition-colors ${
                            index % 2 === 0 ? 'bg-white/2' : ''
                          }`}
                        >
                          <td className="py-4 px-6">
                            <div className="flex items-center space-x-4 space-x-reverse">
                              <div
                                className={`w-12 h-12 rounded-xl flex items-center justify-center text-white shadow-lg bg-gradient-to-br ${
                                  colorOptions.find(c => c.value === category.color)?.gradient || 'from-blue-500 to-blue-600'
                                }`}
                              >
                                {getIconComponent(category.icon, "w-6 h-6")}
                              </div>
                              <div>
                                <p className="text-white font-semibold">{category.name}</p>
                                <p className="text-purple-200 text-sm">{colorOptions.find(c => c.value === category.color)?.name}</p>
                              </div>
                            </div>
                          </td>
                          <td className="py-4 px-6">
                            <span className="text-purple-200">{category.description}</span>
                          </td>
                          <td className="py-4 px-6 text-center">
                            <div className="flex items-center justify-center space-x-2 space-x-reverse">
                              <span className="text-blue-400 font-bold text-lg">{productCount}</span>
                              {isEmpty && <div className="w-2 h-2 bg-gray-500 rounded-full"></div>}
                            </div>
                          </td>
                          <td className="py-4 px-6 text-center">
                            <span className="text-green-400 font-semibold">
                              {categoryValue?.totalValue ? `${categoryValue.totalValue.toLocaleString()} دج` : '0 دج'}
                            </span>
                          </td>
                          <td className="py-4 px-6 text-center">
                            <span className="bg-green-500/20 text-green-300 px-3 py-1 rounded-lg text-sm font-medium">
                              نشط
                            </span>
                          </td>
                          <td className="py-4 px-6">
                            <div className="flex items-center justify-center space-x-2 space-x-reverse">
                              <button
                                onClick={() => handleEdit(category)}
                                className="bg-purple-500/20 text-purple-300 hover:bg-purple-500/30 p-2 rounded-lg transition-colors"
                              >
                                <Edit className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleDelete(category.id)}
                                className="bg-red-500/20 text-red-300 hover:bg-red-500/30 p-2 rounded-lg transition-colors"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      );
                    })
                  ) : (
                    <tr>
                      <td colSpan={6} className="p-12 text-center">
                        <div className="flex flex-col items-center justify-center space-y-4">
                          <div className="bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-full p-6">
                            <Tag className="w-12 h-12 text-purple-300" />
                          </div>
                          <div>
                            <p className="text-white text-lg font-semibold mb-2">لا توجد أقسام</p>
                            <p className="text-purple-200 text-sm">ابدأ بإضافة أقسام لتصنيف منتجاتك</p>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Modal تأكيد حذف جميع الأقسام */}
        {showDeleteAllConfirm && (
          <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-white/10 backdrop-blur-lg rounded-3xl border border-white/20 w-full max-w-md">
              <div className="p-8 text-center">
                <div className="bg-gradient-to-r from-red-500 to-pink-600 p-4 rounded-2xl mx-auto w-fit mb-6">
                  <Trash2 className="w-8 h-8 text-white" />
                </div>

                <h2 className="text-2xl font-bold text-white mb-4">
                  حذف جميع الأقسام
                </h2>

                <p className="text-red-200 mb-2">
                  هل أنت متأكد من حذف جميع الأقسام؟
                </p>

                <p className="text-red-300 text-sm mb-6">
                  سيتم حذف {categories.length} قسم نهائياً ولا يمكن التراجع عن هذا الإجراء!
                </p>

                <div className="flex items-center justify-center space-x-4 space-x-reverse">
                  <button
                    onClick={() => setShowDeleteAllConfirm(false)}
                    disabled={isDeleting}
                    className="bg-white/10 hover:bg-white/20 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    إلغاء
                  </button>

                  <button
                    onClick={handleDeleteAllCategories}
                    disabled={isDeleting}
                    className="bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isDeleting ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                        <span>جاري الحذف...</span>
                      </>
                    ) : (
                      <>
                        <Trash2 className="w-5 h-5" />
                        <span>نعم، احذف الكل</span>
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Categories;