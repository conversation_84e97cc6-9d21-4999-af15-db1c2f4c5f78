; 🏗️ NSIS Installer Script for Arabic Support
; إعدادات المثبت باللغة العربية

!include "MUI2.nsh"
!include "FileFunc.nsh"

; إعدادات عامة
!define PRODUCT_NAME "كشير توسار"
!define PRODUCT_VERSION "1.0.0"
!define PRODUCT_PUBLISHER "فريق توسار"
!define PRODUCT_WEB_SITE "https://toosar.com"
!define PRODUCT_DIR_REGKEY "Software\Microsoft\Windows\CurrentVersion\App Paths\kasheer-toosar.exe"
!define PRODUCT_UNINST_KEY "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}"

; إعدادات MUI
!define MUI_ABORTWARNING
!define MUI_ICON "assets\icon.ico"
!define MUI_UNICON "assets\icon.ico"
!define MUI_WELCOMEFINISHPAGE_BITMAP "assets\installer-banner.bmp"
!define MUI_UNWELCOMEFINISHPAGE_BITMAP "assets\installer-banner.bmp"

; صفحات المثبت
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!insertmacro MUI_PAGE_COMPONENTS
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!define MUI_FINISHPAGE_RUN "$INSTDIR\kasheer-toosar.exe"
!define MUI_FINISHPAGE_RUN_TEXT "تشغيل كشير توسار الآن"
!insertmacro MUI_PAGE_FINISH

; صفحات إلغاء التثبيت
!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; اللغات
!insertmacro MUI_LANGUAGE "Arabic"

; معلومات الإصدار
VIProductVersion "*******"
VIAddVersionKey /LANG=${LANG_ARABIC} "ProductName" "${PRODUCT_NAME}"
VIAddVersionKey /LANG=${LANG_ARABIC} "Comments" "نظام نقاط بيع متكامل"
VIAddVersionKey /LANG=${LANG_ARABIC} "CompanyName" "${PRODUCT_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_ARABIC} "LegalTrademarks" "كشير توسار"
VIAddVersionKey /LANG=${LANG_ARABIC} "LegalCopyright" "© 2024 ${PRODUCT_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_ARABIC} "FileDescription" "${PRODUCT_NAME}"
VIAddVersionKey /LANG=${LANG_ARABIC} "FileVersion" "${PRODUCT_VERSION}"

; إعدادات المثبت
Name "${PRODUCT_NAME} ${PRODUCT_VERSION}"
OutFile "كشير-توسار-مثبت.exe"
InstallDir "$PROGRAMFILES\كشير توسار"
InstallDirRegKey HKLM "${PRODUCT_DIR_REGKEY}" ""
ShowInstDetails show
ShowUnInstDetails show

; التحقق من المتطلبات
Function .onInit
  ; التحقق من وجود Node.js
  nsExec::ExecToStack 'node --version'
  Pop $0
  ${If} $0 != 0
    MessageBox MB_YESNO|MB_ICONQUESTION "Node.js غير مثبت على النظام.$\nهل تريد تحميله وتثبيته؟" IDYES download_nodejs IDNO skip_nodejs
    download_nodejs:
      ExecShell "open" "https://nodejs.org/dist/v18.18.0/node-v18.18.0-x64.msi"
      MessageBox MB_OK "يرجى تثبيت Node.js أولاً ثم إعادة تشغيل المثبت"
      Abort
    skip_nodejs:
  ${EndIf}
  
  ; التحقق من وجود PostgreSQL
  nsExec::ExecToStack 'psql --version'
  Pop $0
  ${If} $0 != 0
    MessageBox MB_YESNO|MB_ICONQUESTION "PostgreSQL غير مثبت على النظام.$\nهل تريد تحميله وتثبيته؟" IDYES download_postgres IDNO skip_postgres
    download_postgres:
      ExecShell "open" "https://get.enterprisedb.com/postgresql/postgresql-15.4-1-windows-x64.exe"
      MessageBox MB_OK "يرجى تثبيت PostgreSQL أولاً ثم إعادة تشغيل المثبت"
      Abort
    skip_postgres:
  ${EndIf}
FunctionEnd

; مكونات التثبيت
Section "البرنامج الأساسي" SEC01
  SectionIn RO
  SetOutPath "$INSTDIR"
  
  ; نسخ الملفات
  File /r "*.*"
  
  ; إنشاء اختصارات
  CreateDirectory "$SMPROGRAMS\كشير توسار"
  CreateShortCut "$SMPROGRAMS\كشير توسار\كشير توسار.lnk" "$INSTDIR\kasheer-toosar.exe"
  CreateShortCut "$SMPROGRAMS\كشير توسار\إلغاء التثبيت.lnk" "$INSTDIR\uninst.exe"
  CreateShortCut "$DESKTOP\كشير توسار.lnk" "$INSTDIR\kasheer-toosar.exe"
  
  ; تسجيل في النظام
  WriteRegStr HKLM "${PRODUCT_DIR_REGKEY}" "" "$INSTDIR\kasheer-toosar.exe"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "DisplayName" "$(^Name)"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "UninstallString" "$INSTDIR\uninst.exe"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "DisplayIcon" "$INSTDIR\kasheer-toosar.exe"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "DisplayVersion" "${PRODUCT_VERSION}"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "URLInfoAbout" "${PRODUCT_WEB_SITE}"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "Publisher" "${PRODUCT_PUBLISHER}"
  WriteUninstaller "$INSTDIR\uninst.exe"
SectionEnd

Section "قاعدة البيانات النموذجية" SEC02
  SetOutPath "$INSTDIR\database"
  
  ; إنشاء قاعدة البيانات
  DetailPrint "إنشاء قاعدة البيانات..."
  nsExec::ExecToLog 'createdb -U postgres pos_system'
  
  ; تشغيل ملفات SQL
  DetailPrint "إعداد جداول قاعدة البيانات..."
  nsExec::ExecToLog 'psql -U postgres -d pos_system -f "$INSTDIR\database\setup_database.sql"'
SectionEnd

; أوصاف المكونات
LangString DESC_SEC01 ${LANG_ARABIC} "الملفات الأساسية للبرنامج"
LangString DESC_SEC02 ${LANG_ARABIC} "إنشاء قاعدة البيانات وإعداد الجداول"

!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC01} $(DESC_SEC01)
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC02} $(DESC_SEC02)
!insertmacro MUI_FUNCTION_DESCRIPTION_END

; إلغاء التثبيت
Section Uninstall
  ; حذف الملفات
  RMDir /r "$INSTDIR"
  
  ; حذف الاختصارات
  Delete "$SMPROGRAMS\كشير توسار\*.*"
  RMDir "$SMPROGRAMS\كشير توسار"
  Delete "$DESKTOP\كشير توسار.lnk"
  
  ; حذف مفاتيح التسجيل
  DeleteRegKey HKLM "${PRODUCT_UNINST_KEY}"
  DeleteRegKey HKLM "${PRODUCT_DIR_REGKEY}"
  
  SetAutoClose true
SectionEnd
