const axios = require('axios');

async function testSettings() {
  try {
    console.log('🧪 اختبار API الإعدادات...');
    
    // اختبار GET
    console.log('📥 اختبار GET /api/settings');
    const getResponse = await axios.get('http://localhost:5002/api/settings');
    console.log('✅ GET نجح:', getResponse.data);
    
    // اختبار PUT
    console.log('📤 اختبار PUT /api/settings');
    const testData = {
      store_name: 'متجر توسار الإلكتروني',
      store_address: 'الجزائر - الجزائر العاصمة',
      store_phone: '+*********** 789',
      store_email: '<EMAIL>',
      store_tax_number: '*********'
    };
    
    const putResponse = await axios.put('http://localhost:5002/api/settings', testData);
    console.log('✅ PUT نجح:', putResponse.data);
    
    console.log('🎉 جميع الاختبارات نجحت!');
    
  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
    if (error.response) {
      console.error('📄 تفاصيل الخطأ:', error.response.data);
    }
  }
}

testSettings();
