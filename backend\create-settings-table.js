const { Pool } = require('pg');

const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'pos_system_db',
  user: 'postgres',
  password: 'toossar',
  ssl: false
});

async function createSettingsTable() {
  try {
    console.log('🔧 إنشاء جدول الإعدادات...');

    // إنشاء جدول الإعدادات
    await pool.query(`
      CREATE TABLE IF NOT EXISTS settings (
        id SERIAL PRIMARY KEY,
        -- إعدادات المتجر
        store_name VARCHAR(255) NOT NULL DEFAULT '',
        store_address TEXT DEFAULT '',
        store_phone VARCHAR(50) DEFAULT '',
        store_email VARCHAR(255) DEFAULT '',
        store_tax_number VARCHAR(100) DEFAULT '',
        store_logo TEXT DEFAULT '',

        -- إعدادات المستخدم
        user_name VARCHAR(255) NOT NULL DEFAULT '',
        user_email VARCHAR(255) DEFAULT '',
        user_role VARCHAR(50) DEFAULT 'admin',

        -- إعدادات النظام
        currency VARCHAR(10) DEFAULT 'DZD',
        language VARCHAR(10) DEFAULT 'ar',
        timezone VARCHAR(50) DEFAULT 'Africa/Algiers',
        date_format VARCHAR(20) DEFAULT 'DD/MM/YYYY',

        -- إعدادات الضرائب والمبيعات
        tax_rate DECIMAL(5,2) DEFAULT 19.00,
        enable_tax BOOLEAN DEFAULT true,
        enable_discount BOOLEAN DEFAULT true,
        enable_barcode BOOLEAN DEFAULT true,

        -- إعدادات التنبيهات
        low_stock_alert BOOLEAN DEFAULT true,
        email_notifications BOOLEAN DEFAULT false,
        sound_notifications BOOLEAN DEFAULT true,

        -- إعدادات المظهر
        theme VARCHAR(20) DEFAULT 'dark',
        primary_color VARCHAR(20) DEFAULT '#3b82f6',
        font_size VARCHAR(20) DEFAULT 'medium',

        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    console.log('✅ تم إنشاء جدول الإعدادات بنجاح');

    // إدراج إعدادات افتراضية
    const result = await pool.query('SELECT COUNT(*) FROM settings');
    if (parseInt(result.rows[0].count) === 0) {
      await pool.query(`
        INSERT INTO settings (
          store_name, store_address, store_phone, store_email,
          user_name, currency, language
        ) VALUES (
          'متجر توسار الإلكتروني',
          'الجزائر - الجزائر العاصمة',
          '+213 XXX XXX XXX',
          '<EMAIL>',
          'المدير',
          'DZD',
          'ar'
        )
      `);
      console.log('✅ تم إدراج الإعدادات الافتراضية');
    }

    console.log('🎉 تم إعداد جدول الإعدادات بنجاح!');
    process.exit(0);

  } catch (error) {
    console.error('❌ خطأ في إنشاء جدول الإعدادات:', error);
    process.exit(1);
  }
}

createSettingsTable();
