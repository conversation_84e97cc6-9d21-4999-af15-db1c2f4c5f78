-- إنشاء الجداول المفقودة لفواتير المشتريات
-- 🗄️ إنشاء جداول فواتير المشتريات

-- 📋 جدول فواتير المشتريات
CREATE TABLE IF NOT EXISTS pos_system.purchase_invoices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    supplier_id UUID REFERENCES pos_system.suppliers(id),
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    amount_paid DECIMAL(10,2) DEFAULT 0,
    is_paid BOOLEAN GENERATED ALWAYS AS (amount_paid >= total_amount) STORED,
    invoice_date DATE NOT NULL DEFAULT CURRENT_DATE,
    due_date DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 📦 جدول عناصر فواتير المشتريات
CREATE TABLE IF NOT EXISTS pos_system.purchase_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    invoice_id UUID NOT NULL REFERENCES pos_system.purchase_invoices(id) ON DELETE CASCADE,
    product_id UUID REFERENCES pos_system.products(id),
    product_name VARCHAR(255) NOT NULL,
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    discount DECIMAL(10,2) DEFAULT 0,
    total_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_purchase_invoices_supplier ON pos_system.purchase_invoices(supplier_id);
CREATE INDEX IF NOT EXISTS idx_purchase_invoices_date ON pos_system.purchase_invoices(invoice_date);
CREATE INDEX IF NOT EXISTS idx_purchase_items_invoice ON pos_system.purchase_items(invoice_id);
CREATE INDEX IF NOT EXISTS idx_purchase_items_product ON pos_system.purchase_items(product_id);

-- تعليقات على الجداول
COMMENT ON TABLE pos_system.purchase_invoices IS 'فواتير المشتريات';
COMMENT ON TABLE pos_system.purchase_items IS 'عناصر فواتير المشتريات';

-- رسالة تأكيد
DO $$
BEGIN
    RAISE NOTICE 'تم إنشاء جداول فواتير المشتريات بنجاح!';
    RAISE NOTICE 'Purchase invoice tables created successfully!';
END $$;
