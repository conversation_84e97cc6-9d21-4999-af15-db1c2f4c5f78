const axios = require('axios');

async function testSalesEndpoint() {
  try {
    console.log('🧪 اختبار نقطة نهاية المبيعات...');

    // بيانات بيع تجريبية
    const saleData = {
      customer_id: null,
      items: [
        {
          product_id: '123e4567-e89b-12d3-a456-426614174000', // معرف تجريبي
          product_name: 'منتج تجريبي',
          quantity: 1,
          unit_price: 100.00,
          discount: 0,
          total_price: 100.00
        }
      ],
      subtotal: 100.00,
      tax_amount: 0.00,
      discount_amount: 0.00,
      total_amount: 100.00,
      payment_method: 'cash',
      amount_paid: 100.00,
      change_amount: 0.00,
      notes: 'اختبار البيع',
      cashier_name: 'الكاشير'
    };

    console.log('📤 إرسال بيانات البيع:', JSON.stringify(saleData, null, 2));

    const response = await axios.post('http://localhost:5002/api/sales', saleData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ نجح البيع!');
    console.log('📋 استجابة الخادم:', response.data);

  } catch (error) {
    console.error('❌ فشل اختبار البيع:');
    
    if (error.response) {
      console.error('📋 رمز الحالة:', error.response.status);
      console.error('📋 رسالة الخطأ:', error.response.data);
    } else if (error.request) {
      console.error('📋 لم يتم استلام استجابة من الخادم');
      console.error('📋 تفاصيل الطلب:', error.request);
    } else {
      console.error('📋 خطأ في إعداد الطلب:', error.message);
    }
    
    console.error('📋 تفاصيل الخطأ الكاملة:', error.stack);
  } finally {
    process.exit();
  }
}

testSalesEndpoint();
