const { spawn } = require('child_process'); 
const path = require('path'); 
const fs = require('fs'); 
 
console.log('Starting Kasheer Toosar...'); 
 
// Start backend 
const backend = spawn('node', ['backend/server.js'], { 
  cwd: __dirname, 
  stdio: 'inherit' 
}); 
 
// Wait a bit then open browser 
setTimeout(() => { 
  const { exec } = require('child_process'); 
  exec('start http://localhost:5003'); 
}, 3000); 
 
process.on('SIGINT', () => { 
  backend.kill(); 
  process.exit(); 
}); 
