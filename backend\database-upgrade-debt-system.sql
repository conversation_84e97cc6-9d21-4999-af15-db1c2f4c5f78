-- 🎯 نظام فاتورة الدين الموحدة المتقدم
-- تطوير: نظام محاسبي ذكي لإدارة ديون العملاء

-- 📋 جدول فواتير الدين الرئيسية (فاتورة واحدة لكل عميل)
CREATE TABLE IF NOT EXISTS pos_system.customer_debt_invoices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES pos_system.customers(id) ON DELETE CASCADE,
    invoice_number VARCHAR(50) UNIQUE NOT NULL, -- DEBT-CUSTOMER-001
    status VARCHAR(20) DEFAULT 'open', -- open, closed, suspended
    
    -- إجماليات الفاتورة
    total_debt_amount DECIMAL(10,2) DEFAULT 0.00,
    total_paid_amount DECIMAL(10,2) DEFAULT 0.00,
    remaining_balance DECIMAL(10,2) DEFAULT 0.00,
    
    -- معلومات إضافية
    credit_limit DECIMAL(10,2) DEFAULT 10000.00, -- حد الائتمان
    payment_terms INTEGER DEFAULT 30, -- مدة السداد بالأيام
    notes TEXT,
    
    -- تواريخ مهمة
    first_purchase_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_purchase_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_payment_date TIMESTAMP,
    due_date TIMESTAMP, -- تاريخ الاستحقاق
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 📦 جدول عناصر فاتورة الدين (تفاصيل كل عملية شراء)
CREATE TABLE IF NOT EXISTS pos_system.debt_invoice_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    debt_invoice_id UUID NOT NULL REFERENCES pos_system.customer_debt_invoices(id) ON DELETE CASCADE,
    sale_id UUID REFERENCES pos_system.sales(id), -- ربط بالبيع الأصلي
    
    -- تفاصيل العملية
    purchase_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    description TEXT NOT NULL, -- وصف العملية (مثل: "خبز + حليب + سكر")
    amount DECIMAL(10,2) NOT NULL,
    
    -- حالة العنصر
    status VARCHAR(20) DEFAULT 'pending', -- pending, paid, cancelled
    paid_amount DECIMAL(10,2) DEFAULT 0.00,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 💰 جدول دفعات الدين (تتبع كل دفعة)
CREATE TABLE IF NOT EXISTS pos_system.debt_payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    debt_invoice_id UUID NOT NULL REFERENCES pos_system.customer_debt_invoices(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES pos_system.customers(id),
    
    -- تفاصيل الدفعة
    payment_amount DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(20) DEFAULT 'cash', -- cash, card, transfer
    payment_reference VARCHAR(100), -- رقم الحوالة أو المرجع
    
    -- معلومات إضافية
    notes TEXT,
    cashier_name VARCHAR(100),
    
    -- تواريخ
    payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 🔄 تحديث جدول المبيعات لربطه بنظام الدين
ALTER TABLE pos_system.sales 
ADD COLUMN IF NOT EXISTS debt_invoice_id UUID REFERENCES pos_system.customer_debt_invoices(id),
ADD COLUMN IF NOT EXISTS is_debt_sale BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS debt_status VARCHAR(20) DEFAULT 'paid'; -- paid, pending, partial

-- 📊 إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_debt_invoices_customer ON pos_system.customer_debt_invoices(customer_id);
CREATE INDEX IF NOT EXISTS idx_debt_invoices_status ON pos_system.customer_debt_invoices(status);
CREATE INDEX IF NOT EXISTS idx_debt_items_invoice ON pos_system.debt_invoice_items(debt_invoice_id);
CREATE INDEX IF NOT EXISTS idx_debt_payments_invoice ON pos_system.debt_payments(debt_invoice_id);
CREATE INDEX IF NOT EXISTS idx_sales_debt_invoice ON pos_system.sales(debt_invoice_id);

-- 🎯 دالة ذكية لحساب رصيد العميل
CREATE OR REPLACE FUNCTION pos_system.calculate_customer_debt_balance(customer_uuid UUID)
RETURNS TABLE(
    total_debt DECIMAL(10,2),
    total_paid DECIMAL(10,2),
    remaining_balance DECIMAL(10,2),
    credit_available DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(cdi.total_debt_amount, 0.00) as total_debt,
        COALESCE(cdi.total_paid_amount, 0.00) as total_paid,
        COALESCE(cdi.remaining_balance, 0.00) as remaining_balance,
        COALESCE(cdi.credit_limit - cdi.remaining_balance, 0.00) as credit_available
    FROM pos_system.customer_debt_invoices cdi
    WHERE cdi.customer_id = customer_uuid 
    AND cdi.status = 'open'
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- 🔄 دالة تحديث رصيد فاتورة الدين
CREATE OR REPLACE FUNCTION pos_system.update_debt_invoice_balance(debt_invoice_uuid UUID)
RETURNS VOID AS $$
DECLARE
    total_items DECIMAL(10,2);
    total_payments DECIMAL(10,2);
BEGIN
    -- حساب إجمالي العناصر
    SELECT COALESCE(SUM(amount), 0.00) INTO total_items
    FROM pos_system.debt_invoice_items
    WHERE debt_invoice_id = debt_invoice_uuid AND status != 'cancelled';
    
    -- حساب إجمالي الدفعات
    SELECT COALESCE(SUM(payment_amount), 0.00) INTO total_payments
    FROM pos_system.debt_payments
    WHERE debt_invoice_id = debt_invoice_uuid;
    
    -- تحديث الفاتورة
    UPDATE pos_system.customer_debt_invoices
    SET 
        total_debt_amount = total_items,
        total_paid_amount = total_payments,
        remaining_balance = total_items - total_payments,
        updated_at = CURRENT_TIMESTAMP,
        last_purchase_date = CURRENT_TIMESTAMP
    WHERE id = debt_invoice_uuid;
END;
$$ LANGUAGE plpgsql;

-- ✅ رسالة نجاح
SELECT '🎉 تم إنشاء نظام فاتورة الدين الموحدة بنجاح!' as message;
