@echo off
title إعداد كشير توسار للعميل
color 0B

echo.
echo ========================================
echo     إعداد كشير توسار للعميل
echo ========================================
echo.

REM التحقق من Node.js
echo التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Node.js غير مثبت!
    echo جاري تحميل وتثبيت Node.js...
    
    REM تحميل Node.js
    powershell -Command "Invoke-WebRequest -Uri 'https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi' -OutFile 'nodejs-installer.msi'"
    
    REM تثبيت Node.js
    echo تثبيت Node.js...
    msiexec /i nodejs-installer.msi /quiet /norestart
    
    REM انتظار التثبيت
    timeout /t 30 /nobreak >nul
    
    REM تنظيف
    del nodejs-installer.msi 2>nul
    
    echo تم تثبيت Node.js بنجاح!
    echo يرجى إعادة تشغيل الكمبيوتر ثم تشغيل هذا الملف مرة أخرى
    pause
    exit /b 0
)

echo Node.js موجود ✓

REM تثبيت المتطلبات
echo تثبيت متطلبات المشروع...
call npm install

if %errorlevel% neq 0 (
    echo خطأ في تثبيت المتطلبات!
    pause
    exit /b 1
)

echo تم تثبيت المتطلبات ✓

REM التحقق من vite
if not exist "node_modules\.bin\vite.cmd" (
    echo تثبيت vite...
    call npm install vite --save-dev
)

REM بناء المشروع
echo بناء المشروع...
call npx vite build

if %errorlevel% neq 0 (
    echo محاولة بديلة للبناء...
    call .\node_modules\.bin\vite.cmd build
    if %errorlevel% neq 0 (
        echo خطأ في بناء المشروع!
        pause
        exit /b 1
    )
)

echo تم بناء المشروع ✓

REM إنشاء اختصار على سطح المكتب
echo إنشاء اختصار على سطح المكتب...
set "desktop=%USERPROFILE%\Desktop"
set "shortcut=%desktop%\كشير توسار.lnk"
set "target=%~dp0كشير-توسار.bat"

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%shortcut%'); $Shortcut.TargetPath = '%target%'; $Shortcut.WorkingDirectory = '%~dp0'; $Shortcut.IconLocation = '%~dp0assets\icon.ico'; $Shortcut.Description = 'كشير توسار - نظام نقاط البيع'; $Shortcut.Save()"

echo تم إنشاء الاختصار ✓

echo.
echo ========================================
echo        تم الإعداد بنجاح!
echo ========================================
echo.
echo يمكنك الآن:
echo 1. تشغيل النظام من الاختصار على سطح المكتب
echo 2. أو تشغيل ملف كشير-توسار.bat
echo.
echo ملاحظة: تأكد من تشغيل PostgreSQL قبل تشغيل النظام
echo.
pause
