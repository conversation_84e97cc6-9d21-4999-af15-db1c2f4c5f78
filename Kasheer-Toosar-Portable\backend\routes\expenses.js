const express = require('express');
const router = express.Router();
const pool = require('../database');

// 📋 جلب جميع المصروفات
router.get('/', async (req, res) => {
  try {
    console.log('📋 طلب جلب المصروفات...');

    const result = await pool.query(`
      SELECT * FROM pos_system.expenses
      ORDER BY created_at DESC
    `);

    console.log(`✅ تم جلب ${result.rows.length} مصروف`);
    res.json(result.rows);
  } catch (error) {
    console.error('❌ خطأ في جلب المصروفات:', error);
    res.status(500).json({ error: 'خطأ في جلب المصروفات' });
  }
});

// 💰 إضافة مصروف جديد
router.post('/', async (req, res) => {
  try {
    const {
      title,
      description,
      amount,
      category = 'مشتريات',
      date,
      payment_method = 'نقدي',
      vendor,
      receipt_number,
      status = 'paid',
      tags = [],
      notes
    } = req.body;

    console.log('💰 إضافة مصروف جديد');
    console.log('📤 البيانات المستلمة:', req.body);

    // التحقق من البيانات المطلوبة
    if (!amount || amount <= 0) {
      console.error('❌ المبلغ مطلوب ويجب أن يكون أكبر من صفر');
      return res.status(400).json({ error: 'المبلغ مطلوب ويجب أن يكون أكبر من صفر' });
    }

    if (!title && !description) {
      console.error('❌ العنوان أو الوصف مطلوب');
      return res.status(400).json({ error: 'العنوان أو الوصف مطلوب' });
    }

    // إنشاء المصروف بالحقول الأساسية فقط
    const result = await pool.query(`
      INSERT INTO pos_system.expenses (
        amount, description, category, payment_method
      ) VALUES ($1, $2, $3, $4)
      RETURNING *
    `, [
      parseFloat(amount),
      title || description || 'مصروف من فاتورة مشتريات',
      category,
      payment_method
    ]);

    const newExpense = result.rows[0];

    console.log(`✅ تم إضافة مصروف جديد: ${newExpense.id} - المبلغ: ${amount} دج`);
    res.status(201).json({
      message: 'تم إضافة المصروف بنجاح',
      expense: newExpense
    });

  } catch (error) {
    console.error('❌ خطأ في إضافة المصروف:', error);
    console.error('❌ تفاصيل الخطأ:', error.message);
    res.status(500).json({
      error: 'خطأ في إضافة المصروف',
      details: error.message
    });
  }
});

// 🗑️ حذف مصروف
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    console.log(`🗑️ حذف مصروف: ${id}`);

    const result = await pool.query(
      'DELETE FROM pos_system.expenses WHERE id = $1 RETURNING *',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'المصروف غير موجود' });
    }

    console.log(`✅ تم حذف المصروف: ${id}`);
    res.json({ message: 'تم حذف المصروف بنجاح' });

  } catch (error) {
    console.error('❌ خطأ في حذف المصروف:', error);
    res.status(500).json({ error: 'خطأ في حذف المصروف' });
  }
});

// 🧹 تنظيف المصروفات المكررة
router.post('/cleanup-duplicates', async (req, res) => {
  try {
    console.log('🧹 بدء تنظيف المصروفات المكررة...');

    // أولاً: فحص جميع المصروفات الموجودة
    const allExpensesResult = await pool.query(`
      SELECT id, description, category, amount, created_at
      FROM pos_system.expenses
      ORDER BY created_at DESC
    `);

    console.log(`📊 إجمالي المصروفات الموجودة: ${allExpensesResult.rows.length}`);

    // طباعة عينة من المصروفات للفحص
    console.log('📋 عينة من المصروفات:');
    allExpensesResult.rows.slice(0, 10).forEach((expense, index) => {
      console.log(`${index + 1}. ${expense.description} - ${expense.category} - ${expense.amount} دج`);
    });

    // حذف المصروفات المرتبطة بالمشتريات (بمعايير أوسع)
    const result = await pool.query(`
      DELETE FROM pos_system.expenses
      WHERE (
        category = 'مشتريات'
        OR category = 'دفعة مورد'
        OR description LIKE '%فاتورة مشتريات%'
        OR description LIKE '%مشتريات من%'
        OR description LIKE '%دفعة لفاتورة%'
        OR description LIKE '%فاتورة رقم%'
        OR description LIKE '%مورد%'
      )
      RETURNING *
    `);

    console.log(`✅ تم حذف ${result.rows.length} مصروف مكرر`);

    // طباعة المصروفات المحذوفة
    if (result.rows.length > 0) {
      console.log('🗑️ المصروفات المحذوفة:');
      result.rows.forEach((expense, index) => {
        console.log(`${index + 1}. ${expense.description} - ${expense.amount} دج`);
      });
    }

    res.json({
      message: `تم تنظيف ${result.rows.length} مصروف مكرر بنجاح`,
      deleted_expenses: result.rows.length,
      deleted_items: result.rows,
      total_expenses_before: allExpensesResult.rows.length,
      total_expenses_after: allExpensesResult.rows.length - result.rows.length
    });

  } catch (error) {
    console.error('❌ خطأ في تنظيف المصروفات:', error);
    res.status(500).json({ error: 'خطأ في تنظيف المصروفات' });
  }
});

// 🔍 تحليل مفصل للمصروفات
router.get('/detailed-analysis', async (req, res) => {
  try {
    console.log('🔍 بدء التحليل المفصل للمصروفات...');

    // جلب جميع المصروفات
    const allExpensesResult = await pool.query(`
      SELECT id, description, category, amount, payment_method, created_at
      FROM pos_system.expenses
      ORDER BY amount DESC
    `);

    const expenses = allExpensesResult.rows;
    const totalExpenses = expenses.reduce((sum, exp) => sum + parseFloat(exp.amount), 0);

    // تجميع حسب الفئة
    const categoriesMap = {};
    expenses.forEach(exp => {
      const category = exp.category || 'غير محدد';
      if (!categoriesMap[category]) {
        categoriesMap[category] = { total: 0, count: 0, items: [] };
      }
      categoriesMap[category].total += parseFloat(exp.amount);
      categoriesMap[category].count += 1;
      categoriesMap[category].items.push(exp);
    });

    const categories = Object.keys(categoriesMap).map(category => ({
      category,
      total: categoriesMap[category].total,
      count: categoriesMap[category].count,
      percentage: (categoriesMap[category].total / totalExpenses * 100).toFixed(1)
    })).sort((a, b) => b.total - a.total);

    // أكبر 10 مصروفات
    const topExpenses = expenses.slice(0, 10).map(exp => ({
      id: exp.id,
      description: exp.description,
      amount: parseFloat(exp.amount),
      category: exp.category,
      date: exp.created_at
    }));

    // تحليل وتوصيات
    const recommendations = [];

    if (totalExpenses > 100000) {
      recommendations.push('💰 المصروفات مرتفعة - راجع أكبر المصروفات');
    }

    const purchaseExpenses = categoriesMap['مشتريات'] || { total: 0 };
    if (purchaseExpenses.total > totalExpenses * 0.7) {
      recommendations.push('📦 مصروفات المشتريات تشكل أكثر من 70% - قد تكون مضاعفة');
    }

    if (categories.length > 10) {
      recommendations.push('📋 عدد فئات المصروفات كثير - يُنصح بالتنظيم');
    }

    if (recommendations.length === 0) {
      recommendations.push('✅ المصروفات تبدو طبيعية');
    }

    const analysis = {
      total_expenses: totalExpenses,
      expenses_count: expenses.length,
      categories,
      top_expenses: topExpenses,
      recommendations,
      suspicious_patterns: {
        duplicate_descriptions: findDuplicateDescriptions(expenses),
        high_amount_expenses: expenses.filter(exp => parseFloat(exp.amount) > 10000).length,
        same_day_expenses: findSameDayExpenses(expenses)
      }
    };

    console.log(`📊 تحليل المصروفات: ${expenses.length} مصروف بقيمة ${totalExpenses} دج`);
    res.json(analysis);

  } catch (error) {
    console.error('❌ خطأ في تحليل المصروفات:', error);
    res.status(500).json({ error: 'خطأ في تحليل المصروفات' });
  }
});

// دالة مساعدة للعثور على الأوصاف المكررة
function findDuplicateDescriptions(expenses) {
  const descriptionCount = {};
  expenses.forEach(exp => {
    const desc = exp.description;
    descriptionCount[desc] = (descriptionCount[desc] || 0) + 1;
  });

  return Object.keys(descriptionCount)
    .filter(desc => descriptionCount[desc] > 1)
    .map(desc => ({ description: desc, count: descriptionCount[desc] }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);
}

// دالة مساعدة للعثور على مصروفات نفس اليوم
function findSameDayExpenses(expenses) {
  const dateCount = {};
  expenses.forEach(exp => {
    const date = exp.created_at.toISOString().split('T')[0];
    dateCount[date] = (dateCount[date] || 0) + 1;
  });

  return Object.keys(dateCount)
    .filter(date => dateCount[date] > 5)
    .map(date => ({ date, count: dateCount[date] }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 3);
}

module.exports = router;
