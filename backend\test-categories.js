const pool = require('./database');

async function testCategories() {
  try {
    console.log('🔍 اختبار إضافة قسم...');
    
    // اختبار الاتصال
    const connectionTest = await pool.query('SELECT current_database(), current_schema()');
    console.log('✅ اتصال قاعدة البيانات:', connectionTest.rows[0]);
    
    // فحص هيكل جدول الأقسام
    const columnsResult = await pool.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_schema = 'pos_system' 
      AND table_name = 'categories'
      ORDER BY ordinal_position
    `);
    
    console.log('📋 أعمدة جدول الأقسام:');
    columnsResult.rows.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} (${col.is_nullable})`);
    });
    
    // اختبار إضافة قسم
    console.log('\n🔍 اختبار إضافة قسم جديد...');
    const testCategory = {
      name: 'قسم تجريبي',
      description: 'وصف تجريبي',
      color: '#3b82f6',
      icon: 'Package'
    };
    
    const result = await pool.query(
      `INSERT INTO pos_system.categories (name, description, color, icon) 
       VALUES ($1, $2, $3, $4) 
       RETURNING *`,
      [testCategory.name, testCategory.description, testCategory.color, testCategory.icon]
    );
    
    console.log('✅ تم إنشاء القسم بنجاح:', result.rows[0]);
    
    // حذف القسم التجريبي
    await pool.query('DELETE FROM pos_system.categories WHERE name = $1', [testCategory.name]);
    console.log('🗑️ تم حذف القسم التجريبي');
    
  } catch (error) {
    console.error('❌ خطأ في الاختبار:');
    console.error('رسالة:', error.message);
    console.error('كود:', error.code);
    console.error('تفاصيل:', error.detail);
    console.error('مكان:', error.where);
  }
  
  process.exit(0);
}

testCategories();
