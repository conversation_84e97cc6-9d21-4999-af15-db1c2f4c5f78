-- إنشاء جدول المصروفات
-- 💰 جدول المصروفات المباشرة

CREATE TABLE IF NOT EXISTS pos_system.expenses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    category VARCHAR(100) NOT NULL,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    description TEXT NOT NULL,
    payment_method VARCHAR(50) DEFAULT 'cash',
    reference_id UUID,
    reference_type VARCHAR(50),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء فهرس للبحث السريع
CREATE INDEX IF NOT EXISTS idx_expenses_category ON pos_system.expenses(category);
CREATE INDEX IF NOT EXISTS idx_expenses_created_at ON pos_system.expenses(created_at);
CREATE INDEX IF NOT EXISTS idx_expenses_reference ON pos_system.expenses(reference_id, reference_type);

-- إض<PERSON><PERSON>ة بعض البيانات التجريبية للمصروفات
INSERT INTO pos_system.expenses (category, amount, description, payment_method, notes) VALUES
('إيجار', 50000.00, 'إيجار المحل - شهر ديسمبر', 'cash', 'دفع شهري'),
('كهرباء', 8500.00, 'فاتورة الكهرباء', 'cash', 'فاتورة شهرية'),
('مياه', 2500.00, 'فاتورة المياه', 'cash', 'فاتورة شهرية'),
('راتب', 45000.00, 'راتب الموظف', 'bank_transfer', 'راتب شهري'),
('صيانة', 12000.00, 'صيانة المعدات', 'cash', 'صيانة دورية'),
('مواد تنظيف', 3500.00, 'مواد تنظيف المحل', 'cash', 'مستلزمات'),
('وقود', 15000.00, 'وقود السيارة', 'cash', 'مصروفات النقل'),
('اتصالات', 4500.00, 'فاتورة الهاتف والإنترنت', 'cash', 'فاتورة شهرية')
ON CONFLICT DO NOTHING;

-- إضافة تعليق على الجدول
COMMENT ON TABLE pos_system.expenses IS 'جدول المصروفات المباشرة للمحل';
COMMENT ON COLUMN pos_system.expenses.category IS 'فئة المصروف (إيجار، كهرباء، راتب، إلخ)';
COMMENT ON COLUMN pos_system.expenses.amount IS 'مبلغ المصروف';
COMMENT ON COLUMN pos_system.expenses.description IS 'وصف المصروف';
COMMENT ON COLUMN pos_system.expenses.reference_id IS 'مرجع للعملية المرتبطة (اختياري)';
COMMENT ON COLUMN pos_system.expenses.reference_type IS 'نوع المرجع (purchase, payment, etc)';
