// حذف الفواتير بطريقة بسيطة
const { Pool } = require('pg');

async function deleteInvoices() {
  const pool = new Pool({
    host: 'localhost',
    port: 5432,
    database: 'pos_system_db',
    user: 'postgres',
    password: 'toossar',
  });

  try {
    console.log('🗑️ حذف الفواتير...');
    
    // حذف جميع الفواتير الموجودة
    await pool.query('DELETE FROM pos_system.purchase_invoice_items');
    console.log('✅ تم حذف عناصر الفواتير');
    
    await pool.query('DELETE FROM pos_system.purchase_invoices');
    console.log('✅ تم حذف الفواتير');
    
    // التحقق من النتيجة
    const result = await pool.query('SELECT COUNT(*) FROM pos_system.purchase_invoices');
    console.log(`📊 عدد الفواتير المتبقية: ${result.rows[0].count}`);
    
  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await pool.end();
  }
}

deleteInvoices();
