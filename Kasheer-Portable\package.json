{"name": "kashe<PERSON>-<PERSON><PERSON>", "productName": "كشير توسار - نظام نقاط البيع", "private": true, "version": "1.0.0", "type": "module", "main": "main.js", "homepage": "./", "scripts": {"dev": "vite", "start": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "electron": "electron .", "electron-dev": "NODE_ENV=development electron .", "build-electron": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never", "pack": "electron-builder --dir"}, "dependencies": {"lucide-react": "^0.344.0", "node-fetch": "^2.7.0", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "electron": "^37.1.0", "electron-builder": "^26.0.12", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}