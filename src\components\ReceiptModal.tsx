import React, { useState, useEffect } from 'react';
import {
  X,
  Printer,
  Download,
  Share2,
  Copy,
  Check,
  Spark<PERSON>,
  Star,
  Award,
  Zap,
  Heart,
  Eye,
  Mail,
  MessageCircle,
  FileText,
  Calendar,
  Clock,
  User,
  Package,
  DollarSign,
  CreditCard,
  Target,
  TrendingUp
} from 'lucide-react';
import { printThermalReceipt } from './ThermalReceipt';
import BarcodeGenerator from './BarcodeGenerator';
import { useApp } from '../context/AppContext';

interface ReceiptModalProps {
  isOpen: boolean;
  onClose: () => void;
  saleData: {
    total: number;
    paid: number;
    change: number;
    items: Array<{
      name: string;
      quantity: number;
      price: number;
    }>;
    paymentMethod: string;
    customer?: string;
    saleNumber: string;
    timestamp: Date;
  };
  companyInfo?: {
    name: string;
    address: string;
    phone: string;
    email?: string;
    taxNumber?: string;
  };
}

const ReceiptModal: React.FC<ReceiptModalProps> = ({
  isOpen,
  onClose,
  saleData,
  companyInfo
}) => {
  const { settings } = useApp();

  // استخدام معلومات المتجر من الإعدادات أو القيم الافتراضية
  const storeInfo = companyInfo || {
    name: settings?.storeName || "متجر توسار الإلكتروني",
    address: settings?.storeAddress || "الجزائر - الجزائر العاصمة",
    phone: settings?.storePhone || "+213 XXX XXX XXX",
    email: settings?.storeEmail || "<EMAIL>",
    taxNumber: settings?.storeTaxNumber || "*********"
  };
  const [animationStep, setAnimationStep] = useState(0);
  const [copied, setCopied] = useState(false);
  const [printed, setPrinted] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setAnimationStep(0);
      const timeouts = [
        setTimeout(() => setAnimationStep(1), 100),
        setTimeout(() => setAnimationStep(2), 300),
        setTimeout(() => setAnimationStep(3), 500),
      ];
      return () => timeouts.forEach(clearTimeout);
    }
  }, [isOpen]);

  // إضافة وظيفة مفتاح Escape للإغلاق
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose]);

  const subtotal = saleData.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const tax = subtotal * 0.05;
  const discount = subtotal + tax - saleData.total;

  const handlePrint = () => {
    printThermalReceipt(saleData, storeInfo);
    setPrinted(true);
    setTimeout(() => setPrinted(false), 2000);
  };

  const handleCopy = () => {
    const receiptText = `
═══ ${storeInfo.name} ═══
📍 ${storeInfo.address}
📞 ${storeInfo.phone}

🧾 رقم الفاتورة: ${saleData.saleNumber}
📅 التاريخ: ${saleData.timestamp.toLocaleDateString('ar-SA')}
🕐 الوقت: ${saleData.timestamp.toLocaleTimeString('ar-SA')}

═══ تفاصيل المشتريات ═══
${saleData.items.map(item =>
  `${item.name} × ${item.quantity} = ${(item.price * item.quantity).toFixed(3)} د.ك`
).join('\n')}

═══ ملخص الفاتورة ═══
💰 المجموع الفرعي: ${subtotal.toFixed(3)} د.ك
📊 الضريبة (5%): ${tax.toFixed(3)} د.ك
🎯 الإجمالي النهائي: ${saleData.total.toFixed(3)} د.ك

💳 طريقة الدفع: ${saleData.paymentMethod === 'cash' ? 'نقداً' : saleData.paymentMethod === 'credit' ? 'آجل' : 'مختلط'}
💰 المبلغ المدفوع: ${saleData.paid.toFixed(3)} د.ك
${saleData.change > 0 ? `💸 الباقي: ${saleData.change.toFixed(3)} د.ك` : ''}

★ ★ ★ شكراً لتسوقكم معنا ★ ★ ★
    `;

    navigator.clipboard.writeText(receiptText);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/40 backdrop-blur-sm transition-opacity duration-500"
        onClick={onClose}
      />

      {/* Modal */}
      <div className={`fixed top-0 right-0 h-full w-96 bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 shadow-2xl transform transition-transform duration-500 ease-out ${
        isOpen ? 'translate-x-0' : 'translate-x-full'
      }`}>
        {/* Animated Background */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>
          <div className="absolute top-40 left-1/2 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000"></div>
        </div>

        <div className="relative z-10 h-full flex flex-col">
          {/* Header */}
          <div className="p-6 border-b border-white/10">
            <div className="flex items-center justify-between mb-4">
              <div className={`transition-all duration-700 delay-100 ${animationStep >= 1 ? 'translate-x-0 opacity-100' : 'translate-x-8 opacity-0'}`}>
                <h2 className="text-2xl font-bold text-white flex items-center">
                  <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-400 rounded-xl flex items-center justify-center ml-3 animate-bounce">
                    <FileText className="w-6 h-6 text-white" />
                  </div>
                  وصل الطباعة
                </h2>
                <p className="text-slate-300 text-sm mt-1">فاتورة احترافية جاهزة للطباعة</p>
              </div>

              <button
                onClick={onClose}
                className="p-2 bg-white/10 backdrop-blur-lg rounded-xl border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-110 group relative"
                title="إغلاق (Escape)"
              >
                <X className="w-5 h-5 text-white" />
                <span className="absolute -bottom-8 right-0 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                  إغلاق (Escape)
                </span>
              </button>
            </div>

            {/* Quick Stats */}
            <div className={`grid grid-cols-2 gap-3 transition-all duration-700 delay-200 ${animationStep >= 2 ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`}>
              <div className="bg-white/10 backdrop-blur-lg rounded-xl p-3 border border-white/20">
                <div className="flex items-center space-x-reverse space-x-2">
                  <DollarSign className="w-5 h-5 text-green-400" />
                  <div>
                    <p className="text-slate-400 text-xs">الإجمالي</p>
                    <p className="text-white font-bold">{saleData.total.toFixed(3)} د.ك</p>
                  </div>
                </div>
              </div>
              <div className="bg-white/10 backdrop-blur-lg rounded-xl p-3 border border-white/20">
                <div className="flex items-center space-x-reverse space-x-2">
                  <Package className="w-5 h-5 text-blue-400" />
                  <div>
                    <p className="text-slate-400 text-xs">الأصناف</p>
                    <p className="text-white font-bold">{saleData.items.length}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Receipt Preview */}
          <div className="flex-1 overflow-y-auto p-6">
            <div className={`transition-all duration-700 delay-300 ${animationStep >= 3 ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`}>
              {/* Receipt Container */}
              <div
                className="bg-white rounded-2xl p-6 shadow-2xl border-2 border-gray-200 text-sm"
                dir="rtl"
                style={{
                  fontFamily: 'Arial, "Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
                  color: '#000',
                  lineHeight: '1.4'
                }}
              >
                {/* Header */}
                <div className="text-center mb-6 border-b-2 border-black pb-4">
                  <div
                    className="text-lg font-bold mb-2"
                    style={{
                      fontFamily: 'Arial, "Segoe UI", sans-serif',
                      fontSize: '18px',
                      color: '#000',
                      fontWeight: 'bold'
                    }}
                  >
                    ═══ {storeInfo.name} ═══
                  </div>
                  <div
                    className="text-xs space-y-1"
                    style={{
                      fontFamily: 'Arial, "Segoe UI", sans-serif',
                      fontSize: '12px',
                      color: '#000'
                    }}
                  >
                    <div style={{ marginBottom: '4px' }}>📍 {storeInfo.address}</div>
                    <div style={{ marginBottom: '4px' }}>📞 {storeInfo.phone}</div>
                    {storeInfo.email && <div style={{ marginBottom: '4px' }}>📧 {storeInfo.email}</div>}
                    {storeInfo.taxNumber && <div style={{ marginBottom: '4px' }}>🏢 الرقم الضريبي: {storeInfo.taxNumber}</div>}
                  </div>
                  <div
                    className="text-sm font-bold mt-3"
                    style={{
                      fontFamily: 'Arial, "Segoe UI", sans-serif',
                      fontSize: '14px',
                      color: '#000',
                      fontWeight: 'bold',
                      marginTop: '12px'
                    }}
                  >
                    ★ ★ ★ فاتورة بيع ★ ★ ★
                  </div>
                </div>

                {/* Sale Info */}
                <div
                  className="mb-4 bg-gray-100 p-3 rounded border"
                  style={{
                    fontFamily: 'Arial, "Segoe UI", sans-serif',
                    fontSize: '12px',
                    color: '#000',
                    marginBottom: '16px'
                  }}
                >
                  <div
                    className="text-center font-bold mb-2"
                    style={{
                      fontWeight: 'bold',
                      marginBottom: '8px',
                      color: '#000'
                    }}
                  >
                    ═══ معلومات الفاتورة ═══
                  </div>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{ color: '#000' }}>🧾 رقم الفاتورة:</span>
                      <span style={{ fontFamily: 'monospace', fontWeight: 'bold', color: '#000' }}>{saleData.saleNumber}</span>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{ color: '#000' }}>📅 التاريخ:</span>
                      <span style={{ color: '#000' }}>{saleData.timestamp.toLocaleDateString('fr-FR')}</span>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{ color: '#000' }}>🕐 الوقت:</span>
                      <span style={{ color: '#000' }}>{saleData.timestamp.toLocaleTimeString('fr-FR')}</span>
                    </div>
                    {saleData.customer && saleData.customer !== 'عميل عادي' && (
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span style={{ color: '#000' }}>🤝 العميل:</span>
                        <span style={{ fontWeight: 'bold', color: '#000' }}>
                          {saleData.customer || 'عميل غير محدد'}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Items */}
                <div
                  className="mb-4"
                  style={{
                    fontFamily: 'Arial, "Segoe UI", sans-serif',
                    marginBottom: '16px'
                  }}
                >
                  <div
                    className="text-center font-bold mb-2"
                    style={{
                      fontWeight: 'bold',
                      marginBottom: '8px',
                      fontSize: '12px',
                      color: '#000'
                    }}
                  >
                    ═══ تفاصيل المشتريات ═══
                  </div>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                    {saleData.items.map((item, index) => (
                      <div
                        key={index}
                        style={{
                          borderBottom: '1px solid #ccc',
                          paddingBottom: '8px'
                        }}
                      >
                        <div
                          style={{
                            fontWeight: 'bold',
                            fontSize: '12px',
                            marginBottom: '4px',
                            color: '#000'
                          }}
                        >
                          {item.name}
                        </div>
                        <div
                          style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            fontSize: '11px',
                            color: '#000'
                          }}
                        >
                          <span>{item.quantity} × {item.price.toFixed(2)}</span>
                          <span style={{ fontWeight: 'bold' }}>{(item.price * item.quantity).toFixed(2)} دج</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Totals */}
                <div
                  className="mb-4 bg-gray-100 p-3 rounded border"
                  style={{
                    fontFamily: 'Arial, "Segoe UI", sans-serif',
                    marginBottom: '16px'
                  }}
                >
                  <div
                    className="text-center font-bold mb-2"
                    style={{
                      fontWeight: 'bold',
                      marginBottom: '8px',
                      fontSize: '12px',
                      color: '#000'
                    }}
                  >
                    ═══ ملخص الفاتورة ═══
                  </div>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '4px', fontSize: '12px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{ color: '#000' }}>💰 المجموع الفرعي:</span>
                      <span style={{ color: '#000' }}>{subtotal.toFixed(2)} دج</span>
                    </div>
                    {discount > 0 && (
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span style={{ color: '#000' }}>🏷️ الخصم:</span>
                        <span style={{ color: '#000' }}>-{discount.toFixed(2)} دج</span>
                      </div>
                    )}
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{ color: '#000' }}>📊 الضريبة (5%):</span>
                      <span style={{ color: '#000' }}>{tax.toFixed(2)} دج</span>
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        fontWeight: 'bold',
                        fontSize: '14px',
                        backgroundColor: '#000',
                        color: '#fff',
                        padding: '8px',
                        borderRadius: '4px',
                        marginTop: '8px'
                      }}
                    >
                      <span>🎯 الإجمالي النهائي:</span>
                      <span>{saleData.total.toFixed(2)} دج</span>
                    </div>
                  </div>
                </div>

                {/* Payment Info */}
                <div
                  className="mb-4 bg-gray-50 p-3 rounded border"
                  style={{
                    fontFamily: 'Arial, "Segoe UI", sans-serif',
                    marginBottom: '16px'
                  }}
                >
                  <div
                    className="text-center font-bold mb-2"
                    style={{
                      fontWeight: 'bold',
                      marginBottom: '8px',
                      fontSize: '12px',
                      color: '#000'
                    }}
                  >
                    ═══ معلومات الدفع ═══
                  </div>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '4px', fontSize: '12px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{ color: '#000' }}>💳 طريقة الدفع:</span>
                      <span style={{ fontWeight: 'bold', color: '#000' }}>
                        {saleData.paymentMethod === 'cash' ? '💵 نقداً' :
                         saleData.paymentMethod === 'credit' ? '📝 آجل' : '🔄 مختلط'}
                      </span>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span style={{ color: '#000' }}>💰 المبلغ المدفوع:</span>
                      <span style={{ fontWeight: 'bold', color: '#000' }}>{(saleData.paid || 0).toFixed(2)} دج</span>
                    </div>
                    {(saleData.change || 0) > 0 && (
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          backgroundColor: '#e8f5e8',
                          padding: '8px',
                          borderRadius: '4px',
                          border: '1px solid #4CAF50',
                          marginTop: '4px'
                        }}
                      >
                        <span style={{ color: '#000' }}>💸 الباقي:</span>
                        <span style={{ fontWeight: 'bold', color: '#2E7D32' }}>{(saleData.change || 0).toFixed(2)} دج</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Footer */}
                <div
                  className="text-center border-t-2 border-black pt-4"
                  style={{
                    fontFamily: 'Arial, "Segoe UI", sans-serif',
                    paddingTop: '16px'
                  }}
                >
                  <div
                    style={{
                      fontSize: '14px',
                      fontWeight: 'bold',
                      marginBottom: '8px',
                      color: '#000'
                    }}
                  >
                    ★ ★ ★ شكراً لتسوقكم معنا ★ ★ ★
                  </div>
                  <div
                    style={{
                      fontSize: '12px',
                      marginBottom: '12px',
                      color: '#000'
                    }}
                  >
                    🙏 نتطلع لخدمتكم مرة أخرى 🙏
                  </div>

                  {/* QR Code */}
                  <div
                    style={{
                      border: '2px solid #000',
                      width: '64px',
                      height: '64px',
                      margin: '0 auto 8px auto',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '12px',
                      fontWeight: 'bold',
                      backgroundColor: '#f5f5f5',
                      color: '#000'
                    }}
                  >
                    📱 QR
                  </div>
                  <div
                    style={{
                      fontSize: '12px',
                      marginBottom: '12px',
                      color: '#000'
                    }}
                  >
                    📲 امسح للتقييم
                  </div>

                  {/* Rating */}
                  <div
                    style={{
                      fontSize: '14px',
                      marginBottom: '8px',
                      color: '#000'
                    }}
                  >
                    ⭐ ⭐ ⭐ ⭐ ⭐
                  </div>

                  {/* Barcode */}
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      marginTop: '12px'
                    }}
                  >
                    <BarcodeGenerator
                      value={saleData.saleNumber}
                      width={180}
                      height={40}
                      displayValue={true}
                      fontSize={10}
                      textAlign="center"
                      textPosition="bottom"
                      background="#ffffff"
                      lineColor="#000000"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="p-6 border-t border-white/10 space-y-4">
            {/* Print Button */}
            <button
              onClick={handlePrint}
              className={`w-full bg-gradient-to-r from-green-500 to-emerald-400 hover:scale-105 transform transition-all duration-300 py-4 rounded-2xl font-bold text-lg shadow-2xl hover:shadow-green-500/25 flex items-center justify-center space-x-reverse space-x-3 ${
                printed ? 'from-emerald-600 to-green-500' : ''
              }`}
            >
              {printed ? (
                <>
                  <Check className="w-6 h-6 text-white" />
                  <span className="text-white">تم الطباعة بنجاح!</span>
                  <Sparkles className="w-5 h-5 text-white animate-pulse" />
                </>
              ) : (
                <>
                  <Printer className="w-6 h-6 text-white" />
                  <span className="text-white">طباعة الوصل</span>
                  <Zap className="w-5 h-5 text-white animate-pulse" />
                </>
              )}
            </button>

            {/* Secondary Actions */}
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={handleCopy}
                className={`bg-white/10 backdrop-blur-lg border border-white/20 hover:border-white/40 py-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center justify-center space-x-reverse space-x-2 ${
                  copied ? 'bg-green-500/20 border-green-400' : ''
                }`}
              >
                {copied ? (
                  <>
                    <Check className="w-5 h-5 text-green-400" />
                    <span className="text-green-400 font-medium">تم النسخ</span>
                  </>
                ) : (
                  <>
                    <Copy className="w-5 h-5 text-white" />
                    <span className="text-white font-medium">نسخ</span>
                  </>
                )}
              </button>

              <button className="bg-white/10 backdrop-blur-lg border border-white/20 hover:border-white/40 py-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center justify-center space-x-reverse space-x-2">
                <Share2 className="w-5 h-5 text-white" />
                <span className="text-white font-medium">مشاركة</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReceiptModal;
