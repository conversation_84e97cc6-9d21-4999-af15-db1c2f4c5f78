const express = require('express');
const router = express.Router();
const pool = require('../database');

// 🎯 الحصول على فاتورة الدين المفتوحة للعميل أو إنشاء واحدة جديدة
router.get('/customer/:customerId/debt-invoice', async (req, res) => {
  try {
    const { customerId } = req.params;
    console.log(`🔍 البحث عن فاتورة دين للعميل: ${customerId}`);

    // البحث عن فاتورة دين مفتوحة
    let debtInvoice = await pool.query(`
      SELECT
        di.*,
        c.name as customer_name,
        c.phone as customer_phone
      FROM pos_system.customer_debt_invoices di
      JOIN pos_system.customers c ON di.customer_id = c.id
      WHERE di.customer_id = $1 AND di.status = 'open'
      LIMIT 1
    `, [customerId]);

    // إذا لم توجد فاتورة، أنشئ واحدة جديدة
    if (debtInvoice.rows.length === 0) {
      console.log('📋 إنشاء فاتورة دين جديدة...');

      // الحصول على معلومات العميل
      const customer = await pool.query(`
        SELECT name FROM pos_system.customers WHERE id = $1
      `, [customerId]);

      if (customer.rows.length === 0) {
        return res.status(404).json({ error: 'العميل غير موجود' });
      }

      // إنشاء رقم فاتورة فريد
      const customerName = customer.rows[0].name;
      const namePrefix = customerName.substring(0, 3).toUpperCase();
      const timestamp = Date.now().toString().slice(-6);
      const invoiceNumber = `DEBT-${namePrefix}-${timestamp}`;

      // إنشاء فاتورة دين جديدة
      const newInvoice = await pool.query(`
        INSERT INTO pos_system.customer_debt_invoices
        (customer_id, invoice_number, credit_limit, payment_terms, notes)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING *
      `, [
        customerId,
        invoiceNumber,
        10000.00, // حد ائتمان افتراضي
        30, // 30 يوم للسداد
        `فاتورة دين للعميل: ${customerName}`
      ]);

      debtInvoice = await pool.query(`
        SELECT
          di.*,
          c.name as customer_name,
          c.phone as customer_phone
        FROM pos_system.customer_debt_invoices di
        JOIN pos_system.customers c ON di.customer_id = c.id
        WHERE di.id = $1
      `, [newInvoice.rows[0].id]);

      console.log(`✅ تم إنشاء فاتورة دين جديدة: ${invoiceNumber}`);
    }

    // الحصول على عناصر الفاتورة
    const invoiceItems = await pool.query(`
      SELECT * FROM pos_system.debt_invoice_items
      WHERE debt_invoice_id = $1
      ORDER BY purchase_date DESC
    `, [debtInvoice.rows[0].id]);

    // الحصول على دفعات الفاتورة
    const payments = await pool.query(`
      SELECT * FROM pos_system.debt_payments
      WHERE debt_invoice_id = $1
      ORDER BY payment_date DESC
    `, [debtInvoice.rows[0].id]);

    const response = {
      success: true,
      debt_invoice: {
        ...debtInvoice.rows[0],
        items: invoiceItems.rows,
        payments: payments.rows,
        credit_available: parseFloat(debtInvoice.rows[0].credit_limit) - parseFloat(debtInvoice.rows[0].remaining_balance)
      }
    };

    console.log(`📊 فاتورة الدين: ${response.debt_invoice.invoice_number} - الرصيد: ${response.debt_invoice.remaining_balance} دج`);
    res.json(response);

  } catch (error) {
    console.error('❌ خطأ في الحصول على فاتورة الدين:', error);
    res.status(500).json({ error: 'خطأ في الحصول على فاتورة الدين' });
  }
});

// 💰 إضافة مشتريات جديدة لفاتورة الدين
router.post('/add-purchase', async (req, res) => {
  try {
    const { customerId, saleId, description, amount } = req.body;
    console.log(`💰 إضافة مشتريات بالدين للعميل: ${customerId}`);

    // الحصول على فاتورة الدين المفتوحة
    const debtInvoice = await pool.query(`
      SELECT * FROM pos_system.customer_debt_invoices
      WHERE customer_id = $1 AND status = 'open'
      LIMIT 1
    `, [customerId]);

    if (debtInvoice.rows.length === 0) {
      return res.status(404).json({ error: 'لا توجد فاتورة دين مفتوحة للعميل' });
    }

    const debtInvoiceId = debtInvoice.rows[0].id;

    // التحقق من حد الائتمان
    const newBalance = parseFloat(debtInvoice.rows[0].remaining_balance) + parseFloat(amount);
    if (newBalance > parseFloat(debtInvoice.rows[0].credit_limit)) {
      return res.status(400).json({
        error: 'تجاوز حد الائتمان المسموح',
        credit_limit: debtInvoice.rows[0].credit_limit,
        current_balance: debtInvoice.rows[0].remaining_balance,
        requested_amount: amount
      });
    }

    // إضافة العنصر الجديد
    const newItem = await pool.query(`
      INSERT INTO pos_system.debt_invoice_items
      (debt_invoice_id, sale_id, description, amount)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `, [debtInvoiceId, saleId, description, amount]);

    // تحديث رصيد الفاتورة
    await pool.query('SELECT pos_system.update_debt_invoice_balance($1)', [debtInvoiceId]);

    // ربط البيع بفاتورة الدين
    if (saleId) {
      await pool.query(`
        UPDATE pos_system.sales
        SET debt_invoice_id = $1, is_debt_sale = true, debt_status = 'pending'
        WHERE id = $2
      `, [debtInvoiceId, saleId]);
    }

    console.log(`✅ تم إضافة مشتريات بقيمة ${amount} دج لفاتورة الدين`);
    res.json({
      success: true,
      message: 'تم إضافة المشتريات لفاتورة الدين بنجاح',
      item: newItem.rows[0]
    });

  } catch (error) {
    console.error('❌ خطأ في إضافة مشتريات الدين:', error);
    res.status(500).json({ error: 'خطأ في إضافة مشتريات الدين' });
  }
});

// 📊 الحصول على جميع العملاء المدينين
router.get('/debtors', async (req, res) => {
  try {
    console.log('🔄 جاري تحميل العملاء المدينين...');

    const debtors = await pool.query(`
      SELECT 
        c.id as customer_id,
        c.name as customer_name,
        c.phone,
        c.email,
        c.address,
        c.balance,
        c.credit_limit,
        cd.id as debt_id,
        cd.amount as debt_amount,
        cd.paid_amount,
        cd.remaining_amount,
        cd.status as debt_status,
        cd.debt_date,
        cd.due_date,
        cd.notes,
        cd.created_at,
        cd.updated_at,
        s.sale_number,
        s.total_amount as sale_total
      FROM pos_system.customers c
      INNER JOIN pos_system.customer_debts cd ON c.id = cd.customer_id
      INNER JOIN pos_system.sales s ON cd.sale_id = s.id
      WHERE cd.remaining_amount > 0
      ORDER BY cd.debt_date DESC
    `);

    console.log(`✅ تم العثور على ${debtors.rows.length} عميل مدين`);
    res.json({
      success: true,
      debtors: debtors.rows
    });

  } catch (error) {
    console.error('❌ خطأ في تحميل المدينين:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في تحميل المدينين'
    });
  }
});

// 📊 إحصائيات الديون
router.get('/stats', async (req, res) => {
  try {
    console.log('📊 طلب إحصائيات الديون...');

    const stats = await pool.query(`
      SELECT
        -- إجمالي العملاء المدينين
        COUNT(DISTINCT di.customer_id) as total_debtors,
        -- إجمالي مبلغ الديون
        COALESCE(SUM(di.remaining_balance), 0) as total_debt_amount,
        -- متوسط الدين لكل عميل
        COALESCE(AVG(di.remaining_balance), 0) as average_debt,
        -- أكبر دين
        COALESCE(MAX(di.remaining_balance), 0) as max_debt,
        -- عدد الفواتير المفتوحة
        COUNT(*) as open_invoices
      FROM pos_system.customer_debt_invoices di
      WHERE di.status = 'open' AND di.remaining_balance > 0
    `);

    const recentPayments = await pool.query(`
      SELECT
        dp.*,
        c.name as customer_name,
        di.invoice_number
      FROM pos_system.debt_payments dp
      JOIN pos_system.customer_debt_invoices di ON dp.debt_invoice_id = di.id
      JOIN pos_system.customers c ON dp.customer_id = c.id
      ORDER BY dp.payment_date DESC
      LIMIT 10
    `);

    res.json({
      success: true,
      stats: stats.rows[0],
      recentPayments: recentPayments.rows
    });

  } catch (error) {
    console.error('❌ خطأ في إحصائيات الديون:', error);
    res.status(500).json({ error: 'خطأ في إحصائيات الديون' });
  }
});

// 📋 الحصول على فاتورة دين عميل معين
router.get('/customer/:customerId/debt-invoice', async (req, res) => {
  try {
    const { customerId } = req.params;
    console.log(`📋 طلب فاتورة دين للعميل: ${customerId}`);
    console.log(`🔍 URL المطلوب: ${req.originalUrl}`);

    const debtInvoice = await pool.query(`
      SELECT
        di.*,
        c.name as customer_name,
        c.phone as customer_phone,
        (SELECT COUNT(*) FROM pos_system.debt_invoice_items WHERE debt_invoice_id = di.id) as items_count,
        (SELECT COUNT(*) FROM pos_system.debt_payments WHERE debt_invoice_id = di.id) as payments_count,
        (SELECT COALESCE(SUM(payment_amount), 0) FROM pos_system.debt_payments WHERE debt_invoice_id = di.id) as total_payments
      FROM pos_system.customer_debt_invoices di
      JOIN pos_system.customers c ON di.customer_id = c.id
      WHERE di.customer_id = $1 AND di.status = 'open'
      ORDER BY di.created_at DESC
      LIMIT 1
    `, [customerId]);

    if (debtInvoice.rows.length === 0) {
      return res.json({
        success: true,
        hasDebt: false,
        message: 'العميل ليس لديه ديون مسجلة'
      });
    }

    const invoice = debtInvoice.rows[0];

    res.json({
      success: true,
      hasDebt: true,
      debtInvoice: {
        id: invoice.id,
        invoiceNumber: invoice.invoice_number,
        customerName: invoice.customer_name,
        customerPhone: invoice.customer_phone,
        totalAmount: parseFloat(invoice.total_debt_amount || 0),
        remainingBalance: parseFloat(invoice.remaining_balance || 0),
        totalPayments: parseFloat(invoice.total_payments || 0),
        itemsCount: parseInt(invoice.items_count || 0),
        paymentsCount: parseInt(invoice.payments_count || 0),
        createdAt: invoice.created_at,
        updatedAt: invoice.updated_at,
        status: invoice.status
      }
    });

  } catch (error) {
    console.error('❌ خطأ في جلب فاتورة دين العميل:', error);
    res.status(500).json({ error: 'خطأ في جلب فاتورة دين العميل' });
  }
});

// 🗑️ حذف فاتورة دين عميل (حذف آمن)
router.delete('/invoice/:customerId', async (req, res) => {
  const client = await pool.connect();
  try {
    const { customerId } = req.params;
    const { confirmDelete } = req.query;

    // التحقق من وجود ديون للعميل
    const debtsResult = await client.query(`
      SELECT 
        cd.*,
        s.sale_number,
        COUNT(cdp.id) as payments_count
      FROM pos_system.customer_debts cd
      LEFT JOIN pos_system.sales s ON cd.sale_id = s.id
      LEFT JOIN pos_system.customer_debt_payments cdp ON cd.id = cdp.debt_id
      WHERE cd.customer_id = $1
      GROUP BY cd.id, s.sale_number
    `, [customerId]);

    if (debtsResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'لا توجد ديون مسجلة لهذا العميل'
      });
    }

    const debts = debtsResult.rows;
    const totalRemaining = debts.reduce((sum, debt) => sum + parseFloat(debt.remaining_amount), 0);
    const hasPayments = debts.some(debt => debt.payments_count > 0);

    // إذا كان الطلب للتحقق فقط
    if (!confirmDelete) {
      return res.json({
        canDelete: true,
        deleteInfo: {
          debtsCount: debts.length,
          totalAmount: totalRemaining,
          hasPayments,
          invoiceNumbers: debts.map(d => d.sale_number)
        }
      });
    }

    await client.query('BEGIN');

    // حذف الدفعات أولاً
    await client.query(`
      DELETE FROM pos_system.customer_debt_payments
      WHERE debt_id IN (
        SELECT id FROM pos_system.customer_debts
        WHERE customer_id = $1
      )
    `, [customerId]);

    // حذف الديون
    await client.query(`
      DELETE FROM pos_system.customer_debts
      WHERE customer_id = $1
    `, [customerId]);

    // تحديث حالة المبيعات المرتبطة
    await client.query(`
      UPDATE pos_system.sales
      SET is_debt_sale = false, debt_status = 'paid'
      WHERE customer_id = $1
    `, [customerId]);

    await client.query('COMMIT');

    console.log(`✅ تم حذف جميع ديون العميل ${customerId}`);
    res.json({
      success: true,
      message: 'تم حذف جميع الديون بنجاح'
    });

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ خطأ في حذف الديون:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'خطأ في حذف الديون'
    });
  } finally {
    client.release();
  }
});

module.exports = router;
