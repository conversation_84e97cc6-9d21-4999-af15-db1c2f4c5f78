const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'pos_system_db',
  user: process.env.DB_USER || 'postgres',
  password: String(process.env.DB_PASSWORD || 'toossar'),
  ssl: false,
  connectionTimeoutMillis: 5000,
  idleTimeoutMillis: 30000,
  max: 20
});

// تعيين schema افتراضي
pool.on('connect', (client) => {
  client.query('SET search_path TO pos_system, public', (err) => {
    if (err) {
      console.error('❌ خطأ في تعيين المخطط الافتراضي:', err.stack);
    } else {
      console.log('✅ تم تعيين المخطط الافتراضي بنجاح');
    }
  });
});

// اختبار الاتصال
pool.connect((err, client, release) => {
  if (err) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', err.stack);
  } else {
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
    client.query('SELECT current_schema()', (err, result) => {
      if (err) {
        console.error('❌ خطأ في التحقق من المخطط الحالي:', err.stack);
      } else {
        console.log('📊 المخطط الحالي:', result.rows[0].current_schema);
      }
      release();
    });
  }
});

module.exports = pool;
