# 🎨 مجلد الأيقونات والأصول

## 📋 الملفات المطلوبة:

### 1. الأيقونات الأساسية:
- **icon.ico** - أيقونة Windows (256x256 بكسل)
- **icon.png** - أيقونة Linux (256x256 بكسل)  
- **icon.icns** - أيقونة macOS (512x512 بكسل)

### 2. أيقونات المثبت:
- **installer-banner.bmp** - صورة المثبت (164x314 بكسل)
- **installer-header.bmp** - رأس المثبت (150x57 بكسل)

## 🛠️ إنشاء الأيقونات:

### الطريقة السهلة:
1. استخدم أي صورة بصيغة PNG
2. اذهب إلى [ConvertICO](https://convertio.co/png-ico/)
3. حول الصورة إلى .ico
4. ضع الملف هنا باسم `icon.ico`

### الطريقة المتقدمة:
```bash
# استخدام ImageMagick
convert logo.png -resize 256x256 icon.ico
convert logo.png -resize 256x256 icon.png
```

## 📐 المواصفات المطلوبة:

### أيقونة التطبيق:
- **الحجم**: 256x256 بكسل (مستحسن)
- **التنسيق**: ICO للويندوز، PNG للينكس
- **الشفافية**: مدعومة
- **الألوان**: 32-bit مع Alpha

### صور المثبت:
- **Banner**: 164x314 بكسل، BMP
- **Header**: 150x57 بكسل، BMP
- **الألوان**: 24-bit RGB

## 🎨 نصائح التصميم:

1. **البساطة**: تجنب التفاصيل الكثيرة
2. **الوضوح**: يجب أن تكون واضحة في الأحجام الصغيرة
3. **الألوان**: استخدم ألوان متباينة
4. **الهوية**: تعكس هوية العلامة التجارية

## 📁 الملفات الحالية:

- [ ] icon.ico - أيقونة Windows
- [ ] icon.png - أيقونة Linux
- [ ] icon.icns - أيقونة macOS
- [ ] installer-banner.bmp - صورة المثبت
- [ ] installer-header.bmp - رأس المثبت

## 🔄 التحديث:

عند تغيير الأيقونات:
1. استبدل الملفات في هذا المجلد
2. أعد بناء التطبيق: `build-exe.bat`
3. اختبر النتيجة
