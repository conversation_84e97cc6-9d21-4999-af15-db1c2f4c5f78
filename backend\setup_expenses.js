const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
});

async function setupExpensesTable() {
  const client = await pool.connect();
  
  try {
    console.log('🔗 الاتصال بقاعدة البيانات...');
    
    // قراءة ملف SQL
    const sqlFile = path.join(__dirname, 'create_expenses_table.sql');
    const sql = fs.readFileSync(sqlFile, 'utf8');
    
    console.log('📦 إنشاء جدول المصروفات...');
    await client.query(sql);
    
    console.log('✅ تم إنشاء جدول المصروفات بنجاح');
    
    // التحقق من البيانات
    const result = await client.query('SELECT COUNT(*) as count, SUM(amount) as total FROM pos_system.expenses');
    const { count, total } = result.rows[0];
    
    console.log(`📊 إحصائيات المصروفات:`);
    console.log(`   - عدد المصروفات: ${count}`);
    console.log(`   - إجمالي المصروفات: ${parseFloat(total || 0).toLocaleString()} دج`);
    
  } catch (error) {
    console.error('❌ خطأ في إعداد جدول المصروفات:', error.message);
  } finally {
    client.release();
    await pool.end();
  }
}

// تشغيل الدالة
setupExpensesTable();
