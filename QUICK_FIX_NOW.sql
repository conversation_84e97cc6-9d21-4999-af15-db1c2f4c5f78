-- 🚀 إصلاح سريع فوري - تحديث جدول sale_items
-- انسخ والصق هذا الكود في pgAdmin Query Tool واضغط F5

-- تعيين المخطط
SET search_path TO pos_system, public;

-- 1. إضافة الحقول المطلوبة إذا لم تكن موجودة
DO $$
BEGIN
    -- إضافة عمود is_unknown
    BEGIN
        ALTER TABLE sale_items ADD COLUMN is_unknown BOOLEAN DEFAULT FALSE;
        RAISE NOTICE '✅ تم إضافة عمود is_unknown';
    EXCEPTION
        WHEN duplicate_column THEN
            RAISE NOTICE 'ℹ️ عمود is_unknown موجود مسبقاً';
    END;
    
    -- إضافة عمود unknown_product_code
    BEGIN
        ALTER TABLE sale_items ADD COLUMN unknown_product_code VARCHAR(50);
        RAISE NOTICE '✅ تم إضافة عمود unknown_product_code';
    EXCEPTION
        WHEN duplicate_column THEN
            RAISE NOTICE 'ℹ️ عمود unknown_product_code موجود مسبقاً';
    END;
    
    -- إضا<PERSON>ة عمود unknown_category
    BEGIN
        ALTER TABLE sale_items ADD COLUMN unknown_category VARCHAR(100) DEFAULT 'منتجات غير معروفة';
        RAISE NOTICE '✅ تم إضافة عمود unknown_category';
    EXCEPTION
        WHEN duplicate_column THEN
            RAISE NOTICE 'ℹ️ عمود unknown_category موجود مسبقاً';
    END;
    
    -- جعل product_id يقبل NULL
    BEGIN
        ALTER TABLE sale_items ALTER COLUMN product_id DROP NOT NULL;
        RAISE NOTICE '✅ تم جعل product_id يقبل NULL';
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'ℹ️ product_id يقبل NULL مسبقاً';
    END;
END $$;

-- 2. إنشاء الفهارس
CREATE INDEX IF NOT EXISTS idx_sale_items_unknown ON sale_items(is_unknown) WHERE is_unknown = TRUE;
CREATE INDEX IF NOT EXISTS idx_sale_items_unknown_code ON sale_items(unknown_product_code) WHERE unknown_product_code IS NOT NULL;

-- 3. إدراج بيع تجريبي للاختبار
DO $$
DECLARE
    test_sale_id UUID;
BEGIN
    -- إنشاء بيع تجريبي
    INSERT INTO sales (
        subtotal, tax_amount, discount_amount, total_amount, 
        payment_method, amount_paid, change_amount, notes, cashier_name
    ) VALUES (
        175.00, 0, 0, 175.00, 'cash', 175.00, 0, 
        'بيع تجريبي مختلط (منتج عادي + غير معروف)', 'النظام'
    ) RETURNING id INTO test_sale_id;
    
    -- إضافة منتج غير معروف
    INSERT INTO sale_items (
        sale_id, product_id, product_name, quantity, unit_price, total_price,
        is_unknown, unknown_product_code, unknown_category
    ) VALUES (
        test_sale_id, NULL, '🔮 منتج تجريبي غير معروف', 1, 100.00, 100.00,
        TRUE, 'UNK-TEST-001', 'منتجات تجريبية'
    );
    
    -- إضافة منتج عادي (إذا كان هناك منتجات في الجدول)
    BEGIN
        INSERT INTO sale_items (
            sale_id, product_id, product_name, quantity, unit_price, total_price,
            is_unknown
        )
        SELECT 
            test_sale_id, p.id, p.name, 1, 75.00, 75.00, FALSE
        FROM products p 
        WHERE p.is_active = TRUE 
        LIMIT 1;
        
        RAISE NOTICE '✅ تم إضافة منتج عادي للبيع التجريبي';
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'ℹ️ لم يتم العثور على منتجات عادية لإضافتها';
    END;
    
    RAISE NOTICE '✅ تم إنشاء بيع تجريبي مختلط برقم: %', test_sale_id;
END $$;

-- 4. فحص النتائج
SELECT 
    'المبيعات الإجمالية' as النوع,
    COUNT(*) as العدد,
    SUM(total_amount) as المبلغ
FROM sales

UNION ALL

SELECT 
    'عناصر عادية' as النوع,
    COUNT(*) as العدد,
    SUM(total_price) as المبلغ
FROM sale_items 
WHERE is_unknown = FALSE OR is_unknown IS NULL

UNION ALL

SELECT 
    'عناصر غير معروفة' as النوع,
    COUNT(*) as العدد,
    SUM(total_price) as المبلغ
FROM sale_items 
WHERE is_unknown = TRUE;

-- 5. عرض آخر المبيعات
SELECT 
    s.sale_number,
    s.total_amount,
    s.created_at,
    COUNT(si.id) as عدد_العناصر,
    COUNT(CASE WHEN si.is_unknown = TRUE THEN 1 END) as عناصر_غير_معروفة,
    COUNT(CASE WHEN si.is_unknown = FALSE OR si.is_unknown IS NULL THEN 1 END) as عناصر_عادية
FROM sales s
LEFT JOIN sale_items si ON s.id = si.sale_id
GROUP BY s.id, s.sale_number, s.total_amount, s.created_at
ORDER BY s.created_at DESC
LIMIT 5;

-- رسالة نجاح
DO $$
BEGIN
    RAISE NOTICE '🎉 تم الإصلاح بنجاح!';
    RAISE NOTICE '✅ الآن يمكن بيع المنتجات العادية وغير المعروفة في فاتورة واحدة';
    RAISE NOTICE '🔄 أعد تشغيل الخادم إذا لزم الأمر: npm run dev';
    RAISE NOTICE '🧪 جرب إضافة منتج عادي + /100 في شاشة البيع';
END $$;
