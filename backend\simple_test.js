const pool = require('./database');

async function testProfitsCalculation() {
  try {
    console.log('🧪 اختبار حساب الأرباح من قاعدة البيانات...');
    
    // اختبار استعلام الأرباح
    const result = await pool.query(`
      SELECT 
        COALESCE(SUM(
          (si.unit_price - p.cost) * si.quantity
        ), 0) as total_profit,
        COUNT(DISTINCT s.id) as total_sales_count
      FROM sale_items si
      JOIN sales s ON si.sale_id = s.id
      JOIN products p ON si.product_id = p.id
      WHERE s.status = 'completed'
    `);
    
    console.log('💰 نتائج حساب الأرباح:');
    console.log('إجمالي الأرباح:', parseFloat(result.rows[0].total_profit));
    console.log('عدد المبيعات:', parseInt(result.rows[0].total_sales_count));
    
    // اختبار استعلام الإيرادات
    const revenueResult = await pool.query(`
      SELECT COALESCE(SUM(total_amount), 0) as total_revenue
      FROM sales 
      WHERE status = 'completed'
    `);
    
    console.log('💵 إجمالي الإيرادات:', parseFloat(revenueResult.rows[0].total_revenue));
    
    // حساب هامش الربح
    const totalProfit = parseFloat(result.rows[0].total_profit);
    const totalRevenue = parseFloat(revenueResult.rows[0].total_revenue);
    const profitMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;
    
    console.log('📊 هامش الربح:', profitMargin.toFixed(2) + '%');
    
    console.log('\n✅ تم اختبار حساب الأرباح بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في اختبار حساب الأرباح:', error.message);
  } finally {
    await pool.end();
  }
}

testProfitsCalculation();
