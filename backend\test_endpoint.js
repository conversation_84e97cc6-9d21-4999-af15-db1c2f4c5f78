const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();
app.use(cors());
app.use(express.json());

// اختبار endpoint بسيط
app.get('/api/financial/smart-analysis', (req, res) => {
  console.log('🤖 تم استلام طلب التحليل الذكي');
  
  res.json({
    success: true,
    message: '🤖 تم إنجاز التحليل الذكي المتقدم بنجاح',
    analysis: {
      trends: {
        growth_rate: 15.5,
        avg_daily_revenue: 1200,
        avg_daily_profit: 300,
        avg_daily_orders: 8,
        trend_direction: 'صاعد قوي'
      },
      predictions: {
        next_week: {
          revenue: 8400,
          profit: 2100
        },
        next_month: {
          revenue: 36000,
          profit: 9000
        },
        confidence: 85
      },
      top_products: [
        {
          name: 'منتج تجريبي',
          total_sold: 50,
          daily_demand: '2.5',
          days_until_stockout: 20,
          profit_margin: 25.0
        }
      ],
      customer_patterns: [
        {
          time: 'الأحد 14:00',
          transactions: 5,
          avg_value: 150.0
        }
      ],
      recommendations: [
        {
          type: 'نمو',
          priority: 'منخفضة',
          message: '📈 الأداء ممتاز!',
          action: 'الحفاظ على الأداء الجيد',
          impact: 'استمرار النمو'
        }
      ],
      smart_kpis: {
        inventory_health: 85,
        demand_stability: 90,
        profit_efficiency: 25
      }
    },
    timestamp: new Date().toISOString(),
    ai_insights: [
      '🤖 تم تحليل 30 يوم من البيانات',
      '📈 معدل النمو: 15.5%',
      '🎯 مستوى الثقة في التنبؤات: 85%',
      '💡 تم توليد 1 توصية ذكية'
    ]
  });
});

app.listen(5003, () => {
  console.log('🧪 Test server running on port 5003');
  console.log('🔗 Test URL: http://localhost:5003/api/financial/smart-analysis');
});
