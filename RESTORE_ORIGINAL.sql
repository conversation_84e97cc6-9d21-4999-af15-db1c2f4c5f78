-- 🔄 إعادة النظام كما كان - بيع عادي بسيط
-- انسخ والصق هذا الكود في pgAdmin Query Tool واضغط F5

-- تعيين المخطط
SET search_path TO pos_system, public;

-- 1. حذف الحقول الإضافية من sale_items
DO $$
BEGIN
    -- حذف عمود is_unknown
    BEGIN
        ALTER TABLE sale_items DROP COLUMN IF EXISTS is_unknown;
        RAISE NOTICE '✅ تم حذف عمود is_unknown';
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'ℹ️ عمود is_unknown غير موجود';
    END;
    
    -- حذف عمود unknown_product_code
    BEGIN
        ALTER TABLE sale_items DROP COLUMN IF EXISTS unknown_product_code;
        RAISE NOTICE '✅ تم حذف عمود unknown_product_code';
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'ℹ️ عمود unknown_product_code غير موجود';
    END;
    
    -- حذف عمود unknown_category
    BEGIN
        ALTER TABLE sale_items DROP COLUMN IF EXISTS unknown_category;
        RAISE NOTICE '✅ تم حذف عمود unknown_category';
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'ℹ️ عمود unknown_category غير موجود';
    END;
    
    -- إعادة product_id كحقل مطلوب
    BEGIN
        ALTER TABLE sale_items ALTER COLUMN product_id SET NOT NULL;
        RAISE NOTICE '✅ تم جعل product_id مطلوب';
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'ℹ️ product_id مطلوب مسبقاً';
    END;
END $$;

-- 2. حذف الجداول الإضافية إذا كانت موجودة
DROP TABLE IF EXISTS unknown_sales CASCADE;
DROP TABLE IF EXISTS unknown_products CASCADE;

-- 3. حذف الفهارس الإضافية
DROP INDEX IF EXISTS idx_sale_items_unknown;
DROP INDEX IF EXISTS idx_sale_items_unknown_code;
DROP INDEX IF EXISTS idx_unknown_products_name;
DROP INDEX IF EXISTS idx_unknown_sales_date;
DROP INDEX IF EXISTS idx_unknown_sales_product;
DROP INDEX IF EXISTS idx_unknown_sales_related;
DROP INDEX IF EXISTS idx_unknown_sales_unified;
DROP INDEX IF EXISTS idx_unknown_sales_number;

-- 4. حذف الـ Views الإضافية
DROP VIEW IF EXISTS unknown_products_summary CASCADE;
DROP VIEW IF EXISTS mixed_sales_summary CASCADE;

-- 5. حذف المبيعات التجريبية
DELETE FROM sale_items WHERE product_name LIKE '%غير معروف%' OR product_name LIKE '%🔮%';
DELETE FROM sales WHERE notes LIKE '%غير معروف%' OR notes LIKE '%تجريبي%';

-- 6. تنظيف أي بيانات تجريبية
DELETE FROM sales WHERE cashier_name = 'النظام' AND notes LIKE '%تجريبي%';

-- 7. فحص النظام النظيف
SELECT 
    'المبيعات' as الجدول,
    COUNT(*) as العدد
FROM sales

UNION ALL

SELECT 
    'عناصر المبيعات' as الجدول,
    COUNT(*) as العدد
FROM sale_items

UNION ALL

SELECT 
    'المنتجات' as الجدول,
    COUNT(*) as العدد
FROM products;

-- 8. عرض هيكل جدول sale_items النظيف
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'pos_system' 
AND table_name = 'sale_items'
ORDER BY ordinal_position;

-- رسالة نجاح
DO $$
BEGIN
    RAISE NOTICE '🎉 تم إعادة النظام كما كان!';
    RAISE NOTICE '✅ بيع عادي بسيط بدون تعقيدات';
    RAISE NOTICE '🔄 أعد تشغيل الخادم: npm run dev';
    RAISE NOTICE '🛒 النظام الآن يدعم المنتجات العادية فقط';
END $$;
