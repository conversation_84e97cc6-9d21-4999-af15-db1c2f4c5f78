const pool = require('./database');

async function debugSalesStats() {
  try {
    console.log('🔍 تحليل مبيعات اليوم...');
    
    // فحص مبيعات اليوم
    const todaySales = await pool.query(`
      SELECT 
        id,
        sale_number,
        total_amount,
        created_at,
        status
      FROM pos_system.sales 
      WHERE DATE(created_at) = CURRENT_DATE
      AND status = 'completed'
      ORDER BY created_at DESC
    `);
    
    console.log(`📅 مبيعات اليوم (${new Date().toLocaleDateString()}):`);
    console.log(`📊 عدد الفواتير: ${todaySales.rows.length}`);
    
    let totalToday = 0;
    todaySales.rows.forEach((sale, index) => {
      const amount = parseFloat(sale.total_amount);
      totalToday += amount;
      console.log(`${index + 1}. فاتورة ${sale.sale_number}: ${amount} دج - ${new Date(sale.created_at).toLocaleString()}`);
    });
    
    console.log(`💰 إجمالي مبيعات اليوم: ${totalToday} دج`);
    console.log('---');
    
    // فحص إجمالي المبيعات
    const totalSales = await pool.query(`
      SELECT 
        COUNT(*) as total_count,
        SUM(total_amount) as total_amount
      FROM pos_system.sales 
      WHERE status = 'completed'
    `);
    
    console.log(`📈 إجمالي المبيعات:`);
    console.log(`📊 عدد الفواتير: ${totalSales.rows[0].total_count}`);
    console.log(`💰 إجمالي المبلغ: ${parseFloat(totalSales.rows[0].total_amount || 0)} دج`);
    console.log('---');
    
    // فحص الاستعلام الحالي في لوحة التحكم
    console.log('🔍 اختبار استعلام لوحة التحكم الحالي...');
    
    const dashboardQuery = await pool.query(`
      SELECT
        COALESCE(SUM(s.total_amount), 0) as today_revenue,
        COUNT(DISTINCT s.id) as today_orders
      FROM pos_system.sales s
      LEFT JOIN pos_system.sale_items si ON s.id = si.sale_id
      LEFT JOIN pos_system.products p ON si.product_id = p.id
      WHERE s.status = 'completed'
        AND DATE(s.created_at) = CURRENT_DATE
    `);
    
    console.log('📊 نتيجة استعلام لوحة التحكم:');
    console.log(`💰 المبيعات: ${parseFloat(dashboardQuery.rows[0].today_revenue)} دج`);
    console.log(`📋 الطلبات: ${dashboardQuery.rows[0].today_orders}`);
    console.log('---');
    
    // استعلام مبسط للمقارنة
    const simpleQuery = await pool.query(`
      SELECT
        COUNT(*) as orders_count,
        SUM(total_amount) as total_revenue
      FROM pos_system.sales
      WHERE status = 'completed'
        AND DATE(created_at) = CURRENT_DATE
    `);
    
    console.log('📊 استعلام مبسط للمقارنة:');
    console.log(`💰 المبيعات: ${parseFloat(simpleQuery.rows[0].total_revenue || 0)} دج`);
    console.log(`📋 الطلبات: ${simpleQuery.rows[0].orders_count}`);
    
  } catch (error) {
    console.error('❌ خطأ في تحليل المبيعات:', error);
  } finally {
    process.exit(0);
  }
}

debugSalesStats();
