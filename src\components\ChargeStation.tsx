import React, { useState, useEffect } from 'react';
import {
  Zap,
  Search,
  DollarSign,
  Phone,
  Users,
  RefreshCw,
  XCircle,
  Wallet,
  Loader
} from 'lucide-react';
import { useApp } from '../context/AppContext';
import apiService from '../services/api';

interface Customer {
  id: string;
  name: string;
  phone: string;
  address: string;
  balance: number;
  credit_limit: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

const ChargeStation: React.FC = () => {
  const { showNotification } = useApp();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [chargeAmount, setChargeAmount] = useState<number>(0);
  const [chargeDescription, setChargeDescription] = useState('شحن رصيد');
  const [isCharging, setIsCharging] = useState(false);

  // مبالغ الشحن السريع
  const quickAmounts = [100, 200, 500, 1000, 2000, 5000];

  // تحميل العملاء من API
  const loadCustomers = async () => {
    try {
      setLoading(true);
      const data = await apiService.getCustomers();
      setCustomers(data);
    } catch (error) {
      console.error('خطأ في تحميل العملاء:', error);
      showNotification('خطأ في تحميل العملاء', 'error');
    } finally {
      setLoading(false);
    }
  };

  // شحن رصيد العميل مع التسديد التلقائي للديون 🧠✨
  const handleChargeBalance = async () => {
    if (!selectedCustomer || chargeAmount <= 0) {
      showNotification('يرجى اختيار عميل وإدخال مبلغ صحيح', 'error');
      return;
    }

    try {
      setIsCharging(true);
      const response = await apiService.updateCustomerBalance(selectedCustomer.id, chargeAmount, chargeDescription);

      console.log('🎯 نتيجة الشحن الذكي:', response);

      // تحديث البيانات المحلية
      setCustomers(prev => prev.map(customer =>
        customer.id === selectedCustomer.id ? {
          ...customer,
          balance: response.customer?.balance || (parseFloat(customer.balance?.toString()) || 0) + chargeAmount
        } : customer
      ));

      // تحديث العميل المحدد
      setSelectedCustomer(prev => prev ? {
        ...prev,
        balance: response.customer?.balance || (parseFloat(prev.balance?.toString()) || 0) + chargeAmount
      } : null);

      // عرض رسالة ذكية حسب النتيجة
      if (response.charge_summary?.debt_auto_paid > 0) {
        showNotification(
          `🧠 ${response.smart_message || `تم شحن ${chargeAmount} دج وتسديد ${response.charge_summary.debt_auto_paid} دج من الديون تلقائياً`}`,
          'success'
        );
      } else {
        showNotification(`💰 تم شحن ${chargeAmount} دج بنجاح لـ ${selectedCustomer.name}`, 'success');
      }

      setChargeAmount(0);
      setChargeDescription('شحن رصيد');

      // إعادة تحميل البيانات لضمان التحديث
      setTimeout(() => loadCustomers(), 500);

    } catch (error) {
      console.error('❌ خطأ في شحن الرصيد:', error);
      showNotification('فشل في شحن الرصيد', 'error');
    } finally {
      setIsCharging(false);
    }
  };

  // فلترة العملاء
  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    customer.phone.includes(searchTerm)
  );

  const formatCurrency = (amount: number | string) => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return `${(numAmount || 0).toFixed(2)} دج`;
  };

  useEffect(() => {
    loadCustomers();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="bg-gradient-to-r from-blue-500 to-cyan-600 p-3 rounded-2xl shadow-lg animate-pulse">
              <Zap className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">⚡ محطة شحن الأرصدة</h1>
              <p className="text-gray-300">شحن أرصدة العملاء بسهولة وسرعة</p>
            </div>
          </div>

          <button
            onClick={loadCustomers}
            disabled={loading}
            className="bg-white/10 backdrop-blur-lg border border-white/20 hover:border-white/40 px-4 py-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse text-white disabled:opacity-50"
          >
            <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
            <span>تحديث</span>
          </button>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">إجمالي العملاء</p>
                <p className="text-3xl font-bold text-white">{customers.length}</p>
              </div>
              <div className="bg-blue-500/20 p-3 rounded-xl">
                <Users className="w-6 h-6 text-blue-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">العملاء النشطون</p>
                <p className="text-3xl font-bold text-green-400">
                  {customers.filter(c => (parseFloat(c.balance?.toString()) || 0) > 0).length}
                </p>
              </div>
              <div className="bg-green-500/20 p-3 rounded-xl">
                <Wallet className="w-6 h-6 text-green-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">إجمالي الأرصدة</p>
                <p className="text-3xl font-bold text-cyan-400">
                  {formatCurrency(customers.reduce((sum, c) => sum + (parseFloat(c.balance?.toString()) || 0), 0))}
                </p>
              </div>
              <div className="bg-cyan-500/20 p-3 rounded-xl">
                <DollarSign className="w-6 h-6 text-cyan-400" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* قسم البحث عن العملاء */}
        <div>
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 p-6">
            <h3 className="text-xl font-bold text-white flex items-center space-x-2 space-x-reverse mb-4">
              <Search className="w-5 h-5" />
              <span>اختيار العميل</span>
            </h3>

            <div className="relative mb-4">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="البحث بالاسم أو رقم الهاتف..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-white/10 border border-white/20 rounded-xl px-12 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 transition-all duration-300"
              />
            </div>

            <div className="max-h-96 overflow-y-auto space-y-2">
              {loading ? (
                <div className="text-center py-8">
                  <Loader className="w-8 h-8 text-blue-400 mx-auto mb-4 animate-spin" />
                  <p className="text-gray-400">جاري تحميل العملاء...</p>
                </div>
              ) : filteredCustomers.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-400 mb-2">
                    {customers.length === 0 ? 'لا توجد عملاء مسجلين' : 'لا توجد نتائج للبحث'}
                  </p>
                </div>
              ) : (
                filteredCustomers.map((customer) => (
                  <div
                    key={customer.id}
                    onClick={() => setSelectedCustomer(customer)}
                    className={`p-4 rounded-xl border cursor-pointer transition-all duration-300 hover:scale-105 ${
                      selectedCustomer?.id === customer.id
                        ? 'border-blue-400 bg-blue-500/20'
                        : 'border-white/20 bg-white/5 hover:border-white/40'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <div className="bg-gradient-to-r from-blue-500 to-cyan-600 w-10 h-10 rounded-full flex items-center justify-center">
                          <span className="text-white font-bold text-sm">
                            {customer.name.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <p className="text-white font-semibold">{customer.name}</p>
                          <p className="text-gray-400 text-sm flex items-center space-x-1 space-x-reverse">
                            <Phone className="w-3 h-3" />
                            <span>{customer.phone}</span>
                          </p>
                        </div>
                      </div>
                      <div className="text-left">
                        <p className={`font-bold ${(parseFloat(customer.balance?.toString()) || 0) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                          {formatCurrency(customer.balance)}
                        </p>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>

        {/* قسم الشحن */}
        <div>
          {selectedCustomer ? (
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-white flex items-center space-x-2 space-x-reverse">
                  <Zap className="w-5 h-5 text-blue-400" />
                  <span>شحن الرصيد</span>
                </h3>
                <button
                  onClick={() => setSelectedCustomer(null)}
                  className="text-gray-400 hover:text-white transition-colors duration-300"
                >
                  <XCircle className="w-6 h-6" />
                </button>
              </div>

              {/* معلومات العميل */}
              <div className="bg-white/5 rounded-xl p-4 mb-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 space-x-reverse">
                    <div className="bg-gradient-to-r from-blue-500 to-cyan-600 w-12 h-12 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold">
                        {selectedCustomer.name.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <p className="text-white font-bold">{selectedCustomer.name}</p>
                      <p className="text-gray-400 text-sm">{selectedCustomer.phone}</p>
                    </div>
                  </div>
                  <div className="text-left">
                    <p className="text-gray-300 text-sm">الرصيد الحالي</p>
                    <p className={`text-2xl font-bold ${(parseFloat(selectedCustomer.balance?.toString()) || 0) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {formatCurrency(selectedCustomer.balance)}
                    </p>
                  </div>
                </div>
              </div>

              {/* المبالغ السريعة */}
              <div className="mb-6">
                <label className="block text-gray-300 text-sm font-semibold mb-3">
                  مبالغ سريعة
                </label>
                <div className="grid grid-cols-3 gap-3">
                  {quickAmounts.map((amount) => (
                    <button
                      key={amount}
                      onClick={() => setChargeAmount(amount)}
                      className={`p-3 rounded-xl border transition-all duration-300 hover:scale-105 ${
                        chargeAmount === amount
                          ? 'border-blue-400 bg-blue-500/20 text-blue-400'
                          : 'border-white/20 bg-white/5 text-white hover:border-blue-400/50'
                      }`}
                    >
                      {formatCurrency(amount)}
                    </button>
                  ))}
                </div>
              </div>

              {/* مبلغ مخصص */}
              <div className="mb-6">
                <label className="block text-gray-300 text-sm font-semibold mb-2">
                  المبلغ (دج)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={chargeAmount || ''}
                  onChange={(e) => setChargeAmount(parseFloat(e.target.value) || 0)}
                  className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 transition-all duration-300"
                  placeholder="أدخل المبلغ"
                />
              </div>

              {/* الوصف */}
              <div className="mb-6">
                <label className="block text-gray-300 text-sm font-semibold mb-2">
                  الوصف
                </label>
                <input
                  type="text"
                  value={chargeDescription}
                  onChange={(e) => setChargeDescription(e.target.value)}
                  className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-blue-400 transition-all duration-300"
                  placeholder="وصف العملية"
                />
              </div>

              {/* معاينة النتيجة */}
              {chargeAmount > 0 && (
                <div className="bg-green-500/10 border border-green-500/20 rounded-xl p-4 mb-6">
                  <div className="flex items-center justify-between">
                    <span className="text-green-300">الرصيد بعد الشحن:</span>
                    <span className="font-bold text-lg text-green-400">
                      {formatCurrency((parseFloat(selectedCustomer.balance?.toString()) || 0) + chargeAmount)}
                    </span>
                  </div>
                </div>
              )}

              {/* زر الشحن */}
              <button
                onClick={handleChargeBalance}
                disabled={chargeAmount <= 0 || isCharging}
                className="w-full bg-gradient-to-r from-blue-500 to-cyan-400 hover:scale-105 transform transition-all duration-300 py-4 rounded-xl font-bold text-white shadow-2xl disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2 space-x-reverse"
              >
                {isCharging ? (
                  <>
                    <Loader className="w-6 h-6 animate-spin" />
                    <span>جاري الشحن...</span>
                  </>
                ) : (
                  <>
                    <Zap className="w-6 h-6" />
                    <span>
                      شحن الرصيد
                      {chargeAmount > 0 && ` - ${formatCurrency(chargeAmount)}`}
                    </span>
                  </>
                )}
              </button>
            </div>
          ) : (
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 p-12 text-center">
              <Zap className="w-24 h-24 text-gray-400 mx-auto mb-6" />
              <h3 className="text-2xl font-bold text-white mb-4">اختر عميل للبدء</h3>
              <p className="text-gray-400">ابحث عن العميل من القائمة على اليسار لبدء عملية الشحن</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChargeStation;