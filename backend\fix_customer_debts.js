const { Pool } = require('pg');
const fs = require('fs');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
});

async function fixCustomerDebts() {
  const client = await pool.connect();
  
  try {
    console.log('🔧 بدء إصلاح جدول customer_debts...');
    
    // تعيين schema
    await client.query(`SET search_path TO ${process.env.DB_SCHEMA}, public`);
    
    // التحقق من وجود العمود وإضافته إذا لم يكن موجوداً
    const checkColumn = await client.query(`
      SELECT 1 FROM information_schema.columns 
      WHERE table_schema = $1 
      AND table_name = 'customer_debts' 
      AND column_name = 'status'
    `, [process.env.DB_SCHEMA]);
    
    if (checkColumn.rows.length === 0) {
      console.log('📝 إضافة عمود status...');
      await client.query(`
        ALTER TABLE customer_debts 
        ADD COLUMN status VARCHAR(20) DEFAULT 'pending' 
        CHECK (status IN ('pending', 'partial', 'paid'))
      `);
      console.log('✅ تم إضافة عمود status');
    } else {
      console.log('✅ عمود status موجود بالفعل');
    }
    
    // إضافة فهرس للعمود الجديد
    try {
      await client.query(`
        CREATE INDEX IF NOT EXISTS idx_customer_debts_status 
        ON customer_debts(status)
      `);
      console.log('✅ تم إنشاء فهرس idx_customer_debts_status');
    } catch (err) {
      console.log('✅ فهرس idx_customer_debts_status موجود بالفعل');
    }
    
    // تحديث الحالة للديون الموجودة
    const updateResult = await client.query(`
      UPDATE customer_debts 
      SET status = CASE 
        WHEN paid_amount >= amount THEN 'paid'
        WHEN paid_amount > 0 THEN 'partial'
        ELSE 'pending'
      END
      WHERE status IS NULL OR status = ''
    `);
    
    console.log(`✅ تم تحديث ${updateResult.rowCount} سجل من الديون`);
    console.log('🎉 تم إصلاح جدول customer_debts بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في الإصلاح:', error.message);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

fixCustomerDebts()
  .then(() => {
    console.log('✅ انتهى الإصلاح بنجاح');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ فشل الإصلاح:', error.message);
    process.exit(1);
  });
