const pool = require('./database');

async function testSuppliers() {
  try {
    console.log('🔍 فحص جدول الموردين...');
    
    // اختبار الاتصال الأساسي
    const client = await pool.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
    
    // التحقق من وجود جدول الموردين
    const tableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'pos_system' 
        AND table_name = 'suppliers'
      );
    `);
    
    console.log('📋 جدول الموردين موجود:', tableCheck.rows[0].exists);
    
    if (tableCheck.rows[0].exists) {
      // الحصول على هيكل الجدول
      const structure = await client.query(`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_schema = 'pos_system' 
        AND table_name = 'suppliers'
        ORDER BY ordinal_position;
      `);
      
      console.log('📊 هيكل الجدول:');
      structure.rows.forEach(col => {
        console.log(`  - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
      });
      
      // عد الموردين
      const count = await client.query('SELECT COUNT(*) FROM pos_system.suppliers');
      console.log('👥 عدد الموردين:', count.rows[0].count);
      
      // التحقق من وجود جدول المشتريات
      const purchasesCheck = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'pos_system' 
          AND table_name = 'purchases'
        );
      `);
      
      console.log('📦 جدول المشتريات موجود:', purchasesCheck.rows[0].exists);
      
      // اختبار الاستعلام الفعلي من API
      try {
        const testQuery = await client.query(`
          SELECT
            s.id,
            s.name,
            s.phone,
            s.address,
            s.balance,
            s.is_active,
            s.created_at,
            s.updated_at
          FROM pos_system.suppliers s
          WHERE s.is_active = true
          ORDER BY s.name
          LIMIT 5
        `);
        
        console.log('✅ الاستعلام البسيط نجح، تم إرجاع', testQuery.rows.length, 'مورد');
        
        if (testQuery.rows.length > 0) {
          console.log('📋 أول مورد:');
          console.log(testQuery.rows[0]);
        }
        
      } catch (queryError) {
        console.error('❌ خطأ في الاستعلام البسيط:', queryError.message);
      }
      
    } else {
      console.log('❌ جدول الموردين غير موجود!');
      
      // التحقق من الجداول الموجودة
      const tables = await client.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'pos_system'
        ORDER BY table_name;
      `);
      
      console.log('📋 الجداول الموجودة في pos_system:');
      tables.rows.forEach(table => {
        console.log(`  - ${table.table_name}`);
      });
    }
    
    client.release();
    
  } catch (error) {
    console.error('❌ خطأ:', error.message);
    console.error('❌ التفاصيل:', error.stack);
  } finally {
    process.exit();
  }
}

testSuppliers();
