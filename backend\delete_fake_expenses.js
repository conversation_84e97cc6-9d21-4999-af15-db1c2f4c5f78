const pool = require('./database');

async function deleteFakeExpenses() {
  try {
    console.log('🧹 حذف البيانات الوهمية من جدول المصروفات...');
    
    // حذف جميع البيانات الوهمية
    const result = await pool.query('DELETE FROM pos_system.expenses');
    console.log(`✅ تم حذف ${result.rowCount} مصروف وهمي`);
    
    // التحقق من النتيجة
    const countResult = await pool.query('SELECT COUNT(*) as count FROM pos_system.expenses');
    const count = parseInt(countResult.rows[0].count);
    
    if (count === 0) {
      console.log('✅ جدول المصروفات فارغ الآن');
      console.log('💡 سيتم إضافة المصروفات تلقائياً في جدول financial_transactions عند:');
      console.log('   - دفع فواتير المشتريات');
      console.log('   - دفع ديون الموردين');
      console.log('   - إضافة مصروفات مباشرة');
    } else {
      console.log(`⚠️ لا يزال هناك ${count} مصروف في الجدول`);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ خطأ في حذف البيانات:', error.message);
    process.exit(1);
  }
}

deleteFakeExpenses();
