const express = require('express');
const router = express.Router();
const pool = require('../database');

// 📋 جلب جميع الموردين مع الإحصائيات الحقيقية
router.get('/', async (req, res) => {
  try {
    console.log('📋 طلب جلب الموردين مع الإحصائيات...');

    const result = await pool.query(`
      SELECT
        s.id,
        s.name,
        s.phone,
        s.address,
        s.balance,
        s.is_active,
        s.created_at,
        s.updated_at,

        -- 📊 إحصائيات حقيقية لكل مورد
        COALESCE(COUNT(DISTINCT p.id), 0) as total_invoices,
        COALESCE(SUM(p.total_amount), 0) as total_purchases,
        COALESCE(SUM(p.amount_paid), 0) as total_paid,
        COALESCE(SUM(p.total_amount - p.amount_paid), 0) as total_debt,

        -- 📈 إحصائيات إضافية
        COALESCE(AVG(p.total_amount), 0) as average_invoice_amount,
        MAX(p.created_at) as last_purchase_date,

        -- 🔍 تفاصيل الديون
        COALESCE(COUNT(DISTINCT CASE WHEN p.amount_paid < p.total_amount THEN p.id END), 0) as unpaid_invoices_count,
        COALESCE(SUM(p.total_amount - p.amount_paid), 0) as outstanding_debt

      FROM pos_system.suppliers s
      LEFT JOIN pos_system.purchase_invoices p ON s.id = p.supplier_id
      WHERE s.is_active = true
      GROUP BY s.id, s.name, s.phone, s.address, s.balance, s.is_active, s.created_at, s.updated_at
      ORDER BY s.name
    `);

    console.log(`✅ تم جلب ${result.rows.length} مورد مع الإحصائيات`);

    // 🎯 تحويل البيانات لتتطابق مع توقعات الفرونت إند
    const suppliersWithStats = result.rows.map(supplier => ({
      id: supplier.id,
      name: supplier.name,
      company: '', // سيتم إضافته لاحقاً
      phone: supplier.phone || '',
      address: supplier.address || '',
      email: '', // سيتم إضافته لاحقاً
      notes: '', // سيتم إضافته لاحقاً
      balance: parseFloat(supplier.balance) || 0,
      is_active: supplier.is_active,
      created_at: supplier.created_at,
      updated_at: supplier.updated_at,

      // إحصائيات محسوبة
      total_invoices: parseInt(supplier.total_invoices) || 0,
      total_purchases: parseFloat(supplier.total_purchases) || 0,
      total_paid: parseFloat(supplier.total_paid) || 0,
      total_debt: parseFloat(supplier.total_debt) || 0,
      average_invoice_amount: parseFloat(supplier.average_invoice_amount) || 0,
      last_purchase_date: supplier.last_purchase_date,
      unpaid_invoices_count: parseInt(supplier.unpaid_invoices_count) || 0,
      outstanding_debt: parseFloat(supplier.outstanding_debt) || 0
    }));

    res.json(suppliersWithStats);
  } catch (error) {
    console.error('❌ خطأ في جلب الموردين:', error);
    res.status(500).json({ error: 'خطأ في جلب الموردين' });
  }
});

// 📈 جلب الإحصائيات العامة للموردين (يجب أن يكون قبل /:id)
router.get('/stats/overview', async (req, res) => {
  try {
    console.log('📈 طلب الإحصائيات العامة للموردين...');

    const result = await pool.query(`
      SELECT
        -- 🏢 إحصائيات الموردين
        COUNT(DISTINCT s.id) as total_suppliers,
        COUNT(DISTINCT CASE WHEN s.is_active = true THEN s.id END) as active_suppliers,

        -- 📋 إحصائيات الفواتير الحقيقية
        COALESCE(COUNT(DISTINCT p.id), 0) as total_invoices,
        COALESCE(SUM(p.total_amount), 0) as total_purchases_amount,
        COALESCE(SUM(p.amount_paid), 0) as total_paid_amount,
        COALESCE(SUM(p.total_amount - p.amount_paid), 0) as total_outstanding_debt,

        -- 📊 متوسطات
        COALESCE(AVG(p.total_amount), 0) as average_invoice_amount,
        CASE
          WHEN COUNT(DISTINCT s.id) > 0 THEN COALESCE(SUM(p.total_amount), 0) / COUNT(DISTINCT s.id)
          ELSE 0
        END as average_purchases_per_supplier,
        CASE
          WHEN COUNT(DISTINCT s.id) > 0 THEN COALESCE(SUM(p.total_amount - p.amount_paid), 0) / COUNT(DISTINCT s.id)
          ELSE 0
        END as average_debt_per_supplier,

        -- 🔍 تفاصيل الديون
        COALESCE(COUNT(DISTINCT CASE WHEN p.amount_paid < p.total_amount THEN p.id END), 0) as unpaid_invoices_count,
        COALESCE(COUNT(DISTINCT CASE WHEN p.amount_paid >= p.total_amount THEN p.id END), 0) as paid_invoices_count,
        COALESCE(COUNT(DISTINCT CASE WHEN p.amount_paid > 0 AND p.amount_paid < p.total_amount THEN p.id END), 0) as partially_paid_invoices_count,

        -- 📅 إحصائيات زمنية
        COALESCE(COUNT(DISTINCT CASE WHEN p.created_at >= CURRENT_DATE - INTERVAL '30 days' THEN p.id END), 0) as invoices_last_30_days,
        COALESCE(SUM(CASE WHEN p.created_at >= CURRENT_DATE - INTERVAL '30 days' THEN p.total_amount ELSE 0 END), 0) as purchases_last_30_days,
        COALESCE(COUNT(DISTINCT CASE WHEN p.created_at >= CURRENT_DATE - INTERVAL '7 days' THEN p.id END), 0) as invoices_last_7_days,
        COALESCE(SUM(CASE WHEN p.created_at >= CURRENT_DATE - INTERVAL '7 days' THEN p.total_amount ELSE 0 END), 0) as purchases_last_7_days,

        -- 🎯 أفضل الموردين
        (SELECT s2.name FROM pos_system.suppliers s2
         LEFT JOIN pos_system.purchase_invoices p2 ON s2.id = p2.supplier_id
         GROUP BY s2.id, s2.name
         ORDER BY COALESCE(SUM(p2.total_amount), 0) DESC
         LIMIT 1) as top_supplier_by_purchases,
        (SELECT COALESCE(SUM(p2.total_amount), 0) FROM pos_system.suppliers s2
         LEFT JOIN pos_system.purchase_invoices p2 ON s2.id = p2.supplier_id
         GROUP BY s2.id
         ORDER BY COALESCE(SUM(p2.total_amount), 0) DESC
         LIMIT 1) as top_supplier_purchases_amount

      FROM pos_system.suppliers s
      LEFT JOIN pos_system.purchase_invoices p ON s.id = p.supplier_id
      WHERE s.is_active = true
    `);

    const stats = result.rows[0];

    // 🎯 تحويل البيانات وحساب النسب المئوية
    const formattedStats = {
      // إحصائيات الموردين
      total_suppliers: parseInt(stats.total_suppliers) || 0,
      active_suppliers: parseInt(stats.active_suppliers) || 0,

      // إحصائيات الفواتير
      total_invoices: parseInt(stats.total_invoices) || 0,
      total_purchases_amount: parseFloat(stats.total_purchases_amount) || 0,
      total_paid_amount: parseFloat(stats.total_paid_amount) || 0,
      total_outstanding_debt: parseFloat(stats.total_outstanding_debt) || 0,

      // متوسطات
      average_invoice_amount: parseFloat(stats.average_invoice_amount) || 0,
      average_purchases_per_supplier: parseFloat(stats.average_purchases_per_supplier) || 0,
      average_debt_per_supplier: parseFloat(stats.average_debt_per_supplier) || 0,

      // تفاصيل الديون
      paid_invoices_count: parseInt(stats.paid_invoices_count) || 0,
      unpaid_invoices_count: parseInt(stats.unpaid_invoices_count) || 0,
      partially_paid_invoices_count: parseInt(stats.partially_paid_invoices_count) || 0,

      // نسب مئوية
      payment_completion_rate: stats.total_invoices > 0 ?
        ((parseFloat(stats.paid_invoices_count) / parseInt(stats.total_invoices)) * 100).toFixed(2) : 0,
      debt_to_purchases_ratio: stats.total_purchases_amount > 0 ?
        ((parseFloat(stats.total_outstanding_debt) / parseFloat(stats.total_purchases_amount)) * 100).toFixed(2) : 0,

      // إحصائيات زمنية
      invoices_last_30_days: parseInt(stats.invoices_last_30_days) || 0,
      purchases_last_30_days: parseFloat(stats.purchases_last_30_days) || 0,
      invoices_last_7_days: parseInt(stats.invoices_last_7_days) || 0,
      purchases_last_7_days: parseFloat(stats.purchases_last_7_days) || 0,

      // أفضل الموردين
      top_supplier_by_purchases: stats.top_supplier_by_purchases || 'لا يوجد',
      top_supplier_purchases_amount: parseFloat(stats.top_supplier_purchases_amount) || 0
    };

    console.log('✅ تم حساب الإحصائيات العامة للموردين');
    res.json(formattedStats);
  } catch (error) {
    console.error('❌ خطأ في حساب الإحصائيات:', error);
    res.status(500).json({ error: 'خطأ في حساب الإحصائيات' });
  }
});

// 📊 جلب تفاصيل مورد واحد مع إحصائياته المفصلة
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📊 طلب تفاصيل المورد: ${id}`);

    const result = await pool.query(`
      SELECT
        s.id,
        s.name,
        s.phone,
        s.address,
        s.balance,
        s.is_active,
        s.created_at,
        s.updated_at,

        -- 📊 إحصائيات الفواتير
        COALESCE(COUNT(DISTINCT pi.id), 0) as total_invoices,
        COALESCE(SUM(pi.total_amount), 0) as total_purchases,
        COALESCE(SUM(pi.amount_paid), 0) as total_paid,
        COALESCE(SUM(pi.total_amount - pi.amount_paid), 0) as total_debt,

        -- 📈 إحصائيات متقدمة
        COALESCE(AVG(pi.total_amount), 0) as average_invoice_amount,
        COALESCE(MIN(pi.total_amount), 0) as min_invoice_amount,
        COALESCE(MAX(pi.total_amount), 0) as max_invoice_amount,
        MIN(pi.created_at) as first_purchase_date,
        MAX(pi.created_at) as last_purchase_date,

        -- 🔍 تفاصيل الديون
        COALESCE(COUNT(DISTINCT CASE WHEN pi.amount_paid >= pi.total_amount THEN pi.id END), 0) as paid_invoices_count,
        COALESCE(COUNT(DISTINCT CASE WHEN pi.amount_paid < pi.total_amount THEN pi.id END), 0) as unpaid_invoices_count,
        COALESCE(SUM(CASE WHEN pi.amount_paid < pi.total_amount THEN pi.total_amount - pi.amount_paid ELSE 0 END), 0) as outstanding_debt,

        -- 📅 إحصائيات زمنية
        COALESCE(COUNT(DISTINCT CASE WHEN pi.created_at >= CURRENT_DATE - INTERVAL '30 days' THEN pi.id END), 0) as invoices_last_30_days,
        COALESCE(SUM(CASE WHEN pi.created_at >= CURRENT_DATE - INTERVAL '30 days' THEN pi.total_amount ELSE 0 END), 0) as purchases_last_30_days

      FROM pos_system.suppliers s
      LEFT JOIN pos_system.purchase_invoices pi ON s.id = pi.supplier_id
      WHERE s.id = $1 AND s.is_active = true
      GROUP BY s.id, s.name, s.phone, s.address, s.balance, s.is_active, s.created_at, s.updated_at
    `, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'المورد غير موجود' });
    }

    const supplier = result.rows[0];

    // 🎯 تحويل البيانات لتتطابق مع توقعات الفرونت إند
    const supplierWithStats = {
      id: supplier.id,
      name: supplier.name,
      phone: supplier.phone || '',
      address: supplier.address || '',
      balance: parseFloat(supplier.balance) || 0,
      is_active: supplier.is_active,
      created_at: supplier.created_at,
      updated_at: supplier.updated_at,

      // إحصائيات أساسية
      total_invoices: parseInt(supplier.total_invoices) || 0,
      total_purchases: parseFloat(supplier.total_purchases) || 0,
      total_paid: parseFloat(supplier.total_paid) || 0,
      total_debt: parseFloat(supplier.total_debt) || 0,

      // إحصائيات متقدمة
      average_invoice_amount: parseFloat(supplier.average_invoice_amount) || 0,
      min_invoice_amount: parseFloat(supplier.min_invoice_amount) || 0,
      max_invoice_amount: parseFloat(supplier.max_invoice_amount) || 0,
      first_purchase_date: supplier.first_purchase_date,
      last_purchase_date: supplier.last_purchase_date,

      // تفاصيل الديون
      paid_invoices_count: parseInt(supplier.paid_invoices_count) || 0,
      unpaid_invoices_count: parseInt(supplier.unpaid_invoices_count) || 0,
      outstanding_debt: parseFloat(supplier.outstanding_debt) || 0,

      // إحصائيات زمنية
      invoices_last_30_days: parseInt(supplier.invoices_last_30_days) || 0,
      purchases_last_30_days: parseFloat(supplier.purchases_last_30_days) || 0
    };

    console.log(`✅ تم جلب تفاصيل المورد: ${supplier.name}`);
    res.json(supplierWithStats);
  } catch (error) {
    console.error('❌ خطأ في جلب تفاصيل المورد:', error);
    res.status(500).json({ error: 'خطأ في جلب تفاصيل المورد' });
  }
});





// إضافة مورد جديد
router.post('/', async (req, res) => {
  try {
    const { name, phone, address, balance = 0 } = req.body;

    console.log('📝 إضافة مورد جديد:', { name, phone, address });

    const result = await pool.query(`
      INSERT INTO pos_system.suppliers (name, phone, address, balance)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `, [name, phone, address || null, balance]);

    console.log('✅ تم إضافة المورد بنجاح:', result.rows[0]);
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('❌ خطأ في إضافة المورد:', error);
    res.status(500).json({ error: 'خطأ في إضافة المورد', details: error.message });
  }
});

// تعديل مورد
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, phone, address } = req.body;

    console.log('📝 تعديل المورد:', { id, name, phone, address });

    const result = await pool.query(`
      UPDATE pos_system.suppliers
      SET name = $1, phone = $2, address = $3, updated_at = CURRENT_TIMESTAMP
      WHERE id = $4
      RETURNING *
    `, [name, phone, address || null, id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'المورد غير موجود' });
    }

    console.log('✅ تم تعديل المورد بنجاح:', result.rows[0]);
    res.json(result.rows[0]);
  } catch (error) {
    console.error('❌ خطأ في تعديل المورد:', error);
    res.status(500).json({ error: 'خطأ في تعديل المورد', details: error.message });
  }
});

// حذف مورد (حذف منطقي)
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(
      'UPDATE pos_system.suppliers SET is_active = false WHERE id = $1 RETURNING *',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'المورد غير موجود' });
    }

    res.json({ message: 'تم حذف المورد بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف المورد:', error);
    res.status(500).json({ error: 'خطأ في حذف المورد' });
  }
});

// 🔧 تصحيح أرصدة الموردين بناءً على الفواتير الفعلية
router.post('/fix-balances', async (req, res) => {
  try {
    console.log('🔧 بدء تصحيح أرصدة الموردين...');

    // أولاً: جلب جميع الموردين النشطين
    const suppliersResult = await pool.query(`
      SELECT id, name FROM pos_system.suppliers WHERE is_active = true
    `);

    console.log(`📊 تم العثور على ${suppliersResult.rows.length} مورد نشط`);

    let updatedCount = 0;
    const results = [];

    // تحديث كل مورد على حدة
    for (const supplier of suppliersResult.rows) {
      try {
        // حساب إجمالي الديون لهذا المورد
        const debtResult = await pool.query(`
          SELECT
            COALESCE(SUM(total_amount - amount_paid), 0) as total_debt,
            COUNT(*) as invoice_count,
            COALESCE(SUM(total_amount), 0) as total_purchases,
            COALESCE(SUM(amount_paid), 0) as total_paid
          FROM pos_system.purchase_invoices
          WHERE supplier_id = $1
        `, [supplier.id]);

        const debt = debtResult.rows[0];
        const totalDebt = parseFloat(debt.total_debt) || 0;

        // تحديث رصيد المورد
        await pool.query(`
          UPDATE pos_system.suppliers
          SET balance = $1, updated_at = CURRENT_TIMESTAMP
          WHERE id = $2
        `, [totalDebt, supplier.id]);

        updatedCount++;

        results.push({
          supplier_name: supplier.name,
          updated_balance: totalDebt,
          total_invoices: parseInt(debt.invoice_count) || 0,
          total_purchases: parseFloat(debt.total_purchases) || 0,
          total_paid: parseFloat(debt.total_paid) || 0,
          status: totalDebt > 0 ? 'مدين له' : totalDebt < 0 ? 'دائن لك' : 'متوازن'
        });

        console.log(`✅ تم تحديث المورد: ${supplier.name} - الرصيد: ${totalDebt} دج`);

      } catch (supplierError) {
        console.error(`❌ خطأ في تحديث المورد ${supplier.name}:`, supplierError.message);
      }
    }

    console.log(`✅ تم تصحيح أرصدة ${updatedCount} مورد`);

    res.json({
      message: `تم تصحيح أرصدة ${updatedCount} مورد بنجاح`,
      suppliers: results,
      updated_count: updatedCount
    });

  } catch (error) {
    console.error('❌ خطأ في تصحيح أرصدة الموردين:', error);
    console.error('❌ تفاصيل الخطأ:', error.stack);
    res.status(500).json({
      error: 'خطأ في تصحيح أرصدة الموردين',
      details: error.message
    });
  }
});



module.exports = router;
