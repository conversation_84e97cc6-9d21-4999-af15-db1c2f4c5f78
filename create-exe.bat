@echo off
title Creating EXE File

echo Creating executable file...

REM Clean old files
if exist "dist" rmdir /s /q "dist"

REM Install what we need
call npm install --force
call npm install -g pkg

REM Create a simple Node.js launcher
echo const { spawn } = require('child_process'); > launcher.js
echo const path = require('path'); >> launcher.js
echo const fs = require('fs'); >> launcher.js
echo. >> launcher.js
echo console.log('Starting Kasheer Toosar...'); >> launcher.js
echo. >> launcher.js
echo // Start backend >> launcher.js
echo const backend = spawn('node', ['backend/server.js'], { >> launcher.js
echo   cwd: __dirname, >> launcher.js
echo   stdio: 'inherit' >> launcher.js
echo }); >> launcher.js
echo. >> launcher.js
echo // Wait a bit then open browser >> launcher.js
echo setTimeout(() =^> { >> launcher.js
echo   const { exec } = require('child_process'); >> launcher.js
echo   exec('start http://localhost:5003'); >> launcher.js
echo }, 3000); >> launcher.js
echo. >> launcher.js
echo process.on('SIGINT', () =^> { >> launcher.js
echo   backend.kill(); >> launcher.js
echo   process.exit(); >> launcher.js
echo }); >> launcher.js

REM Create package.json for launcher
echo { > launcher-package.json
echo   "name": "kasheer-launcher", >> launcher-package.json
echo   "version": "1.0.0", >> launcher-package.json
echo   "main": "launcher.js", >> launcher-package.json
echo   "bin": "launcher.js", >> launcher-package.json
echo   "pkg": { >> launcher-package.json
echo     "scripts": ["backend/**/*.js"], >> launcher-package.json
echo     "assets": ["dist/**/*", "backend/**/*"] >> launcher-package.json
echo   } >> launcher-package.json
echo } >> launcher-package.json

REM Build frontend
call npm run build

REM Create EXE
call pkg launcher.js --target node18-win-x64 --output kasheer-toosar.exe

echo.
echo EXE file created: kasheer-toosar.exe
echo You can now distribute this single file!
pause
