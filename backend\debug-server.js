const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 5000;

// Middleware
app.use(cors());
app.use(express.json());

console.log('🚀 بدء تشغيل السيرفر...');

// اختبار الاتصال
app.get('/api/test', (req, res) => {
  console.log('📞 تم استلام طلب اختبار');
  res.json({ 
    message: '✅ Backend API يعمل بنجاح!',
    timestamp: new Date().toISOString()
  });
});

// مسار المدينين المؤقت
app.get('/api/customers/debtors', (req, res) => {
  console.log('📞 تم استلام طلب المدينين');
  
  // إرجاع بيانات تجريبية بناءً على البيانات التي أرسلتها
  const mockDebtors = [
    {
      id: '82431516-ec6b-44a7-904c-52632c1ced43',
      name: 'عميل تجريبي',
      phone: '0123456789',
      email: '',
      address: 'عنوان تجريبي',
      balance: 0,
      credit_limit: 1000,
      created_at: '2025-06-14T13:54:33.437Z',
      updated_at: '2025-06-14T13:54:33.437Z',
      total_debt: 29.75,
      paid_amount: 0,
      remaining_debt: 29.75,
      debt_count: 1
    }
  ];
  
  console.log('✅ إرسال بيانات المدينين:', mockDebtors);
  res.json(mockDebtors);
});

// معالج الأخطاء العام
app.use((error, req, res, next) => {
  console.error('❌ خطأ عام:', error);
  res.status(500).json({ error: 'خطأ داخلي في الخادم' });
});

app.listen(PORT, () => {
  console.log(`🚀 السيرفر التجريبي يعمل على المنفذ ${PORT}`);
  console.log(`🔗 API متاح على: http://localhost:${PORT}/api`);
});

// معالج إغلاق الخادم
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف الخادم...');
  process.exit(0);
});

process.on('uncaughtException', (error) => {
  console.error('❌ خطأ غير متوقع:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ رفض غير معالج:', reason);
  process.exit(1);
});
