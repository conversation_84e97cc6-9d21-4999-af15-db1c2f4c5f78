-- جداول المبيعات والمعاملات المالية
-- Sales and financial transactions tables

-- تعيين المخطط الافتراضي
SET search_path TO pos_system, public;

-- جدول المبيعات الرئيسي
CREATE TABLE pos_system.sales (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sale_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id UUID REFERENCES pos_system.customers(id) ON DELETE SET NULL,

    -- تفاصيل البيع
    subtotal DECIMAL(10,2) NOT NULL CHECK (subtotal >= 0),
    tax_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (tax_amount >= 0),
    discount_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (discount_amount >= 0),
    total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount >= 0),

    -- طريقة الدفع
    payment_method payment_method NOT NULL,
    amount_paid DECIMAL(10,2) NOT NULL CHECK (amount_paid >= 0),
    change_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (change_amount >= 0),

    -- حالة البيع
    status sale_status DEFAULT 'completed',
    notes TEXT,

    -- معلومات إضافية
    cashier_name VARCHAR(255),
    receipt_printed BOOLEAN DEFAULT false,

    -- ديون
    is_debt_sale BOOLEAN DEFAULT false,
    debt_status debt_status DEFAULT 'paid',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول عناصر المبيعات
CREATE TABLE pos_system.sale_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sale_id UUID NOT NULL REFERENCES pos_system.sales(id) ON DELETE CASCADE,
    product_id UUID REFERENCES pos_system.products(id) ON DELETE RESTRICT, -- يمكن أن يكون NULL للمنتجات غير المعروفة

    -- تفاصيل العنصر
    product_name VARCHAR(255) NOT NULL, -- نسخة من اسم المنتج وقت البيع
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(10,2) NOT NULL CHECK (unit_price >= 0),
    discount DECIMAL(10,2) DEFAULT 0.00 CHECK (discount >= 0),
    total_price DECIMAL(10,2) NOT NULL CHECK (total_price >= 0),

    -- معلومات إضافية للمنتجات غير المعروفة
    is_unknown_product BOOLEAN DEFAULT FALSE, -- علامة للمنتجات غير المعروفة
    unknown_product_code VARCHAR(50), -- كود خاص للمنتجات غير المعروفة

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول فواتير المشتريات
CREATE TABLE pos_system.purchase_invoices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    supplier_id UUID REFERENCES pos_system.suppliers(id) ON DELETE SET NULL,

    -- تفاصيل الفاتورة
    subtotal DECIMAL(10,2) NOT NULL CHECK (subtotal >= 0),
    tax_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (tax_amount >= 0),
    discount_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (discount_amount >= 0),
    total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount >= 0),

    -- حالة الدفع
    amount_paid DECIMAL(10,2) DEFAULT 0.00 CHECK (amount_paid >= 0),
    remaining_amount DECIMAL(10,2) GENERATED ALWAYS AS (total_amount - amount_paid) STORED,
    is_paid BOOLEAN GENERATED ALWAYS AS (amount_paid >= total_amount) STORED,

    -- تواريخ
    invoice_date DATE NOT NULL DEFAULT CURRENT_DATE,
    due_date DATE,

    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول عناصر فواتير المشتريات
CREATE TABLE pos_system.purchase_invoice_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID NOT NULL REFERENCES pos_system.purchase_invoices(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES pos_system.products(id) ON DELETE RESTRICT,

    -- تفاصيل العنصر
    product_name VARCHAR(255) NOT NULL,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_cost DECIMAL(10,2) NOT NULL CHECK (unit_cost >= 0),
    discount DECIMAL(10,2) DEFAULT 0.00 CHECK (discount >= 0),
    total_cost DECIMAL(10,2) NOT NULL CHECK (total_cost >= 0),

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المعاملات المالية العامة
CREATE TABLE pos_system.financial_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_number VARCHAR(50) UNIQUE NOT NULL,

    -- نوع المعاملة
    type transaction_type NOT NULL,
    category VARCHAR(100) NOT NULL, -- فئة المعاملة (مبيعات، مشتريات، مصروفات، إيرادات أخرى)

    -- تفاصيل المعاملة
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    description TEXT NOT NULL,
    reference_id UUID, -- مرجع للمعاملة الأصلية (sale_id, purchase_id, etc.)
    reference_type VARCHAR(50), -- نوع المرجع (sale, purchase, expense, income)

    -- معلومات إضافية
    payment_method payment_method,
    notes TEXT,

    transaction_date DATE NOT NULL DEFAULT CURRENT_DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول حركة المخزون
CREATE TABLE pos_system.inventory_movements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES pos_system.products(id) ON DELETE CASCADE,

    -- تفاصيل الحركة
    movement_type VARCHAR(20) NOT NULL CHECK (movement_type IN ('in', 'out', 'adjustment')),
    quantity INTEGER NOT NULL,
    previous_stock INTEGER NOT NULL,
    new_stock INTEGER NOT NULL,

    -- سبب الحركة
    reason VARCHAR(100) NOT NULL, -- sale, purchase, adjustment, return, damage
    reference_id UUID, -- مرجع للعملية المسببة للحركة
    reference_type VARCHAR(50), -- نوع المرجع

    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول ديون العملاء
CREATE TABLE pos_system.customer_debts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES pos_system.customers(id) ON DELETE CASCADE,
    sale_id UUID NOT NULL REFERENCES pos_system.sales(id) ON DELETE CASCADE,

    -- تفاصيل الدين
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    paid_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (paid_amount >= 0),
    remaining_amount DECIMAL(10,2) GENERATED ALWAYS AS (amount - paid_amount) STORED,
    status debt_status DEFAULT 'pending',

    -- تواريخ
    debt_date DATE NOT NULL DEFAULT CURRENT_DATE,
    due_date DATE,

    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول مدفوعات ديون العملاء
CREATE TABLE pos_system.customer_debt_payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    debt_id UUID NOT NULL REFERENCES pos_system.customer_debts(id) ON DELETE CASCADE,

    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    payment_method payment_method NOT NULL DEFAULT 'cash',
    payment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    notes TEXT,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء فهارس للأداء
CREATE INDEX idx_sales_customer ON pos_system.sales(customer_id);
CREATE INDEX idx_sales_date ON pos_system.sales(created_at);
CREATE INDEX idx_sales_status ON pos_system.sales(status);
CREATE INDEX idx_sales_number ON pos_system.sales(sale_number);

CREATE INDEX idx_sale_items_sale ON pos_system.sale_items(sale_id);
CREATE INDEX idx_sale_items_product ON pos_system.sale_items(product_id);

CREATE INDEX idx_purchase_invoices_supplier ON pos_system.purchase_invoices(supplier_id);
CREATE INDEX idx_purchase_invoices_date ON pos_system.purchase_invoices(invoice_date);
CREATE INDEX idx_purchase_invoices_paid ON pos_system.purchase_invoices(is_paid);

CREATE INDEX idx_purchase_items_invoice ON pos_system.purchase_invoice_items(invoice_id);
CREATE INDEX idx_purchase_items_product ON pos_system.purchase_invoice_items(product_id);

CREATE INDEX idx_financial_transactions_type ON pos_system.financial_transactions(type);
CREATE INDEX idx_financial_transactions_date ON pos_system.financial_transactions(transaction_date);
CREATE INDEX idx_financial_transactions_reference ON pos_system.financial_transactions(reference_id, reference_type);

CREATE INDEX idx_inventory_movements_product ON pos_system.inventory_movements(product_id);
CREATE INDEX idx_inventory_movements_date ON pos_system.inventory_movements(created_at);
CREATE INDEX idx_inventory_movements_type ON pos_system.inventory_movements(movement_type);

CREATE INDEX idx_customer_debts_customer ON pos_system.customer_debts(customer_id);
CREATE INDEX idx_customer_debts_sale ON pos_system.customer_debts(sale_id);
CREATE INDEX idx_customer_debts_status ON pos_system.customer_debts(status);

CREATE INDEX idx_customer_debt_payments_debt ON pos_system.customer_debt_payments(debt_id);

-- تعليقات على الجداول
COMMENT ON TABLE pos_system.sales IS 'جدول المبيعات الرئيسي';
COMMENT ON TABLE pos_system.sale_items IS 'عناصر المبيعات التفصيلية';
COMMENT ON TABLE pos_system.purchase_invoices IS 'فواتير المشتريات';
COMMENT ON TABLE pos_system.purchase_invoice_items IS 'عناصر فواتير المشتريات';
COMMENT ON TABLE pos_system.financial_transactions IS 'المعاملات المالية العامة';
COMMENT ON TABLE pos_system.inventory_movements IS 'حركة المخزون';
COMMENT ON TABLE pos_system.customer_debts IS 'ديون العملاء';
COMMENT ON TABLE pos_system.customer_debt_payments IS 'مدفوعات ديون العملاء';

-- إنشاء الدوال والمشغلات
CREATE OR REPLACE FUNCTION pos_system.update_debt_status()
RETURNS TRIGGER AS $$
BEGIN
    -- تحديث حالة الدين بناءً على المبلغ المدفوع
    IF NEW.paid_amount >= NEW.amount THEN
        NEW.status := 'paid';
    ELSIF NEW.paid_amount > 0 THEN
        NEW.status := 'partial';
    ELSE
        NEW.status := 'pending';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_update_debt_status
    BEFORE INSERT OR UPDATE ON pos_system.customer_debts
    FOR EACH ROW
    EXECUTE FUNCTION pos_system.update_debt_status();

-- دالة لتحديث حالة الدين في جدول المبيعات
CREATE OR REPLACE FUNCTION pos_system.update_sale_debt_status()
RETURNS TRIGGER AS $$
BEGIN
    -- تحديث حالة الدين في جدول المبيعات
    UPDATE pos_system.sales
    SET debt_status = NEW.status,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = NEW.sale_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_update_sale_debt_status
    AFTER INSERT OR UPDATE ON pos_system.customer_debts
    FOR EACH ROW
    EXECUTE FUNCTION pos_system.update_sale_debt_status();
