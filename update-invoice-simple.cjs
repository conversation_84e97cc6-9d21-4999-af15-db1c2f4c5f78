// تحديث رقم الفاتورة بسيط
const { Pool } = require('pg');

async function updateInvoice() {
  const pool = new Pool({
    host: 'localhost',
    port: 5432,
    database: 'pos_system_db',
    user: 'postgres',
    password: 'toossar',
  });

  try {
    console.log('🔧 تحديث رقم الفاتورة...');
    
    // تحديث الفاتورة الأخيرة
    const result = await pool.query(`
      UPDATE pos_system.purchase_invoices 
      SET invoice_number = 'PUR-2025-0001'
      WHERE invoice_number = 'aff6a030-0f28-46ea-a0b1-ca5d5d49c4d8'
      RETURNING *
    `);
    
    if (result.rowCount > 0) {
      console.log('✅ تم تحديث رقم الفاتورة بنجاح!');
      console.log('📋 الرقم الجديد:', result.rows[0].invoice_number);
    } else {
      console.log('⚠️ لم يتم العثور على الفاتورة');
    }
    
  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await pool.end();
  }
}

updateInvoice();
