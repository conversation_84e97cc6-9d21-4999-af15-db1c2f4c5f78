# 🏪 نظام نقطة البيع - كشير توسار

نظام نقطة بيع متكامل باللغة العربية مع قاعدة بيانات PostgreSQL.

## 🚀 كيفية تشغيل النظام

### الطريقة السريعة (مستحسنة)
```bash
# تشغيل النظام الكامل (الخادم + الواجهة)
start-system.bat
```

### الطريقة اليدوية

#### 1. تشغيل الخادم الخلفي
```bash
# الطريقة الأولى
start-backend.bat

# أو الطريقة اليدوية
cd backend
node server.js
```

#### 2. تشغيل الواجهة الأمامية
```bash
# للمشاريع Vite (مستحسن)
npm run dev

# أو
npm start
```

## 🔧 متطلبات النظام

- Node.js (v14 أو أحدث)
- PostgreSQL (v12 أو أحدث)
- npm أو yarn

## 📊 قاعدة البيانات

- **اسم قاعدة البيانات**: `pos_system_db`
- **المخطط**: `pos_system`
- **كلمة المرور**: `toossar`
- **المنفذ**: `5432`

## 🌐 المنافذ

- **الخادم الخلفي**: http://localhost:5002
- **الواجهة الأمامية**: http://localhost:5173 (Vite) أو http://localhost:3000

## 🛠️ استكشاف الأخطاء

### مشكلة "Failed to fetch"
- تأكد من تشغيل الخادم الخلفي على المنفذ 5002
- تحقق من اتصال قاعدة البيانات
- استخدم `start-backend.bat` لتشغيل الخادم

### مشكلة الإحصائيات
- النظام يستخدم حساب محلي في حالة فشل API
- تحقق من logs الخادم للأخطاء

## 📁 هيكل المشروع

```
├── backend/           # الخادم الخلفي (Node.js + Express)
├── src/              # الواجهة الأمامية (React + TypeScript)
├── start-all.bat     # تشغيل النظام الكامل
├── start-backend.bat # تشغيل الخادم فقط
└── README.md         # هذا الملف
```

## 🎯 الميزات

- ✅ إدارة المنتجات والفئات
- ✅ نظام المبيعات مع الباركود
- ✅ إدارة العملاء والموردين
- ✅ التقارير والإحصائيات
- ✅ نظام الديون والائتمان
- ✅ طباعة الفواتير والإيصالات
- ✅ واجهة عربية كاملة

## 📞 الدعم

في حالة وجود مشاكل، تحقق من:
1. تشغيل PostgreSQL
2. تشغيل الخادم الخلفي
3. اتصال الإنترنت
4. logs الخادم في terminal
