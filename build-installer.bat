@echo off
chcp 65001 > nul
title Kasheer Toosar - Build Installer
color 0A

echo.
echo ========================================
echo    Building Kasheer Toosar Installer
echo ========================================
echo.

echo [1/8] Checking Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found
    echo Please download from: https://nodejs.org/
    pause
    exit /b 1
)
echo OK: Node.js found

echo [2/8] Checking npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm not found
    pause
    exit /b 1
)
echo OK: npm found

echo [3/8] Installing dependencies...
if not exist "node_modules" (
    call npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)
echo OK: Dependencies ready

echo [4/8] Installing Electron...
if not exist "node_modules\electron" (
    call npm install electron --save-dev
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install Electron
        pause
        exit /b 1
    )
)
echo OK: Electron ready

echo [5/8] Installing Electron Builder...
if not exist "node_modules\electron-builder" (
    call npm install electron-builder --save-dev
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install Electron Builder
        pause
        exit /b 1
    )
)
echo OK: Electron Builder ready

echo [6/8] Building frontend...
if not exist "dist" (
    call npm run build
    if %errorlevel% neq 0 (
        echo ERROR: Failed to build frontend
        pause
        exit /b 1
    )
)
echo OK: Frontend built

echo [7/8] Preparing Electron package...
copy /Y package-electron-fixed.json package.json >nul
if %errorlevel% neq 0 (
    echo ERROR: Failed to copy package-electron-fixed.json
    pause
    exit /b 1
)
echo OK: Package configuration updated

echo [8/8] Building installer...
call npx electron-builder --win --x64
if %errorlevel% neq 0 (
    echo WARNING: x64 build failed, trying ia32...
    call npx electron-builder --win --ia32
    if %errorlevel% neq 0 (
        echo ERROR: Failed to build installer
        pause
        exit /b 1
    )
)

echo.
echo SUCCESS: Installer built successfully!
echo.
echo Built files:
if exist "dist\*.exe" (
    for %%f in (dist\*.exe) do echo   - %%f
)
if exist "dist\*.msi" (
    for %%f in (dist\*.msi) do echo   - %%f
)
echo.
echo Installer is ready for use!
pause
