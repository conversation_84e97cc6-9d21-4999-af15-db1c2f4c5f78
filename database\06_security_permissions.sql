-- الأمان والصلاحيات
-- Security and Permissions

-- إن<PERSON><PERSON>ء أدوار المستخدمين
CREATE ROLE pos_admin;
CREATE ROLE pos_manager;
CREATE ROLE pos_cashier;
CREATE ROLE pos_readonly;

-- صلاحيات المدير (admin) - صلاحيات كاملة
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA pos_system TO pos_admin;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA pos_system TO pos_admin;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA pos_system TO pos_admin;
GRANT USAGE ON SCHEMA pos_system TO pos_admin;

-- صلاحيات المدير العام (manager) - صلاحيات واسعة مع بعض القيود
GRANT USAGE ON SCHEMA pos_system TO pos_manager;

-- قراءة وكتابة على معظم الجداول
GRANT SELECT, INSERT, UPDATE ON TABLE categories TO pos_manager;
GRANT SELECT, INSERT, UPDATE ON TABLE products TO pos_manager;
GRANT SELECT, INSERT, UPDATE ON TABLE customers TO pos_manager;
GRANT SELECT, INSERT, UPDATE ON TABLE suppliers TO pos_manager;
GRANT SELECT, INSERT, UPDATE ON TABLE sales TO pos_manager;
GRANT SELECT, INSERT, UPDATE ON TABLE sale_items TO pos_manager;
GRANT SELECT, INSERT, UPDATE ON TABLE purchase_invoices TO pos_manager;
GRANT SELECT, INSERT, UPDATE ON TABLE purchase_invoice_items TO pos_manager;
GRANT SELECT, INSERT, UPDATE ON TABLE customer_debts TO pos_manager;
GRANT SELECT, INSERT, UPDATE ON TABLE customer_debt_payments TO pos_manager;

-- قراءة فقط على الجداول الحساسة
GRANT SELECT ON TABLE settings TO pos_manager;
GRANT SELECT ON TABLE printer_settings TO pos_manager;
GRANT SELECT ON TABLE financial_transactions TO pos_manager;
GRANT SELECT ON TABLE inventory_movements TO pos_manager;

-- صلاحيات على الـ Views
GRANT SELECT ON ALL TABLES IN SCHEMA pos_system TO pos_manager;

-- صلاحيات على الـ Sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA pos_system TO pos_manager;

-- صلاحيات الكاشير (cashier) - صلاحيات محدودة للعمليات اليومية
GRANT USAGE ON SCHEMA pos_system TO pos_cashier;

-- قراءة المنتجات والفئات والعملاء
GRANT SELECT ON TABLE categories TO pos_cashier;
GRANT SELECT ON TABLE products TO pos_cashier;
GRANT SELECT ON TABLE customers TO pos_cashier;

-- إضافة وتعديل المبيعات فقط
GRANT SELECT, INSERT ON TABLE sales TO pos_cashier;
GRANT SELECT, INSERT ON TABLE sale_items TO pos_cashier;

-- تحديث معلومات العملاء الأساسية
GRANT UPDATE (name, phone, address) ON TABLE customers TO pos_cashier;

-- قراءة الإعدادات الأساسية
GRANT SELECT ON TABLE settings TO pos_cashier;
GRANT SELECT ON TABLE printer_settings TO pos_cashier;

-- قراءة Views المطلوبة للعمل
GRANT SELECT ON daily_sales_stats TO pos_cashier;
GRANT SELECT ON customer_statistics TO pos_cashier;

-- صلاحيات على الـ Sequences للمبيعات
GRANT USAGE ON SEQUENCE sale_number_seq TO pos_cashier;

-- صلاحيات القراءة فقط (readonly) - للتقارير والمراجعة
GRANT USAGE ON SCHEMA pos_system TO pos_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA pos_system TO pos_readonly;

-- إنشاء مستخدمين افتراضيين
CREATE USER pos_admin_user WITH PASSWORD 'admin_secure_password_2024';
CREATE USER pos_manager_user WITH PASSWORD 'manager_secure_password_2024';
CREATE USER pos_cashier_user WITH PASSWORD 'cashier_secure_password_2024';
CREATE USER pos_readonly_user WITH PASSWORD 'readonly_secure_password_2024';

-- تعيين الأدوار للمستخدمين
GRANT pos_admin TO pos_admin_user;
GRANT pos_manager TO pos_manager_user;
GRANT pos_cashier TO pos_cashier_user;
GRANT pos_readonly TO pos_readonly_user;

-- إنشاء وظائف للتحقق من الصلاحيات
CREATE OR REPLACE FUNCTION check_user_permission(
    user_role TEXT,
    required_permission TEXT
) RETURNS BOOLEAN AS $$
BEGIN
    CASE user_role
        WHEN 'admin' THEN
            RETURN TRUE;
        WHEN 'manager' THEN
            RETURN required_permission IN ('read', 'write', 'update');
        WHEN 'cashier' THEN
            RETURN required_permission IN ('read', 'sale');
        WHEN 'readonly' THEN
            RETURN required_permission = 'read';
        ELSE
            RETURN FALSE;
    END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- وظيفة تسجيل العمليات الحساسة
CREATE TABLE audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_name VARCHAR(255) NOT NULL,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- وظيفة تسجيل العمليات
CREATE OR REPLACE FUNCTION log_audit_trail()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO audit_log (user_name, action, table_name, record_id, old_values)
        VALUES (
            CURRENT_USER,
            'DELETE',
            TG_TABLE_NAME,
            OLD.id,
            row_to_json(OLD)::jsonb
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_log (user_name, action, table_name, record_id, old_values, new_values)
        VALUES (
            CURRENT_USER,
            'UPDATE',
            TG_TABLE_NAME,
            NEW.id,
            row_to_json(OLD)::jsonb,
            row_to_json(NEW)::jsonb
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO audit_log (user_name, action, table_name, record_id, new_values)
        VALUES (
            CURRENT_USER,
            'INSERT',
            TG_TABLE_NAME,
            NEW.id,
            row_to_json(NEW)::jsonb
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- تطبيق مراجعة العمليات على الجداول الحساسة
CREATE TRIGGER audit_settings AFTER INSERT OR UPDATE OR DELETE ON settings
    FOR EACH ROW EXECUTE FUNCTION log_audit_trail();

CREATE TRIGGER audit_products AFTER INSERT OR UPDATE OR DELETE ON products
    FOR EACH ROW EXECUTE FUNCTION log_audit_trail();

CREATE TRIGGER audit_sales AFTER INSERT OR UPDATE OR DELETE ON sales
    FOR EACH ROW EXECUTE FUNCTION log_audit_trail();

CREATE TRIGGER audit_financial_transactions AFTER INSERT OR UPDATE OR DELETE ON financial_transactions
    FOR EACH ROW EXECUTE FUNCTION log_audit_trail();

-- إنشاء فهارس للأداء على جدول المراجعة
CREATE INDEX idx_audit_log_user ON audit_log(user_name);
CREATE INDEX idx_audit_log_table ON audit_log(table_name);
CREATE INDEX idx_audit_log_date ON audit_log(created_at);
CREATE INDEX idx_audit_log_action ON audit_log(action);

-- وظيفة تنظيف سجلات المراجعة القديمة (أكثر من سنة)
CREATE OR REPLACE FUNCTION cleanup_old_audit_logs()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM audit_log 
    WHERE created_at < CURRENT_DATE - INTERVAL '1 year';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- إنشاء مهمة دورية لتنظيف السجلات (يتطلب pg_cron extension)
-- SELECT cron.schedule('cleanup-audit-logs', '0 2 * * 0', 'SELECT cleanup_old_audit_logs();');

-- وظيفة للحصول على إحصائيات الأمان
CREATE OR REPLACE VIEW security_statistics AS
SELECT 
    user_name,
    COUNT(*) as total_actions,
    COUNT(CASE WHEN action = 'INSERT' THEN 1 END) as inserts,
    COUNT(CASE WHEN action = 'UPDATE' THEN 1 END) as updates,
    COUNT(CASE WHEN action = 'DELETE' THEN 1 END) as deletes,
    MIN(created_at) as first_action,
    MAX(created_at) as last_action
FROM audit_log
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY user_name
ORDER BY total_actions DESC;

-- تعليقات
COMMENT ON TABLE audit_log IS 'سجل مراجعة العمليات الحساسة';
COMMENT ON FUNCTION check_user_permission IS 'التحقق من صلاحيات المستخدم';
COMMENT ON FUNCTION cleanup_old_audit_logs IS 'تنظيف سجلات المراجعة القديمة';
COMMENT ON VIEW security_statistics IS 'إحصائيات الأمان والعمليات';
