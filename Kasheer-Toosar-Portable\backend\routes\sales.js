const express = require('express');
const router = express.Router();
const pool = require('../database');

// عرض جميع المبيعات
router.get('/', async (req, res) => {
  try {
    console.log('📋 طلب عرض جميع المبيعات...');

    const result = await pool.query(`
      SELECT
        s.*,
        c.name as customer_name,
        di.invoice_number as debt_invoice_number,
        di.status as debt_invoice_status,
        di.remaining_balance as debt_remaining_balance
      FROM pos_system.sales s
      LEFT JOIN pos_system.customers c ON s.customer_id = c.id
      LEFT JOIN pos_system.customer_debt_invoices di ON s.debt_invoice_id = di.id
      ORDER BY s.created_at DESC
    `);

    console.log(`✅ تم العثور على ${result.rows.length} مبيعة`);
    console.log('📊 أول 3 مبيعات:', result.rows.slice(0, 3).map(sale => ({
      id: sale.id,
      sale_number: sale.sale_number,
      total_amount: sale.total_amount,
      payment_method: sale.payment_method,
      is_debt_sale: sale.is_debt_sale,
      debt_status: sale.debt_status,
      debt_invoice_number: sale.debt_invoice_number,
      created_at: sale.created_at
    })));

    // إضافة معلومات الدين للمبيعات
    const salesWithDebtInfo = result.rows.map(sale => {
      // تحديد اسم العميل مع التعامل مع القيم الفارغة
      const customerDisplayName = sale.customer_name || 'عميل غير معروف';

      return {
        ...sale,
        // تحديد حالة الدين بناءً على النظام الجديد
        debtStatus: sale.is_debt_sale ? (sale.debt_status || 'pending') : 'paid',
        isDebtSale: sale.is_debt_sale || false,
        debtInvoiceNumber: sale.debt_invoice_number || null,
        // تحديد طريقة الدفع الصحيحة
        paymentMethod: sale.payment_method,
        // إضافة معلومات إضافية - تجاوز القيم الفارغة
        customerName: customerDisplayName,
        customer_name: customerDisplayName
      };
    });

    res.json({
      success: true,
      sales: salesWithDebtInfo
    });
  } catch (error) {
    console.error('❌ خطأ في عرض المبيعات:', error);
    res.status(500).json({ error: 'خطأ في عرض المبيعات', details: error.message });
  }
});

// عرض بيع واحد مع العناصر
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // بيانات البيع الأساسية
    const saleResult = await pool.query(`
      SELECT
        s.*,
        c.name as customer_name
      FROM pos_system.sales s
      LEFT JOIN pos_system.customers c ON s.customer_id = c.id
      WHERE s.id = $1
    `, [id]);

    if (saleResult.rows.length === 0) {
      return res.status(404).json({ error: 'البيع غير موجود' });
    }

    // عناصر البيع
    const itemsResult = await pool.query(`
      SELECT
        si.*,
        p.name as product_name
      FROM pos_system.sale_items si
      LEFT JOIN pos_system.products p ON si.product_id = p.id
      WHERE si.sale_id = $1
    `, [id]);

    const sale = saleResult.rows[0];
    sale.items = itemsResult.rows;

    // إصلاح اسم العميل إذا كان فارغاً
    if (!sale.customer_name) {
      sale.customer_name = 'عميل غير معروف';
    }

    res.json(sale);
  } catch (error) {
    console.error('خطأ في عرض البيع:', error);
    res.status(500).json({ error: 'خطأ في عرض البيع' });
  }
});

// 💰 إضافة بيع آجل جديد مع إنشاء سجل دين
router.post('/credit', async (req, res) => {
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    console.log('💳 بيانات البيع الآجل المستلمة:', req.body);

    const {
      customer_id,
      items,
      subtotal,
      tax_amount,
      discount_amount,
      total_amount,
      notes,
      cashier_name
    } = req.body;

    // التحقق من البيانات المطلوبة للبيع الآجل
    if (!customer_id) {
      throw new Error('يجب اختيار عميل للبيع الآجل');
    }

    if (!items || !Array.isArray(items) || items.length === 0) {
      throw new Error('عناصر البيع مطلوبة');
    }

    if (!total_amount) {
      throw new Error('مبلغ البيع مطلوب');
    }

    // إنشاء رقم البيع
    const saleNumberResult = await client.query(`
      SELECT COALESCE(MAX(CAST(SUBSTRING(sale_number FROM 5) AS INTEGER)), 0) + 1 as next_number
      FROM pos_system.sales
      WHERE sale_number LIKE 'INV-%'
    `);
    const nextNumber = saleNumberResult.rows[0].next_number;
    const saleNumber = `INV-${String(nextNumber).padStart(6, '0')}`;

    // إضافة البيع الرئيسي كبيع آجل
    const saleResult = await client.query(`
      INSERT INTO pos_system.sales (
        sale_number, customer_id, subtotal, tax_amount, discount_amount,
        total_amount, payment_method, amount_paid, change_amount,
        notes, cashier_name, is_debt_sale, debt_status
      )
      VALUES ($1, $2, $3, $4, $5, $6, 'credit', 0, 0, $7, $8, true, 'pending')
      RETURNING *
    `, [
      saleNumber,
      customer_id,
      parseFloat(subtotal) || 0,
      parseFloat(tax_amount) || 0,
      parseFloat(discount_amount) || 0,
      parseFloat(total_amount),
      notes || '',
      cashier_name || 'الكاشير'
    ]);

    const sale = saleResult.rows[0];
    console.log('✅ تم إنشاء البيع الآجل:', sale.id);

    // إضافة عناصر البيع وتحديث المخزون
    for (const item of items) {
      // التحقق من المخزون للمنتجات المعروفة
      if (item.product_id && !item.product_id.toString().startsWith('unknown-')) {
        const stockCheck = await client.query(`
          SELECT name, stock FROM pos_system.products WHERE id = $1
        `, [item.product_id]);

        if (stockCheck.rows.length === 0) {
          throw new Error(`المنتج غير موجود: ${item.product_id}`);
        }

        const currentStock = stockCheck.rows[0].stock;
        const requestedQuantity = parseInt(item.quantity);

        if (currentStock < requestedQuantity) {
          throw new Error(`المخزون غير كافي للمنتج "${stockCheck.rows[0].name}". المتاح: ${currentStock}، المطلوب: ${requestedQuantity}`);
        }

        // تحديث المخزون
        await client.query(`
          UPDATE pos_system.products
          SET stock = stock - $1, updated_at = CURRENT_TIMESTAMP
          WHERE id = $2
        `, [requestedQuantity, item.product_id]);
      }

      // إضافة عنصر البيع
      await client.query(`
        INSERT INTO pos_system.sale_items (
          sale_id, product_id, product_name, quantity,
          unit_price, discount, total_price, is_unknown
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `, [
        sale.id,
        item.product_id || null,
        item.product_name,
        parseInt(item.quantity),
        parseFloat(item.unit_price),
        parseFloat(item.discount) || 0,
        parseFloat(item.total_price),
        !item.product_id || item.product_id.toString().startsWith('unknown-')
      ]);
    }

    // إنشاء سجل دين في جدول customer_debts
    const debtResult = await client.query(`
      INSERT INTO pos_system.customer_debts (
        customer_id, sale_id, amount, debt_date,
        paid_amount, status, notes
      )
      VALUES ($1, $2, $3, CURRENT_DATE, 0, 'pending', $4)
      RETURNING *
    `, [
      customer_id,
      sale.id,
      parseFloat(total_amount),
      `دين من البيع رقم ${saleNumber}`
    ]);

    const debt = debtResult.rows[0];
    console.log('✅ تم إنشاء سجل الدين:', debt);

    // الحصول على اسم العميل قبل إنهاء المعاملة
    const customerResult = await client.query('SELECT name FROM pos_system.customers WHERE id = $1', [customer_id]);
    const customerName = customerResult.rows[0]?.name;

    await client.query('COMMIT');

    // إرجاع البيانات المطلوبة
    res.json({
      success: true,
      message: 'تم إنشاء البيع الآجل وتسجيل الدين بنجاح',
      sale: {
        ...sale,
        sale_number: saleNumber,
        debt_invoice_number: saleNumber,
        debt_status: 'pending',
        customer_name: customerName,
        debt_id: debt.id,
        debt_amount: debt.amount
      }
    });
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ خطأ في إنشاء البيع الآجل:', error);
    res.status(500).json({ error: 'خطأ في إنشاء البيع الآجل', details: error.message });
  } finally {
    client.release();
  }
});

// إضافة بيع جديد
router.post('/', async (req, res) => {
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    console.log('بيانات البيع المستلمة:', req.body);

    const {
      customer_id,
      items,
      subtotal,
      tax_amount,
      discount_amount,
      total_amount,
      payment_method,
      amount_paid,
      change_amount,
      notes,
      cashier_name
    } = req.body;

    // التحقق من البيانات المطلوبة
    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({ error: 'عناصر البيع مطلوبة' });
    }

    if (!total_amount || !payment_method) {
      return res.status(400).json({ error: 'بيانات البيع الأساسية مطلوبة' });
    }

    console.log('نوع customer_id:', typeof customer_id, 'القيمة:', customer_id);

    // تصحيح مبلغ الباقي للبيع بالآجل
    let finalAmountPaid = parseFloat(amount_paid) || 0;
    let finalChangeAmount = parseFloat(change_amount) || 0;

    // في البيع بالآجل، الباقي يجب أن يكون 0 أو موجب
    if (payment_method === 'credit') {
      finalChangeAmount = Math.max(0, finalChangeAmount);
    }

    // إضافة البيع الرئيسي
    const saleResult = await client.query(`
      INSERT INTO pos_system.sales (
        customer_id, subtotal, tax_amount, discount_amount,
        total_amount, payment_method, amount_paid, change_amount,
        notes, cashier_name
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *
    `, [
      customer_id || null,
      parseFloat(subtotal) || 0,
      parseFloat(tax_amount) || 0,
      parseFloat(discount_amount) || 0,
      parseFloat(total_amount),
      payment_method,
      finalAmountPaid,
      finalChangeAmount,
      notes || '',
      cashier_name || 'الكاشير'
    ]);

    const sale = saleResult.rows[0];

    // التحقق من توفر المخزون قبل البيع
    console.log(`🔍 بدء فحص المخزون لـ ${items.length} عنصر...`);
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      console.log(`🔍 فحص العنصر ${i + 1}/${items.length}:`, {
        product_id: item.product_id,
        product_name: item.product_name,
        quantity: item.quantity,
        unit_price: item.unit_price
      });

      // التحقق من بيانات العنصر (product_id يمكن أن يكون null للمنتجات غير المعروفة)
      if (!item.product_name || !item.quantity || !item.unit_price) {
        console.error(`❌ بيانات العنصر غير مكتملة:`, item);
        throw new Error(`بيانات العنصر غير مكتملة: ${JSON.stringify(item)}`);
      }

      // فحص المخزون المتاح فقط للمنتجات الموجودة
      if (item.product_id && !item.product_id.toString().startsWith('unknown-')) {
        console.log(`🔍 فحص المخزون للمنتج: ${item.product_id}`);
        const stockCheck = await client.query(`
          SELECT name, stock FROM pos_system.products WHERE id = $1
        `, [item.product_id]);

        if (stockCheck.rows.length === 0) {
          throw new Error(`المنتج غير موجود: ${item.product_id}`);
        }

        const currentStock = stockCheck.rows[0].stock;
        const requestedQuantity = parseInt(item.quantity);

        console.log(`📦 المخزون الحالي: ${currentStock}, المطلوب: ${requestedQuantity}`);

        if (currentStock < requestedQuantity) {
          throw new Error(`المخزون غير كافي للمنتج "${stockCheck.rows[0].name}". المتاح: ${currentStock}، المطلوب: ${requestedQuantity}`);
        }

        console.log(`✅ المخزون كافي للمنتج: ${stockCheck.rows[0].name}`);
      } else {
        console.log(`🔮 منتج غير معروف: "${item.product_name}" - سيتم بيعه بدون فحص المخزون`);
      }
    }

    // إضافة عناصر البيع
    console.log(`📦 بدء إضافة ${items.length} عنصر إلى البيع...`);
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      console.log(`📦 معالجة العنصر ${i + 1}/${items.length}:`, {
        product_id: item.product_id,
        product_name: item.product_name,
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_price: item.total_price
      });

      // تحديد ما إذا كان المنتج غير معروف - دعم كل من product_id و productId
      const productId = item.product_id || item.productId;
      const isUnknownProduct = !productId || productId === null || productId.toString().startsWith('unknown-');
      const unknownProductCode = isUnknownProduct ? `UNK-${Date.now()}-${Math.random().toString(36).substring(2, 11)}` : null;

      console.log(`🔍 تحليل المنتج: productId=${productId}, isUnknown=${isUnknownProduct}`);

      // إدراج عنصر البيع مع معالجة المنتجات غير المعروفة
      if (isUnknownProduct) {
        // للمنتجات غير المعروفة - بدون منتج مرجعي
        console.log('🔮 إدراج منتج غير معروف في sale_items...');
        console.log('📋 بيانات العنصر:', {
          sale_id: sale.id,
          product_name: (item.product_name || item.productName) + ' (غير معروف)',
          quantity: parseInt(item.quantity),
          unit_price: parseFloat(item.unit_price || item.unitPrice),
          discount: parseFloat(item.discount) || 0,
          total_price: parseFloat(item.total_price || item.totalPrice)
        });

        const itemResult = await client.query(`
          INSERT INTO pos_system.sale_items (
            sale_id, product_id, product_name, quantity,
            unit_price, discount, total_price, is_unknown
          )
          VALUES ($1, NULL, $2, $3, $4, $5, $6, TRUE)
          RETURNING *
        `, [
          sale.id,
          (item.product_name || item.productName) + ' (غير معروف)',
          parseInt(item.quantity),
          parseFloat(item.unit_price || item.unitPrice),
          parseFloat(item.discount) || 0,
          parseFloat(item.total_price || item.totalPrice)
        ]);

        console.log(`✅ تم إدراج منتج غير معروف في sale_items:`, itemResult.rows[0]);
      } else {
        // للمنتجات المعروفة
        await client.query(`
          INSERT INTO pos_system.sale_items (
            sale_id, product_id, product_name, quantity,
            unit_price, discount, total_price
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7)
        `, [
          sale.id,
          productId,
          item.product_name || item.productName,
          parseInt(item.quantity),
          parseFloat(item.unit_price || item.unitPrice),
          parseFloat(item.discount) || 0,
          parseFloat(item.total_price || item.totalPrice)
        ]);
      }

      if (isUnknownProduct) {
        console.log(`✨ تم بيع منتج غير معروف: "${item.product_name}" بكود: ${unknownProductCode}`);
      }

      // تحديث المخزون فقط للمنتجات المعروفة (ليس للمنتجات غير المعروفة)
      if (item.product_id && !isUnknownProduct) {
        console.log(`📦 تحديث المخزون للمنتج: ${item.product_id}`);
        const updateResult = await client.query(`
          UPDATE pos_system.products
          SET stock = GREATEST(0, stock - $1), updated_at = CURRENT_TIMESTAMP
          WHERE id = $2
          RETURNING stock, name
        `, [parseInt(item.quantity), item.product_id]);

        if (updateResult.rows.length === 0) {
          throw new Error(`المنتج غير موجود: ${item.product_id}`);
        }

        const previousStock = updateResult.rows[0].stock + parseInt(item.quantity);
        const newStock = updateResult.rows[0].stock;

        console.log(`📦 تم تحديث مخزون "${updateResult.rows[0].name}" من ${previousStock} إلى ${newStock}`);

        // إضافة حركة مخزون
        await client.query(`
          INSERT INTO pos_system.inventory_movements (
            product_id, movement_type, quantity,
            previous_stock, new_stock, reason,
            reference_id, reference_type
          )
          VALUES ($1, 'out', $2, $3, $4, 'بيع', $5, 'sale')
        `, [
          item.product_id,
          parseInt(item.quantity),
          previousStock, // previous stock
          newStock, // new stock
          sale.id
        ]);
      } else if (isUnknownProduct) {
        console.log(`🔮 تم بيع منتج غير معروف: "${item.product_name}" - لا يتم تحديث المخزون`);

        // تسجيل المنتج غير المعروف في المعاملات المالية للتتبع (إذا كان الجدول موجود)
        try {
          await client.query(`
            INSERT INTO financial_transactions (
              type, amount, description, reference_id, reference_type
            )
            VALUES ('revenue', $1, $2, $3, 'unknown_product_sale')
          `, [
            parseFloat(item.total_price),
            `بيع منتج غير معروف: ${item.product_name}`,
            sale.id
          ]);
        } catch (finError) {
          console.log('📝 لم يتم تسجيل المعاملة المالية (الجدول غير موجود)');
        }
      }
    }

    // معالجة طرق الدفع المختلفة
    if (customer_id) {
      try {
        if (payment_method === 'credit') {
          // 🎯 النظام التراكمي للديون - الدين الجديد + القديم
          console.log('💳 معالجة البيع بالدين بالنظام التراكمي المتطور...');

          // الحصول على فاتورة الدين المفتوحة أو إنشاء واحدة جديدة
          let debtInvoice = await client.query(`
            SELECT * FROM pos_system.customer_debt_invoices
            WHERE customer_id = $1 AND status = 'open'
            LIMIT 1
          `, [customer_id]);

          let debtInvoiceId;
          let previousDebtAmount = 0;

          if (debtInvoice.rows.length === 0) {
            // البحث عن ديون قديمة مغلقة للعميل
            const oldDebts = await client.query(`
              SELECT COALESCE(SUM(remaining_balance), 0) as old_debt
              FROM pos_system.customer_debt_invoices
              WHERE customer_id = $1 AND status = 'closed' AND remaining_balance > 0
            `, [customer_id]);

            previousDebtAmount = parseFloat(oldDebts.rows[0]?.old_debt || 0);

            // إنشاء فاتورة دين تراكمية جديدة
            console.log(`📋 إنشاء فاتورة دين تراكمية جديدة (دين قديم: ${previousDebtAmount} دج)...`);

            const customer = await client.query(`
              SELECT name FROM pos_system.customers WHERE id = $1
            `, [customer_id]);

            const customerName = customer.rows[0]?.name || 'عميل';
            const namePrefix = customerName.substring(0, 3).toUpperCase();
            const timestamp = Date.now().toString().slice(-6);
            const invoiceNumber = `DEBT-${namePrefix}-${timestamp}`;

            // إنشاء الفاتورة مع الدين القديم
            const newDebtInvoice = await client.query(`
              INSERT INTO pos_system.customer_debt_invoices
              (customer_id, invoice_number, total_debt_amount, remaining_balance, credit_limit, payment_terms, notes)
              VALUES ($1, $2, $3, $4, $5, $6, $7)
              RETURNING id
            `, [
              customer_id,
              invoiceNumber,
              previousDebtAmount, // البدء بالدين القديم
              previousDebtAmount, // الرصيد المتبقي = الدين القديم
              10000.00, // حد ائتمان افتراضي
              30, // 30 يوم للسداد
              `فاتورة دين تراكمية للعميل: ${customerName} (دين قديم: ${previousDebtAmount} دج)`
            ]);

            debtInvoiceId = newDebtInvoice.rows[0].id;

            // إضافة سجل للدين القديم إذا كان موجوداً
            if (previousDebtAmount > 0) {
              await client.query(`
                INSERT INTO pos_system.debt_invoice_items
                (debt_invoice_id, description, amount)
                VALUES ($1, $2, $3)
              `, [debtInvoiceId, 'رصيد دين سابق مُرحل', previousDebtAmount]);
            }

            console.log(`✅ تم إنشاء فاتورة دين تراكمية: ${invoiceNumber} (إجمالي: ${previousDebtAmount} دج)`);
          } else {
            debtInvoiceId = debtInvoice.rows[0].id;
            previousDebtAmount = parseFloat(debtInvoice.rows[0].remaining_balance || 0);
            console.log(`📋 استخدام فاتورة الدين الموجودة: ${debtInvoice.rows[0].invoice_number} (رصيد حالي: ${previousDebtAmount} دج)`);
          }

          // التحقق من حد الائتمان التراكمي
          const currentDebt = await client.query(`
            SELECT remaining_balance, credit_limit FROM pos_system.customer_debt_invoices
            WHERE id = $1
          `, [debtInvoiceId]);

          const currentBalance = parseFloat(currentDebt.rows[0].remaining_balance || 0);
          const creditLimit = parseFloat(currentDebt.rows[0].credit_limit || 0);
          const newTotalBalance = currentBalance + parseFloat(total_amount);

          console.log(`💰 فحص حد الائتمان: الرصيد الحالي ${currentBalance} دج + المشتريات الجديدة ${total_amount} دج = ${newTotalBalance} دج (الحد الأقصى: ${creditLimit} دج)`);

          if (newTotalBalance > creditLimit) {
            throw new Error(`تجاوز حد الائتمان المسموح. الحد الأقصى: ${creditLimit} دج، الرصيد التراكمي الحالي: ${currentBalance} دج، المطلوب إضافة: ${total_amount} دج`);
          }

          // إضافة البيع لفاتورة الدين الموحدة
          const itemDescription = items.map(item => item.product_name).join(', ');

          await client.query(`
            INSERT INTO pos_system.debt_invoice_items
            (debt_invoice_id, sale_id, description, amount)
            VALUES ($1, $2, $3, $4)
          `, [debtInvoiceId, sale.id, itemDescription, total_amount]);

          // ربط البيع بفاتورة الدين
          await client.query(`
            UPDATE pos_system.sales
            SET debt_invoice_id = $1, is_debt_sale = true, debt_status = 'pending'
            WHERE id = $2
          `, [debtInvoiceId, sale.id]);

          // تحديث رصيد فاتورة الدين التراكمية
          await client.query('SELECT pos_system.update_debt_invoice_balance($1)', [debtInvoiceId]);

          // الحصول على الرصيد الجديد
          const updatedDebt = await client.query(`
            SELECT remaining_balance FROM pos_system.customer_debt_invoices WHERE id = $1
          `, [debtInvoiceId]);

          const finalBalance = parseFloat(updatedDebt.rows[0].remaining_balance || 0);

          console.log(`💰 تم إضافة مشتريات بقيمة ${total_amount} دج لفاتورة الدين التراكمية`);
          console.log(`📊 الرصيد التراكمي الجديد: ${finalBalance} دج (دين قديم + جديد)`);
        } else if (payment_method === 'balance') {
          // الدفع بالرصيد - خصم من رصيد العميل
          const balanceAmount = parseFloat(total_amount);

          // التحقق من كفاية الرصيد
          const customerResult = await client.query(`
            SELECT balance FROM pos_system.customers WHERE id = $1
          `, [customer_id]);

          if (customerResult.rows.length === 0) {
            throw new Error('العميل غير موجود');
          }

          const currentBalance = parseFloat(customerResult.rows[0].balance);
          if (currentBalance < balanceAmount) {
            throw new Error(`رصيد العميل غير كافي. الرصيد المتاح: ${currentBalance.toFixed(2)} دج`);
          }

          // خصم المبلغ من رصيد العميل
          await client.query(`
            UPDATE pos_system.customers
            SET balance = balance - $1, updated_at = CURRENT_TIMESTAMP
            WHERE id = $2
          `, [balanceAmount, customer_id]);

          console.log(`تم خصم ${balanceAmount} دج من رصيد العميل ${customer_id}`);

        } else if (payment_method === 'mixed') {
          // الدفع المختلط - قد يحتوي على رصيد ونقد
          const balanceUsed = parseFloat(req.body.balance_amount) || 0;

          if (balanceUsed > 0) {
            // التحقق من كفاية الرصيد
            const customerResult = await client.query(`
              SELECT balance FROM pos_system.customers WHERE id = $1
            `, [customer_id]);

            if (customerResult.rows.length === 0) {
              throw new Error('العميل غير موجود');
            }

            const currentBalance = parseFloat(customerResult.rows[0].balance);
            if (currentBalance < balanceUsed) {
              throw new Error(`رصيد العميل غير كافي للمبلغ المطلوب من الرصيد: ${balanceUsed.toFixed(2)} دج`);
            }

            // خصم المبلغ المستخدم من الرصيد
            await client.query(`
              UPDATE pos_system.customers
              SET balance = balance - $1, updated_at = CURRENT_TIMESTAMP
              WHERE id = $2
            `, [balanceUsed, customer_id]);

            console.log(`تم خصم ${balanceUsed} دج من رصيد العميل في الدفع المختلط`);
          }
        }
      } catch (paymentError) {
        console.error('خطأ في معالجة طريقة الدفع:', paymentError);
        throw paymentError; // نوقف المعاملة في حالة خطأ الدفع
      }
    }

    await client.query('COMMIT');
    res.status(201).json(sale);

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ خطأ تفصيلي في إضافة البيع:', error);
    console.error('📋 نوع الخطأ:', error.constructor.name);
    console.error('📋 رسالة الخطأ:', error.message);
    console.error('📋 كود الخطأ:', error.code);
    console.error('📋 تفاصيل إضافية:', error.detail);
    console.error('📋 Stack trace:', error.stack);
    console.error('📋 بيانات الطلب:', {
      items_count: req.body.items?.length,
      customer_id: req.body.customer_id,
      total_amount: req.body.total_amount,
      payment_method: req.body.payment_method
    });

    // إرسال رسالة خطأ مفصلة
    const errorMessage = error.message || 'خطأ غير معروف في إضافة البيع';
    res.status(500).json({
      error: 'خطأ في إضافة البيع',
      details: errorMessage,
      code: error.code,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      request_data: {
        items_count: req.body.items?.length,
        has_customer: !!req.body.customer_id,
        payment_method: req.body.payment_method
      }
    });
  } finally {
    client.release();
  }
});

// إحصائيات المبيعات
router.get('/stats/daily', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT
        COUNT(*) as total_sales,
        SUM(total_amount) as total_revenue,
        AVG(total_amount) as average_sale
      FROM sales
      WHERE DATE(created_at) = CURRENT_DATE
    `);
    res.json(result.rows[0]);
  } catch (error) {
    console.error('خطأ في إحصائيات المبيعات:', error);
    res.status(500).json({ error: 'خطأ في إحصائيات المبيعات' });
  }
});

// جلب الديون غير المدفوعة
router.get('/debts', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT
        d.*,
        c.name as customer_name
      FROM pos_system.customer_debts d
      LEFT JOIN pos_system.customers c ON d.customer_id = c.id
      WHERE d.status = 'pending'
    `);
    res.json({ success: true, debts: result.rows });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// تسديد الدين
router.post('/debts/:id/pay', async (req, res) => {
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    const { id } = req.params;
    const { amount } = req.body;

    // التحقق من صحة البيانات
    if (!amount || isNaN(amount) || amount <= 0) {
      throw new Error('المبلغ المدفوع غير صالح');
    }

    // التحقق من وجود الدين
    const debtResult = await client.query(`
      SELECT * FROM pos_system.customer_debts WHERE id = $1
    `, [id]);

    if (debtResult.rows.length === 0) {
      throw new Error('الدين غير موجود');
    }

    const debt = debtResult.rows[0];

    // التحقق من كفاية المبلغ المدفوع
    if (amount > debt.remaining_balance) {
      throw new Error(`المبلغ المدفوع أكبر من المبلغ المستحق (${debt.remaining_balance} دج)`);
    }

    // تحديث حالة الدين والرصيد المتبقي
    await client.query(`
      UPDATE pos_system.customer_debts
      SET status = 'paid', paid_amount = $1, remaining_balance = $2
      WHERE id = $3
    `, [amount, debt.remaining_balance - amount, id]);

    await client.query('COMMIT');
    res.json({ success: true, message: 'تم تسديد الدين بنجاح' });
  } catch (error) {
    await client.query('ROLLBACK');
    res.status(500).json({ error: error.message });
  } finally {
    client.release();
  }
});

// جلب العملاء المدينين فقط (distinct)
router.get('/debtors', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT DISTINCT c.id, c.name, c.phone, c.address
      FROM pos_system.customers c
      INNER JOIN pos_system.customer_debts d ON c.id = d.customer_id
      WHERE d.status = 'pending'
    `);
    res.json({ success: true, debtors: result.rows });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;

// بيع منتج غير معروف - endpoint مبسط
router.post('/unknown-product', async (req, res) => {
  try {
    const { productName, price, quantity = 1, paymentMethod = 'cash' } = req.body;

    console.log('🔮 بيع منتج غير معروف - البيانات المستلمة:', { productName, price, quantity, paymentMethod });

    // التحقق من البيانات المطلوبة
    if (!productName || !price || isNaN(price)) {
      console.error('❌ بيانات غير صحيحة:', { productName, price, quantity });
      return res.status(400).json({
        error: 'اسم المنتج والسعر مطلوبان',
        received: { productName, price, quantity }
      });
    }

    console.log('✅ البيانات صحيحة، بدء الاتصال بقاعدة البيانات...');
    const client = await pool.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    try {
      console.log('🔄 بدء المعاملة (BEGIN)...');
      await client.query('BEGIN');
      console.log('✅ تم بدء المعاملة بنجاح');

      // حساب القيم
      const subtotal = parseFloat(price) * parseInt(quantity);
      const totalAmount = subtotal;

      console.log('💰 القيم المحسوبة:', { subtotal, totalAmount, price, quantity });

      // إنشاء البيع
      console.log('📝 إدراج البيع في جدول sales...');
      const saleResult = await client.query(`
        INSERT INTO pos_system.sales (
          subtotal, tax_amount, discount_amount, total_amount,
          payment_method, amount_paid, change_amount, notes, cashier_name
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING *
      `, [
        subtotal,
        0, // tax_amount
        0, // discount_amount
        totalAmount,
        paymentMethod,
        totalAmount, // amount_paid
        0, // change_amount
        'منتج غير معروف',
        'النظام'
      ]);

      const sale = saleResult.rows[0];
      console.log('✅ تم إدراج البيع بنجاح:', { id: sale.id, sale_number: sale.sale_number });

      // إضافة عنصر البيع
      console.log('📦 إدراج عنصر البيع في جدول sale_items...');
      const itemResult = await client.query(`
        INSERT INTO pos_system.sale_items (
          sale_id, product_id, product_name, quantity,
          unit_price, discount, total_price
        )
        VALUES ($1, NULL, $2, $3, $4, $5, $6)
        RETURNING *
      `, [
        sale.id,
        productName + ' (غير معروف)',
        parseInt(quantity),
        parseFloat(price),
        0, // discount
        subtotal
      ]);

      console.log('✅ تم إدراج عنصر البيع بنجاح:', itemResult.rows[0]);

      console.log('💾 حفظ المعاملة (COMMIT)...');
      await client.query('COMMIT');
      console.log('✅ تم حفظ المعاملة بنجاح!');

      console.log('✅ تم بيع المنتج غير المعروف بنجاح:', sale.sale_number);

      res.json({
        success: true,
        sale: sale,
        message: 'تم بيع المنتج غير المعروف بنجاح'
      });

    } catch (error) {
      console.error('❌ خطأ أثناء المعاملة:', error);
      console.error('📋 تفاصيل الخطأ:', {
        message: error.message,
        code: error.code,
        detail: error.detail,
        hint: error.hint,
        position: error.position,
        internalPosition: error.internalPosition,
        internalQuery: error.internalQuery,
        where: error.where,
        schema: error.schema,
        table: error.table,
        column: error.column,
        dataType: error.dataType,
        constraint: error.constraint
      });

      console.log('🔄 إلغاء المعاملة (ROLLBACK)...');
      await client.query('ROLLBACK');
      console.log('✅ تم إلغاء المعاملة');
      throw error;
    } finally {
      console.log('🔌 إغلاق الاتصال بقاعدة البيانات...');
      client.release();
      console.log('✅ تم إغلاق الاتصال');
    }

  } catch (error) {
    console.error('❌ خطأ عام في بيع المنتج غير المعروف:', error);
    console.error('📋 نوع الخطأ:', typeof error);
    console.error('📋 رسالة الخطأ:', error.message);
    console.error('📋 كود الخطأ:', error.code);

    res.status(500).json({
      error: 'خطأ في بيع المنتج غير المعروف',
      details: error.message,
      code: error.code,
      timestamp: new Date().toISOString()
    });
  }
});

// 🔮 بيع سريع للمنتجات غير المعروفة - مضمون 100%
router.post('/quick-unknown', async (req, res) => {
  try {
    const { name, price } = req.body;

    if (!name || !price) {
      return res.status(400).json({ error: 'الاسم والسعر مطلوبان' });
    }

    console.log('🚀 بيع سريع:', { name, price });

    // إدراج مباشر بدون معاملات معقدة
    const result = await pool.query(`
      INSERT INTO pos_system.unknown_products (name, price)
      VALUES ($1, $2)
      RETURNING *
    `, [name, parseFloat(price)]);

    console.log('✅ تم الحفظ بنجاح:', result.rows[0]);

    res.json({
      success: true,
      product: result.rows[0],
      message: 'تم بيع المنتج غير المعروف بنجاح'
    });

  } catch (error) {
    console.error('❌ خطأ في البيع السريع:', error);
    res.status(500).json({ error: error.message });
  }
});

// 📋 عرض المنتجات غير المعروفة
router.get('/unknown-products', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT * FROM pos_system.unknown_products
      ORDER BY sale_date DESC
    `);

    console.log(`📦 تم العثور على ${result.rows.length} منتج غير معروف`);
    res.json(result.rows);

  } catch (error) {
    console.error('❌ خطأ في عرض المنتجات غير المعروفة:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
