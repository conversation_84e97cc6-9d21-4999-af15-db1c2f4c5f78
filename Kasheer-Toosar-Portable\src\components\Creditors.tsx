import React, { useState, useEffect } from 'react';
import {
  DollarSign,
  Search,
  TrendingUp,
  Wallet,
  Phone,
  MapPin,
  Filter,
  Download,
  RefreshCw,
  Eye,
  MessageSquare,
  Bell,
  Calendar,
  Clock,
  Users,
  CreditCard,
  XCircle,
  CheckCircle,
  Send,
  FileText,
  Calculator,
  Target,
  Zap,
  Gift,
  Star,
  Crown,
  Loader
} from 'lucide-react';
import { useApp } from '../context/AppContext';
import apiService from '../services/api';

interface CreditorCustomer {
  id: string;
  name: string;
  phone: string;
  address: string;
  email?: string;
  balance: number;
  credit_limit: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

const Creditors: React.FC = () => {
  const { showNotification } = useApp();
  const [creditors, setCreditors] = useState<CreditorCustomer[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCreditor, setSelectedCreditor] = useState<CreditorCustomer | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  // تحميل العملاء الدائنين من API
  const loadCreditors = async () => {
    try {
      setLoading(true);
      const data = await apiService.getCreditorCustomers();
      setCreditors(data);
    } catch (error) {
      console.error('خطأ في تحميل العملاء الدائنين:', error);
      showNotification('خطأ في تحميل العملاء الدائنين', 'error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCreditors();
  }, []);

  // إحصائيات الدائنين
  const totalCreditors = creditors.length;
  const totalCreditAmount = creditors.reduce((sum, creditor) => sum + (parseFloat(creditor.balance?.toString()) || 0), 0);
  const averageCredit = totalCreditors > 0 ? totalCreditAmount / totalCreditors : 0;

  // فلترة العملاء الدائنين
  const filteredCreditors = creditors.filter(creditor => {
    const matchesSearch = creditor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         creditor.phone.includes(searchTerm) ||
                         creditor.address.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  });

  const formatCurrency = (amount: number | string) => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return `${(numAmount || 0).toFixed(2)} دج`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const getLoyaltyIcon = (loyalty: string) => {
    switch (loyalty) {
      case 'bronze': return <Gift className="w-4 h-4" />;
      case 'silver': return <Star className="w-4 h-4" />;
      case 'gold': return <Crown className="w-4 h-4" />;
      case 'platinum': return <Crown className="w-4 h-4" />;
      default: return <DollarSign className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-green-900 to-slate-900 p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="bg-gradient-to-r from-green-500 to-emerald-600 p-3 rounded-2xl shadow-lg">
              <DollarSign className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">العملاء الدائنون</h1>
              <p className="text-gray-300">إدارة ومتابعة أرصدة العملاء الإيجابية والمكافآت</p>
            </div>
          </div>

          <div className="flex items-center space-x-4 space-x-reverse">
            <button className="bg-gradient-to-r from-purple-500 to-pink-600 hover:scale-105 transform transition-all duration-300 px-6 py-3 rounded-xl font-bold text-white shadow-2xl hover:shadow-purple-500/25 flex items-center space-x-2 space-x-reverse">
              <Gift className="w-5 h-5" />
              <span>إرسال مكافأة جماعية</span>
            </button>

            <button className="bg-white/10 backdrop-blur-lg border border-white/20 hover:border-white/40 px-4 py-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse text-white">
              <Download className="w-5 h-5" />
              <span>تصدير</span>
            </button>

            <button
              onClick={loadCreditors}
              disabled={loading}
              className="bg-white/10 backdrop-blur-lg border border-white/20 hover:border-white/40 px-4 py-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse text-white disabled:opacity-50"
            >
              <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
              <span>تحديث</span>
            </button>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">إجمالي الدائنين</p>
                <p className="text-3xl font-bold text-white">{totalCreditors}</p>
              </div>
              <div className="bg-green-500/20 p-3 rounded-xl">
                <Users className="w-6 h-6 text-green-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">إجمالي الأرصدة</p>
                <p className="text-3xl font-bold text-green-400">
                  {formatCurrency(totalCreditAmount)}
                </p>
              </div>
              <div className="bg-green-500/20 p-3 rounded-xl">
                <TrendingUp className="w-6 h-6 text-green-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">متوسط الرصيد</p>
                <p className="text-3xl font-bold text-emerald-400">
                  {formatCurrency(averageCredit)}
                </p>
              </div>
              <div className="bg-emerald-500/20 p-3 rounded-xl">
                <Calculator className="w-6 h-6 text-emerald-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">أعلى رصيد</p>
                <p className="text-3xl font-bold text-purple-400">
                  {creditors.length > 0 ? formatCurrency(Math.max(...creditors.map(c => parseFloat(c.balance?.toString()) || 0))) : '0.00 دج'}
                </p>
              </div>
              <div className="bg-purple-500/20 p-3 rounded-xl">
                <Crown className="w-6 h-6 text-purple-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">العملاء النشطون</p>
                <p className="text-3xl font-bold text-yellow-400">{creditors.filter(c => (parseFloat(c.balance?.toString()) || 0) > 100).length}</p>
              </div>
              <div className="bg-yellow-500/20 p-3 rounded-xl">
                <Star className="w-6 h-6 text-yellow-400" />
              </div>
            </div>
          </div>
        </div>

        {/* شريط البحث */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 mb-8">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="البحث بالاسم، الهاتف، أو العنوان..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full bg-white/10 border border-white/20 rounded-xl px-12 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 transition-all duration-300"
            />
          </div>
        </div>
      </div>

      {/* جدول الدائنين */}
      <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-white/5">
              <tr>
                <th className="text-right py-4 px-6 text-gray-300 font-semibold">العميل</th>
                <th className="text-right py-4 px-6 text-gray-300 font-semibold">الهاتف</th>
                <th className="text-right py-4 px-6 text-gray-300 font-semibold">الرصيد</th>
                <th className="text-right py-4 px-6 text-gray-300 font-semibold">آخر إيداع</th>
                <th className="text-right py-4 px-6 text-gray-300 font-semibold">مستوى الولاء</th>
                <th className="text-center py-4 px-6 text-gray-300 font-semibold">الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr>
                  <td colSpan={6} className="text-center py-12">
                    <div className="flex flex-col items-center space-y-4">
                      <Loader className="w-8 h-8 text-green-400 mx-auto mb-4 animate-spin" />
                      <p className="text-gray-400">جاري تحميل العملاء الدائنين...</p>
                    </div>
                  </td>
                </tr>
              ) : filteredCreditors.length === 0 ? (
                <tr>
                  <td colSpan={6} className="text-center py-12">
                    <div className="flex flex-col items-center space-y-4">
                      <DollarSign className="w-16 h-16 text-gray-400" />
                      <p className="text-gray-400 text-lg">لا توجد أرصدة إيجابية</p>
                      <p className="text-gray-500 text-sm">لا يوجد عملاء بأرصدة دائنة حالياً</p>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredCreditors.map((creditor) => (
                  <tr key={creditor.id} className="border-t border-white/10 hover:bg-white/5 transition-all duration-300">
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <div className="bg-gradient-to-r from-green-500 to-emerald-600 w-10 h-10 rounded-full flex items-center justify-center">
                          <span className="text-white font-bold text-sm">
                            {creditor.name.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <p className="text-white font-semibold">{creditor.name}</p>
                          <p className="text-gray-400 text-sm">{creditor.address}</p>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-2 space-x-reverse text-gray-300">
                        <Phone className="w-4 h-4" />
                        <span>{creditor.phone}</span>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <TrendingUp className="w-4 h-4 text-green-400" />
                        <span className="font-bold text-green-400 text-lg">
                          {formatCurrency(creditor.balance)}
                        </span>
                      </div>
                    </td>
                    <td className="py-4 px-6 text-gray-300">
                      {formatDate(creditor.updated_at)}
                    </td>
                    <td className="py-4 px-6">
                      <div className="inline-flex items-center space-x-2 space-x-reverse px-3 py-1 rounded-full border bg-green-500/20 border-green-500/30">
                        <Star className="w-4 h-4 text-green-400" />
                        <span className="text-sm font-semibold text-green-400">
                          نشط
                        </span>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center justify-center space-x-2 space-x-reverse">
                        <button
                          onClick={() => {
                            setSelectedCreditor(creditor);
                            setShowDetailsModal(true);
                          }}
                          className="bg-blue-500/20 hover:bg-blue-500/30 p-2 rounded-lg transition-all duration-300 hover:scale-110"
                          title="عرض التفاصيل"
                        >
                          <Eye className="w-4 h-4 text-blue-400" />
                        </button>

                        <button
                          className="bg-green-500/20 hover:bg-green-500/30 p-2 rounded-lg transition-all duration-300 hover:scale-110"
                          title="كشف حساب"
                        >
                          <FileText className="w-4 h-4 text-green-400" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* مودال تفاصيل الدائن */}
      {showDetailsModal && selectedCreditor && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl border border-white/20 w-full max-w-2xl shadow-2xl overflow-hidden">
            <div className="p-6 border-b border-white/10">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold text-white flex items-center space-x-3 space-x-reverse">
                  <Eye className="w-6 h-6 text-green-400" />
                  <span>تفاصيل العميل</span>
                </h2>
                <button
                  onClick={() => {
                    setShowDetailsModal(false);
                    setSelectedCreditor(null);
                  }}
                  className="text-gray-400 hover:text-white transition-colors duration-300"
                >
                  <XCircle className="w-6 h-6" />
                </button>
              </div>
            </div>

            <div className="p-6">
              <div className="bg-white/5 rounded-xl p-4 mb-6">
                <div className="flex items-center space-x-4 space-x-reverse mb-4">
                  <div className="bg-gradient-to-r from-green-500 to-emerald-600 w-12 h-12 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-lg">
                      {selectedCreditor.name.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <p className="text-white font-bold text-lg">{selectedCreditor.name}</p>
                    <p className="text-gray-400 text-sm">عميل دائن</p>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-3 space-x-reverse text-gray-300">
                    <Phone className="w-4 h-4" />
                    <span>{selectedCreditor.phone}</span>
                  </div>

                  <div className="flex items-center space-x-3 space-x-reverse text-gray-300">
                    <MapPin className="w-4 h-4" />
                    <span>{selectedCreditor.address}</span>
                  </div>

                  <div className="flex items-center space-x-3 space-x-reverse text-green-400">
                    <DollarSign className="w-4 h-4" />
                    <span className="font-bold">{formatCurrency(selectedCreditor.balance)}</span>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <button
                  onClick={() => {
                    setShowDetailsModal(false);
                    setSelectedCreditor(null);
                  }}
                  className="bg-white/10 border border-white/20 hover:border-white/40 px-6 py-3 rounded-xl font-bold text-white transition-all duration-300"
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};



export default Creditors;
