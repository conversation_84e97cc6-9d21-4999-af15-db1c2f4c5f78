@echo off
title نظام نقطة البيع - كشير توسار
color 0A

echo.
echo ========================================
echo    🏪 نظام نقطة البيع - كشير توسار
echo ========================================
echo.

echo 📡 تشغيل الخادم الخلفي...
cd backend
start "Backend Server" cmd /k "node server.js"
cd ..

echo ⏳ انتظار 3 ثوان...
timeout /t 3 /nobreak > nul

echo 🌐 تشغيل الواجهة الأمامية...
start "Frontend" cmd /k "npm run dev"

echo.
echo ✅ تم تشغيل النظام!
echo.
echo 📍 الروابط:
echo   🔗 الخادم الخلفي: http://localhost:5002
echo   🔗 الواجهة الأمامية: http://localhost:5173
echo.
echo 💡 ستفتح نافذتان - لا تغلقهما!
echo.
pause
