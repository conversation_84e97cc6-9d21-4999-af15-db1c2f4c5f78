import React, { useState } from 'react';
import {
  <PERSON>er,
  <PERSON><PERSON><PERSON>,
  Wifi,
  Usb,
  Bluetooth,
  TestTube,
  Save,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Monitor,
  FileText,
  Zap,
  Volume2,
  VolumeX,
  Info
} from 'lucide-react';
import { useApp } from '../context/AppContext';

const PrinterSettings: React.FC = () => {
  const { printerSettings, updatePrinterSettings } = useApp();
  const [activeTab, setActiveTab] = useState('connection');
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [testPrintStatus, setTestPrintStatus] = useState<'idle' | 'printing' | 'success' | 'error'>('idle');

  const [formData, setFormData] = useState({
    // إعدادات الاتصال
    connectionType: printerSettings?.connectionType || 'usb',
    printerName: printerSettings?.printerName || '',
    ipAddress: printerSettings?.ipAddress || '',
    port: printerSettings?.port || 9100,
    bluetoothAddress: printerSettings?.bluetoothAddress || '',

    // إعدادات الطباعة
    paperWidth: printerSettings?.paperWidth || 58,
    paperType: printerSettings?.paperType || 'thermal',
    printDensity: printerSettings?.printDensity || 'medium',
    printSpeed: printerSettings?.printSpeed || 'medium',

    // إعدادات الفاتورة
    printLogo: printerSettings?.printLogo || true,
    printHeader: printerSettings?.printHeader || true,
    printFooter: printerSettings?.printFooter || true,
    printBarcode: printerSettings?.printBarcode || true,
    printQRCode: printerSettings?.printQRCode || false,

    // إعدادات متقدمة
    autoOpenCashDrawer: printerSettings?.autoOpenCashDrawer || false,
    printSound: printerSettings?.printSound || true,
    copies: printerSettings?.copies || 1,
    cutPaper: printerSettings?.cutPaper || true,

    // نص مخصص
    headerText: printerSettings?.headerText || '',
    footerText: printerSettings?.footerText || 'شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى',

    // إعدادات الخط
    fontSize: printerSettings?.fontSize || 'medium',
    fontFamily: printerSettings?.fontFamily || 'arial',
    lineSpacing: printerSettings?.lineSpacing || 'normal'
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    const newValue = type === 'checkbox' ? (e.target as HTMLInputElement).checked : value;

    setFormData(prev => ({ ...prev, [name]: newValue }));
    setUnsavedChanges(true);
  };

  const handleSave = () => {
    updatePrinterSettings(formData);
    setUnsavedChanges(false);
    alert('تم حفظ إعدادات الطابعة بنجاح!');
  };

  const handleTestPrint = async () => {
    setTestPrintStatus('printing');

    // محاكاة طباعة تجريبية
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      setTestPrintStatus('success');
      setTimeout(() => setTestPrintStatus('idle'), 3000);
    } catch (error) {
      setTestPrintStatus('error');
      setTimeout(() => setTestPrintStatus('idle'), 3000);
    }
  };

  const tabs = [
    { id: 'connection', name: 'الاتصال', icon: Wifi },
    { id: 'print', name: 'الطباعة', icon: Printer },
    { id: 'receipt', name: 'الفاتورة', icon: FileText },
    { id: 'advanced', name: 'متقدم', icon: Settings }
  ];

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white mb-2">إعدادات الطابعة</h1>
          <p className="text-slate-400">إدارة إعدادات الطابعة والفواتير</p>
        </div>

        <div className="flex items-center space-x-reverse space-x-4">
          {unsavedChanges && (
            <div className="flex items-center space-x-reverse space-x-2 text-yellow-400">
              <AlertTriangle className="w-4 h-4" />
              <span className="text-sm">تغييرات غير محفوظة</span>
            </div>
          )}

          <button
            onClick={handleTestPrint}
            disabled={testPrintStatus === 'printing'}
            className="bg-green-600 hover:bg-green-700 disabled:bg-slate-600 px-4 py-2 rounded-lg flex items-center space-x-reverse space-x-2 transition-colors"
          >
            {testPrintStatus === 'printing' ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : testPrintStatus === 'success' ? (
              <CheckCircle className="w-4 h-4" />
            ) : testPrintStatus === 'error' ? (
              <XCircle className="w-4 h-4" />
            ) : (
              <TestTube className="w-4 h-4" />
            )}
            <span>
              {testPrintStatus === 'printing' ? 'جاري الطباعة...' :
               testPrintStatus === 'success' ? 'تم بنجاح' :
               testPrintStatus === 'error' ? 'فشل الاختبار' : 'طباعة تجريبية'}
            </span>
          </button>

          <button
            onClick={handleSave}
            disabled={!unsavedChanges}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-slate-600 disabled:cursor-not-allowed px-6 py-2 rounded-lg flex items-center space-x-reverse space-x-2 transition-colors"
          >
            <Save className="w-4 h-4" />
            <span>حفظ الإعدادات</span>
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-slate-800 rounded-xl border border-slate-700 p-4">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-reverse space-x-3 px-4 py-3 rounded-lg transition-colors ${
                      activeTab === tab.id
                        ? 'bg-blue-600 text-white'
                        : 'text-slate-300 hover:bg-slate-700 hover:text-white'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{tab.name}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Printer Status */}
          <div className="bg-slate-800 rounded-xl border border-slate-700 p-4 mt-4">
            <h3 className="text-white font-medium mb-3">حالة الطابعة</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-slate-400 text-sm">الحالة</span>
                <div className="flex items-center space-x-reverse space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-green-400 text-sm">متصلة</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-slate-400 text-sm">الورق</span>
                <span className="text-white text-sm">متوفر</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-slate-400 text-sm">الحبر</span>
                <span className="text-white text-sm">جيد</span>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          <div className="bg-slate-800 rounded-xl border border-slate-700 p-6">
            {/* Connection Settings */}
            {activeTab === 'connection' && (
              <div className="space-y-6">
                <div className="flex items-center space-x-reverse space-x-3 mb-6">
                  <Wifi className="w-6 h-6 text-blue-400" />
                  <h2 className="text-xl font-bold text-white">إعدادات الاتصال</h2>
                </div>

                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-3">نوع الاتصال</label>
                    <div className="grid grid-cols-3 gap-4">
                      <label className="relative">
                        <input
                          type="radio"
                          name="connectionType"
                          value="usb"
                          checked={formData.connectionType === 'usb'}
                          onChange={handleInputChange}
                          className="sr-only peer"
                        />
                        <div className="p-4 bg-slate-700 border-2 border-slate-600 rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-500/10">
                          <div className="text-center">
                            <Usb className="w-8 h-8 text-slate-400 mx-auto mb-2" />
                            <span className="text-white text-sm">USB</span>
                          </div>
                        </div>
                      </label>

                      <label className="relative">
                        <input
                          type="radio"
                          name="connectionType"
                          value="network"
                          checked={formData.connectionType === 'network'}
                          onChange={handleInputChange}
                          className="sr-only peer"
                        />
                        <div className="p-4 bg-slate-700 border-2 border-slate-600 rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-500/10">
                          <div className="text-center">
                            <Wifi className="w-8 h-8 text-slate-400 mx-auto mb-2" />
                            <span className="text-white text-sm">شبكة</span>
                          </div>
                        </div>
                      </label>

                      <label className="relative">
                        <input
                          type="radio"
                          name="connectionType"
                          value="bluetooth"
                          checked={formData.connectionType === 'bluetooth'}
                          onChange={handleInputChange}
                          className="sr-only peer"
                        />
                        <div className="p-4 bg-slate-700 border-2 border-slate-600 rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-500/10">
                          <div className="text-center">
                            <Bluetooth className="w-8 h-8 text-slate-400 mx-auto mb-2" />
                            <span className="text-white text-sm">بلوتوث</span>
                          </div>
                        </div>
                      </label>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">اسم الطابعة</label>
                      <input
                        type="text"
                        name="printerName"
                        value={formData.printerName}
                        onChange={handleInputChange}
                        className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                        placeholder="اسم الطابعة"
                      />
                    </div>

                    {formData.connectionType === 'network' && (
                      <>
                        <div>
                          <label className="block text-sm font-medium text-slate-300 mb-2">عنوان IP</label>
                          <input
                            type="text"
                            name="ipAddress"
                            value={formData.ipAddress}
                            onChange={handleInputChange}
                            className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                            placeholder="*************"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-slate-300 mb-2">المنفذ</label>
                          <input
                            type="number"
                            name="port"
                            value={formData.port}
                            onChange={handleInputChange}
                            className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                            placeholder="9100"
                          />
                        </div>
                      </>
                    )}

                    {formData.connectionType === 'bluetooth' && (
                      <div>
                        <label className="block text-sm font-medium text-slate-300 mb-2">عنوان البلوتوث</label>
                        <input
                          type="text"
                          name="bluetoothAddress"
                          value={formData.bluetoothAddress}
                          onChange={handleInputChange}
                          className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                          placeholder="00:11:22:33:44:55"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Print Settings */}
            {activeTab === 'print' && (
              <div className="space-y-6">
                <div className="flex items-center space-x-reverse space-x-3 mb-6">
                  <Printer className="w-6 h-6 text-green-400" />
                  <h2 className="text-xl font-bold text-white">إعدادات الطباعة</h2>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">عرض الورق (مم)</label>
                    <select
                      name="paperWidth"
                      value={formData.paperWidth}
                      onChange={handleInputChange}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:border-blue-500 focus:outline-none"
                    >
                      <option value={58}>58 مم</option>
                      <option value={80}>80 مم</option>
                      <option value={110}>110 مم</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">نوع الورق</label>
                    <select
                      name="paperType"
                      value={formData.paperType}
                      onChange={handleInputChange}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:border-blue-500 focus:outline-none"
                    >
                      <option value="thermal">حراري</option>
                      <option value="normal">عادي</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">كثافة الطباعة</label>
                    <select
                      name="printDensity"
                      value={formData.printDensity}
                      onChange={handleInputChange}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:border-blue-500 focus:outline-none"
                    >
                      <option value="light">خفيف</option>
                      <option value="medium">متوسط</option>
                      <option value="dark">داكن</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">سرعة الطباعة</label>
                    <select
                      name="printSpeed"
                      value={formData.printSpeed}
                      onChange={handleInputChange}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:border-blue-500 focus:outline-none"
                    >
                      <option value="slow">بطيء</option>
                      <option value="medium">متوسط</option>
                      <option value="fast">سريع</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">حجم الخط</label>
                    <select
                      name="fontSize"
                      value={formData.fontSize}
                      onChange={handleInputChange}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:border-blue-500 focus:outline-none"
                    >
                      <option value="small">صغير</option>
                      <option value="medium">متوسط</option>
                      <option value="large">كبير</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">نوع الخط</label>
                    <select
                      name="fontFamily"
                      value={formData.fontFamily}
                      onChange={handleInputChange}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:border-blue-500 focus:outline-none"
                    >
                      <option value="arial">Arial</option>
                      <option value="courier">Courier</option>
                      <option value="times">Times</option>
                    </select>
                  </div>
                </div>
              </div>
            )}

            {/* Receipt Settings */}
            {activeTab === 'receipt' && (
              <div className="space-y-6">
                <div className="flex items-center space-x-reverse space-x-3 mb-6">
                  <FileText className="w-6 h-6 text-purple-400" />
                  <h2 className="text-xl font-bold text-white">إعدادات الفاتورة</h2>
                </div>

                <div className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-slate-700 rounded-lg">
                      <div>
                        <h3 className="text-white font-medium">طباعة الشعار</h3>
                        <p className="text-slate-400 text-sm">إضافة شعار المتجر في أعلى الفاتورة</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="printLogo"
                          checked={formData.printLogo}
                          onChange={handleInputChange}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-slate-700 rounded-lg">
                      <div>
                        <h3 className="text-white font-medium">طباعة الرأسية</h3>
                        <p className="text-slate-400 text-sm">معلومات المتجر في أعلى الفاتورة</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="printHeader"
                          checked={formData.printHeader}
                          onChange={handleInputChange}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-slate-700 rounded-lg">
                      <div>
                        <h3 className="text-white font-medium">طباعة التذييل</h3>
                        <p className="text-slate-400 text-sm">رسالة شكر في نهاية الفاتورة</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="printFooter"
                          checked={formData.printFooter}
                          onChange={handleInputChange}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-slate-700 rounded-lg">
                      <div>
                        <h3 className="text-white font-medium">طباعة الباركود</h3>
                        <p className="text-slate-400 text-sm">باركود رقم الفاتورة</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="printBarcode"
                          checked={formData.printBarcode}
                          onChange={handleInputChange}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-slate-700 rounded-lg">
                      <div>
                        <h3 className="text-white font-medium">طباعة QR Code</h3>
                        <p className="text-slate-400 text-sm">رمز QR لتفاصيل الفاتورة</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="printQRCode"
                          checked={formData.printQRCode}
                          onChange={handleInputChange}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">نص الرأسية المخصص</label>
                      <textarea
                        name="headerText"
                        value={formData.headerText}
                        onChange={handleInputChange}
                        rows={3}
                        className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none resize-none"
                        placeholder="نص إضافي في أعلى الفاتورة"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">نص التذييل المخصص</label>
                      <textarea
                        name="footerText"
                        value={formData.footerText}
                        onChange={handleInputChange}
                        rows={3}
                        className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none resize-none"
                        placeholder="رسالة شكر أو معلومات إضافية"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Advanced Settings */}
            {activeTab === 'advanced' && (
              <div className="space-y-6">
                <div className="flex items-center space-x-reverse space-x-3 mb-6">
                  <Settings className="w-6 h-6 text-orange-400" />
                  <h2 className="text-xl font-bold text-white">الإعدادات المتقدمة</h2>
                </div>

                <div className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-slate-700 rounded-lg">
                      <div>
                        <h3 className="text-white font-medium">فتح درج النقود تلقائياً</h3>
                        <p className="text-slate-400 text-sm">فتح الدرج عند إتمام البيع</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="autoOpenCashDrawer"
                          checked={formData.autoOpenCashDrawer}
                          onChange={handleInputChange}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-slate-700 rounded-lg">
                      <div>
                        <h3 className="text-white font-medium">صوت الطباعة</h3>
                        <p className="text-slate-400 text-sm">تشغيل صوت عند الطباعة</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="printSound"
                          checked={formData.printSound}
                          onChange={handleInputChange}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-slate-700 rounded-lg">
                      <div>
                        <h3 className="text-white font-medium">قطع الورق تلقائياً</h3>
                        <p className="text-slate-400 text-sm">قطع الورق بعد الطباعة</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          name="cutPaper"
                          checked={formData.cutPaper}
                          onChange={handleInputChange}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">عدد النسخ</label>
                      <input
                        type="number"
                        min="1"
                        max="5"
                        name="copies"
                        value={formData.copies}
                        onChange={handleInputChange}
                        className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white placeholder-slate-400 focus:border-blue-500 focus:outline-none"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">تباعد الأسطر</label>
                      <select
                        name="lineSpacing"
                        value={formData.lineSpacing}
                        onChange={handleInputChange}
                        className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:border-blue-500 focus:outline-none"
                      >
                        <option value="tight">ضيق</option>
                        <option value="normal">عادي</option>
                        <option value="loose">واسع</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PrinterSettings;
