@echo off
chcp 65001 >nul
title Kasheer Toosar - POS System
color 0A

echo.
echo ========================================
echo    Kasheer Toosar - POS System
echo ========================================
echo.
echo Starting system...
echo.

REM Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js not installed!
    echo Please install Node.js first
    echo https://nodejs.org
    pause
    exit /b 1
)

REM Go to project directory
cd /d "%~dp0"

REM Check required files
if not exist "backend\server.js" (
    echo Error: Server files not found!
    pause
    exit /b 1
)

if not exist "package.json" (
    echo Error: package.json not found!
    pause
    exit /b 1
)

REM Install dependencies if needed
if not exist "node_modules\vite" (
    echo Installing dependencies...
    call npm install
    if %errorlevel% neq 0 (
        echo Error installing dependencies!
        pause
        exit /b 1
    )
)

REM Build frontend if not exists
if not exist "dist\index.html" (
    echo Building frontend...
    call npm run build
    if %errorlevel% neq 0 (
        echo Error building frontend!
        pause
        exit /b 1
    )
)

REM Start server in background
echo Starting server...
start /min "Kasheer Server" node backend\server.js

REM Wait for server to start
echo Waiting for server...
timeout /t 5 /nobreak >nul

REM Check server status
:check_server
echo Checking server...
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:5003' -TimeoutSec 2 -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorlevel% equ 0 (
    echo Server is running successfully!
    goto open_browser
)

REM Retry
timeout /t 2 /nobreak >nul
goto check_server

:open_browser
echo Opening browser...
start http://localhost:5003

echo.
echo ========================================
echo   System is now running!
echo   URL: http://localhost:5003
echo ========================================
echo.
echo To stop the system, close this window
echo.

REM Monitor system
:monitor
timeout /t 30 /nobreak >nul
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:5003' -TimeoutSec 2 -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorlevel% neq 0 (
    echo Warning: Server stopped!
    echo Restarting server...
    start /min "Kasheer Server" node backend\server.js
    timeout /t 5 /nobreak >nul
)
goto monitor
