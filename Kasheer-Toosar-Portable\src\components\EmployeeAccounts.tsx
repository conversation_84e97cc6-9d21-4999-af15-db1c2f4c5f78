import React, { useState, useEffect } from 'react';
import {
  UserCheck,
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  Shield,
  Shield<PERSON>heck,
  Crown,
  Star,
  Award,
  Clock,
  Calendar,
  Phone,
  Mail,
  MapPin,
  DollarSign,
  TrendingUp,
  Users,
  Activity,
  Settings,
  Lock,
  Unlock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Filter,
  Download,
  RefreshCw,
  MoreVertical,
  Briefcase,
  Target,
  Zap,
  Gift,
  Bell,
  MessageSquare
} from 'lucide-react';

interface Employee {
  id: string;
  name: string;
  email: string;
  phone: string;
  position: string;
  department: string;
  salary: number;
  hireDate: Date;
  status: 'active' | 'inactive' | 'suspended';
  role: 'admin' | 'manager' | 'cashier' | 'inventory' | 'sales';
  permissions: string[];
  avatar?: string;
  address: string;
  emergencyContact: string;
  lastLogin?: Date;
  totalSales: number;
  performance: 'excellent' | 'good' | 'average' | 'poor';
  workHours: number;
  overtimeHours: number;
  commissionRate: number;
  notes: string;
}

interface EmployeeStats {
  totalEmployees: number;
  activeEmployees: number;
  inactiveEmployees: number;
  totalSalaries: number;
  averageSalary: number;
  topPerformer: Employee | null;
}

const EmployeeAccounts: React.FC = () => {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showPermissionsModal, setShowPermissionsModal] = useState(false);
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive' | 'suspended'>('all');
  const [filterRole, setFilterRole] = useState<'all' | 'admin' | 'manager' | 'cashier' | 'inventory' | 'sales'>('all');
  const [sortBy, setSortBy] = useState<'name' | 'salary' | 'hireDate' | 'performance'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [newEmployee, setNewEmployee] = useState({
    name: '',
    email: '',
    phone: '',
    position: '',
    department: '',
    salary: '',
    role: 'cashier' as 'admin' | 'manager' | 'cashier' | 'inventory' | 'sales',
    address: '',
    emergencyContact: '',
    commissionRate: '',
    notes: ''
  });

  // حساب الإحصائيات
  const stats: EmployeeStats = {
    totalEmployees: employees.length,
    activeEmployees: employees.filter(emp => emp.status === 'active').length,
    inactiveEmployees: employees.filter(emp => emp.status !== 'active').length,
    totalSalaries: employees.reduce((sum, emp) => sum + emp.salary, 0),
    averageSalary: employees.length > 0 ? employees.reduce((sum, emp) => sum + emp.salary, 0) / employees.length : 0,
    topPerformer: employees.find(emp => emp.performance === 'excellent') || null
  };

  // فلترة وترتيب الموظفين
  const filteredEmployees = employees
    .filter(employee => {
      const matchesSearch = employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           employee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           employee.phone.includes(searchTerm) ||
                           employee.position.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = filterStatus === 'all' || employee.status === filterStatus;
      const matchesRole = filterRole === 'all' || employee.role === filterRole;

      return matchesSearch && matchesStatus && matchesRole;
    })
    .sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'salary':
          aValue = a.salary;
          bValue = b.salary;
          break;
        case 'hireDate':
          aValue = a.hireDate.getTime();
          bValue = b.hireDate.getTime();
          break;
        case 'performance':
          const performanceOrder = { excellent: 4, good: 3, average: 2, poor: 1 };
          aValue = performanceOrder[a.performance];
          bValue = performanceOrder[b.performance];
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

  const handleAddEmployee = () => {
    if (!newEmployee.name.trim() || !newEmployee.email.trim() || !newEmployee.phone.trim() || !newEmployee.position.trim() || !newEmployee.salary.trim()) {
      alert('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    const employee: Employee = {
      id: Date.now().toString(),
      name: newEmployee.name.trim(),
      email: newEmployee.email.trim(),
      phone: newEmployee.phone.trim(),
      position: newEmployee.position.trim(),
      department: newEmployee.department.trim() || 'عام',
      salary: parseFloat(newEmployee.salary),
      hireDate: new Date(),
      status: 'active',
      role: newEmployee.role,
      permissions: getDefaultPermissions(newEmployee.role),
      address: newEmployee.address.trim(),
      emergencyContact: newEmployee.emergencyContact.trim(),
      totalSales: 0,
      performance: 'average',
      workHours: 0,
      overtimeHours: 0,
      commissionRate: parseFloat(newEmployee.commissionRate) || 0,
      notes: newEmployee.notes.trim()
    };

    setEmployees([...employees, employee]);
    setNewEmployee({
      name: '',
      email: '',
      phone: '',
      position: '',
      department: '',
      salary: '',
      role: 'cashier',
      address: '',
      emergencyContact: '',
      commissionRate: '',
      notes: ''
    });
    setShowAddModal(false);
    alert('تم إضافة الموظف بنجاح!');
  };

  const getDefaultPermissions = (role: string): string[] => {
    switch (role) {
      case 'admin':
        return ['all'];
      case 'manager':
        return ['sales', 'inventory', 'customers', 'reports'];
      case 'cashier':
        return ['sales', 'customers'];
      case 'inventory':
        return ['inventory', 'products'];
      case 'sales':
        return ['sales', 'customers'];
      default:
        return ['sales'];
    }
  };

  const formatCurrency = (amount: number) => `${amount.toFixed(2)} دج`;
  const formatDate = (date: Date) => date.toLocaleDateString('ar-DZ');
  const formatDateTime = (date: Date) => `${date.toLocaleDateString('ar-DZ')} ${date.toLocaleTimeString('ar-DZ')}`;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400 bg-green-500/20';
      case 'inactive': return 'text-gray-400 bg-gray-500/20';
      case 'suspended': return 'text-red-400 bg-red-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'inactive': return 'غير نشط';
      case 'suspended': return 'معلق';
      default: return 'غير محدد';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'text-purple-400 bg-purple-500/20';
      case 'manager': return 'text-blue-400 bg-blue-500/20';
      case 'cashier': return 'text-green-400 bg-green-500/20';
      case 'inventory': return 'text-yellow-400 bg-yellow-500/20';
      case 'sales': return 'text-pink-400 bg-pink-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const getRoleText = (role: string) => {
    switch (role) {
      case 'admin': return 'مدير النظام';
      case 'manager': return 'مدير';
      case 'cashier': return 'كاشير';
      case 'inventory': return 'مخازن';
      case 'sales': return 'مبيعات';
      default: return 'غير محدد';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin': return <Crown className="w-4 h-4" />;
      case 'manager': return <Shield className="w-4 h-4" />;
      case 'cashier': return <DollarSign className="w-4 h-4" />;
      case 'inventory': return <Briefcase className="w-4 h-4" />;
      case 'sales': return <TrendingUp className="w-4 h-4" />;
      default: return <UserCheck className="w-4 h-4" />;
    }
  };

  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'excellent': return 'text-green-400 bg-green-500/20';
      case 'good': return 'text-blue-400 bg-blue-500/20';
      case 'average': return 'text-yellow-400 bg-yellow-500/20';
      case 'poor': return 'text-red-400 bg-red-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const getPerformanceText = (performance: string) => {
    switch (performance) {
      case 'excellent': return 'ممتاز';
      case 'good': return 'جيد';
      case 'average': return 'متوسط';
      case 'poor': return 'ضعيف';
      default: return 'غير محدد';
    }
  };

  const getPerformanceIcon = (performance: string) => {
    switch (performance) {
      case 'excellent': return <Award className="w-4 h-4" />;
      case 'good': return <Star className="w-4 h-4" />;
      case 'average': return <Target className="w-4 h-4" />;
      case 'poor': return <AlertTriangle className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="bg-gradient-to-r from-purple-500 to-pink-600 p-3 rounded-2xl shadow-lg animate-pulse">
              <UserCheck className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">👥 حسابات الموظفين</h1>
              <p className="text-gray-300">إدارة شاملة لفريق العمل والصلاحيات</p>
            </div>
          </div>

          <div className="flex items-center space-x-4 space-x-reverse">
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-gradient-to-r from-green-500 to-emerald-600 hover:scale-105 transform transition-all duration-300 px-6 py-3 rounded-xl font-bold text-white shadow-2xl hover:shadow-green-500/25 flex items-center space-x-2 space-x-reverse"
            >
              <Plus className="w-5 h-5" />
              <span>إضافة موظف</span>
            </button>

            <button className="bg-white/10 backdrop-blur-lg border border-white/20 hover:border-white/40 px-4 py-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse text-white">
              <Download className="w-5 h-5" />
              <span>تصدير</span>
            </button>

            <button className="bg-white/10 backdrop-blur-lg border border-white/20 hover:border-white/40 px-4 py-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse text-white">
              <RefreshCw className="w-5 h-5" />
              <span>تحديث</span>
            </button>
          </div>
        </div>

        {/* إحصائيات الموظفين */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">إجمالي الموظفين</p>
                <p className="text-3xl font-bold text-white">{stats.totalEmployees}</p>
              </div>
              <div className="bg-purple-500/20 p-3 rounded-xl">
                <Users className="w-6 h-6 text-purple-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">الموظفين النشطين</p>
                <p className="text-3xl font-bold text-green-400">{stats.activeEmployees}</p>
              </div>
              <div className="bg-green-500/20 p-3 rounded-xl">
                <CheckCircle className="w-6 h-6 text-green-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">إجمالي الرواتب</p>
                <p className="text-3xl font-bold text-blue-400">
                  {formatCurrency(stats.totalSalaries)}
                </p>
              </div>
              <div className="bg-blue-500/20 p-3 rounded-xl">
                <DollarSign className="w-6 h-6 text-blue-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">متوسط الراتب</p>
                <p className="text-3xl font-bold text-yellow-400">
                  {formatCurrency(stats.averageSalary)}
                </p>
              </div>
              <div className="bg-yellow-500/20 p-3 rounded-xl">
                <TrendingUp className="w-6 h-6 text-yellow-400" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* شريط البحث والفلاتر */}
      <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 mb-8">
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-4">
          {/* البحث */}
          <div className="lg:col-span-2">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="البحث بالاسم، البريد، الهاتف، أو المنصب..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-white/10 border border-white/20 rounded-xl px-12 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-400 transition-all duration-300"
              />
            </div>
          </div>

          {/* فلتر الحالة */}
          <div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="w-full bg-purple-900/30 border border-purple-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-purple-400 transition-all duration-300 cursor-pointer hover:bg-purple-800/40"
              style={{
                backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23a855f7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'left 12px center',
                backgroundSize: '16px'
              }}
            >
              <option value="all" className="bg-purple-900 text-white">جميع الحالات</option>
              <option value="active" className="bg-purple-900 text-white">نشط</option>
              <option value="inactive" className="bg-purple-900 text-white">غير نشط</option>
              <option value="suspended" className="bg-purple-900 text-white">معلق</option>
            </select>
          </div>

          {/* فلتر الدور */}
          <div>
            <select
              value={filterRole}
              onChange={(e) => setFilterRole(e.target.value as any)}
              className="w-full bg-purple-900/30 border border-purple-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-purple-400 transition-all duration-300 cursor-pointer hover:bg-purple-800/40"
              style={{
                backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23a855f7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'left 12px center',
                backgroundSize: '16px'
              }}
            >
              <option value="all" className="bg-purple-900 text-white">جميع الأدوار</option>
              <option value="admin" className="bg-purple-900 text-white">مدير النظام</option>
              <option value="manager" className="bg-purple-900 text-white">مدير</option>
              <option value="cashier" className="bg-purple-900 text-white">كاشير</option>
              <option value="inventory" className="bg-purple-900 text-white">مخازن</option>
              <option value="sales" className="bg-purple-900 text-white">مبيعات</option>
            </select>
          </div>

          {/* الترتيب */}
          <div>
            <select
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => {
                const [field, order] = e.target.value.split('-');
                setSortBy(field as any);
                setSortOrder(order as any);
              }}
              className="w-full bg-purple-900/30 border border-purple-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-purple-400 transition-all duration-300 cursor-pointer hover:bg-purple-800/40"
              style={{
                backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23a855f7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'left 12px center',
                backgroundSize: '16px'
              }}
            >
              <option value="name-asc" className="bg-purple-900 text-white">الاسم (أ-ي)</option>
              <option value="name-desc" className="bg-purple-900 text-white">الاسم (ي-أ)</option>
              <option value="salary-desc" className="bg-purple-900 text-white">الراتب (الأعلى)</option>
              <option value="salary-asc" className="bg-purple-900 text-white">الراتب (الأقل)</option>
              <option value="hireDate-desc" className="bg-purple-900 text-white">تاريخ التوظيف (الأحدث)</option>
              <option value="hireDate-asc" className="bg-purple-900 text-white">تاريخ التوظيف (الأقدم)</option>
              <option value="performance-desc" className="bg-purple-900 text-white">الأداء (الأفضل)</option>
            </select>
          </div>
        </div>
      </div>

      {/* قائمة الموظفين */}
      <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 overflow-hidden">
        {filteredEmployees.length === 0 ? (
          <div className="text-center py-16">
            <UserCheck className="w-20 h-20 text-gray-400 mx-auto mb-6" />
            <h3 className="text-2xl font-bold text-white mb-4">
              {employees.length === 0 ? 'لا يوجد موظفين مسجلين' : 'لا توجد نتائج للبحث'}
            </h3>
            <p className="text-gray-400 mb-8">
              {employees.length === 0
                ? 'ابدأ بإضافة أول موظف في فريق العمل'
                : 'جرب تغيير معايير البحث أو الفلترة'
              }
            </p>
            {employees.length === 0 && (
              <button
                onClick={() => setShowAddModal(true)}
                className="bg-gradient-to-r from-purple-500 to-pink-600 hover:scale-105 transform transition-all duration-300 px-8 py-4 rounded-xl font-bold text-white shadow-2xl hover:shadow-purple-500/25 flex items-center space-x-2 space-x-reverse mx-auto"
              >
                <Plus className="w-6 h-6" />
                <span>إضافة أول موظف</span>
              </button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-white/5 border-b border-white/10">
                <tr>
                  <th className="text-right py-4 px-6 text-gray-300 font-semibold">الموظف</th>
                  <th className="text-right py-4 px-6 text-gray-300 font-semibold">المنصب</th>
                  <th className="text-right py-4 px-6 text-gray-300 font-semibold">الدور</th>
                  <th className="text-right py-4 px-6 text-gray-300 font-semibold">الراتب</th>
                  <th className="text-right py-4 px-6 text-gray-300 font-semibold">الأداء</th>
                  <th className="text-right py-4 px-6 text-gray-300 font-semibold">الحالة</th>
                  <th className="text-right py-4 px-6 text-gray-300 font-semibold">آخر دخول</th>
                  <th className="text-center py-4 px-6 text-gray-300 font-semibold">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredEmployees.map((employee, index) => (
                  <tr
                    key={employee.id}
                    className="border-b border-white/5 hover:bg-white/5 transition-all duration-300"
                  >
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-4 space-x-reverse">
                        <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center">
                          <span className="text-white font-bold text-lg">
                            {employee.name.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <p className="text-white font-semibold">{employee.name}</p>
                          <p className="text-gray-400 text-sm">{employee.email}</p>
                          <p className="text-gray-500 text-xs">{employee.phone}</p>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <p className="text-white font-medium">{employee.position}</p>
                      <p className="text-gray-400 text-sm">{employee.department}</p>
                    </td>
                    <td className="py-4 px-6">
                      <div className={`inline-flex items-center space-x-2 space-x-reverse px-3 py-1 rounded-full text-sm font-medium ${getRoleColor(employee.role)}`}>
                        {getRoleIcon(employee.role)}
                        <span>{getRoleText(employee.role)}</span>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <p className="text-white font-bold">{formatCurrency(employee.salary)}</p>
                      <p className="text-gray-400 text-sm">عمولة: {employee.commissionRate}%</p>
                    </td>
                    <td className="py-4 px-6">
                      <div className={`inline-flex items-center space-x-2 space-x-reverse px-3 py-1 rounded-full text-sm font-medium ${getPerformanceColor(employee.performance)}`}>
                        {getPerformanceIcon(employee.performance)}
                        <span>{getPerformanceText(employee.performance)}</span>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div className={`inline-flex items-center space-x-2 space-x-reverse px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(employee.status)}`}>
                        {employee.status === 'active' ? <CheckCircle className="w-4 h-4" /> :
                         employee.status === 'suspended' ? <XCircle className="w-4 h-4" /> :
                         <AlertTriangle className="w-4 h-4" />}
                        <span>{getStatusText(employee.status)}</span>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <p className="text-gray-300 text-sm">
                        {employee.lastLogin ? formatDateTime(employee.lastLogin) : 'لم يسجل دخول'}
                      </p>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center justify-center space-x-2 space-x-reverse">
                        <button
                          onClick={() => {
                            setSelectedEmployee(employee);
                            setShowDetailsModal(true);
                          }}
                          className="bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 p-2 rounded-lg transition-all duration-300 hover:scale-110"
                          title="عرض التفاصيل"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => {
                            setSelectedEmployee(employee);
                            setShowEditModal(true);
                          }}
                          className="bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-400 p-2 rounded-lg transition-all duration-300 hover:scale-110"
                          title="تعديل"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => {
                            setSelectedEmployee(employee);
                            setShowPermissionsModal(true);
                          }}
                          className="bg-purple-500/20 hover:bg-purple-500/30 text-purple-400 p-2 rounded-lg transition-all duration-300 hover:scale-110"
                          title="الصلاحيات"
                        >
                          <Shield className="w-4 h-4" />
                        </button>
                        <button
                          className="bg-red-500/20 hover:bg-red-500/30 text-red-400 p-2 rounded-lg transition-all duration-300 hover:scale-110"
                          title="حذف"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* نموذج إضافة موظف جديد */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 rounded-2xl border border-white/20 w-full max-w-4xl max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b border-white/20">
              <div className="flex items-center justify-between">
                <h3 className="text-2xl font-bold text-white flex items-center space-x-2 space-x-reverse">
                  <Plus className="w-6 h-6 text-green-400" />
                  <span>إضافة موظف جديد</span>
                </h3>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="text-gray-400 hover:text-white transition-colors duration-300"
                >
                  <XCircle className="w-8 h-8" />
                </button>
              </div>
            </div>

            <div className="p-6 overflow-y-auto max-h-[70vh]">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* المعلومات الأساسية */}
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2 space-x-reverse">
                    <UserCheck className="w-5 h-5 text-purple-400" />
                    <span>المعلومات الأساسية</span>
                  </h4>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      الاسم الكامل *
                    </label>
                    <input
                      type="text"
                      value={newEmployee.name}
                      onChange={(e) => setNewEmployee({...newEmployee, name: e.target.value})}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-400 transition-all duration-300"
                      placeholder="أدخل الاسم الكامل"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      البريد الإلكتروني *
                    </label>
                    <input
                      type="email"
                      value={newEmployee.email}
                      onChange={(e) => setNewEmployee({...newEmployee, email: e.target.value})}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-400 transition-all duration-300"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      رقم الهاتف *
                    </label>
                    <input
                      type="tel"
                      value={newEmployee.phone}
                      onChange={(e) => setNewEmployee({...newEmployee, phone: e.target.value})}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-400 transition-all duration-300"
                      placeholder="0555123456"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      العنوان
                    </label>
                    <input
                      type="text"
                      value={newEmployee.address}
                      onChange={(e) => setNewEmployee({...newEmployee, address: e.target.value})}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-400 transition-all duration-300"
                      placeholder="العنوان الكامل"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      جهة الاتصال في الطوارئ
                    </label>
                    <input
                      type="text"
                      value={newEmployee.emergencyContact}
                      onChange={(e) => setNewEmployee({...newEmployee, emergencyContact: e.target.value})}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-400 transition-all duration-300"
                      placeholder="اسم ورقم هاتف جهة الاتصال"
                    />
                  </div>
                </div>

                {/* معلومات العمل */}
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2 space-x-reverse">
                    <Briefcase className="w-5 h-5 text-blue-400" />
                    <span>معلومات العمل</span>
                  </h4>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      المنصب *
                    </label>
                    <input
                      type="text"
                      value={newEmployee.position}
                      onChange={(e) => setNewEmployee({...newEmployee, position: e.target.value})}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-400 transition-all duration-300"
                      placeholder="مثل: كاشير أول، مدير مبيعات"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      القسم
                    </label>
                    <input
                      type="text"
                      value={newEmployee.department}
                      onChange={(e) => setNewEmployee({...newEmployee, department: e.target.value})}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-400 transition-all duration-300"
                      placeholder="مثل: المبيعات، المخازن، الإدارة"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      الدور في النظام *
                    </label>
                    <select
                      value={newEmployee.role}
                      onChange={(e) => setNewEmployee({...newEmployee, role: e.target.value as any})}
                      className="w-full bg-purple-900/30 border border-purple-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-purple-400 transition-all duration-300 cursor-pointer hover:bg-purple-800/40"
                      style={{
                        backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23a855f7' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                        backgroundRepeat: 'no-repeat',
                        backgroundPosition: 'left 12px center',
                        backgroundSize: '16px'
                      }}
                    >
                      <option value="cashier" className="bg-purple-900 text-white">كاشير</option>
                      <option value="sales" className="bg-purple-900 text-white">مبيعات</option>
                      <option value="inventory" className="bg-purple-900 text-white">مخازن</option>
                      <option value="manager" className="bg-purple-900 text-white">مدير</option>
                      <option value="admin" className="bg-purple-900 text-white">مدير النظام</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      الراتب الشهري (دج) *
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      value={newEmployee.salary}
                      onChange={(e) => setNewEmployee({...newEmployee, salary: e.target.value})}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-400 transition-all duration-300"
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      نسبة العمولة (%)
                    </label>
                    <input
                      type="number"
                      step="0.1"
                      min="0"
                      max="100"
                      value={newEmployee.commissionRate}
                      onChange={(e) => setNewEmployee({...newEmployee, commissionRate: e.target.value})}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-400 transition-all duration-300"
                      placeholder="0.0"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      ملاحظات
                    </label>
                    <textarea
                      value={newEmployee.notes}
                      onChange={(e) => setNewEmployee({...newEmployee, notes: e.target.value})}
                      rows={3}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-400 transition-all duration-300 resize-none"
                      placeholder="ملاحظات إضافية عن الموظف..."
                    />
                  </div>
                </div>
              </div>

              <div className="flex space-x-4 space-x-reverse pt-6 mt-6 border-t border-white/20">
                <button
                  onClick={handleAddEmployee}
                  disabled={!newEmployee.name.trim() || !newEmployee.email.trim() || !newEmployee.phone.trim() || !newEmployee.position.trim() || !newEmployee.salary.trim()}
                  className="flex-1 bg-gradient-to-r from-green-500 to-emerald-600 hover:scale-105 transform transition-all duration-300 py-3 rounded-xl font-bold text-white shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2 space-x-reverse"
                >
                  <Plus className="w-5 h-5" />
                  <span>إضافة الموظف</span>
                </button>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="flex-1 bg-white/10 border border-white/20 hover:border-white/40 py-3 rounded-xl font-bold text-white transition-all duration-300 hover:scale-105"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmployeeAccounts;