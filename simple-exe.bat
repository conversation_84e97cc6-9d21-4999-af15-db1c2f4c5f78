@echo off
title Simple EXE Builder

echo Creating EXE file...

REM Install nexe
call npm install -g nexe

REM Create simple server file
echo const express = require('express'); > server-standalone.js
echo const path = require('path'); >> server-standalone.js
echo const { spawn } = require('child_process'); >> server-standalone.js
echo. >> server-standalone.js
echo const app = express(); >> server-standalone.js
echo const PORT = 5003; >> server-standalone.js
echo. >> server-standalone.js
echo app.use(express.static('dist')); >> server-standalone.js
echo app.use('/api', require('./backend/routes/customers')); >> server-standalone.js
echo app.use('/api', require('./backend/routes/products')); >> server-standalone.js
echo app.use('/api', require('./backend/routes/sales')); >> server-standalone.js
echo. >> server-standalone.js
echo app.listen(PORT, () =^> { >> server-standalone.js
echo   console.log('Kasheer Toosar started on http://localhost:' + PORT); >> server-standalone.js
echo   setTimeout(() =^> { >> server-standalone.js
echo     require('child_process').exec('start http://localhost:' + PORT); >> server-standalone.js
echo   }, 2000); >> server-standalone.js
echo }); >> server-standalone.js

REM Build frontend
call npm run build

REM Create EXE
call nexe server-standalone.js -o kasheer-toosar.exe

echo Done! Check kasheer-toosar.exe
pause
