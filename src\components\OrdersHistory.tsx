import React, { useState, useEffect } from 'react';
import {
  Search, Filter, Calendar, TrendingUp, TrendingDown,
  Package, DollarSign, Clock, User, Eye, Download,
  Printer, RefreshCw, BarChart3, PieChart, Activity,
  Zap, Star, Award, Target, Sparkles, ChevronDown,
  ChevronRight, MapPin, Phone, Mail, CreditCard,
  Banknote, Calculator, CheckCircle, XCircle, AlertCircle,
  ArrowUpRight, ArrowDownRight, Layers, Grid, List,
  SortAsc, SortDesc, MoreHorizontal, Edit, Trash2,
  FileText, Share2, Copy, ExternalLink, Maximize2,
  X, Send, MessageSquare, Archive, RotateCcw, Wallet
} from 'lucide-react';
import ReceiptModal from './ReceiptModal';
import ThermalReceipt from './ThermalReceipt';
import ProfessionalInvoice from './ProfessionalInvoice';
import { useApp } from '../context/AppContext';

// تم نقل واجهة Order إلى AppContext كـ Sale

const OrdersHistory: React.FC = () => {
  const { sales, getSalesByDateRange, settings } = useApp();
  const [filteredOrders, setFilteredOrders] = useState(sales);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('all');
  const [selectedDebtStatus, setSelectedDebtStatus] = useState('all');
  const [dateRange, setDateRange] = useState('today');
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'analytics'>('grid');
  const [sortBy, setSortBy] = useState<'date' | 'total' | 'customer'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);
  const [showReceiptModal, setShowReceiptModal] = useState(false);
  const [showProfessionalInvoice, setShowProfessionalInvoice] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  // معلومات المتجر من الإعدادات
  const storeInfo = {
    name: settings?.storeName || "متجر توسار الإلكتروني",
    address: settings?.storeAddress || "الجزائر - الجزائر العاصمة",
    phone: settings?.storePhone || "+213 XXX XXX XXX",
    email: settings?.storeEmail || "<EMAIL>",
    taxNumber: settings?.storeTaxNumber || "*********"
  };

  // تحديث البيانات من Context
  useEffect(() => {
    setFilteredOrders(sales);
  }, [sales]);

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  // Filter and sort orders
  useEffect(() => {
    let filtered = sales.filter(sale => {
      const matchesSearch =
        sale.saleNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (sale.customerName && sale.customerName.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesStatus = selectedStatus === 'all' || sale.status === selectedStatus;
      const matchesPayment = selectedPaymentMethod === 'all' || sale.paymentMethod === selectedPaymentMethod;

      // فلتر حالة الدين
      const debtStatus = getDebtStatus(sale);
      const matchesDebtStatus = selectedDebtStatus === 'all' || debtStatus === selectedDebtStatus;

      return matchesSearch && matchesStatus && matchesPayment && matchesDebtStatus;
    });

    // Sort orders
    filtered.sort((a, b) => {
      let comparison = 0;
      switch (sortBy) {
        case 'date':
          comparison = new Date(a.date).getTime() - new Date(b.date).getTime();
          break;
        case 'total':
          comparison = a.total - b.total;
          break;
        case 'customer':
          comparison = (a.customerName || '').localeCompare(b.customerName || '');
          break;
      }
      return sortOrder === 'asc' ? comparison : -comparison;
    });

    setFilteredOrders(filtered);
  }, [sales, searchTerm, selectedStatus, selectedPaymentMethod, selectedDebtStatus, sortBy, sortOrder]);

  // Calculate analytics
  const analytics = {
    totalOrders: filteredOrders.length,
    totalRevenue: filteredOrders.reduce((sum, sale) => sum + sale.total, 0),
    averageOrderValue: filteredOrders.length > 0 ? filteredOrders.reduce((sum, sale) => sum + sale.total, 0) / filteredOrders.length : 0,
    completedOrders: filteredOrders.filter(sale => sale.status === 'completed').length,
    pendingOrders: filteredOrders.filter(sale => sale.status === 'pending').length,
    cancelledOrders: filteredOrders.filter(sale => sale.status === 'cancelled').length
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-400 bg-green-500/20';
      case 'processing': return 'text-yellow-400 bg-yellow-500/20';
      case 'cancelled': return 'text-red-400 bg-red-500/20';
      case 'refunded': return 'text-purple-400 bg-purple-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'text-green-400 bg-green-500/20';
      case 'pending': return 'text-yellow-400 bg-yellow-500/20';
      case 'partial': return 'text-orange-400 bg-orange-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'cash': return <Banknote className="w-4 h-4" />;
      case 'card': return <CreditCard className="w-4 h-4" />;
      case 'credit': return <Clock className="w-4 h-4" />;
      case 'balance': return <Wallet className="w-4 h-4" />;
      case 'transfer': return <Send className="w-4 h-4" />;
      case 'mixed': return <Calculator className="w-4 h-4" />;
      default: return <DollarSign className="w-4 h-4" />;
    }
  };

  // دالة للحصول على حالة الدين
  const getDebtStatus = (sale: any) => {
    // استخدام البيانات الجديدة من الخادم
    if (sale.debtStatus) {
      return sale.debtStatus;
    }
    // للتوافق مع البيانات القديمة
    if (sale.payment_method === 'credit' || sale.is_debt_sale || sale.isDebtSale) {
      return sale.debt_status || 'pending';
    }
    return 'paid';
  };

  // دالة للحصول على لون حالة الدين
  const getDebtStatusColor = (debtStatus: string) => {
    switch (debtStatus) {
      case 'paid': return 'text-green-400 bg-green-500/20';
      case 'pending': return 'text-yellow-400 bg-yellow-500/20';
      case 'partial': return 'text-orange-400 bg-orange-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  // دالة للحصول على نص حالة الدين
  const getDebtStatusText = (debtStatus: string) => {
    switch (debtStatus) {
      case 'paid': return '✅ مدفوع';
      case 'pending': return '⏳ معلق';
      case 'partial': return '🔄 جزئي';
      default: return '❓ غير محدد';
    }
  };

  // وظائف التعامل مع المودالات
  const handleViewOrder = (sale: any) => {
    setSelectedOrder(sale);
    setShowOrderDetails(true);
  };

  const handlePrintReceipt = (sale: any) => {
    setSelectedOrder(sale);
    setShowReceiptModal(true);
  };

  const handlePrintProfessionalInvoice = (sale: any) => {
    setSelectedOrder(sale);
    setShowProfessionalInvoice(true);
  };

  const handleCloseOrderDetails = () => {
    setShowOrderDetails(false);
    setSelectedOrder(null);
  };

  const handleCloseReceipt = () => {
    setShowReceiptModal(false);
  };

  const handleCloseProfessionalInvoice = () => {
    setShowProfessionalInvoice(false);
    setSelectedOrder(null);
  };

  // تحويل Sale إلى SaleData للوصل
  const convertSaleToReceiptData = (sale: any) => {
    return {
      total: sale.total || 0,
      paid: sale.total || 0,
      change: 0,
      items: (sale.items || []).map((item: any) => ({
        name: item.productName || item.product_name || 'منتج غير محدد',
        quantity: item.quantity || 0,
        price: item.unitPrice || item.unit_price || 0
      })),
      paymentMethod: sale.paymentMethod || 'cash',
      customer: sale.customerName || 'عميل عادي',
      saleNumber: sale.saleNumber || 'غير محدد',
      timestamp: sale.date || new Date()
    };
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 relative overflow-hidden">
      {/* CSS للرسوم المتحركة */}
      <style>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(180deg); }
        }
        .animate-float {
          animation: float 6s ease-in-out infinite;
        }
        .animation-delay-2000 {
          animation-delay: 2s;
        }
        .animation-delay-4000 {
          animation-delay: 4s;
        }
      `}</style>
      {/* Animated Background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>
        <div className="absolute top-40 left-1/2 w-80 h-80 bg-cyan-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000"></div>
      </div>

      {/* Floating Particles */}
      <div className="fixed inset-0 pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-white/10 rounded-full animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${3 + Math.random() * 4}s`
            }}
          />
        ))}
      </div>

      <div className="relative z-10 p-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-4xl font-bold text-white mb-2 flex items-center">
                <div className="relative">
                  <FileText className="w-10 h-10 ml-4 text-blue-400" />
                  <div className="absolute inset-0 bg-blue-400 rounded-full blur-lg opacity-30 animate-pulse"></div>
                </div>
                سجل الطلبات الذكي
                <Sparkles className="w-8 h-8 mr-3 text-yellow-400 animate-spin" />
              </h1>
              <p className="text-slate-300 text-lg">إدارة متقدمة لجميع طلبات البيع مع تحليلات شاملة</p>
            </div>
            <div className="text-left">
              <div className="text-white text-xl font-bold">
                {currentTime.toLocaleTimeString('fr-FR')}
              </div>
              <div className="text-slate-400">
                {currentTime.toLocaleDateString('fr-FR')}
              </div>
            </div>
          </div>

          {/* Quick Analytics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-gradient-to-br from-blue-500/20 to-cyan-500/20 backdrop-blur-xl rounded-2xl p-6 border border-blue-400/30 hover:scale-105 transition-all duration-300">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-blue-500/30 rounded-xl">
                  <Package className="w-8 h-8 text-blue-300" />
                </div>
                <TrendingUp className="w-6 h-6 text-green-400" />
              </div>
              <div className="text-white text-2xl font-bold mb-1">{analytics.totalOrders}</div>
              <div className="text-blue-300 text-sm">إجمالي الطلبات</div>
            </div>

            <div className="bg-gradient-to-br from-green-500/20 to-emerald-500/20 backdrop-blur-xl rounded-2xl p-6 border border-green-400/30 hover:scale-105 transition-all duration-300">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-green-500/30 rounded-xl">
                  <DollarSign className="w-8 h-8 text-green-300" />
                </div>
                <ArrowUpRight className="w-6 h-6 text-green-400" />
              </div>
              <div className="text-white text-2xl font-bold mb-1">{analytics.totalRevenue.toFixed(0)} دج</div>
              <div className="text-green-300 text-sm">إجمالي المبيعات</div>
            </div>

            <div className="bg-gradient-to-br from-purple-500/20 to-pink-500/20 backdrop-blur-xl rounded-2xl p-6 border border-purple-400/30 hover:scale-105 transition-all duration-300">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-purple-500/30 rounded-xl">
                  <Target className="w-8 h-8 text-purple-300" />
                </div>
                <Activity className="w-6 h-6 text-purple-400" />
              </div>
              <div className="text-white text-2xl font-bold mb-1">{analytics.averageOrderValue.toFixed(0)} دج</div>
              <div className="text-purple-300 text-sm">متوسط قيمة الطلب</div>
            </div>

            <div className="bg-gradient-to-br from-yellow-500/20 to-orange-500/20 backdrop-blur-xl rounded-2xl p-6 border border-yellow-400/30 hover:scale-105 transition-all duration-300">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-yellow-500/30 rounded-xl">
                  <CheckCircle className="w-8 h-8 text-yellow-300" />
                </div>
                <Award className="w-6 h-6 text-yellow-400" />
              </div>
              <div className="text-white text-2xl font-bold mb-1">{analytics.completedOrders}</div>
              <div className="text-yellow-300 text-sm">طلبات مكتملة</div>
            </div>
          </div>

          {/* Filters and Controls */}
          <div className="bg-white/5 backdrop-blur-xl rounded-3xl p-6 border border-white/10 mb-8">
            <div className="flex flex-col lg:flex-row gap-6">
              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="البحث برقم الطلب، اسم العميل، أو رقم الهاتف..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full bg-white/10 border border-white/20 rounded-xl px-12 py-3 text-white placeholder-slate-400 focus:outline-none focus:border-blue-400 transition-colors"
                />
              </div>

              {/* Status Filter */}
              <div className="relative">
                <Filter className="absolute right-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="bg-white/10 border border-white/20 rounded-xl px-12 py-3 text-white focus:outline-none focus:border-blue-400 transition-colors min-w-[150px]"
                >
                  <option value="all" className="bg-slate-800">جميع الحالات</option>
                  <option value="completed" className="bg-slate-800">مكتمل</option>
                  <option value="pending" className="bg-slate-800">معلق</option>
                  <option value="cancelled" className="bg-slate-800">ملغي</option>
                  <option value="refunded" className="bg-slate-800">مسترد</option>
                </select>
              </div>

              {/* Payment Method Filter */}
              <div className="relative">
                <CreditCard className="absolute right-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                <select
                  value={selectedPaymentMethod}
                  onChange={(e) => setSelectedPaymentMethod(e.target.value)}
                  className="bg-white/10 border border-white/20 rounded-xl px-12 py-3 text-white focus:outline-none focus:border-blue-400 transition-colors min-w-[150px]"
                >
                  <option value="all" className="bg-slate-800">جميع طرق الدفع</option>
                  <option value="cash" className="bg-slate-800">نقداً</option>
                  <option value="card" className="bg-slate-800">بطاقة</option>
                  <option value="credit" className="bg-slate-800">بالدين</option>
                  <option value="balance" className="bg-slate-800">رصيد العميل</option>
                  <option value="transfer" className="bg-slate-800">تحويل</option>
                  <option value="mixed" className="bg-slate-800">مختلط</option>
                </select>
              </div>

              {/* Debt Status Filter */}
              <div className="relative">
                <Clock className="absolute right-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                <select
                  value={selectedDebtStatus}
                  onChange={(e) => setSelectedDebtStatus(e.target.value)}
                  className="bg-white/10 border border-white/20 rounded-xl px-12 py-3 text-white focus:outline-none focus:border-blue-400 transition-colors min-w-[150px]"
                >
                  <option value="all" className="bg-slate-800">جميع حالات الدين</option>
                  <option value="paid" className="bg-slate-800">مدفوع</option>
                  <option value="pending" className="bg-slate-800">معلق (دين)</option>
                  <option value="partial" className="bg-slate-800">مدفوع جزئياً</option>
                </select>
              </div>

              {/* View Mode */}
              <div className="flex bg-white/10 rounded-xl p-1 border border-white/20">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-lg transition-all duration-300 ${
                    viewMode === 'grid'
                      ? 'bg-blue-500 text-white shadow-lg'
                      : 'text-slate-400 hover:text-white hover:bg-white/10'
                  }`}
                >
                  <Grid className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-lg transition-all duration-300 ${
                    viewMode === 'list'
                      ? 'bg-blue-500 text-white shadow-lg'
                      : 'text-slate-400 hover:text-white hover:bg-white/10'
                  }`}
                >
                  <List className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setViewMode('analytics')}
                  className={`p-2 rounded-lg transition-all duration-300 ${
                    viewMode === 'analytics'
                      ? 'bg-blue-500 text-white shadow-lg'
                      : 'text-slate-400 hover:text-white hover:bg-white/10'
                  }`}
                >
                  <BarChart3 className="w-5 h-5" />
                </button>
              </div>

              {/* Sort */}
              <div className="flex items-center space-x-reverse space-x-2">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as 'date' | 'total' | 'customer')}
                  className="bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-blue-400 transition-colors"
                >
                  <option value="date" className="bg-slate-800">التاريخ</option>
                  <option value="total" className="bg-slate-800">المبلغ</option>
                  <option value="customer" className="bg-slate-800">العميل</option>
                </select>
                <button
                  onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                  className="p-3 bg-white/10 border border-white/20 rounded-xl text-slate-400 hover:text-white hover:bg-white/20 transition-all duration-300"
                >
                  {sortOrder === 'asc' ? <SortAsc className="w-5 h-5" /> : <SortDesc className="w-5 h-5" />}
                </button>
              </div>
            </div>
          </div>

          {/* Orders Display */}
          <div className="bg-white/5 backdrop-blur-xl rounded-3xl p-6 border border-white/10">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold text-white flex items-center">
                <Layers className="w-8 h-8 ml-3 text-blue-400" />
                الطلبات
                <span className="bg-blue-500 text-white text-sm px-3 py-1 rounded-full ml-3">
                  {filteredOrders.length}
                </span>
              </h3>
              <div className="flex items-center space-x-reverse space-x-4">
                <button className="bg-gradient-to-r from-green-500 to-emerald-400 hover:scale-105 px-6 py-3 rounded-xl text-white font-medium transition-all duration-300 flex items-center space-x-reverse space-x-2">
                  <Download className="w-5 h-5" />
                  <span>تصدير Excel</span>
                </button>
                <button className="bg-gradient-to-r from-blue-500 to-purple-500 hover:scale-105 px-6 py-3 rounded-xl text-white font-medium transition-all duration-300 flex items-center space-x-reverse space-x-2">
                  <Printer className="w-5 h-5" />
                  <span>طباعة التقرير</span>
                </button>
              </div>
            </div>

            {viewMode === 'grid' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {filteredOrders.map((sale) => (
                  <div
                    key={sale.id}
                    className="group relative overflow-hidden bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10 hover:border-blue-400/50 transition-all duration-500 hover:scale-105 hover:shadow-2xl"
                  >
                    {/* Floating Badges */}
                    <div className="absolute top-4 left-4 flex flex-col gap-2 z-10">
                      <div className={`px-3 py-1 rounded-full text-xs font-bold ${getStatusColor(sale.status)}`}>
                        {sale.status === 'completed' ? '✅ مكتمل' :
                         sale.status === 'pending' ? '⏳ معلق' :
                         sale.status === 'cancelled' ? '❌ ملغي' : '🔄 مسترد'}
                      </div>
                      <div className={`px-3 py-1 rounded-full text-xs font-bold ${getDebtStatusColor(getDebtStatus(sale))}`}>
                        {getDebtStatusText(getDebtStatus(sale))}
                      </div>
                    </div>

                    {/* Order Header */}
                    <div className="mb-6">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="text-white font-bold text-lg group-hover:text-blue-300 transition-colors">
                          {sale.saleNumber}
                        </h4>
                        <div className="flex items-center space-x-reverse space-x-2 text-slate-400">
                          {getPaymentMethodIcon(sale.paymentMethod)}
                          <span className="text-sm">
                            {sale.paymentMethod === 'cash' ? 'نقداً' :
                             sale.paymentMethod === 'card' ? 'بطاقة' :
                             sale.paymentMethod === 'credit' ? 'بالدين' :
                             sale.paymentMethod === 'balance' ? 'رصيد' : 'مختلط'}
                          </span>
                        </div>
                      </div>
                      <div className="text-slate-300 text-sm mb-2">
                        📅 {new Date(sale.date).toLocaleDateString('fr-FR')} - {new Date(sale.date).toLocaleTimeString('fr-FR')}
                      </div>
                      <div className="text-slate-400 text-sm">
                        👤 الكاشير: غير محدد
                      </div>
                    </div>

                    {/* Customer Info */}
                    <div className="mb-6 p-4 bg-white/5 rounded-xl border border-white/10">
                      <div className="flex items-center space-x-reverse space-x-3 mb-3">
                        <User className="w-5 h-5 text-blue-400" />
                        <span className="text-white font-medium">{sale.customerName || 'عميل عادي'}</span>
                      </div>
                    </div>

                    {/* Order Summary */}
                    <div className="mb-6">
                      <div className="flex items-center justify-between mb-3">
                        <span className="text-slate-300 flex items-center">
                          <Package className="w-4 h-4 ml-2" />
                          عدد الأصناف:
                        </span>
                        <span className="text-white font-bold">{sale.items?.length || 0}</span>
                      </div>
                      <div className="flex items-center justify-between mb-3">
                        <span className="text-slate-300">المجموع الفرعي:</span>
                        <span className="text-white">{sale.subtotal.toFixed(2)} دج</span>
                      </div>
                      {sale.discount > 0 && (
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-slate-300">الخصم:</span>
                          <span className="text-red-400">-{sale.discount.toFixed(2)} دج</span>
                        </div>
                      )}
                      <div className="flex items-center justify-between mb-3">
                        <span className="text-slate-300">الضريبة:</span>
                        <span className="text-yellow-400">+{sale.tax.toFixed(2)} دج</span>
                      </div>
                      <hr className="border-white/20 my-3" />
                      <div className="flex items-center justify-between">
                        <span className="text-xl font-bold text-white">الإجمالي:</span>
                        <span className="text-2xl font-bold text-green-400">{sale.total.toFixed(2)} دج</span>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-reverse space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleViewOrder(sale);
                        }}
                        className="flex-1 bg-gradient-to-r from-blue-500 to-purple-500 hover:scale-105 py-2 rounded-xl text-white font-medium transition-all duration-300 flex items-center justify-center space-x-reverse space-x-2"
                      >
                        <Eye className="w-4 h-4" />
                        <span>عرض</span>
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handlePrintReceipt(sale);
                        }}
                        className="bg-gradient-to-r from-green-500 to-emerald-400 hover:scale-105 p-2 rounded-xl text-white transition-all duration-300"
                        title="طباعة الفاتورة"
                      >
                        <Printer className="w-4 h-4" />
                      </button>
                      <div className="relative group">
                        <button
                          onClick={(e) => e.stopPropagation()}
                          className="bg-white/10 hover:bg-white/20 p-2 rounded-xl text-slate-400 hover:text-white transition-all duration-300"
                        >
                          <MoreHorizontal className="w-4 h-4" />
                        </button>
                        {/* قائمة منسدلة للإجراءات الإضافية */}
                        <div className="absolute left-0 top-full mt-2 bg-slate-800 border border-slate-600 rounded-xl shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50 min-w-[150px]">
                          <div className="w-full text-right px-4 py-2 text-slate-300 hover:text-white hover:bg-slate-700 rounded-t-xl flex items-center space-x-reverse space-x-2 cursor-pointer">
                            <Edit className="w-4 h-4" />
                            <span>تعديل</span>
                          </div>
                          <div className="w-full text-right px-4 py-2 text-slate-300 hover:text-white hover:bg-slate-700 flex items-center space-x-reverse space-x-2 cursor-pointer">
                            <Copy className="w-4 h-4" />
                            <span>نسخ</span>
                          </div>
                          <div className="w-full text-right px-4 py-2 text-slate-300 hover:text-white hover:bg-slate-700 flex items-center space-x-reverse space-x-2 cursor-pointer">
                            <Send className="w-4 h-4" />
                            <span>إرسال</span>
                          </div>
                          <div className="w-full text-right px-4 py-2 text-red-400 hover:text-red-300 hover:bg-slate-700 rounded-b-xl flex items-center space-x-reverse space-x-2 cursor-pointer">
                            <Trash2 className="w-4 h-4" />
                            <span>حذف</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Hover Effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                  </div>
                ))}
              </div>
            )}

            {viewMode === 'list' && (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-white/20">
                      <th className="text-right py-4 px-6 text-slate-300 font-medium">رقم الطلب</th>
                      <th className="text-right py-4 px-6 text-slate-300 font-medium">العميل</th>
                      <th className="text-right py-4 px-6 text-slate-300 font-medium">التاريخ</th>
                      <th className="text-right py-4 px-6 text-slate-300 font-medium">المبلغ</th>
                      <th className="text-right py-4 px-6 text-slate-300 font-medium">الحالة</th>
                      <th className="text-right py-4 px-6 text-slate-300 font-medium">الدفع</th>
                      <th className="text-center py-4 px-6 text-slate-300 font-medium">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredOrders.map((sale) => (
                      <tr
                        key={sale.id}
                        className="border-b border-white/10 hover:bg-white/5 transition-all duration-300"
                      >
                        <td className="py-4 px-6">
                          <div
                            className="flex flex-col cursor-pointer hover:text-blue-400 transition-colors"
                            onClick={() => handleViewOrder(sale)}
                          >
                            <span className="text-white font-medium">{sale.saleNumber}</span>
                            <span className="text-slate-400 text-sm">غير محدد</span>
                          </div>
                        </td>
                        <td className="py-4 px-6">
                          <div className="flex flex-col">
                            <span className="text-white font-medium">{sale.customerName || 'عميل عادي'}</span>
                            <span className="text-slate-400 text-sm">غير محدد</span>
                          </div>
                        </td>
                        <td className="py-4 px-6">
                          <div className="flex flex-col">
                            <span className="text-white">{new Date(sale.date).toLocaleDateString('fr-FR')}</span>
                            <span className="text-slate-400 text-sm">{new Date(sale.date).toLocaleTimeString('fr-FR')}</span>
                          </div>
                        </td>
                        <td className="py-4 px-6">
                          <div className="flex flex-col">
                            <span className="text-green-400 font-bold">{sale.total.toFixed(2)} دج</span>
                            <span className="text-slate-400 text-sm">{sale.items?.length || 0} أصناف</span>
                          </div>
                        </td>
                        <td className="py-4 px-6">
                          <div className={`inline-flex px-3 py-1 rounded-full text-xs font-bold ${getStatusColor(sale.status)}`}>
                            {sale.status === 'completed' ? '✅ مكتمل' :
                             sale.status === 'pending' ? '⏳ معلق' :
                             sale.status === 'cancelled' ? '❌ ملغي' : '🔄 مسترد'}
                          </div>
                        </td>
                        <td className="py-4 px-6">
                          <div className="flex items-center space-x-reverse space-x-2">
                            {getPaymentMethodIcon(sale.paymentMethod)}
                            <span className="text-sm text-green-400">
                              مدفوع
                            </span>
                          </div>
                        </td>
                        <td className="py-4 px-6">
                          <div className="flex items-center justify-center space-x-reverse space-x-2">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleViewOrder(sale);
                              }}
                              className="bg-blue-500 hover:bg-blue-600 p-2 rounded-lg text-white transition-all duration-300"
                              title="عرض التفاصيل"
                            >
                              <Eye className="w-4 h-4" />
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handlePrintReceipt(sale);
                              }}
                              className="bg-green-500 hover:bg-green-600 p-2 rounded-lg text-white transition-all duration-300"
                              title="طباعة الفاتورة"
                            >
                              <Printer className="w-4 h-4" />
                            </button>
                            <button
                              onClick={(e) => e.stopPropagation()}
                              className="bg-slate-600 hover:bg-slate-500 p-2 rounded-lg text-white transition-all duration-300"
                              title="المزيد"
                            >
                              <MoreHorizontal className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            {viewMode === 'analytics' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* مخطط دائري لحالات الطلبات */}
                <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10">
                  <h4 className="text-xl font-bold text-white mb-6 flex items-center">
                    <PieChart className="w-6 h-6 ml-3 text-blue-400" />
                    توزيع حالات الطلبات
                  </h4>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-green-500/20 rounded-xl border border-green-400/30">
                      <div className="flex items-center space-x-reverse space-x-3">
                        <CheckCircle className="w-6 h-6 text-green-400" />
                        <span className="text-white font-medium">مكتملة</span>
                      </div>
                      <div className="text-right">
                        <div className="text-green-400 font-bold text-xl">{analytics.completedOrders}</div>
                        <div className="text-green-300 text-sm">
                          {analytics.totalOrders > 0 ? ((analytics.completedOrders / analytics.totalOrders) * 100).toFixed(1) : 0}%
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-yellow-500/20 rounded-xl border border-yellow-400/30">
                      <div className="flex items-center space-x-reverse space-x-3">
                        <Clock className="w-6 h-6 text-yellow-400" />
                        <span className="text-white font-medium">قيد المعالجة</span>
                      </div>
                      <div className="text-right">
                        <div className="text-yellow-400 font-bold text-xl">{analytics.pendingOrders}</div>
                        <div className="text-yellow-300 text-sm">
                          {analytics.totalOrders > 0 ? ((analytics.pendingOrders / analytics.totalOrders) * 100).toFixed(1) : 0}%
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-red-500/20 rounded-xl border border-red-400/30">
                      <div className="flex items-center space-x-reverse space-x-3">
                        <XCircle className="w-6 h-6 text-red-400" />
                        <span className="text-white font-medium">ملغية</span>
                      </div>
                      <div className="text-right">
                        <div className="text-red-400 font-bold text-xl">{analytics.cancelledOrders}</div>
                        <div className="text-red-300 text-sm">
                          {analytics.totalOrders > 0 ? ((analytics.cancelledOrders / analytics.totalOrders) * 100).toFixed(1) : 0}%
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* إحصائيات المبيعات */}
                <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10">
                  <h4 className="text-xl font-bold text-white mb-6 flex items-center">
                    <BarChart3 className="w-6 h-6 ml-3 text-purple-400" />
                    إحصائيات المبيعات
                  </h4>
                  <div className="space-y-6">
                    <div className="text-center p-6 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl border border-blue-400/30">
                      <DollarSign className="w-12 h-12 text-blue-400 mx-auto mb-3" />
                      <div className="text-3xl font-bold text-white mb-2">{analytics.totalRevenue.toFixed(0)} دج</div>
                      <div className="text-blue-300">إجمالي المبيعات</div>
                    </div>

                    <div className="text-center p-6 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-xl border border-green-400/30">
                      <Target className="w-12 h-12 text-green-400 mx-auto mb-3" />
                      <div className="text-3xl font-bold text-white mb-2">{analytics.averageOrderValue.toFixed(0)} دج</div>
                      <div className="text-green-300">متوسط قيمة الطلب</div>
                    </div>
                  </div>
                </div>

                {/* أفضل العملاء */}
                <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10">
                  <h4 className="text-xl font-bold text-white mb-6 flex items-center">
                    <Star className="w-6 h-6 ml-3 text-yellow-400" />
                    أفضل العملاء
                  </h4>
                  <div className="space-y-4">
                    {filteredOrders
                      .reduce((acc: any[], sale) => {
                        const customerName = sale.customerName || 'عميل عادي';
                        const existing = acc.find(item => item.customer === customerName);
                        if (existing) {
                          existing.total += sale.total;
                          existing.orders += 1;
                        } else {
                          acc.push({
                            customer: customerName,
                            phone: 'غير محدد', // لا يوجد customerPhone في Sale interface الجديد
                            total: sale.total,
                            orders: 1
                          });
                        }
                        return acc;
                      }, [])
                      .sort((a, b) => b.total - a.total)
                      .slice(0, 5)
                      .map((customer, index) => (
                        <div key={customer.customer} className="flex items-center justify-between p-4 bg-white/5 rounded-xl border border-white/10">
                          <div className="flex items-center space-x-reverse space-x-3">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold text-white ${
                              index === 0 ? 'bg-yellow-500' : index === 1 ? 'bg-gray-400' : index === 2 ? 'bg-orange-500' : 'bg-blue-500'
                            }`}>
                              {index + 1}
                            </div>
                            <div>
                              <div className="text-white font-medium">{customer.customer}</div>
                              <div className="text-slate-400 text-sm">{customer.phone}</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-green-400 font-bold">{customer.total.toFixed(0)} دج</div>
                            <div className="text-slate-400 text-sm">{customer.orders} طلبات</div>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>

                {/* طرق الدفع */}
                <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10">
                  <h4 className="text-xl font-bold text-white mb-6 flex items-center">
                    <CreditCard className="w-6 h-6 ml-3 text-green-400" />
                    طرق الدفع
                  </h4>
                  <div className="space-y-4">
                    {['cash', 'card', 'transfer', 'mixed'].map(method => {
                      const count = filteredOrders.filter(sale => sale.paymentMethod === method).length;
                      const percentage = analytics.totalOrders > 0 ? (count / analytics.totalOrders) * 100 : 0;
                      return (
                        <div key={method} className="flex items-center justify-between p-4 bg-white/5 rounded-xl border border-white/10">
                          <div className="flex items-center space-x-reverse space-x-3">
                            {getPaymentMethodIcon(method)}
                            <span className="text-white font-medium">
                              {method === 'cash' ? 'نقداً' :
                               method === 'card' ? 'بطاقة' :
                               method === 'transfer' ? 'تحويل' : 'مختلط'}
                            </span>
                          </div>
                          <div className="text-right">
                            <div className="text-white font-bold">{count}</div>
                            <div className="text-slate-400 text-sm">{percentage.toFixed(1)}%</div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}

            {filteredOrders.length === 0 && (
              <div className="text-center py-16">
                <div className="relative mb-6">
                  <FileText className="w-20 h-20 text-slate-600 mx-auto animate-pulse" />
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-xl"></div>
                </div>
                <h3 className="text-xl font-bold text-slate-300 mb-2">لا توجد طلبات</h3>
                <p className="text-slate-400 mb-4">لم يتم العثور على طلبات تطابق المعايير المحددة</p>
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedStatus('all');
                    setSelectedPaymentMethod('all');
                  }}
                  className="bg-gradient-to-r from-blue-500 to-purple-500 px-6 py-2 rounded-xl text-white font-medium hover:scale-105 transition-transform duration-300"
                >
                  إعادة تعيين الفلاتر
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Order Details Modal */}
        {selectedOrder && showOrderDetails && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-3xl p-8 max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-white/20">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl font-bold text-white flex items-center">
                  <FileText className="w-8 h-8 ml-3 text-blue-400" />
                  تفاصيل المبيعة {selectedOrder.saleNumber}
                </h3>
                <button
                  onClick={handleCloseOrderDetails}
                  className="p-2 hover:bg-white/10 rounded-xl transition-all duration-300"
                >
                  <X className="w-6 h-6 text-slate-400 hover:text-white" />
                </button>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* معلومات الطلب */}
                <div className="space-y-6">
                  <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                    <h4 className="text-lg font-bold text-white mb-4 flex items-center">
                      <Package className="w-5 h-5 ml-2 text-blue-400" />
                      معلومات الطلب
                    </h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-slate-400">رقم المبيعة:</span>
                        <span className="text-white font-medium">{selectedOrder.saleNumber}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">التاريخ:</span>
                        <span className="text-white">{new Date(selectedOrder.date).toLocaleDateString('fr-FR')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">الوقت:</span>
                        <span className="text-white">{new Date(selectedOrder.date).toLocaleTimeString('fr-FR')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">الكاشير:</span>
                        <span className="text-white">{selectedOrder.cashierName || 'غير محدد'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">الحالة:</span>
                        <span className={`px-3 py-1 rounded-full text-xs font-bold ${getStatusColor(selectedOrder.status)}`}>
                          {selectedOrder.status === 'completed' ? '✅ مكتمل' :
                           selectedOrder.status === 'pending' ? '⏳ معلق' :
                           selectedOrder.status === 'cancelled' ? '❌ ملغي' : '🔄 مسترد'}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* معلومات العميل */}
                  <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                    <h4 className="text-lg font-bold text-white mb-4 flex items-center">
                      <User className="w-5 h-5 ml-2 text-green-400" />
                      معلومات العميل
                    </h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-slate-400">الاسم:</span>
                        <span className="text-white font-medium">{selectedOrder.customerName || 'عميل عادي'}</span>
                      </div>
                      {selectedOrder.customerPhone && (
                        <div className="flex justify-between">
                          <span className="text-slate-400">الهاتف:</span>
                          <span className="text-white">{selectedOrder.customerPhone}</span>
                        </div>
                      )}
                      {selectedOrder.notes && (
                        <div className="flex justify-between">
                          <span className="text-slate-400">ملاحظات:</span>
                          <span className="text-white">{selectedOrder.notes}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* الأصناف والمبالغ */}
                <div className="space-y-6">
                  <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                    <h4 className="text-lg font-bold text-white mb-4 flex items-center">
                      <Package className="w-5 h-5 ml-2 text-purple-400" />
                      الأصناف المطلوبة
                    </h4>
                    <div className="space-y-3 max-h-60 overflow-y-auto">
                      {selectedOrder.items && selectedOrder.items.length > 0 ? (
                        selectedOrder.items.map((item: any, index: number) => (
                          <div key={index} className="flex justify-between items-center p-3 bg-white/5 rounded-xl">
                            <div>
                              <div className="text-white font-medium">{item.productName}</div>
                              <div className="text-slate-400 text-sm">{item.unitPrice.toFixed(2)} دج × {item.quantity}</div>
                            </div>
                            <div className="text-green-400 font-bold">{item.totalPrice.toFixed(2)} دج</div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center text-slate-400 py-4">
                          لا توجد أصناف متاحة
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                    <h4 className="text-lg font-bold text-white mb-4 flex items-center">
                      <Calculator className="w-5 h-5 ml-2 text-yellow-400" />
                      ملخص المبالغ
                    </h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-slate-400">المجموع الفرعي:</span>
                        <span className="text-white">{selectedOrder.subtotal.toFixed(2)} دج</span>
                      </div>
                      {selectedOrder.discount > 0 && (
                        <div className="flex justify-between">
                          <span className="text-slate-400">الخصم:</span>
                          <span className="text-red-400">-{selectedOrder.discount.toFixed(2)} دج</span>
                        </div>
                      )}
                      <div className="flex justify-between">
                        <span className="text-slate-400">الضريبة:</span>
                        <span className="text-yellow-400">+{selectedOrder.tax.toFixed(2)} دج</span>
                      </div>
                      <hr className="border-white/20" />
                      <div className="flex justify-between">
                        <span className="text-xl font-bold text-white">الإجمالي:</span>
                        <span className="text-2xl font-bold text-green-400">{selectedOrder.total.toFixed(2)} دج</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-400">طريقة الدفع:</span>
                        <div className="flex items-center space-x-reverse space-x-2">
                          {getPaymentMethodIcon(selectedOrder.paymentMethod)}
                          <span className="text-white">
                            {selectedOrder.paymentMethod === 'cash' ? 'نقداً' :
                             selectedOrder.paymentMethod === 'card' ? 'بطاقة' :
                             selectedOrder.paymentMethod === 'transfer' ? 'تحويل' : 'مختلط'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* أزرار الإجراءات */}
              <div className="flex justify-center space-x-reverse space-x-4 mt-8">
                <button
                  onClick={() => handlePrintProfessionalInvoice(selectedOrder)}
                  className="bg-gradient-to-r from-blue-500 to-purple-500 hover:scale-105 px-8 py-3 rounded-xl text-white font-medium transition-all duration-300 flex items-center space-x-reverse space-x-2"
                >
                  <FileText className="w-5 h-5" />
                  <span>فاتورة احترافية</span>
                </button>
                <button
                  onClick={() => handlePrintReceipt(selectedOrder)}
                  className="bg-gradient-to-r from-green-500 to-emerald-400 hover:scale-105 px-8 py-3 rounded-xl text-white font-medium transition-all duration-300 flex items-center space-x-reverse space-x-2"
                >
                  <Printer className="w-5 h-5" />
                  <span>وصل بسيط</span>
                </button>
                <button
                  onClick={handleCloseOrderDetails}
                  className="bg-gradient-to-r from-slate-600 to-slate-700 hover:scale-105 px-8 py-3 rounded-xl text-white font-medium transition-all duration-300"
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Receipt Modal */}
        {selectedOrder && showReceiptModal && (
          <ReceiptModal
            isOpen={showReceiptModal}
            onClose={handleCloseReceipt}
            saleData={convertSaleToReceiptData(selectedOrder)}
            companyInfo={storeInfo}
          />
        )}

        {/* Thermal Receipt Component (Hidden) */}
        {selectedOrder && showReceiptModal && (
          <ThermalReceipt
            saleData={convertSaleToReceiptData(selectedOrder)}
            companyInfo={storeInfo}
          />
        )}

        {/* Professional Invoice Modal */}
        {selectedOrder && showProfessionalInvoice && (
          <ProfessionalInvoice
            isOpen={showProfessionalInvoice}
            onClose={handleCloseProfessionalInvoice}
            saleData={selectedOrder}
          />
        )}
      </div>
    </div>
  );
};

export default OrdersHistory;
