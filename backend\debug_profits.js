const pool = require('./database');

async function debugProfits() {
  try {
    console.log('🔍 تشخيص مشكلة الأرباح...\n');
    
    // 1. فحص المبيعات
    const salesResult = await pool.query(`
      SELECT COUNT(*) as sales_count, SUM(total_amount) as total_revenue
      FROM sales 
      WHERE status = 'completed'
    `);
    
    console.log('📊 المبيعات:');
    console.log('- عدد المبيعات:', salesResult.rows[0].sales_count);
    console.log('- إجمالي الإيرادات:', parseFloat(salesResult.rows[0].total_revenue || 0));
    
    // 2. فحص عناصر المبيعات
    const itemsResult = await pool.query(`
      SELECT COUNT(*) as items_count, SUM(quantity) as total_quantity
      FROM sale_items si
      JOIN sales s ON si.sale_id = s.id
      WHERE s.status = 'completed'
    `);
    
    console.log('\n📦 عناصر المبيعات:');
    console.log('- عدد العناصر:', itemsResult.rows[0].items_count);
    console.log('- إجمالي الكمية:', itemsResult.rows[0].total_quantity);
    
    // 3. فحص المنتجات وتكلفتها
    const productsResult = await pool.query(`
      SELECT 
        COUNT(*) as products_count,
        COUNT(CASE WHEN cost > 0 THEN 1 END) as products_with_cost,
        AVG(cost) as avg_cost,
        AVG(price) as avg_price
      FROM products
    `);
    
    console.log('\n🏷️ المنتجات:');
    console.log('- عدد المنتجات:', productsResult.rows[0].products_count);
    console.log('- منتجات لها تكلفة:', productsResult.rows[0].products_with_cost);
    console.log('- متوسط التكلفة:', parseFloat(productsResult.rows[0].avg_cost || 0));
    console.log('- متوسط السعر:', parseFloat(productsResult.rows[0].avg_price || 0));
    
    // 4. فحص الربح التفصيلي
    const profitDetailResult = await pool.query(`
      SELECT 
        p.name as product_name,
        p.cost,
        p.price,
        si.unit_price,
        si.quantity,
        (si.unit_price - p.cost) as profit_per_unit,
        (si.unit_price - p.cost) * si.quantity as total_profit
      FROM sale_items si
      JOIN sales s ON si.sale_id = s.id
      JOIN products p ON si.product_id = p.id
      WHERE s.status = 'completed'
      ORDER BY s.created_at DESC
      LIMIT 5
    `);
    
    console.log('\n💰 تفاصيل الربح (آخر 5 عناصر):');
    profitDetailResult.rows.forEach((row, index) => {
      console.log(`${index + 1}. ${row.product_name}:`);
      console.log(`   - تكلفة المنتج: ${row.cost}`);
      console.log(`   - سعر البيع: ${row.unit_price}`);
      console.log(`   - الكمية: ${row.quantity}`);
      console.log(`   - ربح الوحدة: ${parseFloat(row.profit_per_unit || 0)}`);
      console.log(`   - إجمالي الربح: ${parseFloat(row.total_profit || 0)}`);
      console.log('');
    });
    
    // 5. حساب الربح الإجمالي
    const totalProfitResult = await pool.query(`
      SELECT 
        COALESCE(SUM((si.unit_price - p.cost) * si.quantity), 0) as total_profit
      FROM sale_items si
      JOIN sales s ON si.sale_id = s.id
      JOIN products p ON si.product_id = p.id
      WHERE s.status = 'completed'
    `);
    
    console.log('🎯 النتيجة النهائية:');
    console.log('إجمالي الأرباح:', parseFloat(totalProfitResult.rows[0].total_profit));
    
    // 6. اقتراحات الحلول
    console.log('\n💡 التشخيص والحلول:');
    
    if (salesResult.rows[0].sales_count == 0) {
      console.log('❌ لا توجد مبيعات مكتملة');
    } else if (productsResult.rows[0].products_with_cost == 0) {
      console.log('❌ المنتجات لا تحتوي على تكلفة');
      console.log('🔧 الحل: أضف تكلفة للمنتجات في إدارة المنتجات');
    } else if (parseFloat(totalProfitResult.rows[0].total_profit) == 0) {
      console.log('❌ الربح صفر - قد تكون التكلفة = سعر البيع');
      console.log('🔧 الحل: تأكد من أن سعر البيع أكبر من التكلفة');
    } else {
      console.log('✅ الحساب يعمل بشكل صحيح');
    }
    
  } catch (error) {
    console.error('❌ خطأ في التشخيص:', error.message);
    console.error(error.stack);
  } finally {
    await pool.end();
  }
}

debugProfits();
