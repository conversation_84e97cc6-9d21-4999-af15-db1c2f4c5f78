const express = require('express');
const router = express.Router();
const pool = require('../database');

// 📋 جلب جميع فواتير المشتريات
router.get('/', async (req, res) => {
  try {
    console.log('📋 طلب جلب فواتير المشتريات...');

    const result = await pool.query(`
      SELECT
        p.*,
        s.name as supplier_name,
        s.phone as supplier_phone
      FROM pos_system.purchase_invoices p
      LEFT JOIN pos_system.suppliers s ON p.supplier_id = s.id
      ORDER BY p.created_at DESC
    `);

    console.log(`✅ تم جلب ${result.rows.length} فاتورة مشتريات`);
    res.json(result.rows);
  } catch (error) {
    console.error('❌ خطأ في جلب فواتير المشتريات:', error);
    res.status(500).json({ error: 'خطأ في جلب فواتير المشتريات' });
  }
});

// 📋 جلب فاتورة مشتريات محددة مع تفاصيلها
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`📋 طلب جلب فاتورة المشتريات: ${id}`);

    // جلب بيانات الفاتورة الأساسية
    const purchaseResult = await pool.query(`
      SELECT
        p.*,
        s.name as supplier_name,
        s.phone as supplier_phone,
        s.address as supplier_address
      FROM pos_system.purchase_invoices p
      LEFT JOIN pos_system.suppliers s ON p.supplier_id = s.id
      WHERE p.id = $1
    `, [id]);

    if (purchaseResult.rows.length === 0) {
      return res.status(404).json({ error: 'فاتورة المشتريات غير موجودة' });
    }

    // جلب عناصر الفاتورة
    const itemsResult = await pool.query(`
      SELECT
        pi.*,
        pr.name as product_name
      FROM pos_system.purchase_invoice_items pi
      LEFT JOIN pos_system.products pr ON pi.product_id = pr.id
      WHERE pi.invoice_id = $1
    `, [id]);

    const purchase = purchaseResult.rows[0];
    purchase.items = itemsResult.rows;

    console.log(`✅ تم جلب فاتورة المشتريات: ${id}`);
    res.json(purchase);
  } catch (error) {
    console.error('❌ خطأ في جلب فاتورة المشتريات:', error);
    res.status(500).json({ error: 'خطأ في جلب فاتورة المشتريات' });
  }
});

// 💰 إنشاء فاتورة مشتريات جديدة مع تحديث المخزون
router.post('/', async (req, res) => {
  try {
    const {
      supplier_id,
      total_amount,
      amount_paid = 0,
      invoice_date,
      notes = '',
      items = []
    } = req.body;

    console.log('💰 إنشاء فاتورة مشتريات جديدة مع تحديث المخزون');
    console.log('📦 عدد المنتجات:', items.length);
    console.log('📋 بيانات الطلب:', { supplier_id, total_amount, amount_paid, items });

    // التحقق من البيانات المطلوبة
    if (!supplier_id || !total_amount) {
      return res.status(400).json({
        error: 'معرف المورد والمبلغ الإجمالي مطلوبان'
      });
    }

    // التحقق من وجود منتجات
    if (!items || items.length === 0) {
      return res.status(400).json({
        error: 'يجب إضافة منتج واحد على الأقل للفاتورة'
      });
    }

    // بدء معاملة قاعدة البيانات
    await pool.query('BEGIN');

    try {
      // إنشاء الفاتورة في الجدول الموجود (سيتم توليد رقم الفاتورة تلقائياً بواسطة trigger)
      const result = await pool.query(`
        INSERT INTO pos_system.purchase_invoices (
          supplier_id, subtotal, total_amount, amount_paid, notes
        ) VALUES ($1, $2, $3, $4, $5)
        RETURNING *
      `, [
        supplier_id,
        total_amount, // subtotal
        total_amount, // total_amount
        amount_paid,
        notes
      ]);

      const newPurchase = result.rows[0];
      console.log(`📋 تم إنشاء الفاتورة: ${newPurchase.id}`);

      // إضافة عناصر الفاتورة وتحديث المخزون
      for (const item of items) {
        console.log(`📦 معالجة المنتج: ${item.product_name}`);
        console.log(`📊 بيانات المنتج:`, {
          product_id: item.product_id,
          quantity: item.quantity,
          unit_cost: item.unit_cost,
          total_cost: item.total_cost
        });

        // التحقق من بيانات المنتج
        if (!item.product_id || !item.quantity || !item.unit_cost) {
          console.error(`❌ بيانات المنتج غير مكتملة:`, item);
          throw new Error(`بيانات المنتج غير مكتملة: ${item.product_name}`);
        }

        // إضافة عنصر الفاتورة
        await pool.query(`
          INSERT INTO pos_system.purchase_invoice_items (
            invoice_id, product_id, product_name, quantity, unit_cost, discount, total_cost
          ) VALUES ($1, $2, $3, $4, $5, $6, $7)
        `, [
          newPurchase.id,
          item.product_id,
          item.product_name,
          parseInt(item.quantity),
          parseFloat(item.unit_cost),
          parseFloat(item.discount) || 0,
          parseFloat(item.total_cost)
        ]);

        // تحديث المخزون (إضافة الكمية)
        const updateResult = await pool.query(`
          UPDATE pos_system.products
          SET stock = stock + $1, updated_at = CURRENT_TIMESTAMP
          WHERE id = $2
          RETURNING stock, name
        `, [parseInt(item.quantity), item.product_id]);

        if (updateResult.rows.length === 0) {
          throw new Error(`المنتج غير موجود: ${item.product_id}`);
        }

        const updatedProduct = updateResult.rows[0];
        console.log(`📈 تم تحديث مخزون ${updatedProduct.name}: +${item.quantity} (الرصيد الجديد: ${updatedProduct.stock})`);

        // إضافة حركة مخزون
        await pool.query(`
          INSERT INTO pos_system.inventory_movements (
            product_id, movement_type, quantity,
            previous_stock, new_stock, reason,
            reference_id, reference_type
          ) VALUES ($1, 'in', $2, $3, $4, 'شراء', $5, 'purchase')
        `, [
          item.product_id,
          parseInt(item.quantity),
          updatedProduct.stock - parseInt(item.quantity), // previous stock
          updatedProduct.stock, // new stock
          newPurchase.id
        ]);
      }

      // حساب الدين الجديد (المبلغ المتبقي)
      const remainingAmount = total_amount - amount_paid;

      // تحديث رصيد المورد
      await pool.query(`
        UPDATE pos_system.suppliers
        SET balance = balance + $1,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `, [remainingAmount, supplier_id]);

      console.log(`💰 رصيد المورد محدث: ${remainingAmount > 0 ? 'دين' : 'مدفوع بالكامل'}`);

      // إذا كان هناك دين، أضفه إلى جدول supplier_debts
      if (remainingAmount > 0) {
        await pool.query(`
          INSERT INTO pos_system.supplier_debts (
            supplier_id, purchase_id, amount, debt_date, paid_amount, status, notes
          ) VALUES ($1, $2, $3, CURRENT_DATE, $4, 'pending', $5)
        `, [
          supplier_id,
          newPurchase.id,
          remainingAmount,
          amount_paid,
          `دين من فاتورة مشتريات رقم ${newPurchase.invoice_number}`
        ]);
        console.log(`💳 تم إدراج دين بقيمة ${remainingAmount} دج في جدول supplier_debts`);
      }

      // إضافة المصروف في جدول المعاملات المالية إذا كان هناك دفعة
      if (amount_paid > 0) {
        try {
          // جلب اسم المورد
          const supplierResult = await pool.query(`
            SELECT name FROM pos_system.suppliers WHERE id = $1
          `, [supplier_id]);

          const supplierName = supplierResult.rows[0]?.name || 'غير محدد';

          // توليد رقم معاملة فريد
          const transactionNumber = `EXP-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

          // إضافة المعاملة في جدول المعاملات المالية
          await pool.query(`
            INSERT INTO pos_system.financial_transactions (
              transaction_number, type, category, amount, description,
              reference_id, reference_type, payment_method, notes
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
          `, [
            transactionNumber,
            'expense',
            'مشتريات',
            amount_paid,
            `دفع فاتورة مشتريات للمورد ${supplierName}`,
            newPurchase.id,
            'purchase_payment',
            'cash',
            `دفع فاتورة رقم ${newPurchase.id} - مبلغ: ${amount_paid} دج`
          ]);

          console.log(`💰 تم إضافة مصروف في المعاملات المالية بقيمة ${amount_paid} دج`);
        } catch (expenseError) {
          console.error('⚠️ خطأ في إضافة المصروف:', expenseError.message);
          // لا نوقف العملية، فقط نسجل الخطأ
        }
      }

      // تأكيد المعاملة
      await pool.query('COMMIT');

      console.log(`✅ تم إنشاء فاتورة المشتريات: ${newPurchase.id}`);
      console.log(`📦 تم تحديث مخزون ${items.length} منتج`);
      console.log(`💰 تم تحديث رصيد المورد بمبلغ: ${remainingAmount} دج`);

      res.status(201).json({
        message: 'تم إنشاء فاتورة المشتريات وتحديث المخزون بنجاح',
        purchase: newPurchase,
        items_processed: items.length,
        supplier_balance_updated: remainingAmount,
        debt_created: remainingAmount > 0
      });

    } catch (transactionError) {
      // إلغاء المعاملة في حالة الخطأ
      await pool.query('ROLLBACK');
      throw transactionError;
    }

  } catch (error) {
    console.error('❌ خطأ في إنشاء فاتورة المشتريات:', error);
    res.status(500).json({ error: 'خطأ في إنشاء فاتورة المشتريات' });
  }
});

// 💳 تحديث دفعة فاتورة مشتريات
router.patch('/:id/payment', async (req, res) => {
  try {
    const { id } = req.params;
    const { amount_paid } = req.body;

    console.log(`💳 تحديث دفعة فاتورة المشتريات: ${id} - مبلغ: ${amount_paid}`);

    // التحقق من وجود الفاتورة
    const purchaseCheck = await pool.query(
      'SELECT * FROM pos_system.purchase_invoices WHERE id = $1',
      [id]
    );

    if (purchaseCheck.rows.length === 0) {
      return res.status(404).json({ error: 'فاتورة المشتريات غير موجودة' });
    }

    const purchase = purchaseCheck.rows[0];

    // التحقق من صحة المبلغ
    if (amount_paid < 0 || amount_paid > purchase.total_amount) {
      return res.status(400).json({
        error: 'المبلغ المدفوع غير صحيح'
      });
    }

    // بدء معاملة قاعدة البيانات
    await pool.query('BEGIN');

    try {
      // حساب الفرق في المبلغ المدفوع
      const oldPaidAmount = purchase.amount_paid;
      const paymentDifference = amount_paid - oldPaidAmount;

      // تحديث المبلغ المدفوع في الفاتورة
      const result = await pool.query(`
        UPDATE pos_system.purchase_invoices
        SET
          amount_paid = $1,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
        RETURNING *
      `, [amount_paid, id]);

      const updatedPurchase = result.rows[0];

      // تحديث رصيد المورد (تقليل الدين)
      await pool.query(`
        UPDATE pos_system.suppliers
        SET balance = balance - $1,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `, [paymentDifference, purchase.supplier_id]);

      // تحديث جدول supplier_debts
      const newRemainingAmount = updatedPurchase.total_amount - updatedPurchase.amount_paid;
      console.log(`💰 المبلغ المتبقي الجديد: ${newRemainingAmount} دج`);

      // ملاحظة: تحديث ديون الموردين يتم من خلال API منفصل (supplier-debts.js)
      // لتجنب التضارب وضمان إضافة المصروفات بشكل صحيح
      console.log(`💳 تحديث دين المورد سيتم من خلال API منفصل`);

      // إضافة المصروف في جدول المعاملات المالية إذا كان هناك دفعة جديدة
      if (paymentDifference > 0) {
        try {
          // جلب اسم المورد
          const supplierResult = await pool.query(`
            SELECT name FROM pos_system.suppliers WHERE id = $1
          `, [purchase.supplier_id]);

          const supplierName = supplierResult.rows[0]?.name || 'غير محدد';

          // توليد رقم معاملة فريد
          const transactionNumber = `EXP-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

          // إضافة المعاملة في جدول المعاملات المالية
          await pool.query(`
            INSERT INTO pos_system.financial_transactions (
              transaction_number, type, category, amount, description,
              reference_id, reference_type, payment_method, notes
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
          `, [
            transactionNumber,
            'expense',
            'مشتريات',
            paymentDifference,
            `دفع فاتورة مشتريات للمورد ${supplierName}`,
            id,
            'purchase_payment',
            'cash',
            `دفع فاتورة رقم ${id} - مبلغ: ${paymentDifference} دج`
          ]);

          console.log(`💰 تم إضافة مصروف في المعاملات المالية بقيمة ${paymentDifference} دج`);
        } catch (expenseError) {
          console.error('⚠️ خطأ في إضافة المصروف:', expenseError.message);
          // لا نوقف العملية، فقط نسجل الخطأ
        }
      }

      // تأكيد المعاملة
      await pool.query('COMMIT');

      console.log(`✅ تم تحديث دفعة فاتورة المشتريات: ${id}`);
      console.log(`💰 تم تحديث رصيد المورد بمبلغ: -${paymentDifference} دج`);

      res.json({
        message: 'تم تحديث الدفعة بنجاح',
        purchase: updatedPurchase,
        payment_difference: paymentDifference,
        new_remaining_amount: newRemainingAmount
      });

    } catch (transactionError) {
      // إلغاء المعاملة في حالة الخطأ
      await pool.query('ROLLBACK');
      throw transactionError;
    }

  } catch (error) {
    console.error('❌ خطأ في تحديث دفعة فاتورة المشتريات:', error);
    res.status(500).json({ error: 'خطأ في تحديث الدفعة' });
  }
});

// 🗑️ حذف فاتورة مشتريات
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`🗑️ حذف فاتورة مشتريات: ${id}`);

    // بدء معاملة قاعدة البيانات
    await pool.query('BEGIN');

    try {
      // جلب بيانات الفاتورة أولاً
      const invoiceResult = await pool.query(
        'SELECT * FROM pos_system.purchase_invoices WHERE id = $1',
        [id]
      );

      if (invoiceResult.rows.length === 0) {
        await pool.query('ROLLBACK');
        return res.status(404).json({ error: 'الفاتورة غير موجودة' });
      }

      const invoice = invoiceResult.rows[0];
      const remainingAmount = invoice.total_amount - invoice.amount_paid;

      // حذف الديون المرتبطة بالفاتورة (معطل مؤقتاً)
      console.log(`🗑️ تنظيف الديون المرتبطة بالفاتورة: ${id}`);

      // await pool.query(
      //   'DELETE FROM pos_system.supplier_debts WHERE purchase_id = $1',
      //   [id]
      // );

      // تحديث رصيد المورد (إزالة الدين)
      if (remainingAmount > 0) {
        await pool.query(`
          UPDATE pos_system.suppliers
          SET balance = balance - $1,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = $2
        `, [remainingAmount, invoice.supplier_id]);
      }

      // حذف الفاتورة
      await pool.query(
        'DELETE FROM pos_system.purchase_invoices WHERE id = $1',
        [id]
      );

      // تأكيد المعاملة
      await pool.query('COMMIT');

      console.log(`✅ تم حذف فاتورة المشتريات: ${id}`);
      console.log(`💰 تم تحديث رصيد المورد بمبلغ: -${remainingAmount} دج`);

      res.json({
        message: 'تم حذف فاتورة المشتريات بنجاح',
        deleted_invoice_id: id,
        supplier_balance_updated: -remainingAmount
      });

    } catch (transactionError) {
      // إلغاء المعاملة في حالة الخطأ
      await pool.query('ROLLBACK');
      throw transactionError;
    }

  } catch (error) {
    console.error('❌ خطأ في حذف فاتورة المشتريات:', error);
    res.status(500).json({ error: 'خطأ في حذف فاتورة المشتريات' });
  }
});

// 🗑️ حذف جميع فواتير المشتريات
router.delete('/delete-all', async (req, res) => {
  try {
    console.log('🗑️ بدء حذف جميع فواتير المشتريات...');

    // بدء معاملة قاعدة البيانات
    await pool.query('BEGIN');

    try {
      // جلب عدد الفواتير قبل الحذف
      const countResult = await pool.query('SELECT COUNT(*) as count FROM pos_system.purchase_invoices');
      const totalInvoices = parseInt(countResult.rows[0].count);

      // حذف جميع الديون المرتبطة بالفواتير (معطل مؤقتاً)
      console.log('🗑️ تنظيف جميع الديون...');
      const debtsResult = { rows: [] }; // مؤقت

      // const debtsResult = await pool.query('DELETE FROM pos_system.supplier_debts RETURNING *');

      // إعادة تصفير أرصدة جميع الموردين
      await pool.query(`
        UPDATE pos_system.suppliers
        SET balance = 0, updated_at = CURRENT_TIMESTAMP
        WHERE is_active = true
      `);

      // حذف جميع فواتير المشتريات
      await pool.query('DELETE FROM pos_system.purchase_invoices');

      // تأكيد المعاملة
      await pool.query('COMMIT');

      console.log(`✅ تم حذف ${totalInvoices} فاتورة مشتريات`);
      console.log(`✅ تم حذف ${debtsResult.rows.length} دين`);
      console.log(`✅ تم إعادة تصفير أرصدة الموردين`);

      res.json({
        message: `تم حذف جميع فواتير المشتريات بنجاح`,
        deleted_invoices: totalInvoices,
        deleted_debts: debtsResult.rows.length,
        suppliers_reset: true
      });

    } catch (transactionError) {
      // إلغاء المعاملة في حالة الخطأ
      await pool.query('ROLLBACK');
      throw transactionError;
    }

  } catch (error) {
    console.error('❌ خطأ في حذف جميع فواتير المشتريات:', error);
    console.error('❌ تفاصيل الخطأ:', error.message);
    res.status(500).json({
      error: 'خطأ في حذف جميع فواتير المشتريات',
      details: error.message
    });
  }
});

module.exports = router;
