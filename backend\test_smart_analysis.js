const pool = require('./database');

async function testSmartAnalysis() {
  try {
    console.log('🤖 اختبار التحليل الذكي...\n');
    
    // اختبار استعلام اتجاهات المبيعات
    const salesTrendsResult = await pool.query(`
      WITH daily_sales AS (
        SELECT 
          DATE(created_at) as sale_date,
          COUNT(*) as daily_orders,
          SUM(total_amount) as daily_revenue,
          SUM((si.unit_price - COALESCE(p.cost, 0)) * si.quantity) as daily_profit
        FROM sales s
        LEFT JOIN sale_items si ON s.id = si.sale_id
        LEFT JOIN products p ON si.product_id = p.id
        WHERE s.status = 'completed' 
          AND s.created_at >= CURRENT_DATE - INTERVAL '30 days'
        GROUP BY DATE(created_at)
        ORDER BY sale_date
      )
      SELECT 
        sale_date,
        daily_orders,
        daily_revenue,
        daily_profit
      FROM daily_sales
      LIMIT 5
    `);
    
    console.log('📊 اتجاهات المبيعات (آخر 5 أيام):');
    salesTrendsResult.rows.forEach(row => {
      console.log(`${row.sale_date}: ${row.daily_orders} طلب، ${parseFloat(row.daily_revenue || 0)} دج إيرادات، ${parseFloat(row.daily_profit || 0)} دج أرباح`);
    });
    
    // اختبار تحليل المنتجات
    const productAnalysisResult = await pool.query(`
      SELECT 
        p.name,
        SUM(si.quantity) as total_sold,
        SUM((si.unit_price - COALESCE(p.cost, 0)) * si.quantity) as total_profit
      FROM products p
      LEFT JOIN sale_items si ON p.id = si.product_id
      LEFT JOIN sales s ON si.sale_id = s.id AND s.status = 'completed'
      WHERE s.created_at >= CURRENT_DATE - INTERVAL '30 days'
      GROUP BY p.id, p.name
      HAVING SUM(si.quantity) > 0
      ORDER BY total_profit DESC
      LIMIT 3
    `);
    
    console.log('\n🏆 أفضل 3 منتجات ربحاً:');
    productAnalysisResult.rows.forEach((row, index) => {
      console.log(`${index + 1}. ${row.name}: ${row.total_sold} مبيع، ${parseFloat(row.total_profit || 0)} دج ربح`);
    });
    
    // حساب معدل النمو
    const recentDays = salesTrendsResult.rows.slice(-7);
    const avgDailyRevenue = recentDays.reduce((sum, day) => sum + parseFloat(day.daily_revenue || 0), 0) / Math.max(recentDays.length, 1);
    const avgDailyProfit = recentDays.reduce((sum, day) => sum + parseFloat(day.daily_profit || 0), 0) / Math.max(recentDays.length, 1);
    
    console.log('\n📈 المؤشرات الذكية:');
    console.log(`متوسط الإيرادات اليومية: ${avgDailyRevenue.toFixed(2)} دج`);
    console.log(`متوسط الأرباح اليومية: ${avgDailyProfit.toFixed(2)} دج`);
    
    // التنبؤات
    const nextWeekRevenue = avgDailyRevenue * 7;
    const nextMonthRevenue = avgDailyRevenue * 30;
    
    console.log('\n🔮 التنبؤات:');
    console.log(`الإيرادات المتوقعة للأسبوع القادم: ${nextWeekRevenue.toFixed(2)} دج`);
    console.log(`الإيرادات المتوقعة للشهر القادم: ${nextMonthRevenue.toFixed(2)} دج`);
    
    console.log('\n✅ التحليل الذكي يعمل بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في التحليل الذكي:', error.message);
    console.error(error.stack);
  } finally {
    await pool.end();
  }
}

testSmartAnalysis();
