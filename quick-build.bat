@echo off
chcp 65001 > nul
title 🚀 بناء سريع - كشير توسار
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🏪 كشير توسار                           ║
echo ║                  بناء ملف التثبيت السريع                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM التحقق من المتطلبات
echo 🔍 التحقق من المتطلبات...

REM التحقق من Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo 📥 يرجى تحميل وتثبيت Node.js من: https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js متوفر

REM التحقق من npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متوفر
    pause
    exit /b 1
)
echo ✅ npm متوفر

echo.
echo 📋 خيارات البناء:
echo.
echo 1. 🏗️  بناء كامل (Frontend + Electron + Installer)
echo 2. 📦  بناء Frontend فقط
echo 3. 🖥️  بناء Electron فقط
echo 4. 🔧  تثبيت التبعيات فقط
echo 5. 🧹  تنظيف وإعادة البناء
echo.
set /p choice="اختر الرقم المطلوب (1-5): "

if "%choice%"=="1" goto full_build
if "%choice%"=="2" goto frontend_only
if "%choice%"=="3" goto electron_only
if "%choice%"=="4" goto install_deps
if "%choice%"=="5" goto clean_build
goto invalid_choice

:full_build
echo.
echo 🚀 بدء البناء الكامل...
echo.

REM تثبيت التبعيات
echo 📦 تثبيت تبعيات Frontend...
call npm install
if %errorlevel% neq 0 goto error_exit

echo 📦 تثبيت تبعيات Backend...
cd backend
call npm install
cd ..
if %errorlevel% neq 0 goto error_exit

echo 📦 تثبيت Electron...
call npm install electron electron-builder --save-dev
if %errorlevel% neq 0 goto error_exit

REM بناء Frontend
echo 🏗️ بناء Frontend...
call npm run build
if %errorlevel% neq 0 goto error_exit

REM تحضير Electron
echo 🔧 تحضير Electron...
copy electron-main.js main.js > nul
copy electron-package.json package-electron.json > nul

REM إنشاء أيقونة افتراضية إذا لم تكن موجودة
if not exist "assets\icon.ico" (
    echo 🎨 إنشاء أيقونة افتراضية...
    echo. > assets\icon.ico
)

REM بناء التطبيق
echo 🏗️ بناء ملف التثبيت...
call npx electron-builder --win
if %errorlevel% neq 0 goto error_exit

goto success

:frontend_only
echo 🏗️ بناء Frontend فقط...
call npm run build
if %errorlevel% neq 0 goto error_exit
goto success

:electron_only
echo 🖥️ بناء Electron فقط...
call npm install electron electron-builder --save-dev
if %errorlevel% neq 0 goto error_exit

copy electron-main.js main.js > nul
copy electron-package.json package-electron.json > nul

call npx electron-builder --win
if %errorlevel% neq 0 goto error_exit
goto success

:install_deps
echo 📦 تثبيت التبعيات...
call npm install
cd backend
call npm install
cd ..
call npm install electron electron-builder --save-dev
if %errorlevel% neq 0 goto error_exit
goto success

:clean_build
echo 🧹 تنظيف المشروع...
if exist "node_modules" rmdir /s /q node_modules
if exist "backend\node_modules" rmdir /s /q backend\node_modules
if exist "dist" rmdir /s /q dist
if exist "dist-electron" rmdir /s /q dist-electron

echo 📦 إعادة تثبيت التبعيات...
call npm install
cd backend
call npm install
cd ..
call npm install electron electron-builder --save-dev

echo 🏗️ إعادة البناء...
call npm run build
copy electron-main.js main.js > nul
copy electron-package.json package-electron.json > nul
call npx electron-builder --win
if %errorlevel% neq 0 goto error_exit
goto success

:invalid_choice
echo ❌ اختيار غير صحيح
pause
exit /b 1

:error_exit
echo.
echo ❌ حدث خطأ أثناء البناء!
echo.
echo 🔧 حلول مقترحة:
echo 1. تأكد من اتصال الإنترنت
echo 2. أغلق برامج مكافحة الفيروسات مؤقتاً
echo 3. شغل Command Prompt كمدير
echo 4. جرب الخيار "تنظيف وإعادة البناء"
echo.
pause
exit /b 1

:success
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        🎉 نجح البناء!                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📁 الملفات المنشأة:
if exist "dist" (
    echo 📦 Frontend: dist\
    dir dist\*.html 2>nul | find ".html"
)
if exist "dist-electron" (
    echo 🖥️ Electron: dist-electron\
    dir dist-electron\*.exe 2>nul
    dir dist-electron\*.msi 2>nul
)
echo.
echo 🚀 يمكنك الآن:
echo 1. اختبار التطبيق: npm run electron
echo 2. توزيع ملفات التثبيت من مجلد dist-electron
echo 3. تشغيل النظام: start.bat
echo.
pause
