const express = require('express'); 
const path = require('path'); 
const { spawn } = require('child_process'); 
 
const app = express(); 
const PORT = 5003; 
 
app.use(express.static('dist')); 
app.use('/api', require('./backend/routes/customers')); 
app.use('/api', require('./backend/routes/products')); 
app.use('/api', require('./backend/routes/sales')); 
 
app.listen(PORT, () => { 
  console.log('Kasheer Toosar started on http://localhost:' + PORT); 
  setTimeout(() => { 
    require('child_process').exec('start http://localhost:' + PORT); 
  }, 2000); 
}); 
