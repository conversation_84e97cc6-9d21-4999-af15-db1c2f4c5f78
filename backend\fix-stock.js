const pool = require('./database');

async function fixStock() {
  try {
    console.log('🔧 إصلاح مخزون المنتجات...');
    
    // تحديث مخزون جميع المنتجات إلى 100
    const result = await pool.query(`
      UPDATE pos_system.products 
      SET stock = 100, updated_at = CURRENT_TIMESTAMP 
      WHERE stock = 0 OR stock IS NULL
      RETURNING id, name, stock
    `);
    
    console.log(`✅ تم تحديث مخزون ${result.rows.length} منتج:`);
    
    result.rows.forEach((product, index) => {
      console.log(`${index + 1}. ${product.name}: ${product.stock} قطعة`);
    });
    
  } catch (error) {
    console.error('❌ خطأ في إصلاح المخزون:', error);
  } finally {
    process.exit(0);
  }
}

fixStock();
