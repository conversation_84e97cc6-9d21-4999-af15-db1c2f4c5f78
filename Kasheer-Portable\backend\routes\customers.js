const express = require('express');
const router = express.Router();
const pool = require('../database');

// عرض جميع العملاء
router.get('/', async (req, res) => {
  try {
    const result = await pool.query(
      'SELECT * FROM pos_system.customers WHERE is_active = true ORDER BY created_at DESC'
    );
    res.json(result.rows);
  } catch (error) {
    console.error('خطأ في عرض العملاء:', error);
    res.status(500).json({ error: 'خطأ في عرض العملاء' });
  }
});

// الحصول على العملاء المدينون مع تفاصيل الديون
router.get('/debtors', async (req, res) => {
  try {
    console.log('🔍 بدء استعلام المدينون...');

    // استعلام مجمع للعملاء الذين لديهم ديون (تجميع كل ديون العميل الواحد)
    const result = await pool.query(`
      SELECT
        c.id::text,
        c.name,
        c.phone,
        c.email,
        c.address,
        c.balance,
        c.credit_limit,
        c.created_at,
        c.updated_at,
        SUM(cd.amount) as total_debt,
        SUM(cd.paid_amount) as paid_amount,
        SUM(cd.amount - cd.paid_amount) as remaining_debt,
        COUNT(cd.id) as debt_count
      FROM pos_system.customers c
      INNER JOIN pos_system.customer_debts cd ON c.id = cd.customer_id
      WHERE cd.status IN ('pending', 'partial')
        AND c.is_active = true
      GROUP BY c.id, c.name, c.phone, c.email, c.address, c.balance, c.credit_limit, c.created_at, c.updated_at
      ORDER BY SUM(cd.amount - cd.paid_amount) DESC
    `);

    console.log('✅ تم العثور على', result.rows.length, 'مدين');
    res.json(result.rows);
  } catch (error) {
    console.error('❌ خطأ في عرض العملاء المدينون:', error.message);
    console.error('تفاصيل:', error);
    res.status(500).json({
      error: 'خطأ في عرض العملاء المدينون',
      details: error.message
    });
  }
});

// الحصول على العملاء الدائنون
router.get('/creditors', async (req, res) => {
  try {
    const result = await pool.query(
      'SELECT * FROM pos_system.customers WHERE balance > 0 AND is_active = true ORDER BY balance DESC'
    );
    res.json(result.rows);
  } catch (error) {
    console.error('خطأ في عرض العملاء الدائنون:', error);
    res.status(500).json({ error: 'خطأ في عرض العملاء الدائنون' });
  }
});

// 💰 تسديد دين عميل
router.post('/debts/:debtId/pay', async (req, res) => {
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    const { debtId } = req.params;
    const { amount, payment_method = 'cash', notes = '' } = req.body;

    console.log(`💰 تسديد دين ${debtId} بمبلغ ${amount} دج`);

    // التحقق من وجود الدين
    const debtResult = await client.query(`
      SELECT cd.*, c.name as customer_name
      FROM pos_system.customer_debts cd
      JOIN pos_system.customers c ON cd.customer_id = c.id
      WHERE cd.id = $1 AND cd.status IN ('pending', 'partial')
    `, [debtId]);

    if (debtResult.rows.length === 0) {
      return res.status(404).json({ error: 'الدين غير موجود أو مسدد بالفعل' });
    }

    const debt = debtResult.rows[0];
    const paymentAmount = parseFloat(amount);
    const remainingAmount = parseFloat(debt.remaining_amount);

    if (paymentAmount <= 0) {
      return res.status(400).json({ error: 'مبلغ الدفع يجب أن يكون أكبر من صفر' });
    }

    if (paymentAmount > remainingAmount) {
      return res.status(400).json({
        error: `مبلغ الدفع أكبر من المبلغ المستحق. المبلغ المستحق: ${remainingAmount} دج`
      });
    }

    // تسجيل الدفعة في جدول customer_debt_payments
    const paymentResult = await client.query(`
      INSERT INTO pos_system.customer_debt_payments (
        debt_id, amount, payment_method, payment_date, notes
      )
      VALUES ($1, $2, $3, CURRENT_DATE, $4)
      RETURNING *
    `, [debtId, paymentAmount, payment_method, notes]);

    // تحديث الدين
    const newPaidAmount = parseFloat(debt.paid_amount) + paymentAmount;
    const newRemainingAmount = parseFloat(debt.amount) - newPaidAmount;
    const newStatus = newRemainingAmount <= 0 ? 'paid' : 'partial';

    await client.query(`
      UPDATE pos_system.customer_debts
      SET paid_amount = $1, status = $2, updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
    `, [newPaidAmount, newStatus, debtId]);

    await client.query('COMMIT');

    console.log(`✅ تم تسديد ${paymentAmount} دج من دين العميل ${debt.customer_name}`);

    res.json({
      success: true,
      message: 'تم تسديد الدفعة بنجاح',
      payment: paymentResult.rows[0],
      debt_status: newStatus,
      remaining_amount: newRemainingAmount
    });

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ خطأ في تسديد الدين:', error);
    res.status(500).json({
      error: 'خطأ في تسديد الدين',
      details: error.message
    });
  } finally {
    client.release();
  }
});

// عرض عميل واحد
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query(
      'SELECT * FROM pos_system.customers WHERE id = $1 AND is_active = true',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'العميل غير موجود' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('خطأ في عرض العميل:', error);
    res.status(500).json({ error: 'خطأ في عرض العميل' });
  }
});

// إضافة عميل جديد
router.post('/', async (req, res) => {
  try {
    const { name, phone, address, email, credit_limit } = req.body;

    const result = await pool.query(`
      INSERT INTO pos_system.customers (name, phone, address, email, credit_limit)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [name, phone, address, email, credit_limit || 0]);

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('خطأ في إضافة العميل:', error);
    res.status(500).json({ error: 'خطأ في إضافة العميل' });
  }
});

// تعديل عميل
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, phone, address, email, credit_limit } = req.body;

    const result = await pool.query(`
      UPDATE pos_system.customers
      SET name = $1, phone = $2, address = $3, email = $4, credit_limit = $5, updated_at = CURRENT_TIMESTAMP
      WHERE id = $6
      RETURNING *
    `, [name, phone, address, email, credit_limit, id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'العميل غير موجود' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('خطأ في تعديل العميل:', error);
    res.status(500).json({ error: 'خطأ في تعديل العميل' });
  }
});

// حذف عميل (حذف منطقي)
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(
      'UPDATE pos_system.customers SET is_active = false WHERE id = $1 RETURNING *',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'العميل غير موجود' });
    }

    res.json({ message: 'تم حذف العميل بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف العميل:', error);
    res.status(500).json({ error: 'خطأ في حذف العميل' });
  }
});

// تحديث رصيد العميل مع التسديد التلقائي للديون 🧠✨
router.patch('/:id/balance', async (req, res) => {
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    const { id } = req.params;
    const { amount, description } = req.body;

    if (!amount || amount === 0) {
      return res.status(400).json({ error: 'المبلغ مطلوب ولا يمكن أن يكون صفر' });
    }

    const chargeAmount = parseFloat(amount);

    // الحصول على معلومات العميل
    const customerResult = await client.query(
      'SELECT * FROM pos_system.customers WHERE id = $1 AND is_active = true',
      [id]
    );

    if (customerResult.rows.length === 0) {
      await client.query('ROLLBACK');
      return res.status(404).json({ error: 'العميل غير موجود' });
    }

    const customer = customerResult.rows[0];
    const currentBalance = parseFloat(customer.balance);

    // 🔍 جلب الديون المستحقة للعميل (مرتبة حسب الأقدم أولاً)
    const debtsResult = await client.query(`
      SELECT id, amount, paid_amount, (amount - paid_amount) as remaining_debt
      FROM pos_system.customer_debts
      WHERE customer_id = $1 AND status != 'paid' AND amount > paid_amount
      ORDER BY created_at ASC
    `, [id]);

    let remainingChargeAmount = chargeAmount;
    let totalDebtPaid = 0;
    const paidDebts = [];

    console.log(`💰 شحن رصيد للعميل ${customer.name}: ${chargeAmount} دج`);
    console.log(`📊 عدد الديون المستحقة: ${debtsResult.rows.length}`);

    // 🎯 التسديد التلقائي للديون من المبلغ المشحون
    for (const debt of debtsResult.rows) {
      if (remainingChargeAmount <= 0) break;

      const debtRemaining = parseFloat(debt.remaining_debt);
      const paymentAmount = Math.min(remainingChargeAmount, debtRemaining);

      if (paymentAmount > 0) {
        const newPaidAmount = parseFloat(debt.paid_amount) + paymentAmount;
        const newStatus = newPaidAmount >= parseFloat(debt.amount) ? 'paid' : 'partial';

        // تحديث الدين
        await client.query(`
          UPDATE pos_system.customer_debts
          SET paid_amount = $1, status = $2, updated_at = CURRENT_TIMESTAMP
          WHERE id = $3
        `, [newPaidAmount, newStatus, debt.id]);

        // تسجيل الدفعة التلقائية
        await client.query(`
          INSERT INTO pos_system.customer_debt_payments (
            debt_id, amount, payment_method, notes
          ) VALUES ($1, $2, $3, $4)
        `, [debt.id, paymentAmount, 'auto_charge', `🤖 تسديد تلقائي من شحن الرصيد - ${description || 'شحن رصيد'}`]);

        remainingChargeAmount -= paymentAmount;
        totalDebtPaid += paymentAmount;

        paidDebts.push({
          debt_id: debt.id,
          paid_amount: paymentAmount,
          status: newStatus,
          remaining_debt: debtRemaining - paymentAmount
        });

        console.log(`✅ تم تسديد ${paymentAmount} دج من الدين ${debt.id} (الحالة: ${newStatus})`);
      }
    }

    // 💳 إضافة المبلغ المتبقي إلى رصيد العميل
    const finalBalance = currentBalance + remainingChargeAmount;

    // تحديث رصيد العميل
    const updateResult = await client.query(
      'UPDATE pos_system.customers SET balance = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 RETURNING *',
      [finalBalance, id]
    );

    await client.query('COMMIT');

    // 📊 إعداد الرد التفصيلي
    const response = {
      success: true,
      message: '🎉 تم شحن الرصيد بنجاح!',
      customer: updateResult.rows[0],
      charge_summary: {
        total_charged: chargeAmount,
        debt_auto_paid: totalDebtPaid,
        added_to_balance: remainingChargeAmount,
        previous_balance: currentBalance,
        final_balance: finalBalance
      },
      auto_payments: paidDebts,
      debts_count: debtsResult.rows.length,
      paid_debts_count: paidDebts.filter(d => d.status === 'paid').length
    };

    // 💬 رسالة توضيحية ذكية
    if (totalDebtPaid > 0) {
      if (remainingChargeAmount > 0) {
        response.smart_message = `🧠 تم تسديد ${totalDebtPaid.toFixed(2)} دج من الديون تلقائياً، وإضافة ${remainingChargeAmount.toFixed(2)} دج للرصيد`;
      } else {
        response.smart_message = `🧠 تم تسديد ${totalDebtPaid.toFixed(2)} دج من الديون تلقائياً (تم استنفاد كامل المبلغ)`;
      }
    } else {
      response.smart_message = `💰 تم إضافة ${chargeAmount.toFixed(2)} دج للرصيد (لا توجد ديون مستحقة)`;
    }

    console.log(`🎯 النتيجة النهائية: ${response.smart_message}`);

    res.json(response);

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ خطأ في تحديث رصيد العميل:', error);
    res.status(500).json({ error: 'خطأ في تحديث رصيد العميل' });
  } finally {
    client.release();
  }
});



// الحصول على تفاصيل ديون عميل محدد
router.get('/:id/debts', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await pool.query(`
      SELECT
        cd.*,
        s.sale_number,
        s.total_amount as sale_total,
        s.created_at as sale_date
      FROM pos_system.customer_debts cd
      LEFT JOIN pos_system.sales s ON cd.sale_id = s.id
      WHERE cd.customer_id = $1
        AND cd.status IN ('pending', 'partial')
      ORDER BY cd.created_at DESC
    `, [id]);
    res.json(result.rows);
  } catch (error) {
    console.error('خطأ في عرض ديون العميل:', error);
    res.status(500).json({ error: 'خطأ في عرض ديون العميل' });
  }
});

// تسديد دين عميل
router.post('/:id/pay-debt', async (req, res) => {
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    const { id } = req.params;
    const { amount, payment_method, notes } = req.body;

    console.log('📊 بيانات الدفع المستلمة:', { customer_id: id, amount, payment_method, notes });
    console.log('📊 نوع المبلغ:', typeof amount, 'القيمة:', amount);

    // تنظيف المبلغ من أي أحرف غير صحيحة
    let cleanAmountStr = amount.toString().trim();

    // إزالة أي أحرف غير رقمية عدا النقطة العشرية
    cleanAmountStr = cleanAmountStr.replace(/[^0-9.]/g, '');

    // التأكد من وجود نقطة عشرية واحدة فقط
    const dotCount = (cleanAmountStr.match(/\./g) || []).length;
    if (dotCount > 1) {
      // إذا كان هناك أكثر من نقطة، احتفظ بالأولى فقط
      const firstDotIndex = cleanAmountStr.indexOf('.');
      cleanAmountStr = cleanAmountStr.substring(0, firstDotIndex + 1) +
                      cleanAmountStr.substring(firstDotIndex + 1).replace(/\./g, '');
    }

    console.log('📊 المبلغ بعد التنظيف:', cleanAmountStr);

    // التحقق من صحة البيانات
    if (!cleanAmountStr) {
      console.error('❌ بيانات الدفع غير صحيحة:', { amount: cleanAmountStr });
      return res.status(400).json({ error: 'بيانات الدفع غير صحيحة' });
    }

    const cleanAmount = parseFloat(cleanAmountStr);
    if (isNaN(cleanAmount) || cleanAmount <= 0) {
      console.error('❌ المبلغ غير صحيح:', cleanAmountStr, 'parsed:', cleanAmount);
      return res.status(400).json({ error: 'المبلغ غير صحيح' });
    }

    console.log('✅ المبلغ النهائي:', cleanAmount);

    // الحصول على ديون العميل مرتبة من الأقدم للأحدث (FIFO)
    const debtsResult = await client.query(`
      SELECT * FROM pos_system.customer_debts
      WHERE customer_id = $1 AND status IN ('pending', 'partial')
      ORDER BY created_at ASC
    `, [id]);

    if (debtsResult.rows.length === 0) {
      return res.status(404).json({ error: 'لا توجد ديون مستحقة لهذا العميل' });
    }

    // حساب إجمالي المبلغ المستحق
    const totalRemaining = debtsResult.rows.reduce((sum, debt) => {
      return sum + (parseFloat(debt.amount) - parseFloat(debt.paid_amount));
    }, 0);

    if (cleanAmount > totalRemaining) {
      return res.status(400).json({
        error: `المبلغ أكبر من إجمالي المبلغ المستحق. المبلغ المستحق: ${totalRemaining.toFixed(2)} دج`
      });
    }

    // توزيع المبلغ على الديون بطريقة FIFO (الأقدم أولاً)
    let remainingPayment = cleanAmount;
    const updatedDebts = [];

    for (const debt of debtsResult.rows) {
      if (remainingPayment <= 0) break;

      const debtRemaining = parseFloat(debt.amount) - parseFloat(debt.paid_amount);
      const paymentForThisDebt = Math.min(remainingPayment, debtRemaining);

      const newPaidAmount = parseFloat(debt.paid_amount) + paymentForThisDebt;
      const newStatus = newPaidAmount >= parseFloat(debt.amount) ? 'paid' : 'partial';

      // تحديث الدين
      await client.query(`
        UPDATE pos_system.customer_debts
        SET paid_amount = $1, status = $2, updated_at = CURRENT_TIMESTAMP
        WHERE id = $3
      `, [newPaidAmount, newStatus, debt.id]);

      // تسجيل الدفعة
      await client.query(`
        INSERT INTO pos_system.customer_debt_payments (
          debt_id, amount, payment_method, payment_date, notes
        )
        VALUES ($1, $2, $3, CURRENT_DATE, $4)
      `, [debt.id, paymentForThisDebt, payment_method || 'نقدي', notes || '']);

      updatedDebts.push({
        debt_id: debt.id,
        amount_paid: paymentForThisDebt,
        new_status: newStatus
      });

      remainingPayment -= paymentForThisDebt;
    }

    await client.query('COMMIT');

    res.json({
      success: true,
      message: 'تم تسديد الدين بنجاح',
      paid_amount: cleanAmount,
      updated_debts: updatedDebts,
      total_remaining: totalRemaining - cleanAmount,
      payment: {
        id: Date.now(),
        payment_date: new Date().toISOString(),
        amount: cleanAmount,
        payment_method: payment_method || 'نقدي',
        notes: notes || 'دفعة من الدين'
      }
    });

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('خطأ في تسديد الدين:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في تسديد الدين',
      details: error.message
    });
  } finally {
    client.release();
  }
});

// جلب العملاء الدائنين (الذين لديهم رصيد إيجابي)
router.get('/creditors', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT
        id,
        name,
        phone,
        email,
        address,
        balance,
        credit_limit,
        is_active,
        created_at,
        updated_at
      FROM pos_system.customers
      WHERE balance > 0 AND is_active = true
      ORDER BY balance DESC
    `);

    res.json(result.rows);
  } catch (error) {
    console.error('خطأ في جلب العملاء الدائنين:', error);
    res.status(500).json({ error: 'خطأ في جلب العملاء الدائنين' });
  }
});

module.exports = router;
