@echo off
title Kasheer POS System
cd /d "%~dp0"

echo Starting Kasheer POS System...

if not exist "node_modules" (
    echo Installing dependencies...
    npm install
)

if not exist "dist\index.html" (
    echo Building project...
    npx vite build
)

echo Starting server...
start /min node backend\server.js

timeout /t 3 /nobreak >nul

echo Opening browser...
start http://localhost:5003

echo.
echo System is running at: http://localhost:5003
echo Close this window to stop the server.
echo.
pause
