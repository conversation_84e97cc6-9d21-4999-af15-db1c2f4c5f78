const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'pos_system_db',
  password: 'toossar',
  port: 5432,
});

async function checkUnknownTables() {
  try {
    console.log('🔍 فحص جدولي unknown_sales و unknown_products...');
    
    // فحص الجداول الموجودة
    const tablesResult = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'pos_system' 
      AND table_name IN ('unknown_sales', 'unknown_products')
      ORDER BY table_name
    `);
    
    console.log('📋 الجداول الموجودة:');
    if (tablesResult.rows.length === 0) {
      console.log('❌ لا توجد جداول unknown_sales أو unknown_products');
    } else {
      tablesResult.rows.forEach(row => {
        console.log(`  ✅ ${row.table_name}`);
      });
    }
    
    // فحص هيكل جدول sale_items للمنتجات غير المعروفة
    console.log('\n🔍 فحص جدول sale_items...');
    const columnsResult = await pool.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_schema = 'pos_system' 
      AND table_name = 'sale_items'
      AND column_name IN ('is_unknown', 'is_unknown_product', 'unknown_product_code')
      ORDER BY column_name
    `);
    
    console.log('📋 أعمدة المنتجات غير المعروفة في sale_items:');
    if (columnsResult.rows.length === 0) {
      console.log('❌ لا توجد أعمدة للمنتجات غير المعروفة');
    } else {
      columnsResult.rows.forEach(row => {
        console.log(`  ✅ ${row.column_name} (${row.data_type})`);
      });
    }
    
    // فحص إمكانية product_id أن يكون NULL
    const productIdResult = await pool.query(`
      SELECT column_name, is_nullable
      FROM information_schema.columns 
      WHERE table_schema = 'pos_system' 
      AND table_name = 'sale_items'
      AND column_name = 'product_id'
    `);
    
    if (productIdResult.rows.length > 0) {
      const isNullable = productIdResult.rows[0].is_nullable;
      console.log(`\n📋 product_id nullable: ${isNullable === 'YES' ? '✅ نعم' : '❌ لا'}`);
    }
    
  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await pool.end();
    process.exit(0);
  }
}

checkUnknownTables();
