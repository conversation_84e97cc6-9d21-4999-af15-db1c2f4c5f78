import React, { useState } from 'react';
import {
  Cal<PERSON>tor,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Wallet,
  BarChart3,
  PieChart,
  Activity,
  Target,
  RefreshCw,
  Download,
  ArrowUpRight,
  ArrowDownRight,
  ShoppingCart,
  Package,
  CreditCard,
  Loader,
  Eye,
  AlertTriangle,
  CheckCircle,
  Bell,
  FileText,
  Calendar,
  MoreVertical,
  Trash2,
  Award,
  Building
} from 'lucide-react';

interface FinancialData {
  revenue: {
    total: number;
    today: number;
    month: number;
    growth: number;
    breakdown: {
      sales: {
        cash_sales: number;
        pending_credit_sales: number; // المبيعات الآجلة (لم تُدفع بعد)
        total_sales: number; // إجمالي المبيعات (نقدي + آجل)
      };
      debt_payments: {
        total_collected: number;
        payments_count: number;
      };
      totals: {
        grand_total: number;
        today_total: number;
        month_total: number;
        growth_percentage: number;
      };
    };
    sales_count: number;
    avg_sale: number;
  };
  expenses: {
    total: number;
    today?: number;
    month?: number;
    growth?: number;
    breakdown?: {
      direct_expenses?: {
        total: number;
        count: number;
        today: number;
        month: number;
      };
      purchases_expenses?: {
        total: number;
        count: number;
        today: number;
        month: number;
      };
      categories?: Array<{
        name: string;
        amount: number;
        count: number;
      }>;
    };
    status?: 'active' | 'error' | 'pending';
    message?: string;
  };
  profit: {
    net_profit: number;
    margin: number;
  };
  cash_flow: {
    total: number;
    breakdown?: {
      cash_received: number;
      creditors_balance: number;
      debtors_balance: number;
      pending_debts: number;
    };
    status?: 'positive' | 'negative' | 'neutral';
    message?: string;
  };
  alerts?: {
    total_count: number;
    high_priority: number;
    medium_priority: number;
    low_priority: number;
    by_category: {
      inventory: number;
      debt: number;
      performance: number;
      cash_flow: number;
    };
    items: Array<{
      id: string;
      type: 'success' | 'warning' | 'danger' | 'info';
      category: string;
      title: string;
      message: string;
      details: string;
      severity: 'high' | 'medium' | 'low';
      icon: string;
      action: string;
      created_at: string;
    }>;
  };
}

interface MonthlyData {
  month: string;
  revenue: number;
  expenses: number;
  profit: number;
}

interface CategoryExpense {
  category: string;
  amount: number;
  percentage: number;
  color: string;
}

interface FinancialAlert {
  id: string;
  type: 'warning' | 'danger' | 'info' | 'success';
  title: string;
  message: string;
  date: Date;
}

const FinancialCenter: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<'today' | 'week' | 'month' | 'quarter' | 'year'>('month');
  const [activeTab, setActiveTab] = useState<'overview' | 'revenue' | 'expenses' | 'balance' | 'reports'>('overview');
  const [loading, setLoading] = useState(false);

  // البيانات المالية الحقيقية
  const [financialData, setFinancialData] = useState<FinancialData | null>(null);
  const [expenseCategories] = useState<CategoryExpense[]>([]);

  // تحميل البيانات المالية الحقيقية
  const loadFinancialData = async () => {
    try {
      setLoading(true);
      console.log('🔄 جاري تحميل البيانات المالية...');

      const response = await fetch('/api/financial/overview');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ تم تحميل البيانات المالية:', data);

      setFinancialData(data);
    } catch (error) {
      console.error('❌ خطأ في تحميل البيانات المالية:', error);
      // عرض بيانات فارغة في حالة الخطأ
      setFinancialData({
        revenue: {
          total: 0,
          today: 0,
          month: 0,
          growth: 0,
          breakdown: {
            sales: { cash_sales: 0, pending_credit_sales: 0, total_sales: 0 },
            debt_payments: { total_collected: 0, payments_count: 0 },
            totals: { grand_total: 0, today_total: 0, month_total: 0, growth_percentage: 0 }
          },
          sales_count: 0,
          avg_sale: 0
        },
        expenses: { total: 0, message: 'لم يتم ربط المصروفات بعد' },
        profit: { net_profit: 0, margin: 0 },
        cash_flow: { total: 0, message: 'حساب مبدئي' }
      });
    } finally {
      setLoading(false);
    }
  };

  // 🔍 فحص وتحليل المصروفات
  const analyzeExpenses = async () => {
    try {
      setLoading(true);
      console.log('🔍 بدء فحص وتحليل المصروفات...');

      const response = await fetch('/api/expenses/detailed-analysis', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('📊 تحليل المصروفات:', result);

      // عرض التحليل المفصل
      const analysisMessage = `📊 تحليل المصروفات المفصل:

💰 إجمالي المصروفات: ${result.total_expenses} دج
📋 عدد المصروفات: ${result.expenses_count}

📈 توزيع المصروفات:
${result.categories.map((cat: any) => `- ${cat.category}: ${cat.total} دج (${cat.count} عملية)`).join('\n')}

🔍 أكبر 5 مصروفات:
${result.top_expenses.map((exp: any, i: number) => `${i+1}. ${exp.description}: ${exp.amount} دج`).join('\n')}

💡 التوصيات:
${result.recommendations.join('\n')}`;

      alert(analysisMessage);

    } catch (error) {
      console.error('❌ خطأ في تحليل المصروفات:', error);
      alert('حدث خطأ في تحليل المصروفات. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoading(false);
    }
  };

  // 🗑️ حذف جميع فواتير المشتريات
  const deleteAllInvoices = async () => {
    const confirmMessage = `🚨 تحذير خطير! هل أنت متأكد من حذف جميع فواتير المشتريات؟

🗑️ سيتم حذف:
- جميع فواتير المشتريات (نهائياً)
- جميع الديون المرتبطة بها
- إعادة تصفير أرصدة جميع الموردين

⚠️ تحذير: هذا الإجراء خطير ولا يمكن التراجع عنه!
⚠️ ستفقد جميع بيانات المشتريات نهائياً!

اكتب "نعم احذف الكل" للتأكيد:`;

    const userInput = prompt(confirmMessage);
    if (userInput !== "نعم احذف الكل") {
      alert('تم إلغاء العملية للحماية');
      return;
    }

    try {
      setLoading(true);
      console.log('🗑️ بدء حذف جميع فواتير المشتريات...');

      const response = await fetch('/api/purchases/delete-all', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ تم حذف جميع الفواتير:', result);

      // إعادة تحميل البيانات المالية
      await loadFinancialData();

      alert(`✅ تم حذف جميع فواتير المشتريات بنجاح!

📊 النتائج:
- عدد الفواتير المحذوفة: ${result.deleted_invoices}
- عدد الديون المحذوفة: ${result.deleted_debts}
- تم إعادة تصفير أرصدة الموردين: ${result.suppliers_reset ? 'نعم' : 'لا'}

🔄 تم تحديث البيانات المالية تلقائياً`);

    } catch (error) {
      console.error('❌ خطأ في حذف الفواتير:', error);
      alert('حدث خطأ في حذف الفواتير. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoading(false);
    }
  };

  // 🧹 تنظيف المصروفات المكررة
  const cleanupDuplicateExpenses = async () => {
    const confirmMessage = `⚠️ هل أنت متأكد من تنظيف المصروفات المكررة؟

🧹 سيتم حذف:
- المصروفات المرتبطة بفواتير المشتريات
- المصروفات التي تم إنشاؤها تلقائياً
- المصروفات المكررة

⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!`;

    if (!confirm(confirmMessage)) {
      return;
    }

    try {
      setLoading(true);
      console.log('🧹 بدء تنظيف المصروفات المكررة...');

      const response = await fetch('/api/expenses/cleanup-duplicates', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ تم تنظيف المصروفات:', result);

      // إعادة تحميل البيانات المالية
      await loadFinancialData();

      alert(`✅ تم تنظيف المصروفات بنجاح!

📊 النتائج:
- عدد المصروفات المحذوفة: ${result.deleted_expenses}
- ${result.message}

🔄 تم تحديث البيانات المالية تلقائياً`);

    } catch (error) {
      console.error('❌ خطأ في تنظيف المصروفات:', error);
      alert('حدث خطأ في تنظيف المصروفات. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoading(false);
    }
  };

  // تحميل البيانات عند بدء الصفحة
  React.useEffect(() => {
    loadFinancialData();

    // إضافة مستمع للتنقل
    const handleNavigate = (event: any) => {
      const targetView = event.detail;
      // إرسال إشارة للمكون الرئيسي للتنقل
      if (window.parent && window.parent.postMessage) {
        window.parent.postMessage({ type: 'navigate', view: targetView }, '*');
      }
      // أو استخدام localStorage كحل بديل
      localStorage.setItem('pendingNavigation', targetView);
      window.location.reload();
    };

    window.addEventListener('navigate', handleNavigate);

    return () => {
      window.removeEventListener('navigate', handleNavigate);
    };
  }, []);

  const formatCurrency = (amount: number | undefined) => {
    const numAmount = amount || 0;
    return `${numAmount.toLocaleString()} دج`;
  };

  const formatPercentage = (value: number | undefined) => {
    const numValue = value || 0;
    return `${numValue.toFixed(1)}%`;
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'warning': return <AlertTriangle className="w-5 h-5 text-yellow-400" />;
      case 'danger': return <AlertTriangle className="w-5 h-5 text-red-400" />;
      case 'success': return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'info': return <Bell className="w-5 h-5 text-blue-400" />;
      default: return <Bell className="w-5 h-5 text-gray-400" />;
    }
  };

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'warning': return 'border-yellow-400/30 bg-yellow-500/10';
      case 'danger': return 'border-red-400/30 bg-red-500/10';
      case 'success': return 'border-green-400/30 bg-green-500/10';
      case 'info': return 'border-blue-400/30 bg-blue-500/10';
      default: return 'border-gray-400/30 bg-gray-500/10';
    }
  };

  // 🔔 معالجة إجراءات التنبيهات الذكية
  const handleAlertAction = (alert: any) => {
    // إنشاء إشعار مؤقت
    const showNotification = (message: string, color: string = 'green') => {
      const notification = document.createElement('div');
      notification.innerHTML = `
        <div class="fixed top-4 right-4 bg-${color}-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 animate-bounce">
          ${message}
        </div>
      `;
      document.body.appendChild(notification);

      setTimeout(() => {
        if (document.body.contains(notification)) {
          document.body.removeChild(notification);
        }
      }, 3000);
    };

    switch (alert.category) {
      case 'inventory':
        showNotification('🔄 جاري الانتقال لصفحة المخزون...', 'blue');
        // استخدام localStorage للتنقل
        localStorage.setItem('pendingNavigation', 'product-management');
        window.location.reload();
        break;
      case 'debt':
        showNotification('🔄 جاري الانتقال لصفحة المدينين...', 'orange');
        localStorage.setItem('pendingNavigation', 'debtors');
        window.location.reload();
        break;
      case 'performance':
        showNotification('🔄 جاري الانتقال لصفحة المبيعات...', 'purple');
        localStorage.setItem('pendingNavigation', 'sales');
        window.location.reload();
        break;
      case 'cash_flow':
        showNotification('🔄 جاري الانتقال لصفحة العملاء...', 'indigo');
        localStorage.setItem('pendingNavigation', 'customers');
        window.location.reload();
        break;
      default:
        showNotification(`✅ تم تنفيذ: ${alert.action}`, 'green');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-indigo-900 to-slate-900 p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="bg-gradient-to-r from-indigo-500 to-purple-600 p-3 rounded-2xl shadow-lg animate-pulse">
              <Calculator className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">💰 المركز المالي</h1>
              <p className="text-gray-300">نظرة شاملة على الوضع المالي والأداء</p>
            </div>
          </div>

          <div className="flex items-center space-x-4 space-x-reverse">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value as any)}
              className="bg-indigo-900/30 border border-indigo-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-indigo-400 transition-all duration-300 cursor-pointer hover:bg-indigo-800/40"
              style={{
                backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236366f1' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'left 12px center',
                backgroundSize: '16px'
              }}
            >
              <option value="today" className="bg-indigo-900 text-white">اليوم</option>
              <option value="week" className="bg-indigo-900 text-white">هذا الأسبوع</option>
              <option value="month" className="bg-indigo-900 text-white">هذا الشهر</option>
              <option value="quarter" className="bg-indigo-900 text-white">هذا الربع</option>
              <option value="year" className="bg-indigo-900 text-white">هذا العام</option>
            </select>

            <button className="bg-white/10 backdrop-blur-lg border border-white/20 hover:border-white/40 px-4 py-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse text-white">
              <Download className="w-5 h-5" />
              <span>تصدير</span>
            </button>

            <button
              onClick={analyzeExpenses}
              disabled={loading}
              className="bg-blue-500/20 backdrop-blur-lg border border-blue-400/30 hover:border-blue-400/50 px-4 py-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse text-blue-300 hover:text-blue-200 disabled:opacity-50"
            >
              <Eye className="w-5 h-5" />
              <span>فحص المصروفات</span>
            </button>

            <button
              onClick={deleteAllInvoices}
              disabled={loading}
              className="bg-red-600/30 backdrop-blur-lg border border-red-500/40 hover:border-red-500/60 px-4 py-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse text-red-200 hover:text-red-100 disabled:opacity-50"
            >
              <Trash2 className="w-5 h-5" />
              <span>حذف جميع الفواتير</span>
            </button>

            <button
              onClick={cleanupDuplicateExpenses}
              disabled={loading}
              className="bg-red-500/20 backdrop-blur-lg border border-red-400/30 hover:border-red-400/50 px-4 py-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse text-red-300 hover:text-red-200 disabled:opacity-50"
            >
              <AlertTriangle className="w-5 h-5" />
              <span>تنظيف المصروفات</span>
            </button>

            <button
              onClick={loadFinancialData}
              disabled={loading}
              className="bg-white/10 backdrop-blur-lg border border-white/20 hover:border-white/40 px-4 py-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse text-white disabled:opacity-50"
            >
              <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
              <span>{loading ? 'جاري التحديث...' : 'تحديث'}</span>
            </button>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-2 border border-white/20">
          <div className="flex space-x-2 space-x-reverse">
            {[
              { id: 'overview', label: 'نظرة عامة', icon: <PieChart className="w-5 h-5" /> },
              { id: 'revenue', label: 'الإيرادات', icon: <TrendingUp className="w-5 h-5" /> },
              { id: 'expenses', label: 'المصروفات', icon: <TrendingDown className="w-5 h-5" /> },
              { id: 'balance', label: 'الميزانية', icon: <BarChart3 className="w-5 h-5" /> },
              { id: 'reports', label: 'التقارير', icon: <FileText className="w-5 h-5" /> }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex-1 flex items-center justify-center space-x-2 space-x-reverse px-4 py-3 rounded-xl font-semibold transition-all duration-300 ${
                  activeTab === tab.id
                    ? 'bg-indigo-500 text-white shadow-lg'
                    : 'text-gray-300 hover:text-white hover:bg-white/10'
                }`}
              >
                {tab.icon}
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* المحتوى الرئيسي */}
      {loading && (
        <div className="text-center py-16">
          <Loader className="w-12 h-12 text-indigo-400 mx-auto mb-4 animate-spin" />
          <p className="text-white text-lg font-semibold">🔄 جاري تحميل البيانات المالية...</p>
          <p className="text-gray-400 text-sm">يتم جلب البيانات الحقيقية من قاعدة البيانات</p>
        </div>
      )}

      {!loading && activeTab === 'overview' && financialData && (
        <div className="space-y-8">
          {/* الإحصائيات الرئيسية */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* 💰 إجمالي الإيرادات الحقيقية */}
            <div className="bg-gradient-to-br from-green-500/20 to-emerald-600/20 backdrop-blur-lg rounded-2xl p-6 border border-green-400/30 hover:border-green-400/50 transition-all duration-300 hover:scale-105 group">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-300 text-sm mb-1 flex items-center space-x-1 space-x-reverse">
                    <DollarSign className="w-4 h-4" />
                    <span>إجمالي الإيرادات</span>
                    <span className="bg-green-500/30 text-green-200 text-xs px-2 py-1 rounded-full">حقيقي</span>
                  </p>
                  <p className="text-3xl font-bold text-white">{formatCurrency(financialData.revenue?.total)}</p>
                  <div className="flex items-center space-x-1 space-x-reverse mt-2">
                    {(financialData.revenue?.growth || 0) >= 0 ? (
                      <ArrowUpRight className="w-4 h-4 text-green-400" />
                    ) : (
                      <ArrowDownRight className="w-4 h-4 text-red-400" />
                    )}
                    <span className={`text-sm font-semibold ${(financialData.revenue?.growth || 0) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {(financialData.revenue?.growth || 0) >= 0 ? '+' : ''}{formatPercentage(financialData.revenue?.growth)}
                    </span>
                  </div>
                  <div className="mt-2 text-xs text-green-200">
                    <p>🛒 مبيعات نقدية: {formatCurrency(financialData.revenue?.breakdown?.sales?.cash_sales)}</p>
                    <p>💰 تحصيل ديون: {formatCurrency(financialData.revenue?.breakdown?.debt_payments?.total_collected)}</p>
                  </div>
                </div>
                <div className="bg-green-500/30 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                  <TrendingUp className="w-6 h-6 text-green-400" />
                </div>
              </div>
            </div>

            {/* 📉 إجمالي المصروفات (مؤقت) */}
            <div className="bg-gradient-to-br from-red-500/20 to-pink-600/20 backdrop-blur-lg rounded-2xl p-6 border border-red-400/30 hover:border-red-400/50 transition-all duration-300 hover:scale-105 group">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-red-300 text-sm mb-1 flex items-center space-x-1 space-x-reverse">
                    <TrendingDown className="w-4 h-4" />
                    <span>إجمالي المصروفات</span>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      financialData.expenses?.status === 'active'
                        ? 'bg-green-500/30 text-green-200'
                        : 'bg-yellow-500/30 text-yellow-200'
                    }`}>
                      {financialData.expenses?.status === 'active' ? 'مربوط' : 'قريباً'}
                    </span>
                  </p>
                  <p className="text-3xl font-bold text-white">{formatCurrency(financialData.expenses?.total)}</p>
                  <div className="flex items-center space-x-1 space-x-reverse mt-2">
                    {financialData.expenses?.status === 'active' ? (
                      <>
                        <Package className="w-4 h-4 text-green-400" />
                        <span className="text-green-300 text-xs">
                          {financialData.expenses?.breakdown?.direct_expenses?.count || 0} مصروف مباشر + {financialData.expenses?.breakdown?.purchases_expenses?.count || 0} فاتورة مشتريات
                        </span>
                      </>
                    ) : (
                      <>
                        <Package className="w-4 h-4 text-yellow-400" />
                        <span className="text-yellow-400 text-sm font-semibold">ينتظر ربط المشتريات</span>
                      </>
                    )}
                  </div>
                  {financialData.expenses?.message && (
                    <p className="mt-2 text-xs text-red-200">{financialData.expenses.message}</p>
                  )}

                  {/* 📊 تفصيل المصروفات */}
                  {financialData.expenses?.status === 'active' && financialData.expenses?.breakdown && (
                    <div className="mt-3 space-y-1">
                      <div className="flex justify-between text-xs">
                        <span className="text-red-200">مصروفات مباشرة:</span>
                        <span className="text-white font-semibold">{formatCurrency(financialData.expenses.breakdown.direct_expenses?.total)}</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span className="text-red-200">مشتريات مدفوعة:</span>
                        <span className="text-white font-semibold">{formatCurrency(financialData.expenses.breakdown.purchases_expenses?.total)}</span>
                      </div>
                    </div>
                  )}
                </div>
                <div className="bg-red-500/30 p-3 rounded-xl group-hover:scale-110 transition-transform duration-300">
                  <TrendingDown className="w-6 h-6 text-red-400" />
                </div>
              </div>
            </div>



            {/* 💧 التدفق النقدي الذكي */}
            <div className={`bg-gradient-to-br backdrop-blur-lg rounded-2xl p-6 border transition-all duration-300 hover:scale-105 group ${
              financialData.cash_flow?.status === 'positive'
                ? 'from-purple-500/20 to-pink-600/20 border-purple-400/30 hover:border-purple-400/50'
                : financialData.cash_flow?.status === 'negative'
                ? 'from-red-500/20 to-orange-600/20 border-red-400/30 hover:border-red-400/50'
                : 'from-gray-500/20 to-slate-600/20 border-gray-400/30 hover:border-gray-400/50'
            }`}>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-300 text-sm mb-1 flex items-center space-x-1 space-x-reverse">
                    <Wallet className="w-4 h-4" />
                    <span>التدفق النقدي</span>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      financialData.cash_flow?.status === 'positive'
                        ? 'bg-green-500/30 text-green-200'
                        : financialData.cash_flow?.status === 'negative'
                        ? 'bg-red-500/30 text-red-200'
                        : 'bg-gray-500/30 text-gray-200'
                    }`}>
                      {financialData.cash_flow?.status === 'positive' ? '✅ إيجابي' :
                       financialData.cash_flow?.status === 'negative' ? '⚠️ سلبي' : '➖ متوازن'}
                    </span>
                  </p>
                  <p className={`text-3xl font-bold ${
                    financialData.cash_flow?.status === 'positive' ? 'text-green-400' :
                    financialData.cash_flow?.status === 'negative' ? 'text-red-400' : 'text-white'
                  }`}>
                    {formatCurrency(financialData.cash_flow?.total)}
                  </p>
                  <div className="flex items-center space-x-1 space-x-reverse mt-2">
                    {financialData.cash_flow?.status === 'positive' ? (
                      <TrendingUp className="w-4 h-4 text-green-400" />
                    ) : financialData.cash_flow?.status === 'negative' ? (
                      <TrendingDown className="w-4 h-4 text-red-400" />
                    ) : (
                      <Activity className="w-4 h-4 text-gray-400" />
                    )}
                    <span className="text-purple-400 text-sm font-semibold">حساب ذكي شامل</span>
                  </div>
                  {financialData.cash_flow?.breakdown && (
                    <div className="mt-2 text-xs text-purple-200">
                      <p>💰 نقد محصل: {formatCurrency(financialData.cash_flow.breakdown.cash_received)}</p>
                      <p>📈 أرصدة دائنة: {formatCurrency(financialData.cash_flow.breakdown.creditors_balance)}</p>
                      <p>📉 ديون مستحقة: -{formatCurrency(financialData.cash_flow.breakdown.debtors_balance + financialData.cash_flow.breakdown.pending_debts)}</p>
                    </div>
                  )}
                </div>
                <div className={`p-3 rounded-xl group-hover:scale-110 transition-transform duration-300 ${
                  financialData.cash_flow?.status === 'positive'
                    ? 'bg-green-500/30'
                    : financialData.cash_flow?.status === 'negative'
                    ? 'bg-red-500/30'
                    : 'bg-gray-500/30'
                }`}>
                  <Wallet className={`w-6 h-6 ${
                    financialData.cash_flow?.status === 'positive'
                      ? 'text-green-400'
                      : financialData.cash_flow?.status === 'negative'
                      ? 'text-red-400'
                      : 'text-gray-400'
                  }`} />
                </div>
              </div>
            </div>
          </div>

          {/* 💰 تفاصيل الإيرادات الحقيقية */}
          <div className="bg-gradient-to-r from-green-500/20 to-emerald-600/20 backdrop-blur-lg rounded-2xl p-6 border border-green-400/30">
            <h3 className="text-2xl font-bold text-white mb-6 flex items-center space-x-2 space-x-reverse">
              <DollarSign className="w-6 h-6 text-green-400" />
              <span>💰 تفاصيل الإيرادات الحقيقية</span>
              <span className="bg-green-500/30 text-green-200 text-sm px-3 py-1 rounded-full">مباشر من قاعدة البيانات</span>
            </h3>

            {/* توضيح الحساب */}
            <div className="mb-6 p-4 bg-white/10 rounded-xl border border-green-400/20">
              <h4 className="text-lg font-semibold text-white mb-3 flex items-center space-x-2 space-x-reverse">
                <Calculator className="w-5 h-5 text-green-400" />
                <span>🧮 كيف يتم حساب الإيرادات الحقيقية</span>
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="text-center p-3 bg-green-500/20 rounded-lg">
                  <p className="text-green-300 font-semibold">المبيعات النقدية</p>
                  <p className="text-white text-lg font-bold">{formatCurrency(financialData.revenue?.breakdown?.sales?.cash_sales)}</p>
                  <p className="text-green-200 text-xs">تم دفعها فور البيع</p>
                </div>
                <div className="text-center p-3 bg-purple-500/20 rounded-lg">
                  <p className="text-purple-300 font-semibold">تحصيل الديون</p>
                  <p className="text-white text-lg font-bold">{formatCurrency(financialData.revenue?.breakdown?.debt_payments?.total_collected)}</p>
                  <p className="text-purple-200 text-xs">تم تحصيلها من العملاء</p>
                </div>
                <div className="text-center p-3 bg-yellow-500/20 rounded-lg">
                  <p className="text-yellow-300 font-semibold">إجمالي الإيرادات</p>
                  <p className="text-white text-lg font-bold">{formatCurrency(financialData.revenue?.total)}</p>
                  <p className="text-yellow-200 text-xs">النقدي + التحصيل</p>
                </div>
              </div>

              <div className="mt-4 p-3 bg-orange-500/20 rounded-lg">
                <p className="text-orange-300 font-semibold text-center">⚠️ المبيعات الآجلة غير المدفوعة</p>
                <p className="text-white text-lg font-bold text-center">{formatCurrency(financialData.revenue?.breakdown?.sales?.pending_credit_sales)}</p>
                <p className="text-orange-200 text-xs text-center">هذه ديون على العملاء - ليست إيرادات بعد</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* المبيعات النقدية */}
              <div className="bg-white/10 rounded-xl p-4 border border-green-400/20">
                <div className="flex items-center space-x-2 space-x-reverse mb-3">
                  <div className="bg-green-500/30 p-2 rounded-lg">
                    <ShoppingCart className="w-5 h-5 text-green-400" />
                  </div>
                  <div>
                    <p className="text-green-300 text-sm">المبيعات النقدية</p>
                    <p className="text-white font-bold text-lg">{formatCurrency(financialData.revenue?.breakdown?.sales?.cash_sales)}</p>
                  </div>
                </div>
              </div>

              {/* المبيعات الآجلة (لم تُدفع بعد) */}
              <div className="bg-white/10 rounded-xl p-4 border border-orange-400/20">
                <div className="flex items-center space-x-2 space-x-reverse mb-3">
                  <div className="bg-orange-500/30 p-2 rounded-lg">
                    <CreditCard className="w-5 h-5 text-orange-400" />
                  </div>
                  <div>
                    <p className="text-orange-300 text-sm">المبيعات الآجلة</p>
                    <p className="text-white font-bold text-lg">{formatCurrency(financialData.revenue?.breakdown?.sales?.pending_credit_sales)}</p>
                    <p className="text-orange-200 text-xs">لم تُدفع بعد</p>
                  </div>
                </div>
              </div>

              {/* تحصيل الديون */}
              <div className="bg-white/10 rounded-xl p-4 border border-purple-400/20">
                <div className="flex items-center space-x-2 space-x-reverse mb-3">
                  <div className="bg-purple-500/30 p-2 rounded-lg">
                    <Wallet className="w-5 h-5 text-purple-400" />
                  </div>
                  <div>
                    <p className="text-purple-300 text-sm">تحصيل الديون</p>
                    <p className="text-white font-bold text-lg">{formatCurrency(financialData.revenue?.breakdown?.debt_payments?.total_collected)}</p>
                    <p className="text-purple-200 text-xs">{financialData.revenue?.breakdown?.debt_payments?.payments_count || 0} دفعة</p>
                  </div>
                </div>
              </div>

              {/* إجمالي اليوم */}
              <div className="bg-white/10 rounded-xl p-4 border border-yellow-400/20">
                <div className="flex items-center space-x-2 space-x-reverse mb-3">
                  <div className="bg-yellow-500/30 p-2 rounded-lg">
                    <Calendar className="w-5 h-5 text-yellow-400" />
                  </div>
                  <div>
                    <p className="text-yellow-300 text-sm">إيرادات اليوم</p>
                    <p className="text-white font-bold text-lg">{formatCurrency(financialData.revenue?.today)}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* إحصائيات إضافية */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-white/5 rounded-xl p-4 text-center">
                <p className="text-gray-300 text-sm mb-1">عدد المبيعات</p>
                <p className="text-2xl font-bold text-white">{financialData.revenue?.sales_count || 0}</p>
              </div>
              <div className="bg-white/5 rounded-xl p-4 text-center">
                <p className="text-gray-300 text-sm mb-1">متوسط البيعة</p>
                <p className="text-2xl font-bold text-white">{formatCurrency(financialData.revenue?.avg_sale)}</p>
              </div>
              <div className="bg-white/5 rounded-xl p-4 text-center">
                <p className="text-gray-300 text-sm mb-1">إيرادات الشهر</p>
                <p className="text-2xl font-bold text-white">{formatCurrency(financialData.revenue?.month)}</p>
              </div>
            </div>
          </div>

          {/* الرسوم البيانية والتحليلات */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* مخطط الإيرادات الشهرية */}
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-white flex items-center space-x-2 space-x-reverse">
                  <BarChart3 className="w-6 h-6 text-indigo-400" />
                  <span>الأداء الشهري</span>
                </h3>
                <button className="text-gray-400 hover:text-white transition-colors duration-300">
                  <MoreVertical className="w-5 h-5" />
                </button>
              </div>

              {!financialData ? (
                <div className="text-center py-12">
                  <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-400 text-lg">جاري تحميل البيانات...</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* الشهر الحالي */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-300">هذا الشهر (يونيو 2025)</span>
                      <span className="text-white font-semibold">{formatCurrency(financialData.revenue?.month || 0)}</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-3">
                      <div
                        className="bg-gradient-to-r from-indigo-500 to-purple-600 h-3 rounded-full transition-all duration-1000"
                        style={{ width: `${Math.min((financialData.revenue?.month || 0) / 1000, 100)}%` }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-xs text-gray-400">
                      <span>إيرادات الشهر</span>
                      <span>{formatPercentage((financialData.revenue?.month || 0) / 1000 * 100)} من الهدف</span>
                    </div>
                  </div>

                  {/* اليوم */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-300">اليوم</span>
                      <span className="text-white font-semibold">{formatCurrency(financialData.revenue?.today || 0)}</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-green-500 to-emerald-600 h-2 rounded-full transition-all duration-1000"
                        style={{ width: `${Math.min((financialData.revenue?.today || 0) / 500, 100)}%` }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-xs text-gray-400">
                      <span>إيرادات اليوم</span>
                      <span>{formatPercentage((financialData.revenue?.today || 0) / 500 * 100)} من الهدف اليومي</span>
                    </div>
                  </div>

                  {/* نمو المبيعات */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-300">نمو المبيعات</span>
                      <span className={`font-semibold ${(financialData.revenue?.growth || 0) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {(financialData.revenue?.growth || 0) >= 0 ? '+' : ''}{formatPercentage(financialData.revenue?.growth || 0)}
                      </span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-1000 ${
                          (financialData.revenue?.growth || 0) >= 0
                            ? 'bg-gradient-to-r from-green-500 to-emerald-600'
                            : 'bg-gradient-to-r from-red-500 to-pink-600'
                        }`}
                        style={{ width: `${Math.min(Math.abs(financialData.revenue?.growth || 0), 100)}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* توزيع المصروفات */}
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-white flex items-center space-x-2 space-x-reverse">
                  <PieChart className="w-6 h-6 text-indigo-400" />
                  <span>توزيع المصروفات</span>
                </h3>
                <button className="text-gray-400 hover:text-white transition-colors duration-300">
                  <Eye className="w-5 h-5" />
                </button>
              </div>

              {expenseCategories.length === 0 ? (
                <div className="text-center py-12">
                  <PieChart className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-400 text-lg">لا توجد بيانات مصروفات</p>
                  <p className="text-gray-500 text-sm">ستظهر توزيع المصروفات هنا عند إدخال البيانات</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {expenseCategories.map((category, index) => (
                    <div key={category.category} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <div
                          className="w-4 h-4 rounded-full"
                          style={{ backgroundColor: category.color }}
                        ></div>
                        <span className="text-gray-300 text-sm">{category.category}</span>
                      </div>
                      <div className="text-left">
                        <p className="text-white font-semibold">{formatCurrency(category.amount)}</p>
                        <p className="text-gray-400 text-xs">{formatPercentage(category.percentage)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* المؤشرات المالية والتنبيهات */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* المؤشرات المالية */}
            <div className="lg:col-span-2 bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <h3 className="text-xl font-bold text-white mb-6 flex items-center space-x-2 space-x-reverse">
                <Target className="w-6 h-6 text-indigo-400" />
                <span>المؤشرات المالية الرئيسية</span>
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* نمو المبيعات */}
                <div className="text-center p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-all duration-300">
                  <div className="bg-blue-500/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Activity className="w-6 h-6 text-blue-400" />
                  </div>
                  <p className="text-gray-300 text-sm mb-1">نمو المبيعات</p>
                  <p className={`text-2xl font-bold ${(financialData?.revenue?.growth || 0) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {(financialData?.revenue?.growth || 0) >= 0 ? '+' : ''}{formatPercentage(financialData?.revenue?.growth || 0)}
                  </p>
                  <div className="mt-2">
                    {(financialData?.revenue?.growth || 0) > 0 ? (
                      <span className="text-green-400 text-xs">📈 نمو إيجابي</span>
                    ) : (financialData?.revenue?.growth || 0) === 0 ? (
                      <span className="text-gray-400 text-xs">➖ ثابت</span>
                    ) : (
                      <span className="text-red-400 text-xs">📉 يحتاج انتباه</span>
                    )}
                  </div>
                </div>

                {/* متوسط البيعة */}
                <div className="text-center p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-all duration-300">
                  <div className="bg-purple-500/20 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Award className="w-6 h-6 text-purple-400" />
                  </div>
                  <p className="text-gray-300 text-sm mb-1">متوسط البيعة</p>
                  <p className="text-2xl font-bold text-white">
                    {formatCurrency(financialData?.revenue?.avg_sale || 0)}
                  </p>
                  <div className="mt-2">
                    <span className="text-purple-400 text-xs">
                      📊 من {financialData?.revenue?.sales_count || 0} معاملة
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* التنبيهات الذكية */}
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <h3 className="text-xl font-bold text-white mb-6 flex items-center space-x-2 space-x-reverse">
                <Bell className="w-6 h-6 text-indigo-400" />
                <span>التنبيهات الذكية</span>
                {financialData.alerts && financialData.alerts.total_count > 0 && (
                  <span className="bg-red-500 text-white text-sm px-2 py-1 rounded-full">
                    {financialData.alerts.total_count}
                  </span>
                )}
              </h3>

              {!financialData.alerts || financialData.alerts.total_count === 0 ? (
                <div className="text-center py-12">
                  <Bell className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-400 text-lg">لا توجد تنبيهات</p>
                  <p className="text-gray-500 text-sm">النظام يعمل بشكل مثالي</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* إحصائيات التنبيهات */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div className="text-center p-3 bg-red-500/20 rounded-lg">
                      <p className="text-red-300 text-sm">عالية</p>
                      <p className="text-white text-xl font-bold">{financialData.alerts.high_priority}</p>
                    </div>
                    <div className="text-center p-3 bg-yellow-500/20 rounded-lg">
                      <p className="text-yellow-300 text-sm">متوسطة</p>
                      <p className="text-white text-xl font-bold">{financialData.alerts.medium_priority}</p>
                    </div>
                    <div className="text-center p-3 bg-blue-500/20 rounded-lg">
                      <p className="text-blue-300 text-sm">منخفضة</p>
                      <p className="text-white text-xl font-bold">{financialData.alerts.low_priority}</p>
                    </div>
                    <div className="text-center p-3 bg-green-500/20 rounded-lg">
                      <p className="text-green-300 text-sm">إجمالي</p>
                      <p className="text-white text-xl font-bold">{financialData.alerts.total_count}</p>
                    </div>
                  </div>

                  {/* قائمة التنبيهات */}
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {financialData.alerts.items.map((alert, index) => (
                      <div
                        key={alert.id}
                        className={`p-4 rounded-xl border transition-all duration-300 hover:scale-105 ${
                          alert.type === 'danger'
                            ? 'bg-red-500/20 border-red-400/30 hover:border-red-400/50'
                            : alert.type === 'warning'
                            ? 'bg-yellow-500/20 border-yellow-400/30 hover:border-yellow-400/50'
                            : alert.type === 'success'
                            ? 'bg-green-500/20 border-green-400/30 hover:border-green-400/50'
                            : 'bg-blue-500/20 border-blue-400/30 hover:border-blue-400/50'
                        }`}
                      >
                        <div className="flex items-start space-x-3 space-x-reverse">
                          <div className="text-2xl">{alert.icon}</div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="text-white font-semibold">{alert.title}</h4>
                              <span
                                className={`text-xs px-2 py-1 rounded-full ${
                                  alert.severity === 'high'
                                    ? 'bg-red-500/30 text-red-200'
                                    : alert.severity === 'medium'
                                    ? 'bg-yellow-500/30 text-yellow-200'
                                    : 'bg-blue-500/30 text-blue-200'
                                }`}
                              >
                                {alert.severity === 'high' ? 'عاجل' : alert.severity === 'medium' ? 'مهم' : 'عادي'}
                              </span>
                            </div>
                            <p className="text-gray-300 text-sm mb-1">{alert.message}</p>
                            <p className="text-gray-400 text-xs mb-2">{alert.details}</p>
                            <button
                              onClick={() => handleAlertAction(alert)}
                              className={`text-xs px-3 py-1 rounded-full transition-colors hover:scale-105 ${
                                alert.type === 'danger'
                                  ? 'bg-red-500/30 text-red-200 hover:bg-red-500/50'
                                  : alert.type === 'warning'
                                  ? 'bg-yellow-500/30 text-yellow-200 hover:bg-yellow-500/50'
                                  : alert.type === 'success'
                                  ? 'bg-green-500/30 text-green-200 hover:bg-green-500/50'
                                  : 'bg-blue-500/30 text-blue-200 hover:bg-blue-500/50'
                              }`}
                            >
                              {alert.action}
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* تبويب الإيرادات */}
      {activeTab === 'revenue' && financialData && (
        <div className="space-y-8">
          {/* إحصائيات الإيرادات الرئيسية */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* إجمالي الإيرادات */}
            <div className="bg-gradient-to-br from-green-500/20 to-emerald-600/20 backdrop-blur-lg rounded-2xl p-6 border border-green-400/30 hover:border-green-400/50 transition-all duration-300 hover:scale-105">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-300 text-sm mb-1">إجمالي الإيرادات</p>
                  <p className="text-3xl font-bold text-white">{formatCurrency(financialData.revenue?.total)}</p>
                  <div className="flex items-center space-x-1 space-x-reverse mt-2">
                    <TrendingUp className="w-4 h-4 text-green-400" />
                    <span className="text-green-400 text-sm font-semibold">+{formatPercentage(financialData.revenue?.growth || 0)}</span>
                  </div>
                </div>
                <div className="bg-green-500/30 p-3 rounded-xl">
                  <DollarSign className="w-6 h-6 text-green-400" />
                </div>
              </div>
            </div>

            {/* المبيعات النقدية */}
            <div className="bg-gradient-to-br from-blue-500/20 to-cyan-600/20 backdrop-blur-lg rounded-2xl p-6 border border-blue-400/30 hover:border-blue-400/50 transition-all duration-300 hover:scale-105">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-300 text-sm mb-1">المبيعات النقدية</p>
                  <p className="text-3xl font-bold text-white">{formatCurrency(financialData.revenue?.breakdown?.sales?.cash_sales)}</p>
                  <p className="text-blue-200 text-xs mt-2">دفع فوري</p>
                </div>
                <div className="bg-blue-500/30 p-3 rounded-xl">
                  <ShoppingCart className="w-6 h-6 text-blue-400" />
                </div>
              </div>
            </div>

            {/* تحصيل الديون */}
            <div className="bg-gradient-to-br from-purple-500/20 to-pink-600/20 backdrop-blur-lg rounded-2xl p-6 border border-purple-400/30 hover:border-purple-400/50 transition-all duration-300 hover:scale-105">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-300 text-sm mb-1">تحصيل الديون</p>
                  <p className="text-3xl font-bold text-white">{formatCurrency(financialData.revenue?.breakdown?.debt_payments?.total_collected)}</p>
                  <p className="text-purple-200 text-xs mt-2">{financialData.revenue?.breakdown?.debt_payments?.payments_count || 0} دفعة</p>
                </div>
                <div className="bg-purple-500/30 p-3 rounded-xl">
                  <Wallet className="w-6 h-6 text-purple-400" />
                </div>
              </div>
            </div>

            {/* إيرادات اليوم */}
            <div className="bg-gradient-to-br from-yellow-500/20 to-orange-600/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30 hover:border-yellow-400/50 transition-all duration-300 hover:scale-105">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-yellow-300 text-sm mb-1">إيرادات اليوم</p>
                  <p className="text-3xl font-bold text-white">{formatCurrency(financialData.revenue?.today)}</p>
                  <p className="text-yellow-200 text-xs mt-2">اليوم الحالي</p>
                </div>
                <div className="bg-yellow-500/30 p-3 rounded-xl">
                  <Calendar className="w-6 h-6 text-yellow-400" />
                </div>
              </div>
            </div>
          </div>

          {/* تحليل مصادر الإيرادات */}
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <h3 className="text-2xl font-bold text-white mb-6 flex items-center space-x-2 space-x-reverse">
              <PieChart className="w-6 h-6 text-green-400" />
              <span>🔍 تحليل مصادر الإيرادات</span>
            </h3>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* الرسم البياني الدائري */}
              <div className="space-y-6">
                <h4 className="text-lg font-semibold text-white">توزيع الإيرادات</h4>

                {/* المبيعات النقدية */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <div className="w-4 h-4 rounded-full bg-green-500"></div>
                      <span className="text-gray-300">المبيعات النقدية</span>
                    </div>
                    <span className="text-white font-semibold">{formatCurrency(financialData.revenue?.breakdown?.sales?.cash_sales)}</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-3">
                    <div
                      className="bg-gradient-to-r from-green-500 to-emerald-600 h-3 rounded-full transition-all duration-1000"
                      style={{
                        width: `${((financialData.revenue?.breakdown?.sales?.cash_sales || 0) / (financialData.revenue?.total || 1)) * 100}%`
                      }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-400">
                    {formatPercentage(((financialData.revenue?.breakdown?.sales?.cash_sales || 0) / (financialData.revenue?.total || 1)) * 100)} من إجمالي الإيرادات
                  </p>
                </div>

                {/* تحصيل الديون */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <div className="w-4 h-4 rounded-full bg-purple-500"></div>
                      <span className="text-gray-300">تحصيل الديون</span>
                    </div>
                    <span className="text-white font-semibold">{formatCurrency(financialData.revenue?.breakdown?.debt_payments?.total_collected)}</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-3">
                    <div
                      className="bg-gradient-to-r from-purple-500 to-pink-600 h-3 rounded-full transition-all duration-1000"
                      style={{
                        width: `${((financialData.revenue?.breakdown?.debt_payments?.total_collected || 0) / (financialData.revenue?.total || 1)) * 100}%`
                      }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-400">
                    {formatPercentage(((financialData.revenue?.breakdown?.debt_payments?.total_collected || 0) / (financialData.revenue?.total || 1)) * 100)} من إجمالي الإيرادات
                  </p>
                </div>
              </div>

              {/* الإحصائيات التفصيلية */}
              <div className="space-y-6">
                <h4 className="text-lg font-semibold text-white">الإحصائيات التفصيلية</h4>

                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-white/5 rounded-xl p-4 text-center">
                    <p className="text-gray-300 text-sm mb-1">عدد المبيعات</p>
                    <p className="text-2xl font-bold text-white">{financialData.revenue?.sales_count || 0}</p>
                  </div>
                  <div className="bg-white/5 rounded-xl p-4 text-center">
                    <p className="text-gray-300 text-sm mb-1">متوسط البيعة</p>
                    <p className="text-2xl font-bold text-white">{formatCurrency(financialData.revenue?.avg_sale)}</p>
                  </div>
                  <div className="bg-white/5 rounded-xl p-4 text-center">
                    <p className="text-gray-300 text-sm mb-1">إيرادات الشهر</p>
                    <p className="text-2xl font-bold text-white">{formatCurrency(financialData.revenue?.month)}</p>
                  </div>
                  <div className="bg-white/5 rounded-xl p-4 text-center">
                    <p className="text-gray-300 text-sm mb-1">معدل النمو</p>
                    <p className={`text-2xl font-bold ${(financialData.revenue?.growth || 0) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {(financialData.revenue?.growth || 0) >= 0 ? '+' : ''}{formatPercentage(financialData.revenue?.growth)}
                    </p>
                  </div>
                </div>

                {/* تفاصيل تحصيل الديون */}
                {financialData.revenue?.breakdown?.debt_payments && (
                  <div className="bg-purple-500/10 rounded-xl p-4 border border-purple-400/20">
                    <h5 className="text-purple-300 font-semibold mb-3">📊 تفاصيل تحصيل الديون</h5>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-300">إجمالي المحصل:</span>
                        <span className="text-white font-semibold">{formatCurrency(financialData.revenue.breakdown.debt_payments.total_collected)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-300">عدد الدفعات:</span>
                        <span className="text-white font-semibold">{financialData.revenue.breakdown.debt_payments.payments_count}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-300">متوسط الدفعة:</span>
                        <span className="text-white font-semibold">
                          {formatCurrency((financialData.revenue.breakdown.debt_payments.total_collected || 0) / (financialData.revenue.breakdown.debt_payments.payments_count || 1))}
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* تحليل الأداء الزمني */}
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <h3 className="text-2xl font-bold text-white mb-6 flex items-center space-x-2 space-x-reverse">
              <BarChart3 className="w-6 h-6 text-indigo-400" />
              <span>📈 تحليل الأداء الزمني</span>
            </h3>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* إيرادات اليوم */}
              <div className="bg-gradient-to-br from-green-500/10 to-emerald-600/10 rounded-xl p-6 border border-green-400/20">
                <h4 className="text-green-300 font-semibold mb-4 flex items-center space-x-2 space-x-reverse">
                  <Calendar className="w-5 h-5" />
                  <span>اليوم</span>
                </h4>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-300">الإيرادات:</span>
                    <span className="text-white font-bold">{formatCurrency(financialData.revenue?.today)}</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-green-500 to-emerald-600 h-2 rounded-full transition-all duration-1000"
                      style={{ width: `${Math.min((financialData.revenue?.today || 0) / 500, 100)}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-400">الهدف اليومي: 500.00 دج</p>
                </div>
              </div>

              {/* إيرادات الشهر */}
              <div className="bg-gradient-to-br from-blue-500/10 to-cyan-600/10 rounded-xl p-6 border border-blue-400/20">
                <h4 className="text-blue-300 font-semibold mb-4 flex items-center space-x-2 space-x-reverse">
                  <Calendar className="w-5 h-5" />
                  <span>هذا الشهر</span>
                </h4>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-300">الإيرادات:</span>
                    <span className="text-white font-bold">{formatCurrency(financialData.revenue?.month)}</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-cyan-600 h-2 rounded-full transition-all duration-1000"
                      style={{ width: `${Math.min((financialData.revenue?.month || 0) / 15000, 100)}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-400">الهدف الشهري: 15,000.00 دج</p>
                </div>
              </div>

              {/* معدل النمو */}
              <div className="bg-gradient-to-br from-purple-500/10 to-pink-600/10 rounded-xl p-6 border border-purple-400/20">
                <h4 className="text-purple-300 font-semibold mb-4 flex items-center space-x-2 space-x-reverse">
                  <TrendingUp className="w-5 h-5" />
                  <span>معدل النمو</span>
                </h4>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-300">النمو:</span>
                    <span className={`font-bold ${(financialData.revenue?.growth || 0) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {(financialData.revenue?.growth || 0) >= 0 ? '+' : ''}{formatPercentage(financialData.revenue?.growth)}
                    </span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-1000 ${
                        (financialData.revenue?.growth || 0) >= 0
                          ? 'bg-gradient-to-r from-green-500 to-emerald-600'
                          : 'bg-gradient-to-r from-red-500 to-pink-600'
                      }`}
                      style={{ width: `${Math.min(Math.abs(financialData.revenue?.growth || 0), 100)}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-400">
                    {(financialData.revenue?.growth || 0) >= 0 ? 'نمو إيجابي' : 'انخفاض في الإيرادات'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* تحليل المبيعات المعلقة */}
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <h3 className="text-2xl font-bold text-white mb-6 flex items-center space-x-2 space-x-reverse">
              <CreditCard className="w-6 h-6 text-orange-400" />
              <span>⚠️ المبيعات الآجلة (غير محصلة)</span>
            </h3>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="space-y-4">
                <div className="bg-orange-500/10 rounded-xl p-6 border border-orange-400/20">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-orange-300 font-semibold">المبيعات المعلقة</h4>
                    <div className="bg-orange-500/30 p-2 rounded-lg">
                      <AlertTriangle className="w-5 h-5 text-orange-400" />
                    </div>
                  </div>
                  <p className="text-3xl font-bold text-white mb-2">
                    {formatCurrency(financialData.revenue?.breakdown?.sales?.pending_credit_sales)}
                  </p>
                  <p className="text-orange-200 text-sm">هذه المبالغ لم تُحصل بعد</p>
                </div>

                <div className="bg-yellow-500/10 rounded-xl p-4 border border-yellow-400/20">
                  <h5 className="text-yellow-300 font-semibold mb-2">💡 نصائح لتحسين التحصيل</h5>
                  <ul className="text-sm text-gray-300 space-y-1">
                    <li>• متابعة العملاء المدينين بانتظام</li>
                    <li>• وضع سياسات دفع واضحة</li>
                    <li>• تقديم خصومات للدفع المبكر</li>
                    <li>• استخدام نظام التذكير الآلي</li>
                  </ul>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="text-white font-semibold">مقارنة الإيرادات</h4>

                <div className="space-y-3">
                  <div className="flex justify-between items-center p-3 bg-green-500/10 rounded-lg">
                    <span className="text-green-300">✅ إيرادات محصلة</span>
                    <span className="text-white font-bold">{formatCurrency(financialData.revenue?.total)}</span>
                  </div>

                  <div className="flex justify-between items-center p-3 bg-orange-500/10 rounded-lg">
                    <span className="text-orange-300">⏳ إيرادات معلقة</span>
                    <span className="text-white font-bold">{formatCurrency(financialData.revenue?.breakdown?.sales?.pending_credit_sales)}</span>
                  </div>

                  <div className="flex justify-between items-center p-3 bg-blue-500/10 rounded-lg border-2 border-blue-400/30">
                    <span className="text-blue-300">📊 إجمالي المبيعات</span>
                    <span className="text-white font-bold">
                      {formatCurrency((financialData.revenue?.total || 0) + (financialData.revenue?.breakdown?.sales?.pending_credit_sales || 0))}
                    </span>
                  </div>
                </div>

                <div className="mt-4 p-4 bg-indigo-500/10 rounded-xl border border-indigo-400/20">
                  <h5 className="text-indigo-300 font-semibold mb-2">📈 معدل التحصيل</h5>
                  <div className="w-full bg-gray-700 rounded-full h-3 mb-2">
                    <div
                      className="bg-gradient-to-r from-indigo-500 to-purple-600 h-3 rounded-full transition-all duration-1000"
                      style={{
                        width: `${((financialData.revenue?.total || 0) / ((financialData.revenue?.total || 0) + (financialData.revenue?.breakdown?.sales?.pending_credit_sales || 0))) * 100}%`
                      }}
                    ></div>
                  </div>
                  <p className="text-indigo-200 text-sm">
                    {formatPercentage(((financialData.revenue?.total || 0) / ((financialData.revenue?.total || 0) + (financialData.revenue?.breakdown?.sales?.pending_credit_sales || 0))) * 100)} من المبيعات محصلة
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* تبويب المصروفات */}
      {activeTab === 'expenses' && financialData && (
        <div className="space-y-8">
          {/* إحصائيات المصروفات الرئيسية */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* إجمالي المصروفات */}
            <div className="bg-gradient-to-br from-red-500/20 to-pink-600/20 backdrop-blur-lg rounded-2xl p-6 border border-red-400/30 hover:border-red-400/50 transition-all duration-300 hover:scale-105">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-red-300 text-sm mb-1">إجمالي المصروفات</p>
                  <p className="text-3xl font-bold text-white">{formatCurrency(financialData.expenses?.total)}</p>
                  <div className="flex items-center space-x-1 space-x-reverse mt-2">
                    <TrendingDown className="w-4 h-4 text-red-400" />
                    <span className="text-red-200 text-xs">
                      {financialData.expenses?.growth && financialData.expenses.growth > 0 ? '+' : ''}{formatPercentage(financialData.expenses?.growth)} هذا الشهر
                    </span>
                  </div>
                </div>
                <div className="bg-red-500/30 p-3 rounded-xl">
                  <TrendingDown className="w-6 h-6 text-red-400" />
                </div>
              </div>
            </div>

            {/* المصروفات المباشرة */}
            <div className="bg-gradient-to-br from-orange-500/20 to-red-600/20 backdrop-blur-lg rounded-2xl p-6 border border-orange-400/30 hover:border-orange-400/50 transition-all duration-300 hover:scale-105">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-300 text-sm mb-1">مصروفات مباشرة</p>
                  <p className="text-3xl font-bold text-white">{formatCurrency(financialData.expenses?.breakdown?.direct_expenses?.total)}</p>
                  <p className="text-orange-200 text-xs mt-2">{financialData.expenses?.breakdown?.direct_expenses?.count || 0} مصروف</p>
                </div>
                <div className="bg-orange-500/30 p-3 rounded-xl">
                  <Package className="w-6 h-6 text-orange-400" />
                </div>
              </div>
            </div>

            {/* مشتريات مدفوعة */}
            <div className="bg-gradient-to-br from-purple-500/20 to-pink-600/20 backdrop-blur-lg rounded-2xl p-6 border border-purple-400/30 hover:border-purple-400/50 transition-all duration-300 hover:scale-105">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-300 text-sm mb-1">مشتريات مدفوعة</p>
                  <p className="text-3xl font-bold text-white">{formatCurrency(financialData.expenses?.breakdown?.purchases_expenses?.total)}</p>
                  <p className="text-purple-200 text-xs mt-2">{financialData.expenses?.breakdown?.purchases_expenses?.count || 0} فاتورة</p>
                </div>
                <div className="bg-purple-500/30 p-3 rounded-xl">
                  <ShoppingCart className="w-6 h-6 text-purple-400" />
                </div>
              </div>
            </div>

            {/* مصروفات اليوم */}
            <div className="bg-gradient-to-br from-yellow-500/20 to-orange-600/20 backdrop-blur-lg rounded-2xl p-6 border border-yellow-400/30 hover:border-yellow-400/50 transition-all duration-300 hover:scale-105">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-yellow-300 text-sm mb-1">مصروفات اليوم</p>
                  <p className="text-3xl font-bold text-white">{formatCurrency(financialData.expenses?.today)}</p>
                  <p className="text-yellow-200 text-xs mt-2">اليوم الحالي</p>
                </div>
                <div className="bg-yellow-500/30 p-3 rounded-xl">
                  <Calendar className="w-6 h-6 text-yellow-400" />
                </div>
              </div>
            </div>
          </div>

          {/* تفصيل المصروفات */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* تصنيف المصروفات */}
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <h4 className="text-xl font-bold text-white mb-6 flex items-center space-x-2 space-x-reverse">
                <BarChart3 className="w-5 h-5 text-red-400" />
                <span>تصنيف المصروفات</span>
              </h4>

              {financialData.expenses?.breakdown?.categories && financialData.expenses.breakdown.categories.length > 0 ? (
                <div className="space-y-4">
                  {financialData.expenses.breakdown.categories.map((category, index) => (
                    <div key={category.name} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <div className="w-3 h-3 rounded-full bg-gradient-to-r from-red-500 to-pink-600"></div>
                        <span className="text-white font-medium">{category.name}</span>
                        <span className="text-gray-400 text-sm">({category.count} مصروف)</span>
                      </div>
                      <span className="text-white font-bold">{formatCurrency(category.amount)}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-400">لا توجد تصنيفات مصروفات</p>
                </div>
              )}
            </div>

            {/* مقارنة شهرية */}
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <h4 className="text-xl font-bold text-white mb-6 flex items-center space-x-2 space-x-reverse">
                <Calendar className="w-5 h-5 text-blue-400" />
                <span>المقارنة الشهرية</span>
              </h4>

              <div className="space-y-4">
                <div className="flex justify-between items-center p-4 bg-red-500/10 rounded-xl">
                  <span className="text-red-300">هذا الشهر</span>
                  <span className="text-white font-bold">{formatCurrency(financialData.expenses?.month)}</span>
                </div>

                <div className="flex justify-between items-center p-4 bg-gray-500/10 rounded-xl">
                  <span className="text-gray-300">النمو الشهري</span>
                  <span className={`font-bold ${
                    (financialData.expenses?.growth || 0) > 0 ? 'text-red-400' : 'text-green-400'
                  }`}>
                    {financialData.expenses?.growth && financialData.expenses.growth > 0 ? '+' : ''}{formatPercentage(financialData.expenses?.growth)}
                  </span>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-300">مصروفات مباشرة</span>
                    <span className="text-white">{formatCurrency(financialData.expenses?.breakdown?.direct_expenses?.month)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-300">مشتريات مدفوعة</span>
                    <span className="text-white">{formatCurrency(financialData.expenses?.breakdown?.purchases_expenses?.month)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* رسالة الحالة */}
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className={`p-2 rounded-lg ${
                financialData.expenses?.status === 'active'
                  ? 'bg-green-500/30'
                  : 'bg-yellow-500/30'
              }`}>
                {financialData.expenses?.status === 'active' ? (
                  <CheckCircle className="w-5 h-5 text-green-400" />
                ) : (
                  <AlertTriangle className="w-5 h-5 text-yellow-400" />
                )}
              </div>
              <div>
                <p className="text-white font-semibold">حالة نظام المصروفات</p>
                <p className="text-gray-300 text-sm">{financialData.expenses?.message}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* تبويب الميزانية */}
      {activeTab === 'balance' && (
        <div className="space-y-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* الأصول */}
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <h3 className="text-xl font-bold text-white mb-6 flex items-center space-x-2 space-x-reverse">
                <Building className="w-6 h-6 text-green-400" />
                <span>الأصول</span>
              </h3>

              <div className="text-center py-12">
                <Building className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-400 text-lg">لا توجد بيانات أصول</p>
                <p className="text-gray-500 text-sm">ستظهر تفاصيل الأصول هنا عند إدخال البيانات</p>
              </div>
            </div>

            {/* الخصوم */}
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <h3 className="text-xl font-bold text-white mb-6 flex items-center space-x-2 space-x-reverse">
                <CreditCard className="w-6 h-6 text-red-400" />
                <span>الخصوم</span>
              </h3>

              <div className="text-center py-12">
                <CreditCard className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-400 text-lg">لا توجد بيانات خصوم</p>
                <p className="text-gray-500 text-sm">ستظهر تفاصيل الخصوم هنا عند إدخال البيانات</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* تبويب التقارير */}
      {activeTab === 'reports' && (
        <div className="space-y-8">
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <h3 className="text-2xl font-bold text-white mb-6 flex items-center space-x-2 space-x-reverse">
              <FileText className="w-6 h-6 text-indigo-400" />
              <span>التقارير المالية</span>
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[
                { title: 'قائمة الدخل', description: 'تقرير شامل عن الإيرادات والمصروفات', icon: <TrendingUp className="w-8 h-8 text-green-400" /> },
                { title: 'الميزانية العمومية', description: 'وضع الأصول والخصوم وحقوق الملكية', icon: <BarChart3 className="w-8 h-8 text-blue-400" /> },
                { title: 'قائمة التدفق النقدي', description: 'حركة النقد الداخل والخارج', icon: <Wallet className="w-8 h-8 text-purple-400" /> },
                { title: 'تحليل الربحية', description: 'تحليل مفصل لهوامش الربح', icon: <Target className="w-8 h-8 text-yellow-400" /> },
                { title: 'تقرير المبيعات', description: 'إحصائيات شاملة عن المبيعات', icon: <ShoppingCart className="w-8 h-8 text-pink-400" /> },
                { title: 'تقرير المخزون', description: 'حالة المخزون والحركة', icon: <Package className="w-8 h-8 text-orange-400" /> }
              ].map((report, index) => (
                <div key={index} className="bg-white/5 rounded-xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300 cursor-pointer hover:scale-105">
                  <div className="flex flex-col items-center text-center">
                    <div className="mb-4">
                      {report.icon}
                    </div>
                    <h4 className="text-white font-semibold mb-2">{report.title}</h4>
                    <p className="text-gray-400 text-sm">{report.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FinancialCenter;