const pool = require('./database');

async function checkSalesTable() {
  try {
    console.log('🔍 فحص جدول sales...');

    // التحقق من أعمدة جدول sales
    const result = await pool.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'sales' 
      ORDER BY ordinal_position
    `);

    console.log('📋 أعمدة جدول sales:');
    result.rows.forEach(row => {
      console.log(`  ✓ ${row.column_name} (${row.data_type})`);
    });

    // اختبار إدراج بيع تجريبي
    console.log('\n🧪 اختبار إدراج بيع تجريبي...');
    
    const testSale = await pool.query(`
      INSERT INTO sales (
        customer_id, total_amount, amount_paid, payment_method, payment_status, notes
      )
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [
      null,
      100.00,
      100.00,
      'cash',
      'paid',
      'اختبار'
    ]);

    console.log('✅ تم إدراج البيع التجريبي بنجاح:', testSale.rows[0]);

    // حذف البيع التجريبي
    await pool.query('DELETE FROM sales WHERE notes = $1', ['اختبار']);
    console.log('🗑️ تم حذف البيع التجريبي');

    process.exit(0);
  } catch (error) {
    console.error('❌ خطأ:', error.message);
    process.exit(1);
  }
}

checkSalesTable();
