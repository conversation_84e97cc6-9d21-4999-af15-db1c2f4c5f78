<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8" />
<title>Inno Setup 6 Revision History</title>
<base href="https://jrsoftware.org/" />
<style type="text/css">
    body         { font: calc(14rem/16)/1.5 "Segoe UI", sans-serif; color: #282828; background-color: white }
    a:link, a:visited { color: hsl(206, 100%, 37%); background-color: transparent; text-decoration: none }
    a[href]:hover { color: hsl(206, 100%, 30%); text-decoration: underline }
    tt, pre      { font: calc(13rem/16)/calc(21rem/16) monospace; color: inherit; background-color: #f0f0f0; padding: 2px 4px; border-radius: 4px }
    pre          { line-height: normal; padding: 10px; border-radius: 6px }
    li, div.limargins { margin-top: 5px; margin-bottom: 5px }
    div.bluehead { text-align: center; color: white; background-color: hsl(206, 100%, 30%); padding: 5px; font-weight: bold; line-height: normal }
    .date        { font-size: calc(11rem/16); font-weight: bold }
    .head1       { font-size: calc(32rem/16) }
    .head2       { font-size: calc(20rem/16) }
    .ver         { font-size: calc(20rem/16); font-weight: bold }
</style>
</head>
<body>

<div class="bluehead"><span class="head1">Inno Setup 6</span><br /><span class="head2">Revision History</span></div>

<p>Copyright © 1997-2025 <a href="./">Jordan Russell</a>. All rights reserved.<br />
Portions Copyright © 2000-2025 Martijn Laan. All rights reserved.<br />
For conditions of distribution and use, see <a href="files/is/license.txt">LICENSE.TXT</a>.
</p>

<p><b>Want to be notified by e-mail of new Inno Setup releases?</b> <a href="ismail.php">Subscribe</a> to the Inno Setup Mailing List!</p>

<p><a name="6.4.3"></a><span class="ver">6.4.3 </span><span class="date">(2025-05-03)</span></p>
<ul>
  <li>Compiler IDE change: The editor now restores selections on undo and redo.</li>
  <li>Inno Setup now includes a new command-line tool, ISSigTool.exe. This tool is designed to sign files using ECDSA P-256 cryptographic signatures. Compil32, ISCC, and ISCmplr use these signatures to verify a number of DLL files before loading them. This is a &quot;technology preview&quot; that is subject to change (e.g., any signatures you create now may have to be recreated when upgrading to the next version).<br />Note: ISSigTool.exe does not replace Microsoft's signtool.exe in any way and is in fact not related to Authenticode Code Signing at all.</li>
  <li><i>Fix:</i> Autocomplete support for parameter names in the <tt>[Components]</tt> and <tt>[Dirs]</tt> sections was broken since 6.1.1.</li>
  <li><i>Fix:</i> Pascal Scripting support function <tt>Extract7ZipArchive</tt> which was introduced by 6.4.0 would fail with error 11 on older versions of Windows, at least Windows 8.1 and Windows Server 2016.</li>
  <li>Minor tweaks and documentation improvements.</li>
</ul>

<p><a name="6.4.2"></a><span class="ver">6.4.2 </span><span class="date">(2025-03-12)</span></p>
<ul>
  <li>Added <tt>[Setup]</tt> section directive <tt>CloseApplicationsFilterExcludes</tt>.</li>
  <li>Inno Setup is now built using Delphi 12.1 Athens instead of Delphi 11.3 Alexandria.</li>
  <li>Inno Setup is now signed using a new code signing certificate.</li>
</ul>

<p><a name="6.4.1"></a><span class="ver">6.4.1 </span><span class="date">(2025-02-12)</span></p>
<p><span class="head2">Compiler IDE changes</span></p>
<ul>
  <li>Added mouseover tooltips for all Pascal Scripting support functions and class members showing their prototype. Always shows all classes' members instead of just those of the current object's class.</li>
  <li>Autocompletion lists now use the same font as the editor.</li>
  <li><i>Fix:</i> When the IDE was started for the first time on a system with a DPI setting over 100%, the editor's initial font size was larger than expected.</li>
</ul>
<p><span class="head2">Other changes</span></p>
<ul>
  <li>{reg:...} constants can now also embed REG_DWORD-type registry values.</li>
  <li><i>Fix:</i> In 6.4.0, using &quot;Shift+&quot; in a <tt>HotKey</tt> parameter in the [Icons] section didn't work and required &quot;Shift&quot; instead, so for example &quot;Ctrl+ShiftM&quot; instead of &quot;Ctrl+Shift+M&quot;.</li>
  <li><i>Fix:</i> In 6.4.0, a custom form shown using <tt>[Code]</tt> didn't have a taskbar button if Setup's wizard was not visible at the time.</li>
  <li>Added official Arabic translation.</li>
  <li>Some minor tweaks and improvements.</li>
</ul>

<p><a name="6.4.0"></a><span class="ver">6.4.0 </span><span class="date">(2025-01-09)</span></p>
<p><span class="head2">Compiler IDE changes</span></p>
<p>Updated the Scintilla editor component used by the Compiler IDE to the latest version.</p>
<p>Multiple selection editing has been improved:</p>
<ul>
  <li>Added new <i>Add Next Occurrence</i> menu item to the <i>Edit</i> menu to add the next occurrence of the current word or selected text as an additional selection (Shift+Alt+. or Ctrl+D, see below).</li>
  <li>Added new <i>Select All Occurrences</i> menu item to the <i>Edit</i> menu to select all occurrences of the current word or selected text (Shift+Alt+; or Ctrl+Shift+L).</li>
  <li>Added new <i>Select All Find Matches</i> menu item to the <i>Edit</i> menu to select all matches of the last find text (Alt+Enter).<br />Additionally, the <i>Find</i> (Ctrl+F) and <i>Replace</i> (Ctrl+H) dialogs now both support being closed by Alt+Enter to directly select all matches.</li>
  <li>Added shortcuts to add a cursor or selection up or down (Ctrl+Alt+Up and Ctrl+Alt+Down). For multi-line selections this extends the selection up or down and never shrinks.</li>
  <li>Added shortcut to add cursors to line ends (Shift+Alt+I). Behaves the same as in Visual Studio Code, so for example does nothing if all selections are empty.</li>
  <li>Added shortcuts to add a word or line as an additional selection (Ctrl+Double Click and Ctrl+Triple Click or Alt+Double Click and Alt+Triple Click).</li>
  <li>Added shortcut to remove a selection by clicking it (Ctrl+Click or Alt+Click).</li>
  <li>Multiple selection now works over Left, Right, Up, Down, Home and End navigation and selection commands.</li>
  <li>Multiple selection now works over word and line deletion commands, and line end insertion.</li>
  <li>Multiple selection now works better with Copy and Paste commands.</li>
  <li>Left, Right, etc. navigation with rectangular selection is now allowed.</li>
  <li>The Find and Replace dialogs and the tools from the <i>Tools</i> menu which generate script text now all work better with multiple selections present.</li>
</ul>
<p>Other editor changes:</p>
<ul>
  <li>Added support for Visual Studio Code-style editor shortcuts, like Ctrl+D to Add Next Occurrence, Ctrl+Shift+K to delete a line and Alt+Click to add an additional cursor or remove a selection.<br />To activate this use the <i>Options</i> menu item in the <i>Tools</i> menu to set the new <i>Keys</i> option in the <i>Editor</i> group to <i>Visual Studio Code</i>.<br />The updated <a href="https://jrsoftware.org/ishelp/index.php?topic=compformshortcuts">Compiler IDE Keyboard And Mouse Commands</a> help topic lists all differences with the classic keyboard and mouse shortcuts.</li>
  <li>Only if Visual Studio Code-style editor shortcuts have been activated: Added shortcuts to copy line down (Shift+Alt+Down) and to indent or unindent lines (Ctrl+] and Ctrl+[).</li>
  <li>Added parameter hints for all Pascal Scripting support functions for quick reference to the function's parameter names, types, and order. Parameter hints can be invoked manually by pressing Ctrl+Shift+Space or automatically by using the new <i>Invoke parameter hints automatically</i> option which is enabled by default.</li>
  <li>Added autocompletion support for all Pascal Scripting support functions, types, constants, etcetera. Existing option <i>Invoke autocompletion automatically</i> controls whether the autocompletion suggestions appear automatically or only when invoked manually by pressing Ctrl+Space or Ctrl+I.</li>
  <li>Added parameter hints and autocompletion support for all Pascal Scripting support class members and properties. Both always show all classes' members and properties instead of just those of the current object's class.</li>
  <li>Added autocompletion support for all Pascal Scripting event function parameters. Always shows all parameters instead of just those of the current event function.</li>
  <li>Added autocompletion support for the [Messages] section.</li>
  <li>Improved autocompletion support for all Flags parameters: now works for multiple flags instead of for the first only.</li>
  <li>Added new <i>Enable section folding</i> option which allows you to temporarily hide sections while editing by clicking the new minus or plus icons in the editor's gutter or by using the new keyboard shortcuts (Ctrl+Shift+[ to fold and Ctrl+Shift+] to unfold) or menu items. Enabled by default.</li>
  <li>The editor's gutter now shows change history to keep track of saved and unsaved modifications. Always enabled.</li>
  <li>The editor's font now defaults to Consolas if available, consistent with most other modern editors.</li>
  <li>The editor can now be scrolled horizontally instead of vertically by holding the Shift key while rotating the mouse wheel. Horizontal scroll wheels are now also supported.</li>
  <li>Cut (Ctrl+X or Shift+Delete) and Copy (Ctrl+C or Ctrl+Insert) now cut or copy the entire line if there's no selection, consistent with most other modern editors.</li>
  <li>Added new shortcuts to move selected lines up or down (Alt+Up and Alt+Down).</li>
  <li>Added new shortcut and menu item to the <i>Edit</i> menu to toggle line comment (Ctrl+/).</li>
  <li>Added new shortcut and menu item to the <i>Edit</i> menu to go to matching brace (Ctrl+Shift+\).</li>
  <li>Moved the <i>Word Wrap</i> option to the <i>View</i> menu and added a shortcut for it (Alt+Z).</li>
  <li>Added a right-click popup menu to the editor's gutter column for breakpoints.</li>
  <li>Added dark mode support to autocompletion lists and also added a minimum width.</li>
  <li>Added new <i>Show whitespace</i> option. Disabled by default.</li>
  <li>Improved brace highlighting.</li>
  <li>Fixed an issue when the <i>Auto indent mode</i> and <i>Allow cursor to move beyond end of lines</i> options are both enabled.</li>  
</ul>
<p>Other Compiler IDE changes:</p>
<ul>
  <li>Shortcuts Alt+Left and Alt+Right now always navigate back and forward even if Visual Studio-style menu shortcuts have been activated.<br />Because of this Alt+Right can no longer be used to initiate auto complete, instead the existing Ctrl+Space or Ctrl+I alternatives must be used.</li>
  <li>Moved the list of recently opened files into a new <i>Open Recent</i> submenu of the <i>Files</i> menu.</li>
  <li>Added new <i>Use Regular Expressions</i> option to the <i>Edit</i> menu to enable or disable the use of regular expressions for all find and replace operations and added a shortcut for it (Ctrl+Alt+R or Alt+R). Also added a small panel to the statusbar to indicate the current state.</li>
  <li>The Find and Replace dialogs now support using Shift+Enter to temporarily search in the opposite direction.</li>
  <li>Added shortcuts to select a tab (Ctrl+1 through Ctrl+9).</li>
  <li>Added alternative shortcut for the <i>Compile</i> menu item in the <i>Build</i> menu (Shift+F9 or F7).</li>
  <li>Added shortcut to the <i>Options</i> menu item in the <i>Tools</i> menu (Ctrl+,).</li>
  <li>Removed the length limitation when entering a Sign Tool command and increased control height.</li>
  <li>Added a banner which is displayed to each user after each update and links to this revision history.</li>
  <li>Enabled dark mode support for the menus on Windows 11 Version 24H2 (2024 Update).</li>
</ul>
<p><span class="head2">Other changes</span></p>
<ul>
  <li>Updated the LZMA SDK used by Inno Setup to the latest version, increasing the speed of LZMA and LZMA2 compression (by 10% in a test with default settings) without changing the compression ratio. Compression memory requirements have increased by about 4%. This also made it possible to add support for extracting 7-Zip archives, see below.</li>
  <li>Updated the encryption algorithm and key derivation function used by Inno Setup to XChaCha20 and PBKDF2-HMAC-SHA256 respectively, increasing security. This code is built-in: the separate ISCrypt.dll "encryption module" is no longer used and will be automatically deleted when you update.</li>
  <li>Added <tt>[Setup]</tt> section directive <tt>EncryptionKeyDerivation</tt> to change the number of PBKDF2-HMAC-SHA256 iterations to use from the default of 200000 to another value.</li>
  <li>Replaced all remaining use of MD5 and SHA-1 hashes with SHA-256 hashes, without removing the MD5 and SHA-1 Pascal Scripting and ISPP support functions.</li>
  <li>At long last, Setup's wizard window now shows a thumbnail image on its taskbar button, and animates correctly when minimized and restored. As part of this work, support for the long-deprecated <tt>[Setup]</tt> section directive <tt>WindowVisible</tt>, which was used to enable a 1990s-style blue gradient background behind the wizard window, has been dropped. For the same reason Pascal Scripting support object <tt>MainForm</tt> has been removed.</li>
  <li>The aspect ratio of Setup's large and small wizard images (as specified by <tt>WizardImageFile</tt> and <tt>WizardSmallImageFile</tt>) is now maintained when the window is scaled. Previously, depending on the font and font size used, they could have appeared horizontally stretched or squished.</li>
  <li>The size of the small wizard image area has been extended to 58&times;58 (at standard DPI with the default font). Previous versions used a non-square 55&times;58 size, which made the default image look slightly stretched.</li>
  <li>When disk spanning is enabled and Setup cannot find the needed disk slice file (e.g., <tt>setup-2.bin</tt>) in the source directory, it no longer automatically searches for it in a directory named <tt>DISKx</tt> one level up, where <tt>x</tt> is the disk number. Though consistent with old installers from the 16-bit era, this behavior wasn't documented.</li>
  <li>The New Script Wizard now sets <tt>UninstallDisplayIcon</tt> when an .exe is chosen as the main executable file.</li>
  <li>Merged the Inno Setup Preprocessor documentation into the main documentation instead of being separate.</li>
  <li>Added a dark mode version of the documentation, automatically used by the Compiler IDE if a dark theme is chosen.</li>
  <li>Pascal Scripting changes:
  <ul>
    <li>Added new <tt>Extract7ZipArchive</tt> support function to extract a 7-Zip archive, based on the &quot;7z ANSI-C Decoder&quot; from the LZMA SDK by Igor Pavlov. See the new <a href="https://jrsoftware.org/ishelp/index.php?topic=isxfunc_extract7ziparchive">help topic</a> for information about its limitations.<br />Added new <tt>CreateExtractionPage</tt> support function to easily show the extraction progress to the user.</li>
    <li>Added new <tt>ExecAndCaptureOutput</tt> support function to execute a program or batch file and capture its <i>stdout</i> and <i>stderr</i> outputs separately.</li>
    <li>Added new <tt>StringJoin</tt>, <tt>StringSplit</tt>, and <tt>StringSplitEx</tt> support functions.</li>
    <li>Output logging now raises an exception if there was an error setting up output redirection (which should be very rare). The <i>PowerShell.iss</i> example script has been updated to catch the exception.</li>
    <li>Added new <tt>NewFolderName</tt> property to support class <tt>TInputDirWizardPage</tt> update the initial value passed to <tt>CreateInputDirPage</tt>.</li>
    <li>Added new <tt>PopupMode</tt> and <tt>PopupParent</tt> properties to support class <tt>TForm</tt>.</li>
    <li>Documented support functions <tt>VarArrayGet</tt> and <tt>VarArraySet</tt> which were already available but not documented.</li>
    <li>Renamed the <tt>FileCopy</tt> support function to <tt>CopyFile</tt>. The old name is still supported, but it is recommended to update your scripts to the new name and the compiler will issue a warning if you don't.</li>
    <li>Fixed support function <tt>TStream.CopyFrom</tt> by adding a <tt>BufferSize</tt> parameter which was required but missing. Using <tt>$100000</tt> as the value is recommended.</li>
    <li>Condensed the logging of DLL function imports.</li>
    <li>Added new <tt>Debugging</tt> support function.</li>
  </ul>
  </li>
  <li>ISPP change: Added support functions <tt>GetSHA256OfFile</tt>, <tt>GetSHA256OfString</tt>, and <tt>GetSHA256OfUnicodeString</tt>.</li>
  <li>Inno Setup's Delphi <a href="https://github.com/jrsoftware/issrc" target="_blank">source code</a> has been reorganized to use unit scope names and additionally various units have been renamed for clarity. This makes it a lot easier to get started with working with the Inno Setup source code and making contributions, even with the free <a href="https://www.embarcadero.com/products/delphi/starter/free-download">Delphi Community Edition</a>.</li>
  <li>Added official Swedish and Tamil translations.</li>
  <li>Various tweaks and improvements.</li>
</ul>

<p>Contributions via <a href="https://github.com/jrsoftware/issrc" target="_blank">GitHub</a>: Thanks to Sergii Leonov, John Stevenson, and jogo- for their contributions!</p>

<p>Thanks to Neil Hodgson and Igor Pavlov for their continued work on Scintilla and the LZMA SDK!</p>

<p>Some messages have been added and changed in this version: (<a href="https://github.com/jrsoftware/issrc/commit/d18bab1c9234aa8bba6c8e277121e562724d331c">View differences in Default.isl</a>.)</p>
<ul>
  <li><b>New messages:</b>
  <ul>
    <li>ExtractionLabel, ButtonStopExtraction, StopExtraction, ErrorExtractionAborted, ErrorExtractionFailed.</li>
  </ul>
  </li>
</ul>

<p>Note: The official Icelandic translation has not yet been updated for these changes.</li>

<p><a href="files/is6.3-whatsnew.htm">Inno Setup 6.3 Revision History</a></p>

</body>
</html>
