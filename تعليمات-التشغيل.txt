========================================
    كشير توسار - نظام نقاط البيع
       تعليمات التشغيل للعميل
========================================

📋 المتطلبات:
-----------
✅ Windows 10 أو أحدث
✅ PostgreSQL (قاعدة البيانات)
✅ اتصال بالإنترنت (للتثبيت الأولي فقط)

🚀 التشغيل لأول مرة:
------------------
1️⃣ شغل ملف: setup-for-client.bat
   - سيثبت Node.js تلقائياً
   - سيثبت جميع المتطلبات
   - سيبني المشروع
   - سينشئ اختصار على سطح المكتب

2️⃣ تأكد من تشغيل PostgreSQL

3️⃣ شغل ملف: كشير-توسار.bat
   أو استخدم الاختصار على سطح المكتب

⚡ التشغيل اليومي:
----------------
- انقر نقراً مزدوجاً على اختصار "كشير توسار" على سطح المكتب
- أو شغل ملف كشير-توسار.bat مباشرة

🔧 ما يحدث عند التشغيل:
---------------------
✅ يتحقق من وجود Node.js
✅ يبني الواجهة الأمامية (إذا لزم الأمر)
✅ يشغل الخادم في الخلفية
✅ يفتح المتصفح تلقائياً
✅ يراقب النظام ويعيد تشغيله عند الحاجة

🌐 الوصول للنظام:
-----------------
العنوان: http://localhost:5003
المتصفح سيفتح تلقائياً

🛑 إيقاف النظام:
---------------
- أغلق نافذة الأوامر (Command Prompt)
- أو اضغط Ctrl+C في النافذة

⚠️ ملاحظات مهمة:
----------------
🔹 تأكد من تشغيل PostgreSQL قبل تشغيل النظام
🔹 لا تغلق نافذة الأوامر أثناء العمل
🔹 النظام يعمل محلياً على جهازك فقط
🔹 البيانات محفوظة في قاعدة البيانات المحلية

🆘 في حالة المشاكل:
------------------
1. تأكد من تشغيل PostgreSQL
2. أعد تشغيل الكمبيوتر
3. شغل setup-for-client.bat مرة أخرى
4. تواصل مع الدعم الفني

========================================
        نتمنى لك تجربة ممتعة!
========================================
