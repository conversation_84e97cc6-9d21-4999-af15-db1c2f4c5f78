-- إنشاء جدول ديون العملاء
CREATE TABLE IF NOT EXISTS customer_debts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
  sale_id UUID REFERENCES sales(id) ON DELETE CASCADE,
  amount DECIMAL(10,2) NOT NULL,
  debt_date DATE NOT NULL DEFAULT CURRENT_DATE,
  paid_amount DECIMAL(10,2) DEFAULT 0,
  remaining_amount DECIMAL(10,2) GENERATED ALWAYS AS (amount - paid_amount) STORED,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'partial', 'paid')),
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إضافة فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_customer_debts_customer_id ON customer_debts(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_debts_sale_id ON customer_debts(sale_id);
CREATE INDEX IF NOT EXISTS idx_customer_debts_status ON customer_debts(status);
CREATE INDEX IF NOT EXISTS idx_customer_debts_debt_date ON customer_debts(debt_date);

-- إنشاء جدول مدفوعات الديون
CREATE TABLE IF NOT EXISTS customer_debt_payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  debt_id UUID REFERENCES customer_debts(id) ON DELETE CASCADE,
  amount DECIMAL(10,2) NOT NULL,
  payment_method VARCHAR(50) NOT NULL DEFAULT 'cash',
  payment_date DATE NOT NULL DEFAULT CURRENT_DATE,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إضافة فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_customer_debt_payments_debt_id ON customer_debt_payments(debt_id);
CREATE INDEX IF NOT EXISTS idx_customer_debt_payments_payment_date ON customer_debt_payments(payment_date);
