# اختبار بيع مباشر لتشخيص المشكلة
$headers = @{
    'Content-Type' = 'application/json'
}

$saleData = @{
    customer_id = $null
    items = @(
        @{
            product_id = $null
            product_name = "منتج تجريبي للتشخيص"
            quantity = 1
            unit_price = 50.00
            discount = 0
            total_price = 50.00
        }
    )
    subtotal = 50.00
    tax_amount = 0.00
    discount_amount = 0.00
    total_amount = 50.00
    payment_method = "cash"
    amount_paid = 50.00
    change_amount = 0.00
    notes = "اختبار تشخيص المشكلة"
    cashier_name = "مطور"
}

$body = $saleData | ConvertTo-Json -Depth 10

Write-Host "🧪 اختبار بيع مباشر للتشخيص..." -ForegroundColor Yellow
Write-Host "📤 إرسال طلب البيع..." -ForegroundColor Cyan

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5002/api/sales" -Method POST -Headers $headers -Body $body
    
    Write-Host "✅ نجح البيع!" -ForegroundColor Green
    Write-Host "📊 رقم البيع: $($response.sale_number)" -ForegroundColor Green
    Write-Host "💰 المبلغ الإجمالي: $($response.total_amount)" -ForegroundColor Green
    Write-Host "🆔 معرف البيع: $($response.id)" -ForegroundColor Green
    Write-Host "🎉 البيع تم حفظه في قاعدة البيانات بنجاح!" -ForegroundColor Magenta
    
    # التحقق من وجود البيع في قاعدة البيانات
    Write-Host "`n🔍 التحقق من البيع في قاعدة البيانات..." -ForegroundColor Cyan
    $checkResponse = Invoke-RestMethod -Uri "http://localhost:5002/api/sales" -Method GET -Headers $headers
    
    $foundSale = $checkResponse | Where-Object { $_.id -eq $response.id }
    if ($foundSale) {
        Write-Host "✅ تم العثور على البيع في قاعدة البيانات!" -ForegroundColor Green
        Write-Host "📋 تفاصيل البيع المحفوظ:" -ForegroundColor Green
        Write-Host "   - الرقم: $($foundSale.sale_number)" -ForegroundColor White
        Write-Host "   - المبلغ: $($foundSale.total_amount)" -ForegroundColor White
        Write-Host "   - التاريخ: $($foundSale.created_at)" -ForegroundColor White
    } else {
        Write-Host "❌ لم يتم العثور على البيع في قاعدة البيانات!" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ فشل البيع:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    if ($_.ErrorDetails) {
        Write-Host "🔍 التفاصيل: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
}
