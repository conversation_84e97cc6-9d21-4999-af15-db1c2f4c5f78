// إنشاء جداول فواتير المشتريات فوراً
const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'pos_system',
  password: '123',
  port: 5432,
});

async function createTablesNow() {
  try {
    console.log('🔄 بدء إنشاء جداول فواتير المشتريات...');

    // إنشاء جدول فواتير المشتريات
    await pool.query(`
      CREATE TABLE IF NOT EXISTS pos_system.purchase_invoices (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        invoice_number VARCHAR(50) UNIQUE NOT NULL,
        supplier_id UUID REFERENCES pos_system.suppliers(id),
        subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
        tax_amount DECIMAL(10,2) DEFAULT 0,
        discount_amount DECIMAL(10,2) DEFAULT 0,
        total_amount DECIMAL(10,2) NOT NULL,
        amount_paid DECIMAL(10,2) DEFAULT 0,
        is_paid BOOLEAN GENERATED ALWAYS AS (amount_paid >= total_amount) STORED,
        invoice_date DATE NOT NULL DEFAULT CURRENT_DATE,
        due_date DATE,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ تم إنشاء جدول فواتير المشتريات');

    // إنشاء جدول عناصر فواتير المشتريات
    await pool.query(`
      CREATE TABLE IF NOT EXISTS pos_system.purchase_items (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        invoice_id UUID NOT NULL REFERENCES pos_system.purchase_invoices(id) ON DELETE CASCADE,
        product_id UUID REFERENCES pos_system.products(id),
        product_name VARCHAR(255) NOT NULL,
        quantity INTEGER NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        discount DECIMAL(10,2) DEFAULT 0,
        total_price DECIMAL(10,2) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ تم إنشاء جدول عناصر فواتير المشتريات');

    console.log('🎉 تم إنشاء جميع الجداول بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء الجداول:', error);
  } finally {
    await pool.end();
  }
}

// تشغيل الدالة
createTablesNow();
