// حذف الفواتير عبر API
const axios = require('axios');

async function deleteInvoicesViaAPI() {
  try {
    console.log('🗑️ بدء حذف الفواتير عبر API...');
    
    const invoiceIds = [
      '0c16f02b-2fac-400f-a7fb-35871e02ff0d',
      'ef44c51a-983c-49b4-8557-1922b6bfb53a'
    ];
    
    for (const invoiceId of invoiceIds) {
      try {
        console.log(`🗑️ حذف الفاتورة: ${invoiceId}`);
        
        const response = await axios.delete(`http://localhost:5000/api/purchases/${invoiceId}`);
        
        if (response.status === 200) {
          console.log(`✅ تم حذف الفاتورة: ${invoiceId}`);
          console.log(`📄 الرد: ${response.data.message}`);
        }
        
      } catch (error) {
        if (error.response && error.response.status === 404) {
          console.log(`⚠️ الفاتورة غير موجودة: ${invoiceId}`);
        } else {
          console.error(`❌ خطأ في حذف الفاتورة ${invoiceId}:`, error.message);
        }
      }
    }
    
    console.log('✅ انتهى حذف الفواتير!');
    
  } catch (error) {
    console.error('❌ خطأ عام:', error.message);
  }
}

deleteInvoicesViaAPI();
