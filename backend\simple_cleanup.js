const pool = require('./database');

async function cleanup() {
  try {
    console.log('🧹 حذف الجداول غير المفيدة...');
    
    // حذف الجداول القديمة
    await pool.query('DROP TABLE IF EXISTS pos_system.purchases CASCADE');
    console.log('✅ تم حذف جدول purchases');
    
    await pool.query('DROP TABLE IF EXISTS pos_system.purchase_items CASCADE');
    console.log('✅ تم حذف جدول purchase_items');
    
    await pool.query('DROP TABLE IF EXISTS pos_system.expenses CASCADE');
    console.log('✅ تم حذف جدول expenses');
    
    // عرض الجداول المتبقية
    const result = await pool.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'pos_system' 
      ORDER BY table_name
    `);
    
    console.log('\n📊 الجداول المتبقية:');
    result.rows.forEach(row => {
      console.log(`   ✅ ${row.table_name}`);
    });
    
    console.log('\n🎯 تم تنظيف قاعدة البيانات بنجاح!');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ خطأ:', error.message);
    process.exit(1);
  }
}

cleanup();
