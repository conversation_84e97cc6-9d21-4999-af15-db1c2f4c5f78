-- الجداول الأساسية للنظام
-- Core system tables

-- تعيين المخطط الافتراضي
SET search_path TO pos_system, public;

-- جدول الإعدادات العامة
CREATE TABLE pos_system.settings (
    id SERIAL PRIMARY KEY,
    -- إعد<PERSON>ات المتجر
    store_name VARCHAR(255) NOT NULL DEFAULT '',
    store_address TEXT DEFAULT '',
    store_phone VARCHAR(50) DEFAULT '',
    store_email VARCHAR(255) DEFAULT '',
    store_tax_number VARCHAR(100) DEFAULT '',
    store_logo TEXT DEFAULT '',
    
    -- إ<PERSON>د<PERSON><PERSON><PERSON> المستخدم
    user_name VARCHAR(255) NOT NULL DEFAULT '',
    user_email VARCHAR(255) DEFAULT '',
    user_role user_role DEFAULT 'admin',
    
    -- إعدادات النظام
    currency VARCHAR(10) DEFAULT 'DZD',
    language VARCHAR(10) DEFAULT 'ar',
    timezone VARCHAR(50) DEFAULT 'Africa/Algiers',
    date_format VARCHAR(20) DEFAULT 'DD/MM/YYYY',
    
    -- إعدادات الضرائب والمبيعات
    tax_rate DECIMAL(5,2) DEFAULT 19.00,
    enable_tax BOOLEAN DEFAULT true,
    enable_discount BOOLEAN DEFAULT true,
    enable_barcode BOOLEAN DEFAULT true,
    
    -- إعدادات التنبيهات
    low_stock_alert BOOLEAN DEFAULT true,
    email_notifications BOOLEAN DEFAULT false,
    sound_notifications BOOLEAN DEFAULT true,
    
    -- إعدادات المظهر
    theme theme_type DEFAULT 'dark',
    primary_color VARCHAR(20) DEFAULT '#3b82f6',
    font_size font_size DEFAULT 'medium',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول إعدادات الطابعة
CREATE TABLE pos_system.printer_settings (
    id SERIAL PRIMARY KEY,
    -- إعدادات الاتصال
    connection_type connection_type DEFAULT 'usb',
    printer_name VARCHAR(255) DEFAULT '',
    ip_address INET,
    port INTEGER DEFAULT 9100,
    bluetooth_address VARCHAR(50) DEFAULT '',
    
    -- إعدادات الطباعة
    paper_width INTEGER DEFAULT 58,
    paper_type paper_type DEFAULT 'thermal',
    print_density print_density DEFAULT 'medium',
    print_speed print_speed DEFAULT 'medium',
    
    -- إعدادات الفاتورة
    print_logo BOOLEAN DEFAULT true,
    print_header BOOLEAN DEFAULT true,
    print_footer BOOLEAN DEFAULT true,
    print_barcode BOOLEAN DEFAULT true,
    print_qr_code BOOLEAN DEFAULT false,
    
    -- إعدادات متقدمة
    auto_open_cash_drawer BOOLEAN DEFAULT false,
    print_sound BOOLEAN DEFAULT true,
    copies INTEGER DEFAULT 1,
    cut_paper BOOLEAN DEFAULT true,
    
    -- نص مخصص
    header_text TEXT DEFAULT '',
    footer_text TEXT DEFAULT 'شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى',
    
    -- إعدادات الخط
    font_size font_size DEFAULT 'medium',
    font_family VARCHAR(50) DEFAULT 'arial',
    line_spacing line_spacing DEFAULT 'normal',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الفئات/الأقسام
CREATE TABLE pos_system.categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    color VARCHAR(20) DEFAULT '#3b82f6',
    icon VARCHAR(50) DEFAULT 'Package',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول المنتجات
CREATE TABLE pos_system.products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    barcode VARCHAR(100) UNIQUE,
    category_id UUID REFERENCES pos_system.categories(id) ON DELETE SET NULL,
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    cost DECIMAL(10,2) NOT NULL CHECK (cost >= 0),
    stock INTEGER NOT NULL DEFAULT 0 CHECK (stock >= 0),
    min_stock INTEGER DEFAULT 0 CHECK (min_stock >= 0),
    unit VARCHAR(50) DEFAULT 'قطعة',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول العملاء
CREATE TABLE pos_system.customers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    address TEXT,
    email VARCHAR(255),
    balance DECIMAL(10,2) DEFAULT 0.00,
    credit_limit DECIMAL(10,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الموردين
CREATE TABLE pos_system.suppliers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    address TEXT,
    email VARCHAR(255),
    balance DECIMAL(10,2) DEFAULT 0.00, -- المبلغ المستحق للمورد
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء فهارس للأداء
CREATE INDEX idx_products_category ON pos_system.products(category_id);
CREATE INDEX idx_products_barcode ON pos_system.products(barcode);
CREATE INDEX idx_products_name ON pos_system.products(name);
CREATE INDEX idx_products_active ON pos_system.products(is_active);
CREATE INDEX idx_customers_phone ON pos_system.customers(phone);
CREATE INDEX idx_suppliers_name ON pos_system.suppliers(name);
CREATE INDEX idx_categories_active ON pos_system.categories(is_active);

-- تعليقات على الجداول
COMMENT ON TABLE pos_system.settings IS 'إعدادات النظام العامة';
COMMENT ON TABLE pos_system.printer_settings IS 'إعدادات الطابعة والطباعة';
COMMENT ON TABLE pos_system.categories IS 'فئات وأقسام المنتجات';
COMMENT ON TABLE pos_system.products IS 'جدول المنتجات الرئيسي';
COMMENT ON TABLE pos_system.customers IS 'بيانات العملاء';
COMMENT ON TABLE pos_system.suppliers IS 'بيانات الموردين';

-- إدراج البيانات الافتراضية
INSERT INTO pos_system.settings DEFAULT VALUES;
INSERT INTO pos_system.printer_settings DEFAULT VALUES;
