@echo off
chcp 65001 >nul
REM سكريبت التحقق والإعداد
REM Check and Setup Script

echo ========================================
echo    فحص وإعداد قاعدة بيانات نقطة البيع
echo    POS Database Check and Setup
echo ========================================

echo.
echo 1. التحقق من تثبيت PostgreSQL...
echo 1. Checking PostgreSQL installation...

REM التحقق من وجود psql
where psql >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ PostgreSQL غير مثبت أو غير مضاف إلى PATH
    echo ❌ PostgreSQL not installed or not in PATH
    echo.
    echo الحلول المتاحة:
    echo Available solutions:
    echo.
    echo 1️⃣ تثبيت PostgreSQL:
    echo    Download from: https://www.postgresql.org/download/windows/
    echo.
    echo 2️⃣ إضافة PostgreSQL إلى PATH:
    echo    Add to PATH: C:\Program Files\PostgreSQL\15\bin
    echo.
    echo 3️⃣ استخدام pgAdmin مباشرة:
    echo    Use pgAdmin directly with the SQL files
    echo.
    echo بعد التثبيت، شغل هذا السكريبت مرة أخرى
    echo After installation, run this script again
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على PostgreSQL
echo ✅ PostgreSQL found

REM التحقق من إصدار PostgreSQL
echo.
echo 2. فحص إصدار PostgreSQL...
echo 2. Checking PostgreSQL version...
psql --version

REM إعداد متغيرات البيئة
set PGHOST=localhost
set PGPORT=5432
set PGUSER=postgres
set PGPASSWORD=toossar

echo.
echo 3. اختبار الاتصال...
echo 3. Testing connection...

REM اختبار الاتصال
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -c "SELECT version();" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ فشل في الاتصال بـ PostgreSQL
    echo ❌ Failed to connect to PostgreSQL
    echo.
    echo تأكد من:
    echo Make sure:
    echo - PostgreSQL يعمل (خدمة نشطة)
    echo - PostgreSQL service is running
    echo - كلمة السر صحيحة: toossar
    echo - Password is correct: toossar
    echo - المنفذ 5432 متاح
    echo - Port 5432 is available
    echo.
    echo لبدء خدمة PostgreSQL:
    echo To start PostgreSQL service:
    echo net start postgresql-x64-15
    echo.
    pause
    exit /b 1
)

echo ✅ الاتصال ناجح
echo ✅ Connection successful

echo.
echo 4. إنشاء قاعدة البيانات...
echo 4. Creating database...

REM حذف قاعدة البيانات إذا كانت موجودة
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -c "DROP DATABASE IF EXISTS pos_system_db;" >nul 2>&1

REM إنشاء قاعدة البيانات الجديدة
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -c "CREATE DATABASE pos_system_db WITH OWNER = postgres ENCODING = 'UTF8';"

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في إنشاء قاعدة البيانات
    echo ❌ Failed to create database
    pause
    exit /b 1
)

echo ✅ تم إنشاء قاعدة البيانات
echo ✅ Database created

echo.
echo 5. التحقق من ملفات SQL...
echo 5. Checking SQL files...

if not exist "setup_database.sql" (
    echo ❌ ملف setup_database.sql غير موجود
    echo ❌ setup_database.sql file not found
    echo.
    echo تأكد من وجود الملفات التالية:
    echo Make sure these files exist:
    echo - setup_database.sql
    echo - 01_create_database.sql
    echo - 02_core_tables.sql
    echo - 03_sales_tables.sql
    echo - 04_triggers_functions.sql
    echo - 05_views_reports.sql
    echo - 06_security_permissions.sql
    echo.
    pause
    exit /b 1
)

echo ✅ ملفات SQL موجودة
echo ✅ SQL files found

echo.
echo 6. تشغيل سكريبت الإعداد...
echo 6. Running setup script...

psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d pos_system_db -f "setup_database.sql"

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تشغيل سكريبت الإعداد
    echo ❌ Failed to run setup script
    pause
    exit /b 1
)

echo.
echo 7. التحقق من النتائج...
echo 7. Verifying results...

psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d pos_system_db -c "SELECT 'الجداول: ' || COUNT(*) FROM information_schema.tables WHERE table_schema = 'pos_system';"

psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d pos_system_db -c "SELECT 'المنتجات: ' || COUNT(*) FROM pos_system.products UNION ALL SELECT 'الفئات: ' || COUNT(*) FROM pos_system.categories;"

echo.
echo ========================================
echo 🎉 تم الإعداد بنجاح!
echo 🎉 Setup completed successfully!
echo ========================================
echo.
echo معلومات الاتصال:
echo Connection info:
echo 🔗 Host: localhost
echo 🔗 Port: 5432
echo 🔗 Database: pos_system_db
echo 🔗 Username: postgres
echo 🔗 Password: toossar
echo.
echo يمكنك الآن فتح pgAdmin والاتصال!
echo You can now open pgAdmin and connect!
echo.
pause
