-- إصلاح جدول ديون العملاء - إضافة عمود status المفقود
-- Fix customer_debts table - Add missing status column

-- التحقق من وجود العمود وإضافته إذا لم يكن موجوداً
DO $$
BEGIN
    -- إضافة عمود status إذا لم يكن موجوداً
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'pos_system' 
        AND table_name = 'customer_debts' 
        AND column_name = 'status'
    ) THEN
        ALTER TABLE pos_system.customer_debts 
        ADD COLUMN status VARCHAR(20) DEFAULT 'pending' 
        CHECK (status IN ('pending', 'partial', 'paid'));
        
        RAISE NOTICE '✅ تم إضافة عمود status إلى جدول customer_debts';
    ELSE
        RAISE NOTICE '✅ عمود status موجود بالفعل في جدول customer_debts';
    END IF;
    
    -- إضافة فهرس للعمود الجديد إذا لم يكن موجوداً
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE schemaname = 'pos_system' 
        AND tablename = 'customer_debts' 
        AND indexname = 'idx_customer_debts_status'
    ) THEN
        CREATE INDEX idx_customer_debts_status ON pos_system.customer_debts(status);
        RAISE NOTICE '✅ تم إنشاء فهرس idx_customer_debts_status';
    ELSE
        RAISE NOTICE '✅ فهرس idx_customer_debts_status موجود بالفعل';
    END IF;
    
    -- إضافة مشغل تحديث updated_at إذا لم يكن موجوداً
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger 
        WHERE tgname = 'update_customer_debts_updated_at'
    ) THEN
        CREATE TRIGGER update_customer_debts_updated_at 
        BEFORE UPDATE ON pos_system.customer_debts
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
        
        RAISE NOTICE '✅ تم إنشاء مشغل update_customer_debts_updated_at';
    ELSE
        RAISE NOTICE '✅ مشغل update_customer_debts_updated_at موجود بالفعل';
    END IF;
    
    -- تحديث الحالة للديون الموجودة
    UPDATE pos_system.customer_debts 
    SET status = CASE 
        WHEN paid_amount >= amount THEN 'paid'
        WHEN paid_amount > 0 THEN 'partial'
        ELSE 'pending'
    END
    WHERE status IS NULL OR status = '';
    
    RAISE NOTICE '✅ تم تحديث حالة الديون الموجودة';
    RAISE NOTICE '🎉 تم إصلاح جدول customer_debts بنجاح!';
    
END $$;
