<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API المركز المالي</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار API المركز المالي</h1>
        
        <button onclick="testFinancialAPI()">اختبار API المركز المالي</button>
        <button onclick="testDirectAPI()">اختبار API مباشر</button>
        <button onclick="clearResults()">مسح النتائج</button>
        
        <div id="results"></div>
    </div>

    <script>
        async function testFinancialAPI() {
            const resultsDiv = document.getElementById('results');
            
            try {
                addResult('🔄 جاري اختبار API المركز المالي...', 'info');
                
                const response = await fetch('/api/financial/overview');
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                addResult('✅ نجح الاتصال بـ API المركز المالي!', 'success');
                addResult('📊 البيانات المستلمة:\n' + JSON.stringify(data, null, 2), 'success');
                
            } catch (error) {
                addResult('❌ فشل الاتصال بـ API المركز المالي:\n' + error.message, 'error');
                console.error('خطأ في API:', error);
            }
        }
        
        async function testDirectAPI() {
            const resultsDiv = document.getElementById('results');
            
            try {
                addResult('🔄 جاري اختبار API مباشر...', 'info');
                
                const response = await fetch('http://localhost:5003/api/financial/overview');
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                addResult('✅ نجح الاتصال المباشر بـ API!', 'success');
                addResult('📊 البيانات المستلمة:\n' + JSON.stringify(data, null, 2), 'success');
                
            } catch (error) {
                addResult('❌ فشل الاتصال المباشر بـ API:\n' + error.message, 'error');
                console.error('خطأ في API المباشر:', error);
            }
        }
        
        function addResult(message, type) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            addResult('🚀 مرحباً! اضغط على الأزرار لاختبار الاتصال بـ API', 'info');
        };
    </script>
</body>
</html>
