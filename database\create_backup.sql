-- إنشاء نسخة احتياطية جاهزة للاستيراد
-- Create backup ready for import

-- تشغيل هذا الملف لإنشاء قاعدة البيانات مع البيانات
-- Run this file to create database with data

-- إنشاء قاعدة البيانات
DROP DATABASE IF EXISTS pos_system_db;
CREATE DATABASE pos_system_db
    WITH 
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'en_US.UTF-8'
    LC_CTYPE = 'en_US.UTF-8'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- الاتصال بقاعدة البيانات
\c pos_system_db;

-- إنشاء امتدادات مفيدة
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- إنشاء schema للنظام
CREATE SCHEMA IF NOT EXISTS pos_system;

-- تعيين المسار الافتراضي
SET search_path TO pos_system, public;

-- إنشاء أنواع البيانات المخصصة (ENUMS)
CREATE TYPE user_role AS ENUM ('admin', 'manager', 'cashier');
CREATE TYPE payment_method AS ENUM ('cash', 'card', 'credit', 'mixed');
CREATE TYPE sale_status AS ENUM ('completed', 'pending', 'cancelled', 'refunded');
CREATE TYPE transaction_type AS ENUM ('income', 'expense');
CREATE TYPE connection_type AS ENUM ('usb', 'network', 'bluetooth');
CREATE TYPE paper_type AS ENUM ('thermal', 'normal');
CREATE TYPE print_density AS ENUM ('light', 'medium', 'dark');
CREATE TYPE print_speed AS ENUM ('slow', 'medium', 'fast');
CREATE TYPE font_size AS ENUM ('small', 'medium', 'large');
CREATE TYPE line_spacing AS ENUM ('tight', 'normal', 'loose');
CREATE TYPE theme_type AS ENUM ('dark', 'light');

-- رسالة للمستخدم
DO $$
BEGIN
    RAISE NOTICE 'تم إنشاء قاعدة البيانات والأنواع المخصصة بنجاح!';
    RAISE NOTICE 'Database and custom types created successfully!';
    RAISE NOTICE 'الآن قم بتشغيل باقي ملفات SQL بالترتيب...';
    RAISE NOTICE 'Now run the remaining SQL files in order...';
END $$;
