@echo off
title Creating Portable Version

echo Creating portable version...

REM Create portable folder
if exist "Kasheer-Toosar-Portable" rmdir /s /q "Kasheer-Toosar-Portable"
mkdir "Kasheer-Toosar-Portable"

REM Copy all files
xcopy /E /I /H /Y "backend" "Kasheer-Toosar-Portable\backend"
xcopy /E /I /H /Y "src" "Kasheer-Toosar-Portable\src"
xcopy /E /I /H /Y "node_modules" "Kasheer-Toosar-Portable\node_modules"
copy "package.json" "Kasheer-Toosar-Portable\"
copy "start.bat" "Kasheer-Toosar-Portable\"
copy "*.js" "Kasheer-Toosar-Portable\" 2>nul
copy "*.json" "Kasheer-Toosar-Portable\" 2>nul

REM Build frontend
call npm run build
xcopy /E /I /H /Y "dist" "Kasheer-Toosar-Portable\dist"

REM Create simple launcher
echo @echo off > "Kasheer-Toosar-Portable\RUN-KASHEER.bat"
echo title Kasheer Toosar >> "Kasheer-Toosar-Portable\RUN-KASHEER.bat"
echo echo Starting Kasheer Toosar... >> "Kasheer-Toosar-Portable\RUN-KASHEER.bat"
echo start /min node backend/server.js >> "Kasheer-Toosar-Portable\RUN-KASHEER.bat"
echo timeout /t 3 /nobreak ^>nul >> "Kasheer-Toosar-Portable\RUN-KASHEER.bat"
echo start http://localhost:5003 >> "Kasheer-Toosar-Portable\RUN-KASHEER.bat"

echo.
echo Portable version created in: Kasheer-Toosar-Portable
echo Run RUN-KASHEER.bat to start the system
echo.
echo You can copy this entire folder to any computer with Node.js
pause
