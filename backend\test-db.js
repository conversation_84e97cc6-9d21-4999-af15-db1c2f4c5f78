const pool = require('./database');

async function testDatabase() {
  try {
    console.log('🔍 Testing database connection...');

    // Test basic connection
    const client = await pool.connect();
    console.log('✅ Database connection successful');

    // Test schema access
    const schemaResult = await client.query('SELECT current_schema()');
    console.log('📋 Current schema:', schemaResult.rows[0].current_schema);

    // Test customers table
    const customersResult = await client.query('SELECT COUNT(*) FROM pos_system.customers');
    console.log('👥 Customers count:', customersResult.rows[0].count);

    // Test customer_debts table structure
    const debtStructure = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_schema = 'pos_system'
        AND table_name = 'customer_debts'
      ORDER BY ordinal_position
    `);
    console.log('💳 Customer debts table structure:');
    debtStructure.rows.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} (${col.is_nullable})`);
    });

    // Test the debtors query
    console.log('🔍 Testing debtors query...');
    const debtorsResult = await client.query(`
      SELECT
        c.id,
        c.name,
        c.phone,
        c.email,
        c.address,
        c.balance,
        c.credit_limit,
        c.created_at,
        c.updated_at,
        COALESCE(debt_summary.total_debt, 0) as total_debt,
        COALESCE(debt_summary.paid_amount, 0) as paid_amount,
        COALESCE(debt_summary.remaining_debt, 0) as remaining_debt,
        COALESCE(debt_summary.debt_count, 0) as debt_count
      FROM pos_system.customers c
      LEFT JOIN (
        SELECT
          customer_id,
          SUM(amount) as total_debt,
          SUM(paid_amount) as paid_amount,
          SUM(amount - paid_amount) as remaining_debt,
          COUNT(*) as debt_count
        FROM pos_system.customer_debts
        WHERE status IN ('pending', 'partial')
        GROUP BY customer_id
      ) debt_summary ON c.id = debt_summary.customer_id
      WHERE (c.balance < 0 OR debt_summary.remaining_debt > 0)
        AND c.is_active = true
      ORDER BY debt_summary.remaining_debt DESC, c.balance ASC
    `);
    console.log('✅ Debtors query successful, found:', debtorsResult.rows.length, 'debtors');

    client.release();
    console.log('🎉 All tests passed!');

  } catch (error) {
    console.error('❌ Database test failed:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    process.exit(0);
  }
}

testDatabase();
