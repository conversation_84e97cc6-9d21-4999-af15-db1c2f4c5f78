const axios = require('axios');

async function testExpensesAPI() {
  try {
    console.log('🧪 اختبار API المصروفات...');
    
    // اختبار API الإحصائيات المالية
    console.log('\n📊 اختبار إحصائيات المركز المالي...');
    const response = await axios.get('http://localhost:5002/api/financial/dashboard-stats');
    
    if (response.data && response.data.expenses) {
      const expenses = response.data.expenses;
      console.log('✅ تم جلب بيانات المصروفات بنجاح:');
      console.log(`   - إجمالي المصروفات: ${expenses.total.toLocaleString()} دج`);
      console.log(`   - مصروفات اليوم: ${expenses.today.toLocaleString()} دج`);
      console.log(`   - مصروفات الشهر: ${expenses.month.toLocaleString()} دج`);
      console.log(`   - نمو المصروفات: ${expenses.growth}%`);
      
      if (expenses.breakdown) {
        console.log('\n📋 تفصيل المصروفات:');
        console.log(`   - مصروفات مباشرة: ${expenses.breakdown.direct_expenses.total.toLocaleString()} دج`);
        console.log(`   - مصروفات المشتريات: ${expenses.breakdown.purchases_expenses.total.toLocaleString()} دج`);
        
        if (expenses.breakdown.categories && expenses.breakdown.categories.length > 0) {
          console.log('\n🏷️ فئات المصروفات:');
          expenses.breakdown.categories.forEach(cat => {
            console.log(`   - ${cat.name}: ${cat.amount.toLocaleString()} دج (${cat.count} مصروف)`);
          });
        }
      }
    } else {
      console.log('❌ لم يتم العثور على بيانات المصروفات في الاستجابة');
    }
    
    // اختبار API المصروفات المباشرة
    console.log('\n💰 اختبار API المصروفات المباشرة...');
    try {
      const expensesResponse = await axios.get('http://localhost:5002/api/expenses');
      console.log(`✅ تم جلب ${expensesResponse.data.length} مصروف مباشر`);
    } catch (error) {
      console.log('❌ خطأ في جلب المصروفات المباشرة:', error.message);
    }
    
  } catch (error) {
    console.error('❌ خطأ في اختبار API:', error.message);
    if (error.response) {
      console.error('📄 تفاصيل الخطأ:', error.response.data);
    }
  }
}

// تشغيل الاختبار
testExpensesAPI();
