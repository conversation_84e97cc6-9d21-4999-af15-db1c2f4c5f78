const pool = require('./database');

async function testCreditSaleSystem() {
  console.log('🧪 اختبار نظام البيع بالأجل...');
  
  try {
    // 1. التحقق من وجود العملاء
    console.log('\n1️⃣ التحقق من العملاء...');
    const customersResult = await pool.query(`
      SELECT id, name, phone, credit_limit 
      FROM pos_system.customers 
      WHERE is_active = true 
      ORDER BY created_at DESC 
      LIMIT 5
    `);
    
    if (customersResult.rows.length === 0) {
      console.log('❌ لا توجد عملاء في النظام');
      return;
    }
    
    console.log('✅ العملاء الموجودون:');
    customersResult.rows.forEach(customer => {
      console.log(`   - ${customer.name} (${customer.phone}) - حد الائتمان: ${customer.credit_limit} دج`);
    });
    
    // 2. التحقق من وجود المنتجات
    console.log('\n2️⃣ التحقق من المنتجات...');
    const productsResult = await pool.query(`
      SELECT id, name, price, stock 
      FROM pos_system.products 
      WHERE is_active = true AND stock > 0
      ORDER BY created_at DESC 
      LIMIT 5
    `);
    
    if (productsResult.rows.length === 0) {
      console.log('❌ لا توجد منتجات متاحة في النظام');
      return;
    }
    
    console.log('✅ المنتجات المتاحة:');
    productsResult.rows.forEach(product => {
      console.log(`   - ${product.name} - السعر: ${product.price} دج - المخزون: ${product.stock}`);
    });
    
    // 3. اختبار إنشاء بيع آجل
    console.log('\n3️⃣ اختبار إنشاء بيع آجل...');
    const testCustomer = customersResult.rows[0];
    const testProduct = productsResult.rows[0];
    
    const saleData = {
      customer_id: testCustomer.id,
      items: [
        {
          product_id: testProduct.id,
          product_name: testProduct.name,
          quantity: 2,
          unit_price: testProduct.price,
          total_price: testProduct.price * 2
        }
      ],
      subtotal: testProduct.price * 2,
      tax_amount: 0,
      discount_amount: 0,
      total_amount: testProduct.price * 2,
      notes: 'اختبار البيع بالأجل',
      cashier_name: 'مختبر النظام'
    };
    
    console.log(`📝 بيانات البيع الاختباري:`);
    console.log(`   - العميل: ${testCustomer.name}`);
    console.log(`   - المنتج: ${testProduct.name} × 2`);
    console.log(`   - المبلغ الإجمالي: ${saleData.total_amount} دج`);

    // اختبار مباشر في قاعدة البيانات
    const client = await pool.connect();

    try {
      await client.query('BEGIN');

      const saleNumber = `TEST-CREDIT-${Date.now()}`;
      const totalAmount = saleData.total_amount;

      // إنشاء البيع
      const saleResult = await client.query(`
        INSERT INTO pos_system.sales (
          sale_number, customer_id, subtotal, tax_amount, discount_amount,
          total_amount, payment_method, amount_paid, change_amount,
          notes, cashier_name
        )
        VALUES ($1, $2, $3, $4, $5, $6, 'credit', 0, 0, $7, $8)
        RETURNING *
      `, [
        saleNumber,
        testCustomer.id,
        saleData.subtotal,
        saleData.tax_amount,
        saleData.discount_amount,
        totalAmount,
        saleData.notes,
        saleData.cashier_name
      ]);

      const sale = saleResult.rows[0];

      // إضافة عناصر البيع
      for (const item of saleData.items) {
        await client.query(`
          INSERT INTO pos_system.sale_items (
            sale_id, product_id, product_name, quantity, unit_price, total_price
          )
          VALUES ($1, $2, $3, $4, $5, $6)
        `, [
          sale.id,
          item.product_id,
          item.product_name,
          item.quantity,
          item.unit_price,
          item.total_price
        ]);
      }

      // إنشاء سجل الدين
      const debtResult = await client.query(`
        INSERT INTO pos_system.customer_debts (
          customer_id, sale_id, amount, debt_date,
          paid_amount, status, notes
        )
        VALUES ($1, $2, $3, CURRENT_DATE, 0, 'pending', $4)
        RETURNING *
      `, [
        testCustomer.id,
        sale.id,
        totalAmount,
        `دين من البيع رقم ${saleNumber} - العميل: ${testCustomer.name}`
      ]);

      const debt = debtResult.rows[0];

      await client.query('COMMIT');

      // محاكاة نتيجة ناجحة
      const result = {
        success: true,
        sale: {
          ...sale,
          sale_number: saleNumber,
          debt_id: debt.id,
          debt_amount: debt.amount
        }
      };

    if (result.success) {
      console.log('✅ تم إنشاء البيع الآجل بنجاح!');
      console.log(`   - رقم البيع: ${result.sale.sale_number}`);
      console.log(`   - معرف الدين: ${result.sale.debt_id}`);
      console.log(`   - مبلغ الدين: ${result.sale.debt_amount} دج`);
      
      // 4. التحقق من إدراج الدين في جدول customer_debts
      console.log('\n4️⃣ التحقق من إدراج الدين...');
      const debtResult = await pool.query(`
        SELECT cd.*, c.name as customer_name
        FROM pos_system.customer_debts cd
        JOIN pos_system.customers c ON cd.customer_id = c.id
        WHERE cd.id = $1
      `, [result.sale.debt_id]);
      
      if (debtResult.rows.length > 0) {
        const debt = debtResult.rows[0];
        console.log('✅ تم إدراج الدين بنجاح:');
        console.log(`   - العميل: ${debt.customer_name}`);
        console.log(`   - مبلغ الدين: ${debt.amount} دج`);
        console.log(`   - المبلغ المدفوع: ${debt.paid_amount} دج`);
        console.log(`   - المبلغ المتبقي: ${debt.remaining_amount} دج`);
        console.log(`   - الحالة: ${debt.status}`);
      } else {
        console.log('❌ لم يتم إدراج الدين في الجدول');
      }
      
      // 5. التحقق من ظهور العميل في قائمة المدينين
      console.log('\n5️⃣ التحقق من قائمة العملاء المدينين...');
      const debtorsResult = await pool.query(`
        SELECT
          c.id::text,
          c.name,
          c.phone,
          cd.amount as total_debt,
          cd.paid_amount,
          (cd.amount - cd.paid_amount) as remaining_debt
        FROM pos_system.customers c
        INNER JOIN pos_system.customer_debts cd ON c.id = cd.customer_id
        WHERE cd.status IN ('pending', 'partial')
          AND c.is_active = true
          AND c.id = $1
      `, [testCustomer.id]);
      
      if (debtorsResult.rows.length > 0) {
        console.log('✅ العميل يظهر في قائمة المدينين:');
        debtorsResult.rows.forEach(debtor => {
          console.log(`   - ${debtor.name} (${debtor.phone})`);
          console.log(`   - إجمالي الدين: ${debtor.total_debt} دج`);
          console.log(`   - المبلغ المتبقي: ${debtor.remaining_debt} دج`);
        });
      } else {
        console.log('❌ العميل لا يظهر في قائمة المدينين');
      }

    } else {
      console.log('❌ فشل في إنشاء البيع الآجل:');
      console.log(`   - الخطأ: ${result.error}`);
      console.log(`   - التفاصيل: ${result.details}`);
    }

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }

  } catch (error) {
    console.error('❌ خطأ في اختبار النظام:', error.message);
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testCreditSaleSystem()
    .then(() => {
      console.log('\n🎉 انتهى الاختبار!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل الاختبار:', error);
      process.exit(1);
    });
}

module.exports = testCreditSaleSystem;
