-- 🚀 إصلاح سريع للمنتجات غير المعروفة
-- انسخ والصق هذا الكود في pgAdmin Query Tool واضغط F5

-- تعيين المخطط
SET search_path TO pos_system, public;

-- إنشاء جدول المنتجات غير المعروفة
CREATE TABLE IF NOT EXISTS unknown_products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    description TEXT DEFAULT 'منتج غير معروف',
    barcode VARCHAR(100) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول المبيعات غير المعروفة (محدث للبيع الموحد)
CREATE TABLE IF NOT EXISTS unknown_sales (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sale_number VARCHAR(50) NOT NULL DEFAULT 'UNK-' || EXTRACT(EPOCH FROM NOW())::TEXT,
    product_name VARCHAR(255) NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1 CHECK (quantity > 0),
    unit_price DECIMAL(10,2) NOT NULL CHECK (unit_price >= 0),
    total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount >= 0),
    payment_method VARCHAR(20) DEFAULT 'cash',
    notes TEXT DEFAULT 'منتج غير معروف',
    cashier_name VARCHAR(255) DEFAULT 'النظام',
    sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- حقول جديدة للربط مع المبيعات الرئيسية
    related_sale_id UUID REFERENCES sales(id) ON DELETE SET NULL,
    is_part_of_unified_sale BOOLEAN DEFAULT FALSE
);

-- إنشاء الفهارس
CREATE INDEX IF NOT EXISTS idx_unknown_products_name ON unknown_products(name);
CREATE INDEX IF NOT EXISTS idx_unknown_sales_date ON unknown_sales(sale_date);
CREATE INDEX IF NOT EXISTS idx_unknown_sales_product ON unknown_sales(product_name);
CREATE INDEX IF NOT EXISTS idx_unknown_sales_related ON unknown_sales(related_sale_id);
CREATE INDEX IF NOT EXISTS idx_unknown_sales_unified ON unknown_sales(is_part_of_unified_sale);
CREATE INDEX IF NOT EXISTS idx_unknown_sales_number ON unknown_sales(sale_number);

-- إدراج بيانات تجريبية للاختبار
INSERT INTO unknown_sales (
    sale_number, product_name, quantity, unit_price, total_amount, payment_method, is_part_of_unified_sale
) VALUES
    ('TEST-001', 'منتج تجريبي 1', 1, 100.00, 100.00, 'cash', FALSE),
    ('TEST-002', 'منتج تجريبي 2', 2, 50.00, 100.00, 'cash', FALSE),
    ('TEST-003', 'منتج تجريبي 3', 1, 250.00, 250.00, 'cash', FALSE)
ON CONFLICT (sale_number) DO NOTHING;

-- فحص النتائج
SELECT
    'unknown_sales' as table_name,
    COUNT(*) as record_count
FROM unknown_sales

UNION ALL

SELECT
    'unknown_products' as table_name,
    COUNT(*) as record_count
FROM unknown_products;

-- رسالة نجاح
DO $$
BEGIN
    RAISE NOTICE '✅ تم إنشاء الجداول بنجاح!';
    RAISE NOTICE '🔮 يمكنك الآن استخدام المنتجات غير المعروفة';
    RAISE NOTICE '📋 أعد تشغيل الخادم: npm run dev';
END $$;
