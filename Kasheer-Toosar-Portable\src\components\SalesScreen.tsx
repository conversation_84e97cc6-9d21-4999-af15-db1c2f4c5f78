import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  ShoppingCart,
  Search,
  Scan,
  Plus,
  Minus,
  Trash2,
  CreditCard,
  Banknote,
  Calculator,
  User,
  Package,
  Star,
  Tag,
  Wallet,
  Filter,
  Grid,
  List,
  Clock,
  Check,
  X,
  Sparkles,
  Target,
  Award
} from 'lucide-react';
import { useApp } from '../context/AppContext';
import SaleSuccessModal from './SaleSuccessModal';
import ThermalReceipt from './ThermalReceipt';
import ReceiptModal from './ReceiptModal';
import apiService from '../services/api';

// تم نقل واجهة Product إلى AppContext

interface PaymentDetails {
  type: 'cash' | 'credit' | 'balance' | 'mixed';
  cashAmount: number;
  creditAmount: number;
  balanceAmount: number;
  totalPaid: number;
}

const SalesScreen: React.FC = () => {
  const {
    cart,
    addToCart,
    removeFromCart,
    updateCartQuantity,
    clearCart,
    getCartTotal,
    products,
    categories,
    customers,
    completeSale,
    settings
  } = useApp();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState('');
  const [customerDebts, setCustomerDebts] = useState<{[key: string]: number}>({});
  const [selectedCategory, setSelectedCategory] = useState('الكل');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [paymentDetails, setPaymentDetails] = useState<PaymentDetails>({
    type: 'cash',
    cashAmount: 0,
    creditAmount: 0,
    balanceAmount: 0,
    totalPaid: 0
  });
  const [receivedAmount, setReceivedAmount] = useState('');
  const [isOnline] = useState(true);
  const [showPayment, setShowPayment] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showReceiptModal, setShowReceiptModal] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [lastSaleData, setLastSaleData] = useState<any>(null);
  const [barcodeInput, setBarcodeInput] = useState('');

  // معلومات المتجر من الإعدادات
  const storeInfo = {
    name: settings?.storeName || "متجر توسار الإلكتروني",
    address: settings?.storeAddress || "الجزائر - الجزائر العاصمة",
    phone: settings?.storePhone || "+213 XXX XXX XXX",
    email: settings?.storeEmail || "<EMAIL>",
    taxNumber: settings?.storeTaxNumber || "*********"
  };

  // 📄 إعدادات التحميل التدريجي للمنتجات
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(12); // 12 منتج لكل صفحة في شاشة البيع

  // الحصول على أسماء الفئات من Context
  const categoryNames = ['الكل', ...categories.map(cat => cat.name)];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // تحميل ديون العملاء
  useEffect(() => {
    const loadCustomerDebts = async () => {
      try {
        const debts: {[key: string]: number} = {};

        for (const customer of customers) {
          try {
            const response = await apiService.getCustomerDebts(customer.id);
            if (response.success && response.hasDebt) {
              debts[customer.id] = response.debtInvoice.remainingBalance || 0;
            }
          } catch (error) {
            // تجاهل الأخطاء للعملاء الفرديين
          }
        }

        setCustomerDebts(debts);
      } catch (error) {
        console.error('خطأ في تحميل ديون العملاء:', error);
      }
    };

    if (customers.length > 0) {
      loadCustomerDebts();
    }
  }, [customers]);

  // Cart calculations
  const cartTotal = getCartTotal();
  const discountAmount = cart.reduce((sum, item) => {
    return sum + item.discount;
  }, 0);
  const finalTotal = cartTotal - discountAmount;
  const change = paymentDetails.totalPaid - finalTotal;

  // Customer balance calculations
  const selectedCustomerData = selectedCustomer ? customers.find(c => c.id === selectedCustomer) : null;
  const customerBalance = selectedCustomerData?.balance || 0;
  const canPayWithBalance = customerBalance > 0 && selectedCustomer;
  const maxBalancePayment = Math.min(customerBalance, finalTotal);

  // إضافة وظائف لوحة المفاتيح
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // تجاهل الأحداث إذا كان المستخدم يكتب في حقل إدخال
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return;
      }

      // تجاهل الأحداث إذا كانت نافذة النجاح مفتوحة
      if (showSuccessModal) {
        return;
      }

      switch (event.key) {
        case 'Enter':
          event.preventDefault();
          if (!showPayment && cart.length > 0) {
            // Enter الأولى: إتمام عملية البيع (فتح شاشة الدفع)
            handleCheckout();
          } else if (showPayment) {
            // Enter الثانية: تأكيد الدفع
            const isPaymentValid = !(paymentDetails.type === 'cash' && receivedAmount !== '' && change < 0);
            if (isPaymentValid) {
              handlePayment();
            }
          }
          break;
        case 'Escape':
          event.preventDefault();
          if (showPayment) {
            // Escape: إغلاق شاشة الدفع والعودة للبيع
            setShowPayment(false);
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [showPayment, cart.length, showSuccessModal, paymentDetails, receivedAmount, change]);

  // 🔍 تصفية محسنة مع التحميل التدريجي
  const { paginatedProducts, totalPages, totalItems } = useMemo(() => {
    let filtered = products.filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           product.barcode.includes(searchTerm) ||
                           product.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'الكل' ||
                             categories.find(cat => cat.id === product.category)?.name === selectedCategory;
      return matchesSearch && matchesCategory && product.isActive;
    });

    // التحميل التدريجي
    const totalItems = filtered.length;
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedProducts = filtered.slice(startIndex, endIndex);

    return {
      paginatedProducts,
      totalPages,
      totalItems
    };
  }, [products, searchTerm, selectedCategory, categories, currentPage, itemsPerPage]);



  const handleCheckout = () => {
    if (cart.length === 0) return;
    setShowPayment(true);
  };

  const handlePayment = async () => {
    // التحقق من صحة الدفع بالرصيد
    if (paymentDetails.type === 'balance') {
      if (!selectedCustomer) {
        alert('يجب اختيار عميل للدفع بالرصيد');
        return;
      }
      if (customerBalance < finalTotal) {
        alert(`رصيد العميل غير كافي. الرصيد المتاح: ${customerBalance.toFixed(2)} دج`);
        return;
      }
    }

    if (paymentDetails.type === 'mixed' && (paymentDetails.cashAmount + paymentDetails.creditAmount + paymentDetails.balanceAmount) < finalTotal) {
      alert('المبلغ المدفوع أقل من الإجمالي المطلوب');
      return;
    }

    // حساب المبلغ المدفوع والباقي حسب طريقة الدفع
    let actualPaid = 0;
    let actualChange = 0;

    switch (paymentDetails.type) {
      case 'cash':
        if (!receivedAmount) {
          actualPaid = finalTotal;
          actualChange = 0;
        } else {
          if (paymentDetails.totalPaid < finalTotal) {
            alert('المبلغ المدفوع أقل من الإجمالي المطلوب');
            return;
          }
          actualPaid = paymentDetails.totalPaid;
          actualChange = change;
        }
        break;
      case 'credit':
        actualPaid = 0;
        actualChange = 0;
        break;
      case 'balance':
        actualPaid = finalTotal; // المبلغ مدفوع من الرصيد
        actualChange = 0;
        break;
      case 'mixed':
        actualPaid = paymentDetails.cashAmount + paymentDetails.balanceAmount;
        actualChange = Math.max(0, actualPaid - finalTotal);
        break;
      default:
        actualPaid = paymentDetails.totalPaid;
        actualChange = change;
    }

    try {
      // إعداد بيانات البيع للـ API
      const saleApiData = {
        customer_id: selectedCustomer || null,
        items: cart.map(item => ({
          product_id: item.productId.startsWith('unknown-') ? null : item.productId, // null للمنتجات غير المعروفة
          product_name: item.productName,
          quantity: item.quantity,
          unit_price: item.unitPrice,
          discount: item.discount || 0,
          total_price: item.quantity * item.unitPrice - (item.discount || 0)
        })),
        subtotal: cartTotal,
        tax_amount: 0,
        discount_amount: discountAmount,
        total_amount: finalTotal,
        payment_method: paymentDetails.type,
        amount_paid: actualPaid,
        change_amount: actualChange,
        balance_amount: paymentDetails.type === 'balance' ? finalTotal : (paymentDetails.balanceAmount || 0),
        notes: '',
        cashier_name: 'الكاشير' // يمكن تحديثه لاحقاً من الإعدادات
      };

      console.log('🔄 بدء عملية حفظ البيع...');
      console.log('📋 بيانات البيع المرسلة:', saleApiData);
      console.log('🛒 السلة:', cart);
      console.log('💳 طريقة الدفع:', paymentDetails.type);

      // حفظ البيع في قاعدة البيانات
      console.log('📡 إرسال البيع إلى الخادم...');
      const savedSale = await completeSale(saleApiData);
      console.log('✅ تم حفظ البيع بنجاح:', savedSale);

      // عد المنتجات غير المعروفة
      const unknownProductsCount = cart.filter(item => item.productId.startsWith('unknown-')).length;

      if (unknownProductsCount > 0) {
        console.log(`🔮 تم بيع ${unknownProductsCount} منتج غير معروف بنجاح!`);
      }

      // الحصول على اسم العميل
      const customerName = selectedCustomer
        ? customers.find(c => c.id === selectedCustomer)?.name || 'عميل غير معروف'
        : 'عميل عادي';

      // إنشاء بيانات البيع للمودال
      const saleData = {
        total: finalTotal,
        paid: actualPaid,
        change: actualChange,
        items: cart.map(item => ({
          name: item.productName,
          quantity: item.quantity,
          price: item.unitPrice
        })),
        paymentMethod: paymentDetails.type,
        customer: customerName,
        saleNumber: savedSale?.sale_number || `INV-${Date.now().toString().slice(-6)}`,
        timestamp: new Date()
      };

      setLastSaleData(saleData);
      setShowSuccessModal(true);

      // إخفاء شاشة الدفع
      setShowPayment(false);
      setReceivedAmount('');
      setPaymentDetails({
        type: 'cash',
        cashAmount: 0,
        creditAmount: 0,
        balanceAmount: 0,
        totalPaid: 0
      });

    } catch (error) {
      console.error('❌ خطأ في حفظ البيع:', error);
      console.error('🔍 تفاصيل الخطأ:', error instanceof Error ? error.message : 'خطأ غير معروف');
      console.error('📋 بيانات البيع التي فشلت:', {
        cart,
        paymentDetails,
        selectedCustomer,
        finalTotal
      });

      // عرض رسالة خطأ مفصلة للمستخدم
      const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف';
      alert(`❌ فشل في حفظ البيع!\n\nالخطأ: ${errorMessage}\n\nيرجى المحاولة مرة أخرى أو التحقق من اتصال الإنترنت.`);
    }
  };

  const handleCloseSuccessModal = () => {
    setShowSuccessModal(false);
    clearCart();
  };

  const handleOpenReceipt = () => {
    setShowSuccessModal(false);
    setShowReceiptModal(true);
  };

  const handleCloseReceipt = () => {
    setShowReceiptModal(false);
    clearCart();
  };

  const updatePaymentAmount = (amount: number) => {
    if (paymentDetails.type === 'cash') {
      setPaymentDetails(prev => ({
        ...prev,
        cashAmount: amount,
        totalPaid: amount
      }));
    }
  };

  // وظيفة البحث بالباركود والإضافة التلقائية
  const handleBarcodeInput = (value: string) => {
    setBarcodeInput(value);

    // التحقق من طول الباركود (عادة 12-13 رقم)
    if (value.length >= 12) {
      // البحث عن المنتج بالباركود
      const foundProduct = products.find(product => product.barcode === value);

      if (foundProduct) {
        // إضافة المنتج للسلة فوراً
        addToCart(foundProduct);

        // تأثير بصري للنجاح
        const inputElement = document.querySelector('#barcode-input') as HTMLInputElement;
        if (inputElement) {
          inputElement.style.borderColor = '#10B981';
          inputElement.style.backgroundColor = 'rgba(16, 185, 129, 0.2)';
          inputElement.style.boxShadow = '0 0 20px rgba(16, 185, 129, 0.5)';

          // إضافة رسالة نجاح مؤقتة
          const successMsg = document.createElement('div');
          successMsg.textContent = `✅ تم إضافة ${foundProduct.name}`;
          successMsg.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #10B981, #059669);
            color: white;
            padding: 12px 20px;
            border-radius: 12px;
            font-weight: bold;
            z-index: 9999;
            animation: slideIn 0.3s ease-out;
            box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
          `;
          document.body.appendChild(successMsg);

          // إزالة الرسالة بعد ثانيتين
          setTimeout(() => {
            successMsg.remove();
          }, 2000);
        }

        // مسح الحقل فوراً
        setTimeout(() => {
          setBarcodeInput('');
          if (inputElement) {
            inputElement.style.borderColor = '';
            inputElement.style.backgroundColor = '';
            inputElement.style.boxShadow = '';
            inputElement.focus(); // إعادة التركيز للمسح التالي
          }
        }, 300);

      } else {
        // تأثير بصري للخطأ
        const inputElement = document.querySelector('#barcode-input') as HTMLInputElement;
        if (inputElement) {
          inputElement.style.borderColor = '#EF4444';
          inputElement.style.backgroundColor = 'rgba(239, 68, 68, 0.1)';
          inputElement.style.boxShadow = '0 0 20px rgba(239, 68, 68, 0.3)';

          // رسالة خطأ
          const errorMsg = document.createElement('div');
          errorMsg.textContent = `❌ لم يتم العثور على المنتج`;
          errorMsg.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #EF4444, #DC2626);
            color: white;
            padding: 12px 20px;
            border-radius: 12px;
            font-weight: bold;
            z-index: 9999;
            animation: slideIn 0.3s ease-out;
            box-shadow: 0 10px 25px rgba(239, 68, 68, 0.3);
          `;
          document.body.appendChild(errorMsg);

          setTimeout(() => {
            errorMsg.remove();
            setBarcodeInput('');
            if (inputElement) {
              inputElement.style.borderColor = '';
              inputElement.style.backgroundColor = '';
              inputElement.style.boxShadow = '';
              inputElement.focus();
            }
          }, 1500);
        }
      }
    }
  };

  // إعادة تعيين الصفحة عند تغيير البحث أو الفلتر
  const handleSearchChange = useCallback((value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  }, []);

  const handleCategoryChange = useCallback((value: string) => {
    setSelectedCategory(value);
    setCurrentPage(1);
  }, []);

  // وظيفة معالجة البحث (بدون إضافة تلقائية)
  const handleSearchInput = useCallback((value: string) => {
    handleSearchChange(value);
  }, [handleSearchChange]);

  // وظيفة إضافة منتج غير معروف عند الضغط على Enter
  // دالة لتحديد أي تقسيمات الـ 7-segment تضيء لكل رقم
  const getSegmentClass = (digit: string, segment: string) => {
    const segments: { [key: string]: string[] } = {
      '0': ['a', 'b', 'c', 'd', 'e', 'f'],
      '1': ['b', 'c'],
      '2': ['a', 'b', 'g', 'e', 'd'],
      '3': ['a', 'b', 'g', 'c', 'd'],
      '4': ['f', 'g', 'b', 'c'],
      '5': ['a', 'f', 'g', 'c', 'd'],
      '6': ['a', 'f', 'g', 'e', 'd', 'c'],
      '7': ['a', 'b', 'c'],
      '8': ['a', 'b', 'c', 'd', 'e', 'f', 'g'],
      '9': ['a', 'b', 'c', 'd', 'f', 'g']
    };

    const isActive = segments[digit]?.includes(segment);

    if (isActive) {
      return `bg-green-400 rounded-sm`;
    } else {
      return `bg-gray-800 opacity-10 rounded-sm`;
    }
  };

  const handleAddUnknownProduct = async () => {
    // التحقق من نمط /المبلغ
    const pricePattern = /^\/(\d+(?:\.\d{1,2})?)$/;
    const match = searchTerm.match(pricePattern);

    if (match) {
      const price = parseFloat(match[1]);

      if (price > 0) {
        // إنشاء منتج غير معروف مع معرف خاص
        const unknownProduct = {
          id: `unknown-${Date.now()}`, // معرف خاص للمنتجات غير المعروفة
          name: `🔮 منتج غير معروف - ${price.toFixed(2)} دج`,
          price: price,
          cost: price * 0.7, // تكلفة افتراضية 70% من السعر
          stock: 999,
          minStock: 0,
          barcode: `UNK${Date.now()}`,
          category: 'غير محدد',
          description: 'منتج غير معروف تم إضافته يدوياً من شاشة البيع',
          supplier: 'غير محدد',
          unit: 'قطعة',
          createdDate: new Date(),
          updatedDate: new Date(),
          isActive: true
        };

        // إضافة المنتج للسلة
        addToCart(unknownProduct);

        // رسالة نجاح سحرية محسنة
        const successMsg = document.createElement('div');
        successMsg.innerHTML = `
          <div style="display: flex; align-items: center; gap: 8px;">
            <div style="font-size: 20px;">🔮</div>
            <div>
              <div style="font-weight: bold;">تم إضافة منتج غير معروف!</div>
              <div style="font-size: 12px; opacity: 0.9;">بقيمة ${price.toFixed(2)} دج</div>
            </div>
          </div>
        `;
        successMsg.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: linear-gradient(135deg, #8B5CF6, #7C3AED);
          color: white;
          padding: 16px 24px;
          border-radius: 16px;
          font-weight: bold;
          z-index: 9999;
          animation: magicSlideIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
          box-shadow: 0 20px 40px rgba(139, 92, 246, 0.4);
          border: 2px solid rgba(255, 255, 255, 0.2);
        `;

        // إضافة CSS للرسوم المتحركة
        if (!document.getElementById('magic-animations')) {
          const style = document.createElement('style');
          style.id = 'magic-animations';
          style.textContent = `
            @keyframes magicSlideIn {
              0% { transform: translateX(100%) scale(0.8); opacity: 0; }
              50% { transform: translateX(-10px) scale(1.05); }
              100% { transform: translateX(0) scale(1); opacity: 1; }
            }
            @keyframes magicPulse {
              0%, 100% { transform: scale(1); }
              50% { transform: scale(1.05); }
            }
          `;
          document.head.appendChild(style);
        }

        document.body.appendChild(successMsg);

        // تأثير نبضة
        setTimeout(() => {
          successMsg.style.animation = 'magicPulse 0.3s ease-in-out';
        }, 500);

        setTimeout(() => {
          successMsg.style.animation = 'magicSlideIn 0.3s ease-out reverse';
          setTimeout(() => successMsg.remove(), 300);
        }, 3000);

        // مسح حقل البحث
        setSearchTerm('');

        // تركيز على حقل البحث
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
          searchInput.focus();
        }

        // إحصائيات المنتجات غير المعروفة في السلة
        const unknownCount = cart.filter(item => item.productId.startsWith('unknown-')).length + 1;
        console.log(`🔮 إجمالي المنتجات غير المعروفة في السلة: ${unknownCount}`);
      } else {
        // رسالة خطأ محسنة
        const errorMsg = document.createElement('div');
        errorMsg.innerHTML = `
          <div style="display: flex; align-items: center; gap: 8px;">
            <div style="font-size: 20px;">⚠️</div>
            <div>
              <div style="font-weight: bold;">خطأ في السعر!</div>
              <div style="font-size: 12px; opacity: 0.9;">يجب أن يكون السعر أكبر من صفر</div>
            </div>
          </div>
        `;
        errorMsg.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: linear-gradient(135deg, #EF4444, #DC2626);
          color: white;
          padding: 16px 24px;
          border-radius: 16px;
          font-weight: bold;
          z-index: 9999;
          animation: magicSlideIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
          box-shadow: 0 20px 40px rgba(239, 68, 68, 0.4);
          border: 2px solid rgba(255, 255, 255, 0.2);
        `;
        document.body.appendChild(errorMsg);

        setTimeout(() => {
          errorMsg.style.animation = 'magicSlideIn 0.3s ease-out reverse';
          setTimeout(() => errorMsg.remove(), 300);
        }, 2000);
      }
    } else {
      // رسالة تنسيق خاطئ محسنة
      const formatMsg = document.createElement('div');
      formatMsg.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px;">
          <div style="font-size: 20px;">💡</div>
          <div>
            <div style="font-weight: bold;">تنسيق غير صحيح!</div>
            <div style="font-size: 12px; opacity: 0.9;">استخدم: /السعر (مثال: /100)</div>
          </div>
        </div>
      `;
      formatMsg.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #F59E0B, #D97706);
        color: white;
        padding: 16px 24px;
        border-radius: 16px;
        font-weight: bold;
        z-index: 9999;
        animation: magicSlideIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        box-shadow: 0 20px 40px rgba(245, 158, 11, 0.4);
        border: 2px solid rgba(255, 255, 255, 0.2);
      `;
      document.body.appendChild(formatMsg);

      setTimeout(() => {
        formatMsg.style.animation = 'magicSlideIn 0.3s ease-out reverse';
        setTimeout(() => formatMsg.remove(), 300);
      }, 2500);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900">
      {/* CSS للرسائل المنبثقة والتأثيرات */}
      <style>{`
        @keyframes slideIn {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }

        .animation-delay-100 {
          animation-delay: 0.1s;
        }

        .animation-delay-300 {
          animation-delay: 0.3s;
        }

        .animation-delay-400 {
          animation-delay: 0.4s;
        }

        .animation-delay-500 {
          animation-delay: 0.5s;
        }

        .animation-delay-600 {
          animation-delay: 0.6s;
        }

        .animation-delay-700 {
          animation-delay: 0.7s;
        }

        @keyframes shimmer {
          0% {
            background-position: -200% 0;
          }
          100% {
            background-position: 200% 0;
          }
        }

        .shimmer {
          background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
          background-size: 200% 100%;
          animation: shimmer 2s infinite;
        }

        .digital-display {
          font-family: 'Courier New', 'Monaco', 'Lucida Console', monospace;
          font-weight: 900;
          letter-spacing: 0.15em;
          text-rendering: optimizeLegibility;
        }

        @keyframes flicker {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.8; }
        }

        @keyframes scanline {
          0% { transform: translateY(-100%); }
          100% { transform: translateY(100vh); }
        }
      `}</style>

      {/* Animated Background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>
        <div className="absolute top-40 left-1/2 w-80 h-80 bg-cyan-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000"></div>
      </div>

      <div className="relative z-10 flex h-screen">
        {/* Left Panel - Products */}
        <div className="flex-1 p-6 overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-reverse space-x-6">
              <div className="relative">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center shadow-2xl">
                  <ShoppingCart className="w-8 h-8 text-white animate-bounce" />
                </div>
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full animate-ping"></div>
                </div>
              </div>
              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-white via-blue-200 to-purple-200 bg-clip-text text-transparent mb-2">
                  🛒 شاشة البيع
                </h1>
                <p className="text-slate-300 text-lg">نظام بيع متطور وسريع</p>
                <div className="flex items-center mt-2 text-sm text-slate-400">
                  <Clock className="w-4 h-4 ml-2" />
                  <span>{currentTime.toLocaleTimeString('ar-SA')}</span>
                  <div className="flex items-center ml-4 space-x-reverse space-x-2">
                    <div className={`w-3 h-3 rounded-full ${isOnline ? 'bg-green-400 animate-pulse' : 'bg-red-400'}`}></div>
                    <span className="text-sm">{isOnline ? 'متصل' : 'غير متصل'}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* الأرقام الإلكترونية الحقيقية في الوسط */}
            <div className="flex-1 flex justify-center">
              <div className="flex items-center space-x-reverse space-x-2">
                {/* عرض الأرقام بتقسيمات 7-segment حقيقية */}
                {finalTotal.toFixed(2).split('').reverse().map((char, index) => (
                  <div key={index} className="relative">
                    {char === '.' ? (
                      // نقطة عشرية
                      <div className="w-3 h-3 mx-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{
                            backgroundColor: '#00ff00',
                            boxShadow: '0 0 6px #00ff00, 0 0 12px #00ff00'
                          }}
                        ></div>
                      </div>
                    ) : (
                      // رقم بتقسيمات 7-segment
                      <div className="relative w-16 h-24 mx-1">
                        {/* الخلفية المظلمة للتقسيمات */}
                        <div className="absolute inset-0">
                          {/* التقسيمات السبعة */}
                          {/* أعلى */}
                          <div
                            className={`absolute top-0 left-2 right-2 h-2 ${getSegmentClass(char, 'a')}`}
                            style={getSegmentClass(char, 'a').includes('bg-green-400') ? {
                              boxShadow: '0 0 4px #00ff00, 0 0 8px #00ff00'
                            } : {}}
                          ></div>
                          {/* أعلى يمين */}
                          <div
                            className={`absolute top-1 right-0 w-2 h-10 ${getSegmentClass(char, 'b')}`}
                            style={getSegmentClass(char, 'b').includes('bg-green-400') ? {
                              boxShadow: '0 0 4px #00ff00, 0 0 8px #00ff00'
                            } : {}}
                          ></div>
                          {/* أسفل يمين */}
                          <div
                            className={`absolute bottom-1 right-0 w-2 h-10 ${getSegmentClass(char, 'c')}`}
                            style={getSegmentClass(char, 'c').includes('bg-green-400') ? {
                              boxShadow: '0 0 4px #00ff00, 0 0 8px #00ff00'
                            } : {}}
                          ></div>
                          {/* أسفل */}
                          <div
                            className={`absolute bottom-0 left-2 right-2 h-2 ${getSegmentClass(char, 'd')}`}
                            style={getSegmentClass(char, 'd').includes('bg-green-400') ? {
                              boxShadow: '0 0 4px #00ff00, 0 0 8px #00ff00'
                            } : {}}
                          ></div>
                          {/* أسفل يسار */}
                          <div
                            className={`absolute bottom-1 left-0 w-2 h-10 ${getSegmentClass(char, 'e')}`}
                            style={getSegmentClass(char, 'e').includes('bg-green-400') ? {
                              boxShadow: '0 0 4px #00ff00, 0 0 8px #00ff00'
                            } : {}}
                          ></div>
                          {/* أعلى يسار */}
                          <div
                            className={`absolute top-1 left-0 w-2 h-10 ${getSegmentClass(char, 'f')}`}
                            style={getSegmentClass(char, 'f').includes('bg-green-400') ? {
                              boxShadow: '0 0 4px #00ff00, 0 0 8px #00ff00'
                            } : {}}
                          ></div>
                          {/* وسط */}
                          <div
                            className={`absolute top-11 left-2 right-2 h-2 ${getSegmentClass(char, 'g')}`}
                            style={getSegmentClass(char, 'g').includes('bg-green-400') ? {
                              boxShadow: '0 0 4px #00ff00, 0 0 8px #00ff00'
                            } : {}}
                          ></div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}

                {/* وحدة العملة */}
                <div className="ml-6 text-4xl font-bold" style={{
                  color: '#00ff00',
                  textShadow: '0 0 5px #00ff00, 0 0 10px #00ff00, 0 0 15px #00ff00',
                  fontFamily: '"Courier New", monospace',
                  letterSpacing: '3px'
                }}>
                  دج
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-reverse space-x-4">
              {/* عدد العناصر في الصفحة */}
              <select
                value={itemsPerPage}
                onChange={(e) => {
                  setItemsPerPage(Number(e.target.value));
                  setCurrentPage(1);
                }}
                className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl px-3 py-2 text-white text-sm focus:outline-none focus:border-blue-400"
              >
                <option value={6} className="bg-slate-800">6 منتجات</option>
                <option value={12} className="bg-slate-800">12 منتج</option>
                <option value={24} className="bg-slate-800">24 منتج</option>
                <option value={48} className="bg-slate-800">48 منتج</option>
              </select>

              <button
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                className="p-3 bg-white/10 backdrop-blur-lg rounded-xl border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105"
              >
                {viewMode === 'grid' ? <List className="w-5 h-5 text-white" /> : <Grid className="w-5 h-5 text-white" />}
              </button>
              <button className="p-3 bg-gradient-to-r from-green-500 to-emerald-400 rounded-xl hover:scale-105 transition-transform duration-300 shadow-lg">
                <Scan className="w-5 h-5 text-white" />
              </button>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="bg-white/5 backdrop-blur-xl rounded-3xl p-6 border border-white/10 mb-6">
            <div className="flex flex-col lg:flex-row gap-4 mb-6">
              {/* Barcode Scanner Input */}
              <div className="relative flex-1 group">
                <Scan className={`absolute right-4 top-1/2 transform -translate-y-1/2 w-6 h-6 transition-all duration-300 drop-shadow-lg ${
                  barcodeInput.length > 0
                    ? 'text-yellow-300 animate-spin shadow-lg'
                    : 'text-yellow-300 animate-pulse shadow-lg'
                }`} style={{
                  filter: barcodeInput.length > 0
                    ? 'drop-shadow(0 0 8px rgba(251, 191, 36, 0.8))'
                    : 'drop-shadow(0 0 8px rgba(251, 191, 36, 0.6))'
                }} />
                <input
                  id="barcode-input"
                  type="text"
                  placeholder="امسح الباركود هنا للإضافة السريعة..."
                  value={barcodeInput}
                  onChange={(e) => handleBarcodeInput(e.target.value)}
                  className={`w-full rounded-xl px-12 py-3 focus:outline-none transition-all duration-300 font-mono text-lg font-bold ${
                    barcodeInput.length > 0
                      ? 'bg-yellow-500/20 border-2 border-yellow-400/50 text-black placeholder-yellow-600/80 focus:border-yellow-400 focus:shadow-lg focus:shadow-yellow-400/30 bg-yellow-200'
                      : 'bg-yellow-500/20 border-2 border-yellow-400/50 text-yellow-300 placeholder-yellow-300/80 focus:border-yellow-400 focus:shadow-lg focus:shadow-yellow-400/30'
                  }`}
                  style={{
                    textShadow: barcodeInput.length > 0
                      ? '0 0 10px rgba(251, 191, 36, 0.8)'
                      : '0 0 10px rgba(251, 191, 36, 0.6)',
                    letterSpacing: '2px',
                    boxShadow: barcodeInput.length > 0
                      ? '0 0 30px rgba(251, 191, 36, 0.4), inset 0 0 20px rgba(251, 191, 36, 0.2)'
                      : '0 0 30px rgba(251, 191, 36, 0.3), inset 0 0 20px rgba(251, 191, 36, 0.1)'
                  }}
                  autoComplete="off"
                  autoFocus
                />
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2 flex items-center space-x-reverse space-x-2">
                  <div className={`w-3 h-3 rounded-full transition-all duration-300 shadow-lg ${
                    barcodeInput.length > 0
                      ? 'bg-gradient-to-r from-yellow-400 to-orange-400 animate-bounce shadow-yellow-400/50'
                      : 'bg-gradient-to-r from-yellow-400 to-orange-400 animate-ping shadow-yellow-400/50'
                  }`}></div>
                  <span className={`text-xs font-bold transition-all duration-300 drop-shadow-lg ${
                    barcodeInput.length > 0
                      ? 'text-yellow-300 animate-pulse'
                      : 'text-yellow-300'
                  }`}>
                    {barcodeInput.length > 0 ? `${barcodeInput.length}/12` : 'باركود'}
                  </span>
                </div>

                {/* شريط التقدم */}
                {barcodeInput.length > 0 && (
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-700/30 rounded-b-xl overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-yellow-400 to-orange-400 transition-all duration-300 ease-out"
                      style={{
                        width: `${(barcodeInput.length / 12) * 100}%`,
                        boxShadow: '0 0 10px rgba(251, 191, 36, 0.6)'
                      }}
                    ></div>
                  </div>
                )}
              </div>

              {/* Search Bar */}
              <div className="relative flex-1">
                <Search className={`absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 transition-all duration-300 ${
                  searchTerm.startsWith('/') ? 'text-purple-400 animate-pulse' : 'text-slate-400'
                }`} />
                <input
                  id="search-input"
                  type="text"
                  placeholder="البحث عن المنتجات أو /المبلغ للمنتج غير معروف..."
                  value={searchTerm}
                  onChange={(e) => handleSearchInput(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && searchTerm.startsWith('/')) {
                      e.preventDefault(); // منع السلوك الافتراضي
                      handleAddUnknownProduct(); // إضافة المنتج عند الضغط على Enter
                    }
                  }}
                  className={`w-full rounded-xl px-12 py-3 text-white placeholder-slate-400 focus:outline-none transition-all duration-300 ${
                    (() => {
                      const priceMatch = searchTerm.match(/^\/(\d+(?:\.\d{1,2})?)$/);
                      if (priceMatch) {
                        // نمط صحيح - جاهز للإضافة
                        return 'bg-gradient-to-r from-purple-500/30 to-pink-500/30 border-2 border-purple-400 focus:border-purple-300 focus:shadow-lg focus:shadow-purple-400/40 animate-pulse';
                      } else if (searchTerm.startsWith('/')) {
                        // يكتب نمط السعر لكن غير مكتمل
                        return 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 border-2 border-purple-400/50 focus:border-purple-400 focus:shadow-lg focus:shadow-purple-400/25';
                      } else {
                        // بحث عادي
                        return 'bg-white/10 border border-white/20 focus:border-blue-400';
                      }
                    })()
                  }`}
                  style={{
                    textShadow: searchTerm.startsWith('/') ? '0 0 10px rgba(139, 92, 246, 0.8)' : 'none'
                  }}
                />
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2 flex items-center space-x-reverse space-x-2">
                  {(() => {
                    const priceMatch = searchTerm.match(/^\/(\d+(?:\.\d{1,2})?)$/);
                    if (priceMatch) {
                      // نمط صحيح - جاهز للإضافة
                      return (
                        <>
                          <div className="w-3 h-3 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-ping shadow-lg"></div>
                          <span className="text-purple-300 text-xs font-bold animate-pulse">اضغط Enter!</span>
                        </>
                      );
                    } else if (searchTerm.startsWith('/')) {
                      // يكتب نمط السعر لكن غير مكتمل
                      return (
                        <>
                          <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"></div>
                          <span className="text-purple-400 text-xs font-bold">سعر مخصص</span>
                        </>
                      );
                    } else {
                      // بحث عادي
                      return (
                        <button className="p-1 hover:bg-white/10 rounded-lg transition-colors">
                          <Filter className="w-5 h-5 text-slate-400 hover:text-white" />
                        </button>
                      );
                    }
                  })()}
                </div>
              </div>

              {/* تلميح للسعر المخصص */}
              {searchTerm.startsWith('/') && (
                <div className="mt-2 p-3 bg-gradient-to-r from-purple-500/10 to-pink-500/10 border border-purple-400/30 rounded-xl">
                  <div className="flex items-center space-x-reverse space-x-2">
                    <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                    <span className="text-purple-300 text-sm font-medium">
                      {(() => {
                        const priceMatch = searchTerm.match(/^\/(\d+(?:\.\d{1,2})?)$/);
                        if (priceMatch) {
                          const price = parseFloat(priceMatch[1]);
                          return (
                            <span className="flex items-center space-x-reverse space-x-2">
                              <span>✨ منتج بسعر {price.toFixed(2)} دج جاهز</span>
                              <span className="bg-purple-500 text-white px-2 py-1 rounded text-xs font-bold animate-pulse">
                                اضغط Enter ⏎
                              </span>
                            </span>
                          );
                        } else {
                          return '💡 مثال: اكتب /100 ثم اضغط Enter لإضافة منتج بسعر 100 دج';
                        }
                      })()}
                    </span>
                  </div>
                </div>
              )}

              {/* Category Filter */}
              <div className="relative">
                <Filter className="absolute right-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                <select
                  value={selectedCategory}
                  onChange={(e) => handleCategoryChange(e.target.value)}
                  className="bg-white/10 border border-white/20 rounded-xl px-12 py-3 text-white focus:outline-none focus:border-blue-400 transition-colors min-w-[150px]"
                >
                  {categoryNames.map(categoryName => (
                    <option key={categoryName} value={categoryName} className="bg-slate-800">
                      {categoryName}
                    </option>
                  ))}
                </select>
              </div>

              {/* Customer Selection */}
              <div className="relative">
                <User className="absolute right-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                <select
                  value={selectedCustomer}
                  onChange={(e) => setSelectedCustomer(e.target.value)}
                  className="bg-white/10 border border-white/20 rounded-xl px-12 py-3 text-white focus:outline-none focus:border-blue-400 transition-colors min-w-[200px]"
                >
                  <option value="" className="bg-slate-800">اختر العميل (اختياري)</option>
                  {customers.map((customer) => {
                    const customerDebt = customerDebts[customer.id] || 0;
                    return (
                      <option key={customer.id} value={customer.id} className="bg-slate-800">
                        {customer.name} - {customer.phone}
                        {customer.balance > 0 && (
                          ` 💰 رصيد: ${customer.balance.toFixed(2)} دج`
                        )}
                        {customerDebt > 0 && (
                          ` 🔴 دين: ${customerDebt.toFixed(2)} دج`
                        )}
                      </option>
                    );
                  })}
                </select>
              </div>
            </div>

            {/* Customer Balance Notification */}
            {selectedCustomerData && customerBalance > 0 && (
              <div className="mb-6 p-4 bg-gradient-to-r from-yellow-500/20 to-orange-400/20 border border-yellow-400/30 rounded-xl">
                <div className="flex items-center space-x-reverse space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full flex items-center justify-center">
                    <Wallet className="w-5 h-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <div className="text-yellow-200 font-bold">
                      💰 العميل {selectedCustomerData.name} لديه رصيد متاح
                    </div>
                    <div className="text-yellow-100 text-sm">
                      الرصيد المتاح: {customerBalance.toFixed(2)} دج - يمكن استخدامه في الدفع
                    </div>
                  </div>
                  {customerBalance >= finalTotal && (
                    <div className="text-green-400 font-bold text-sm bg-green-500/20 px-3 py-1 rounded-lg">
                      ✅ يكفي للدفع الكامل
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Quick Stats */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-white/5 rounded-xl p-4 border border-white/10">
                <div className="flex items-center space-x-reverse space-x-3">
                  <Package className="w-8 h-8 text-blue-400" />
                  <div>
                    <p className="text-slate-400 text-sm">إجمالي المنتجات</p>
                    <p className="text-white font-bold text-lg">{products.length}</p>
                  </div>
                </div>
              </div>
              <div className="bg-white/5 rounded-xl p-4 border border-white/10">
                <div className="flex items-center space-x-reverse space-x-3">
                  <Star className="w-8 h-8 text-yellow-400" />
                  <div>
                    <p className="text-slate-400 text-sm">منتجات مميزة</p>
                    <p className="text-white font-bold text-lg">{products.filter(p => p.stock > 50).length}</p>
                  </div>
                </div>
              </div>
              <div className="bg-white/5 rounded-xl p-4 border border-white/10">
                <div className="flex items-center space-x-reverse space-x-3">
                  <Tag className="w-8 h-8 text-green-400" />
                  <div>
                    <p className="text-slate-400 text-sm">عروض خاصة</p>
                    <p className="text-white font-bold text-lg">{products.filter(p => p.price < p.cost * 1.5).length}</p>
                  </div>
                </div>
              </div>
              <div className="bg-white/5 rounded-xl p-4 border border-white/10">
                <div className="flex items-center space-x-reverse space-x-3">
                  <Sparkles className="w-8 h-8 text-purple-400" />
                  <div>
                    <p className="text-slate-400 text-sm">منتجات جديدة</p>
                    <p className="text-white font-bold text-lg">{products.filter(p => {
                      const daysSinceCreated = Math.floor((new Date().getTime() - new Date(p.createdDate).getTime()) / (1000 * 60 * 60 * 24));
                      return daysSinceCreated <= 7;
                    }).length}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 📊 شريط المعلومات والتحكم */}
          <div className="bg-gradient-to-r from-slate-800/50 to-slate-700/50 backdrop-blur-lg rounded-2xl border border-slate-600/30 p-4 mb-6">
            <div className="flex items-center justify-between flex-wrap gap-4">
              {/* معلومات العدد */}
              <div className="flex items-center space-x-6 space-x-reverse">
                <div className="text-white">
                  <span className="text-sm text-slate-300">عرض </span>
                  <span className="font-bold text-blue-400">{paginatedProducts.length}</span>
                  <span className="text-sm text-slate-300"> من </span>
                  <span className="font-bold text-purple-400">{totalItems}</span>
                  <span className="text-sm text-slate-300"> منتج</span>
                </div>


              </div>

              {/* أزرار التنقل */}
              {totalPages > 1 && (
                <div className="flex items-center space-x-2 space-x-reverse">
                  <button
                    onClick={() => setCurrentPage(1)}
                    disabled={currentPage === 1}
                    className="px-3 py-1 rounded-lg bg-slate-700 text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-slate-600 transition-colors"
                  >
                    الأولى
                  </button>

                  <button
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                    className="px-3 py-1 rounded-lg bg-slate-700 text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-slate-600 transition-colors"
                  >
                    السابقة
                  </button>

                  <div className="flex items-center space-x-2 space-x-reverse">
                    <span className="text-slate-300 text-sm">صفحة</span>
                    <span className="font-bold text-white">{currentPage}</span>
                    <span className="text-slate-300 text-sm">من</span>
                    <span className="font-bold text-white">{totalPages}</span>
                  </div>

                  <button
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    disabled={currentPage === totalPages}
                    className="px-3 py-1 rounded-lg bg-slate-700 text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-slate-600 transition-colors"
                  >
                    التالية
                  </button>

                  <button
                    onClick={() => setCurrentPage(totalPages)}
                    disabled={currentPage === totalPages}
                    className="px-3 py-1 rounded-lg bg-slate-700 text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-slate-600 transition-colors"
                  >
                    الأخيرة
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Products Grid */}
          <div className="bg-white/5 backdrop-blur-xl rounded-3xl border border-white/10 flex flex-col h-[600px]">
            {/* Header */}
            <div className="p-6 border-b border-white/10 flex-shrink-0">
              <div className="flex items-center justify-between">
                <h3 className="text-2xl font-bold text-white flex items-center">
                  <Package className="w-8 h-8 ml-3 text-blue-400" />
                  المنتجات المتاحة
                  <span className="bg-blue-500 text-white text-sm px-2 py-1 rounded-full ml-3">
                    {totalItems}
                  </span>
                </h3>
                <div className="flex items-center space-x-reverse space-x-2">
                  <span className="text-slate-400 text-sm">عرض:</span>
                  <span className="text-blue-400 font-medium">{selectedCategory}</span>
                </div>
              </div>
            </div>

            {/* Scrollable Content */}
            <div className="flex-1 overflow-y-auto p-6">
              {totalItems > 0 ? (
                <div className={`grid gap-4 ${viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' : 'grid-cols-1'}`}>
                {paginatedProducts.map((product) => (
                  <div
                    key={product.id}
                    className="group relative overflow-hidden bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105 hover:shadow-2xl cursor-pointer"
                    onClick={() => addToCart(product)}
                  >
                    {/* شارة الحالة */}
                    <div className="absolute top-4 left-4 z-10">
                      {product.stock === 0 ? (
                        <span className="bg-red-500 text-white px-2 py-1 rounded-lg text-xs font-bold flex items-center space-x-1 space-x-reverse">
                          <X className="w-3 h-3" />
                          <span>نفد</span>
                        </span>
                      ) : product.stock <= product.minStock ? (
                        <span className="bg-yellow-500 text-white px-2 py-1 rounded-lg text-xs font-bold flex items-center space-x-1 space-x-reverse">
                          <Target className="w-3 h-3" />
                          <span>منخفض</span>
                        </span>
                      ) : (
                        <span className="bg-green-500 text-white px-2 py-1 rounded-lg text-xs font-bold flex items-center space-x-1 space-x-reverse">
                          <Check className="w-3 h-3" />
                          <span>متوفر</span>
                        </span>
                      )}
                    </div>

                    {/* شارة القسم */}
                    <div className="absolute top-4 right-4 z-10">
                      <span className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-3 py-1 rounded-lg text-xs font-medium">
                        {categories.find(cat => cat.id === product.category)?.name || 'غير محدد'}
                      </span>
                    </div>

                    {/* صورة المنتج مصغرة */}
                    <div className="h-32 bg-gradient-to-br from-blue-500/20 to-purple-500/20 relative flex items-center justify-center overflow-hidden mb-4 rounded-xl">
                      <Package className="w-12 h-12 text-white/60 relative z-10" />
                      {/* تأثير الهولوجرام */}
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -skew-x-12 translate-x-full group-hover:translate-x-[-200%] transition-transform duration-1000"></div>
                    </div>

                    {/* محتوى البطاقة مضغوط */}
                    <div className="p-4">
                      {/* اسم المنتج */}
                      <div className="mb-3">
                        <h3 className="text-lg font-bold text-white mb-1 group-hover:text-blue-300 transition-colors line-clamp-1">
                          {product.name}
                        </h3>
                        <p className="text-blue-200 text-xs line-clamp-1">{categories.find(cat => cat.id === product.category)?.name || 'غير محدد'}</p>
                      </div>

                      {/* السعر والمخزون */}
                      <div className="grid grid-cols-2 gap-2 mb-3">
                        <div className="bg-white/5 rounded-lg p-2 text-center">
                          <p className="text-blue-200 text-xs mb-1">السعر</p>
                          <p className="text-green-400 font-bold text-sm">{product.price.toFixed(2)} دج</p>
                        </div>
                        <div className="bg-white/5 rounded-lg p-2 text-center">
                          <p className="text-blue-200 text-xs mb-1">المخزون</p>
                          <p className="text-white font-bold text-sm">{product.stock}</p>
                        </div>
                      </div>

                      {/* الباركود */}
                      <div className="mb-3">
                        <div className="bg-yellow-500/20 border-2 border-yellow-400/50 rounded-lg p-2">
                          <div className="text-center">
                            <p className="text-yellow-300 text-xs font-bold mb-1">🏷️ الباركود</p>
                            <p className="text-black font-mono text-sm bg-yellow-200 px-3 py-1 rounded font-bold tracking-widest border border-yellow-400">
                              {product.barcode}
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* زر الإضافة مضغوط */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          addToCart(product);
                        }}
                        className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 py-2 rounded-lg flex items-center justify-center space-x-2 space-x-reverse transition-all duration-300 hover:scale-105"
                      >
                        <Plus className="w-4 h-4" />
                        <span className="text-sm">إضافة</span>
                      </button>
                    </div>

                    {/* تأثير الإضاءة */}
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/0 via-purple-500/5 to-pink-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <div className="relative">
                  <Search className="w-20 h-20 text-slate-600 mx-auto mb-6 animate-pulse" />
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-xl"></div>
                </div>
                <h3 className="text-xl font-bold text-slate-300 mb-2">لم يتم العثور على منتجات</h3>
                <p className="text-slate-400 mb-4">جرب البحث بكلمات مختلفة أو تغيير الفئة</p>
                <button
                  onClick={() => {
                    handleSearchChange('');
                    handleCategoryChange('الكل');
                  }}
                  className="bg-gradient-to-r from-blue-500 to-purple-500 px-6 py-2 rounded-xl text-white font-medium hover:scale-105 transition-transform duration-300"
                >
                  إعادة تعيين البحث
                </button>
              </div>
            )}
            </div>
          </div>
        </div>

        {/* Right Panel - Cart */}
        <div className="w-96 bg-white/5 backdrop-blur-xl border-l border-white/10 flex flex-col">
          {/* Cart Header */}
          <div className="p-6 border-b border-white/10">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-white flex items-center">
                <ShoppingCart className="w-8 h-8 ml-3 text-blue-400 animate-bounce" />
                سلة المشتريات الذكية
              </h2>
              {cart.length > 0 && (
                <button
                  onClick={clearCart}
                  className="text-red-400 hover:text-red-300 p-2 hover:bg-white/10 rounded-xl transition-all duration-300 hover:scale-110"
                >
                  <Trash2 className="w-6 h-6" />
                </button>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="bg-white/10 rounded-xl p-4 border border-white/20">
                <div className="flex items-center space-x-reverse space-x-3">
                  <Package className="w-6 h-6 text-blue-400" />
                  <div>
                    <p className="text-slate-400 text-sm">عدد الأصناف</p>
                    <p className="text-white font-bold text-lg">{cart.length}</p>
                  </div>
                </div>
              </div>
              <div className="bg-white/10 rounded-xl p-4 border border-white/20">
                <div className="flex items-center space-x-reverse space-x-3">
                  <Calculator className="w-6 h-6 text-green-400" />
                  <div>
                    <p className="text-slate-400 text-sm">إجمالي القطع</p>
                    <p className="text-white font-bold text-lg">{cart.reduce((sum, item) => sum + item.quantity, 0)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Cart Items */}
          <div className="flex-1 overflow-y-auto p-6">
            {cart.length > 0 ? (
              <div className="space-y-4">
                {cart.map((item) => (
                  <div key={item.id} className="group bg-white/5 backdrop-blur-lg rounded-xl p-3 border border-white/10 hover:border-blue-400/50 transition-all duration-300">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-bold text-white text-xs group-hover:text-blue-300 transition-colors truncate flex-1 ml-2">
                        {item.productName}
                      </h4>
                      <button
                        onClick={() => removeFromCart(item.productId)}
                        className="text-red-400 hover:text-red-300 p-1 hover:bg-white/10 rounded-lg transition-all duration-300 hover:scale-110 flex-shrink-0"
                      >
                        <Trash2 className="w-3 h-3" />
                      </button>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-reverse space-x-2">
                        <button
                          onClick={() => updateCartQuantity(item.productId, item.quantity - 1)}
                          className="bg-gradient-to-r from-red-500 to-pink-500 hover:scale-110 p-1.5 rounded-lg transition-all duration-300 shadow-lg"
                        >
                          <Minus className="w-3 h-3 text-white" />
                        </button>
                        <div className="bg-white/10 backdrop-blur-lg px-3 py-1 rounded-lg border border-white/20 min-w-[3rem] text-center">
                          <span className="font-bold text-white text-sm">{item.quantity}</span>
                        </div>
                        <button
                          onClick={() => updateCartQuantity(item.productId, item.quantity + 1)}
                          className="bg-gradient-to-r from-green-500 to-emerald-500 hover:scale-110 p-1.5 rounded-lg transition-all duration-300 shadow-lg"
                        >
                          <Plus className="w-3 h-3 text-white" />
                        </button>
                      </div>

                      <div className="text-left">
                        <p className="text-xs text-slate-400">{item.unitPrice.toFixed(2)} × {item.quantity}</p>
                        <p className="font-bold text-green-400 text-sm">{item.totalPrice.toFixed(2)} دج</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <div className="relative mb-6">
                  <ShoppingCart className="w-20 h-20 text-slate-600 mx-auto animate-pulse" />
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-xl"></div>
                </div>
                <h3 className="text-xl font-bold text-slate-300 mb-2">السلة فارغة</h3>
                <p className="text-slate-400 mb-4">أضف منتجات لبدء عملية البيع</p>
                <div className="flex items-center justify-center space-x-reverse space-x-2 text-slate-500">
                  <Sparkles className="w-4 h-4" />
                  <span className="text-sm">اختر من المنتجات المتاحة</span>
                </div>
              </div>
            )}
          </div>

          {/* Cart Footer */}
          {cart.length > 0 && (
            <div className="p-6 border-t border-white/10 space-y-6">
              {/* Calculations */}
              <div className="bg-white/5 backdrop-blur-lg rounded-xl p-4 border border-white/10 space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-slate-300 flex items-center text-sm">
                    <Calculator className="w-3 h-3 ml-1" />
                    المجموع الفرعي:
                  </span>
                  <span className="font-bold text-white">{cartTotal.toFixed(2)} دج</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-300 flex items-center text-sm">
                    <Tag className="w-3 h-3 ml-1" />
                    الخصم:
                  </span>
                  <span className="font-bold text-red-400">-{discountAmount.toFixed(2)} دج</span>
                </div>
                <hr className="border-white/20 my-2" />
                <div className="flex items-center justify-between">
                  <span className="text-lg font-bold text-white flex items-center">
                    <Award className="w-5 h-5 ml-1 text-green-400" />
                    الإجمالي النهائي:
                  </span>
                  <span className="text-xl font-bold text-green-400">{finalTotal.toFixed(2)} دج</span>
                </div>
              </div>

              {!showPayment ? (
                <button
                  onClick={handleCheckout}
                  className="w-full bg-gradient-to-r from-green-500 to-emerald-400 hover:scale-105 transform transition-all duration-300 py-3 rounded-xl font-bold shadow-2xl hover:shadow-green-500/25 flex items-center justify-center space-x-reverse space-x-3 group"
                >
                  <CreditCard className="w-5 h-5 text-white" />
                  <div className="flex flex-col items-center">
                    <span className="text-white">إتمام عملية البيع</span>
                    <span className="text-xs text-green-100 opacity-75 group-hover:opacity-100 transition-opacity">
                      أو اضغط Enter
                    </span>
                  </div>
                  <Sparkles className="w-4 h-4 text-white animate-pulse" />
                </button>
              ) : (
                <div className="space-y-6">
                  {/* Payment Methods */}
                  <div>
                    <label className="block text-lg font-bold text-white mb-4 flex items-center">
                      <CreditCard className="w-5 h-5 ml-2" />
                      طريقة الدفع
                    </label>
                    <div className="grid grid-cols-2 gap-2 mb-3">
                      <button
                        onClick={() => setPaymentDetails(prev => ({ ...prev, type: 'cash' }))}
                        className={`p-3 rounded-lg flex items-center justify-center space-x-reverse space-x-2 transition-all duration-300 ${
                          paymentDetails.type === 'cash'
                            ? 'bg-gradient-to-r from-green-500 to-emerald-400 text-white scale-105 shadow-lg'
                            : 'bg-white/10 text-slate-300 hover:bg-white/20'
                        }`}
                      >
                        <Banknote className="w-4 h-4" />
                        <span className="text-sm font-medium">نقداً</span>
                      </button>
                      <button
                        onClick={() => setPaymentDetails(prev => ({ ...prev, type: 'credit' }))}
                        className={`p-3 rounded-lg flex items-center justify-center space-x-reverse space-x-2 transition-all duration-300 ${
                          paymentDetails.type === 'credit'
                            ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white scale-105 shadow-lg'
                            : 'bg-white/10 text-slate-300 hover:bg-white/20'
                        }`}
                      >
                        <CreditCard className="w-4 h-4" />
                        <span className="text-sm font-medium">آجل</span>
                      </button>
                    </div>

                    {/* زر الدفع بالرصيد - يظهر فقط إذا كان العميل لديه رصيد */}
                    {canPayWithBalance && (
                      <div className="mb-3">
                        <button
                          onClick={() => setPaymentDetails(prev => ({ ...prev, type: 'balance' }))}
                          className={`w-full p-3 rounded-lg flex items-center justify-between transition-all duration-300 ${
                            paymentDetails.type === 'balance'
                              ? 'bg-gradient-to-r from-yellow-500 to-orange-400 text-white scale-105 shadow-lg'
                              : 'bg-white/10 text-slate-300 hover:bg-white/20'
                          }`}
                        >
                          <div className="flex items-center space-x-reverse space-x-2">
                            <div className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full flex items-center justify-center">
                              <Wallet className="w-4 h-4 text-white" />
                            </div>
                            <div className="text-right">
                              <div className="font-bold text-sm">دفع بالرصيد</div>
                              <div className="text-xs opacity-80">
                                رصيد: {customerBalance.toFixed(2)} دج
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-xs opacity-80">يمكن دفع</div>
                            <div className="font-bold text-sm">{maxBalancePayment.toFixed(2)} دج</div>
                          </div>
                        </button>
                      </div>
                    )}

                    <div className="grid grid-cols-1 gap-2">
                      <button
                        onClick={() => setPaymentDetails(prev => ({ ...prev, type: 'mixed' }))}
                        className={`p-3 rounded-lg flex items-center justify-center space-x-reverse space-x-2 transition-all duration-300 ${
                          paymentDetails.type === 'mixed'
                            ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white scale-105 shadow-lg'
                            : 'bg-white/10 text-slate-300 hover:bg-white/20'
                        }`}
                      >
                        <Calculator className="w-4 h-4" />
                        <span className="text-sm font-medium">دفع مختلط</span>
                      </button>
                    </div>
                  </div>

                  {/* Payment Input */}
                  {paymentDetails.type === 'cash' && (
                    <div className="space-y-3">
                      {/* زر الدفع بالمبلغ الدقيق */}
                      <button
                        onClick={() => {
                          setReceivedAmount(finalTotal.toString());
                          updatePaymentAmount(finalTotal);
                        }}
                        className="w-full bg-gradient-to-r from-green-500 to-emerald-400 hover:scale-105 transform transition-all duration-300 py-2 rounded-lg font-bold text-white shadow-lg flex items-center justify-center space-x-reverse space-x-2"
                      >
                        <Check className="w-4 h-4" />
                        <span className="text-sm">دفع المبلغ الدقيق ({finalTotal.toFixed(2)} دج)</span>
                        <Target className="w-4 h-4" />
                      </button>

                      <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                          <div className="w-full border-t border-white/20"></div>
                        </div>
                        <div className="relative flex justify-center text-xs">
                          <span className="bg-slate-900 px-2 text-slate-400">أو أدخل مبلغ مختلف</span>
                        </div>
                      </div>

                      <div>
                        <label className="block text-xs font-medium text-slate-300 mb-2 flex items-center">
                          <Banknote className="w-3 h-3 ml-1" />
                          المبلغ المستلم (اختياري)
                        </label>
                        <input
                          type="number"
                          step="0.01"
                          value={receivedAmount}
                          onChange={(e) => {
                            setReceivedAmount(e.target.value);
                            updatePaymentAmount(parseFloat(e.target.value) || 0);
                          }}
                          placeholder={`${finalTotal.toFixed(2)} (المبلغ الدقيق)`}
                          className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white font-bold placeholder-slate-400 focus:outline-none focus:border-green-400 transition-colors"
                        />
                        {receivedAmount && parseFloat(receivedAmount) !== finalTotal && (
                          <div className="mt-2 p-2 bg-white/10 rounded-lg border border-white/20">
                            <div className="flex justify-between items-center">
                              <span className="text-slate-300 flex items-center text-xs">
                                <Calculator className="w-3 h-3 ml-1" />
                                الباقي:
                              </span>
                              <span className={`font-bold ${change >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                                {change.toFixed(2)} دج
                              </span>
                            </div>
                          </div>
                        )}

                        {/* مساعدات سريعة للمبالغ */}
                        <div className="mt-2 grid grid-cols-3 gap-1">
                          {[
                            Math.ceil(finalTotal / 10) * 10, // أقرب عشرة
                            Math.ceil(finalTotal / 50) * 50, // أقرب خمسين
                            Math.ceil(finalTotal / 100) * 100 // أقرب مئة
                          ].filter(amount => amount > finalTotal).slice(0, 3).map((amount, index) => (
                            <button
                              key={`amount-${amount}-${index}`}
                              onClick={() => {
                                setReceivedAmount(amount.toString());
                                updatePaymentAmount(amount);
                              }}
                              className="bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg py-1 px-2 text-white text-xs font-medium transition-colors"
                            >
                              {amount.toFixed(0)} دج
                            </button>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Balance Payment - Simple confirmation */}
                  {paymentDetails.type === 'balance' && (
                    <div className="bg-gradient-to-r from-yellow-500/20 to-orange-400/20 border border-yellow-400/30 rounded-lg p-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-reverse space-x-2">
                          <Wallet className="w-4 h-4 text-yellow-400" />
                          <span className="text-white text-sm font-medium">الدفع بالرصيد</span>
                        </div>
                        <div className="text-right">
                          <div className="text-yellow-400 font-bold text-sm">{finalTotal.toFixed(2)} دج</div>
                          <div className="text-xs text-yellow-200">
                            الرصيد بعد الدفع: {(customerBalance - finalTotal).toFixed(2)} دج
                          </div>
                        </div>
                      </div>
                      {customerBalance < finalTotal && (
                        <div className="mt-2 text-red-200 text-xs flex items-center">
                          <X className="w-3 h-3 ml-1" />
                          رصيد غير كافي
                        </div>
                      )}
                    </div>
                  )}

                  {/* Mixed Payment */}
                  {paymentDetails.type === 'mixed' && (
                    <div className="bg-gradient-to-r from-purple-500/20 to-pink-400/20 border border-purple-400/30 rounded-lg p-4">
                      <h3 className="text-sm font-bold text-white mb-3 flex items-center">
                        <Calculator className="w-4 h-4 ml-1" />
                        الدفع المختلط
                      </h3>

                      <div className="space-y-3">
                        {/* Cash Amount */}
                        <div>
                          <label className="block text-xs font-medium text-slate-300 mb-1">المبلغ النقدي</label>
                          <input
                            type="number"
                            step="0.01"
                            value={paymentDetails.cashAmount}
                            onChange={(e) => setPaymentDetails(prev => ({
                              ...prev,
                              cashAmount: parseFloat(e.target.value) || 0,
                              totalPaid: (parseFloat(e.target.value) || 0) + prev.balanceAmount
                            }))}
                            className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white text-sm"
                          />
                        </div>

                        {/* Balance Amount */}
                        {canPayWithBalance && (
                          <div>
                            <label className="block text-xs font-medium text-slate-300 mb-1">
                              من الرصيد (حد أقصى: {maxBalancePayment.toFixed(2)} دج)
                            </label>
                            <input
                              type="number"
                              step="0.01"
                              max={maxBalancePayment}
                              value={paymentDetails.balanceAmount}
                              onChange={(e) => {
                                const amount = Math.min(parseFloat(e.target.value) || 0, maxBalancePayment);
                                setPaymentDetails(prev => ({
                                  ...prev,
                                  balanceAmount: amount,
                                  totalPaid: prev.cashAmount + amount
                                }));
                              }}
                              className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white text-sm"
                            />
                          </div>
                        )}

                        <div className="bg-white/10 rounded-lg p-2">
                          <div className="flex justify-between items-center text-xs">
                            <span className="text-slate-300">إجمالي المدفوع:</span>
                            <span className="font-bold text-white">
                              {(paymentDetails.cashAmount + paymentDetails.balanceAmount).toFixed(2)} دج
                            </span>
                          </div>
                          <div className="flex justify-between items-center mt-1 text-xs">
                            <span className="text-slate-300">المتبقي:</span>
                            <span className={`font-bold ${
                              (finalTotal - paymentDetails.cashAmount - paymentDetails.balanceAmount) <= 0
                                ? 'text-green-400' : 'text-red-400'
                            }`}>
                              {Math.max(0, finalTotal - paymentDetails.cashAmount - paymentDetails.balanceAmount).toFixed(2)} دج
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="grid grid-cols-2 gap-3">
                    <button
                      onClick={() => setShowPayment(false)}
                      className="bg-gradient-to-r from-red-500 to-pink-500 hover:scale-105 py-3 rounded-lg transition-all duration-300 font-medium flex items-center justify-center space-x-reverse space-x-2 group"
                    >
                      <X className="w-4 h-4 text-white" />
                      <div className="flex flex-col items-center">
                        <span className="text-white text-sm">إلغاء</span>
                        <span className="text-xs text-pink-100 opacity-75 group-hover:opacity-100 transition-opacity">
                          أو Escape
                        </span>
                      </div>
                    </button>
                    <button
                      onClick={handlePayment}
                      disabled={paymentDetails.type === 'cash' && receivedAmount !== '' && change < 0}
                      className="bg-gradient-to-r from-green-500 to-emerald-400 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed py-3 rounded-lg transition-all duration-300 font-medium flex items-center justify-center space-x-reverse space-x-2 shadow-lg group"
                    >
                      <Check className="w-4 h-4 text-white" />
                      <div className="flex flex-col items-center">
                        <span className="text-white text-sm">
                          {paymentDetails.type === 'cash' && !receivedAmount
                            ? `تأكيد (${finalTotal.toFixed(0)} دج)`
                            : 'تأكيد الدفع'
                          }
                        </span>
                        <span className="text-xs text-green-100 opacity-75 group-hover:opacity-100 transition-opacity">
                          أو Enter
                        </span>
                      </div>
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Success Modal */}
      {lastSaleData && (
        <SaleSuccessModal
          isOpen={showSuccessModal}
          onClose={handleCloseSuccessModal}
          onOpenReceipt={handleOpenReceipt}
          saleData={lastSaleData}
        />
      )}

      {/* Receipt Modal */}
      {lastSaleData && (
        <ReceiptModal
          isOpen={showReceiptModal}
          onClose={handleCloseReceipt}
          saleData={lastSaleData}
          companyInfo={storeInfo}
        />
      )}

      {/* Thermal Receipt Component (Hidden) */}
      {lastSaleData && (
        <ThermalReceipt
          saleData={lastSaleData}
          companyInfo={storeInfo}
        />
      )}
    </div>
  );
};

export default SalesScreen;
