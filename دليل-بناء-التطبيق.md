# 🏗️ دليل بناء ملف التثبيت - كشير توسار

## 📋 المتطلبات الأساسية

### 1. البرامج المطلوبة:
- **Node.js** (الإصدار 16 أو أحدث) - [تحميل](https://nodejs.org/)
- **Git** (اختياري) - [تحميل](https://git-scm.com/)
- **PostgreSQL** (للاختبار) - [تحميل](https://www.postgresql.org/)

### 2. أدوات البناء:
```bash
npm install -g electron
npm install -g electron-builder
```

## 🚀 خطوات بناء ملف التثبيت

### الطريقة السريعة (مستحسنة):
```bash
# تشغيل ملف البناء التلقائي
build-exe.bat
```

### الطريقة اليدوية:

#### 1. تحضير المشروع
```bash
# بناء الواجهة الأمامية
npm run build

# تثبيت Electron
npm install electron electron-builder --save-dev
```

#### 2. إعداد ملفات Electron
```bash
# نسخ ملفات الإعداد
copy electron-main.js main.js
copy electron-package.json package.json
```

#### 3. إنشاء الأيقونات
- ضع ملف `icon.ico` في مجلد `assets/`
- الحجم المطلوب: 256x256 بكسل
- تنسيقات مدعومة: .ico, .png, .icns

#### 4. بناء التطبيق
```bash
# بناء ملف .exe
npx electron-builder --win nsis

# بناء ملف .msi  
npx electron-builder --win msi

# بناء كلاهما
npx electron-builder --win
```

## 📁 هيكل الملفات المطلوبة

```
project/
├── 📱 Frontend Files
│   ├── dist/                 # الواجهة المبنية
│   ├── src/                  # الكود المصدري
│   └── package.json
├── 🔧 Backend Files  
│   ├── backend/
│   │   ├── server.js
│   │   ├── database.js
│   │   ├── routes/
│   │   └── package.json
├── 🖥️ Electron Files
│   ├── electron-main.js      # العملية الرئيسية
│   ├── electron-package.json # إعدادات Electron
│   └── main.js              # نقطة الدخول
├── 🎨 Assets
│   ├── icon.ico             # أيقونة Windows
│   ├── icon.png             # أيقونة Linux
│   └── icon.icns            # أيقونة macOS
├── 🏗️ Build Files
│   ├── build-exe.bat        # ملف البناء
│   ├── installer.nsh        # إعدادات NSIS
│   └── LICENSE.txt          # رخصة الاستخدام
└── 📄 Documentation
    └── دليل-بناء-التطبيق.md
```

## ⚙️ إعدادات متقدمة

### 1. تخصيص المثبت (NSIS):
```javascript
"nsis": {
  "oneClick": false,
  "allowToChangeInstallationDirectory": true,
  "createDesktopShortcut": true,
  "createStartMenuShortcut": true,
  "shortcutName": "كشير توسار",
  "language": "1025", // Arabic
  "license": "LICENSE.txt"
}
```

### 2. تخصيص MSI:
```javascript
"msi": {
  "oneClick": false,
  "upgradeCode": "{GUID}",
  "shortcutName": "كشير توسار"
}
```

### 3. إعدادات الأمان:
```javascript
"win": {
  "requestedExecutionLevel": "asInvoker",
  "certificateFile": "certificate.p12",
  "certificatePassword": "password"
}
```

## 🎯 أنواع ملفات التثبيت

### 1. NSIS Installer (.exe):
- **المميزات**: مرونة عالية، دعم كامل للعربية
- **الحجم**: صغير نسبياً
- **التخصيص**: إمكانيات تخصيص واسعة

### 2. MSI Installer (.msi):
- **المميزات**: دعم Windows Installer، سهولة النشر
- **الإدارة**: دعم Group Policy
- **التحديث**: نظام تحديث متقدم

### 3. Portable (.zip):
- **المميزات**: لا يحتاج تثبيت
- **الاستخدام**: مناسب للاختبار
- **الحجم**: أكبر حجماً

## 🔧 حل المشاكل الشائعة

### 1. خطأ في بناء Electron:
```bash
# حذف node_modules وإعادة التثبيت
rmdir /s node_modules
npm install
```

### 2. مشكلة في الأيقونات:
- تأكد من وجود ملف `icon.ico` في مجلد `assets/`
- استخدم أداة تحويل الأيقونات مثل [ConvertICO](https://convertio.co/png-ico/)

### 3. خطأ في التوقيع الرقمي:
```bash
# تعطيل التوقيع مؤقتاً للاختبار
"win": {
  "sign": false
}
```

### 4. مشكلة في حجم الملف:
```bash
# تحسين الحجم
"compression": "maximum",
"nsis": {
  "differentialPackage": false
}
```

## 📦 التوزيع والنشر

### 1. اختبار ملف التثبيت:
- اختبر على أجهزة مختلفة
- تأكد من عمل جميع الميزات
- اختبر عملية إلغاء التثبيت

### 2. التوقيع الرقمي:
```bash
# الحصول على شهادة رقمية
# توقيع الملف لزيادة الثقة
```

### 3. النشر:
- رفع على موقع الشركة
- توزيع عبر متاجر التطبيقات
- إرسال للعملاء مباشرة

## 🎉 النتيجة النهائية

بعد اتباع هذه الخطوات، ستحصل على:

- ✅ ملف `كشير-توسار-1.0.0.exe` (NSIS Installer)
- ✅ ملف `كشير-توسار-1.0.0.msi` (Windows Installer)
- ✅ مثبت باللغة العربية
- ✅ اختصارات على سطح المكتب وقائمة ابدأ
- ✅ إمكانية إلغاء التثبيت
- ✅ دعم التحديثات التلقائية

## 📞 الدعم

للمساعدة في بناء التطبيق:
- 📧 البريد الإلكتروني: <EMAIL>
- 🌐 الموقع: https://toosar.com
- 📱 الهاتف: +966-XX-XXX-XXXX
