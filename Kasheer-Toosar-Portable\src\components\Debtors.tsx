import React, { useState, useEffect } from 'react';
import {
  CreditCard,
  Search,
  AlertTriangle,
  TrendingDown,
  DollarSign,
  Phone,
  MapPin,
  Download,
  RefreshCw,
  Eye,
  MessageSquare,
  Bell,
  Calendar,
  Clock,
  Users,
  Wallet,
  XCircle,
  CheckCircle,
  Send,
  FileText,
  Calculator,
  Target,
  Zap,
  Trash2
} from 'lucide-react';
import apiService from '../services/api';

// تعريف الواجهات
interface Customer {
  id: string;
  name: string;
  phone?: string;
  email?: string;
  address?: string;
  balance: number;
  credit_limit?: number;
}

interface CustomerDebt {
  id: number;
  customer_id: string;
  customer?: Customer;
  sale_id: number;
  amount: number;
  debt_date: string;
  paid_amount: number;
  remaining_amount: number;
  status: 'pending' | 'partial' | 'paid';
  notes?: string;
  created_at: string;
  updated_at: string;
}

interface DebtorCustomer {
  id: string;
  name: string;
  phone: string;
  email?: string;
  address?: string;
  balance: number;
  credit_limit?: number;
  debts: CustomerDebt[];
  total_debt: number;
  paid_amount: number;
  remaining_debt: number;
  debt_count: number;
  created_at?: string;
  updated_at: string | Date;
  uniqueKey?: string;
  riskLevel?: 'low' | 'medium' | 'high' | 'critical';
}

interface PaymentRecord {
  id: string;
  date: Date;
  amount: number;
  method: 'cash' | 'transfer' | 'check';
  description: string;
}

const Debtors: React.FC = () => {
  const [debtors, setDebtors] = useState<DebtorCustomer[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDebtor, setSelectedDebtor] = useState<DebtorCustomer | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showReminderModal, setShowReminderModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteInfo, setDeleteInfo] = useState<any>(null);
  const [filterRisk, setFilterRisk] = useState<'all' | 'low' | 'medium' | 'high' | 'critical'>('all');
  const [sortBy, setSortBy] = useState<'name' | 'debt' | 'lastPurchase' | 'risk'>('debt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [loading, setLoading] = useState(true);

  // تحميل المدينون من API
  const loadDebtors = async () => {
    try {
      setLoading(true);
      console.log('🔄 تحميل العملاء المدينين...');

      const response = await apiService.getDebtorCustomers();
      console.log('📋 استجابة العملاء المدينين:', response);

      if (!Array.isArray(response) || response.length === 0) {
        console.log('ℹ️ لا توجد ديون معلقة حالياً');
        setDebtors([]);
        return;
      }

      // تحويل البيانات إلى تنسيق DebtorCustomer
      const debtorsWithRisk = response.map(debtor => ({
        id: debtor.id,
        name: debtor.name,
        phone: debtor.phone || '',
        email: debtor.email || '',
        address: debtor.address || '',
        balance: parseFloat(debtor.balance) || 0,
        credit_limit: parseFloat(debtor.credit_limit) || 0,
        total_debt: parseFloat(debtor.total_debt) || 0,
        paid_amount: parseFloat(debtor.paid_amount) || 0,
        remaining_debt: parseFloat(debtor.remaining_debt) || 0,
        debt_count: parseInt(debtor.debt_count) || 1,
        created_at: debtor.created_at,
        updated_at: debtor.updated_at,
        debts: [{
          id: `debt-${debtor.id}`,
          customer_id: debtor.id,
          sale_id: `sale-${debtor.id}`,
          amount: parseFloat(debtor.total_debt) || 0,
          paid_amount: parseFloat(debtor.paid_amount) || 0,
          remaining_amount: parseFloat(debtor.remaining_debt) || 0,
          status: 'pending',
          debt_date: new Date().toISOString(),
          due_date: null,
          notes: '',
          created_at: debtor.created_at,
          updated_at: debtor.updated_at
        }],
        riskLevel: calculateRiskLevel(parseFloat(debtor.remaining_debt) || 0, parseFloat(debtor.balance) || 0)
      }));

      console.log(`✅ تم تحميل ${debtorsWithRisk.length} عميل مدين`);
      console.log('📊 تفاصيل العملاء المدينين:', debtorsWithRisk.map(d => ({
        name: d.name,
        remaining_debt: d.remaining_debt,
        paid_amount: d.paid_amount,
        debt_count: d.debt_count
      })));

      setDebtors(debtorsWithRisk as any);
    } catch (error) {
      console.error('❌ خطأ في تحميل المدينون:', error);
      setDebtors([]);
    } finally {
      setLoading(false);
    }
  };

  // حساب مستوى المخاطر
  const calculateRiskLevel = (debt: number, balance: number): 'low' | 'medium' | 'high' | 'critical' => {
    if (debt <= 0) return 'low';
    if (debt < 1000) return 'low';
    if (debt < 5000) return 'medium';
    if (debt < 10000) return 'high';
    return 'critical';
  };

  useEffect(() => {
    loadDebtors();
  }, []);

  // 🗑️ دالة حذف فاتورة دين العميل
  const handleDeleteDebtor = async (debtor: DebtorCustomer) => {
    try {
      console.log(`🗑️ طلب حذف فاتورة دين للعميل: ${debtor.name}`);

      // الحصول على معلومات الحذف أولاً
      const deleteResponse = await apiService.deleteCustomerDebtInvoice(debtor.id, false);

      if (deleteResponse.canDelete) {
        setDeleteInfo(deleteResponse.deleteInfo);
        setSelectedDebtor(debtor);
        setShowDeleteModal(true);
      } else {
        alert('لا يمكن حذف هذا العميل: ' + deleteResponse.error);
      }
    } catch (error) {
      console.error('❌ خطأ في طلب حذف العميل:', error);
      alert('حدث خطأ في طلب حذف العميل');
    }
  };

  // 🗑️ تأكيد الحذف
  const confirmDelete = async () => {
    if (!selectedDebtor) return;

    try {
      console.log(`🗑️ تأكيد حذف فاتورة دين للعميل: ${selectedDebtor.name}`);

      const deleteResponse = await apiService.deleteCustomerDebtInvoice(selectedDebtor.id, true);

      if (deleteResponse.success) {
        console.log('✅ تم حذف فاتورة الدين بنجاح');

        // إزالة العميل من القائمة
        setDebtors(debtors.filter(d => d.id !== selectedDebtor.id));

        // إغلاق المودال
        setShowDeleteModal(false);
        setSelectedDebtor(null);
        setDeleteInfo(null);

        // إعادة تحميل البيانات
        loadDebtors();

        alert(`تم حذف فاتورة دين العميل "${selectedDebtor.name}" بنجاح`);
      } else {
        alert('فشل في حذف فاتورة الدين: ' + deleteResponse.error);
      }
    } catch (error) {
      console.error('❌ خطأ في حذف فاتورة الدين:', error);
      alert('حدث خطأ في حذف فاتورة الدين');
    }
  };

  // إحصائيات المدينين
  const totalDebtors = debtors.length;
  const totalDebtAmount = debtors.reduce((sum, debtor) => sum + debtor.remaining_debt, 0);
  const averageDebt = totalDebtors > 0 ? totalDebtAmount / totalDebtors : 0;
  const criticalDebtors = debtors.filter(d => d.riskLevel === 'critical').length;
  const highRiskDebtors = debtors.filter(d => d.riskLevel === 'high').length;

  // فلترة وترتيب المدينين
  const filteredDebtors = debtors
    .filter(debtor => {
      const matchesSearch = debtor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           debtor.phone.includes(searchTerm) ||
                           (debtor.address && debtor.address.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesFilter = filterRisk === 'all' || debtor.riskLevel === filterRisk;

      return matchesSearch && matchesFilter;
    })
    .sort((a, b) => {
      let comparison = 0;
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'debt':
          comparison = a.remaining_debt - b.remaining_debt;
          break;
        case 'lastPurchase':
          comparison = new Date(a.updated_at).getTime() - new Date(b.updated_at).getTime();
          break;
        case 'risk':
          const riskOrder = { low: 1, medium: 2, high: 3, critical: 4 };
          comparison = (riskOrder[a.riskLevel || 'low'] || 1) - (riskOrder[b.riskLevel || 'low'] || 1);
          break;
      }
      return sortOrder === 'asc' ? comparison : -comparison;
    });

  const formatCurrency = (amount: number | string | undefined): string => {
    if (amount === undefined) return '0.00 دج';
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return `${numAmount.toFixed(2)} دج`;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('fr-FR');
  };

  const getRiskColor = (risk?: string) => {
    switch (risk) {
      case 'low': return 'text-green-400';
      case 'medium': return 'text-yellow-400';
      case 'high': return 'text-orange-400';
      case 'critical': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getRiskBgColor = (risk?: string) => {
    switch (risk) {
      case 'low': return 'bg-green-500/20 border-green-500/30';
      case 'medium': return 'bg-yellow-500/20 border-yellow-500/30';
      case 'high': return 'bg-orange-500/20 border-orange-500/30';
      case 'critical': return 'bg-red-500/20 border-red-500/30';
      default: return 'bg-gray-500/20 border-gray-500/30';
    }
  };

  const getRiskLabel = (risk?: string) => {
    switch (risk) {
      case 'low': return 'منخفض';
      case 'medium': return 'متوسط';
      case 'high': return 'عالي';
      case 'critical': return 'حرج';
      default: return 'غير محدد';
    }
  };

  const getRiskIcon = (risk?: string) => {
    switch (risk) {
      case 'low': return <CheckCircle className="w-4 h-4" />;
      case 'medium': return <Clock className="w-4 h-4" />;
      case 'high': return <AlertTriangle className="w-4 h-4" />;
      case 'critical': return <XCircle className="w-4 h-4" />;
      default: return <DollarSign className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-red-900 to-slate-900 p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="bg-gradient-to-r from-red-500 to-orange-600 p-3 rounded-2xl shadow-lg">
              <CreditCard className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">العملاء المدينون</h1>
              <p className="text-gray-300">إدارة ومتابعة ديون العملاء والمستحقات</p>
            </div>
          </div>

          <div className="flex items-center space-x-4 space-x-reverse">
            <button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:scale-105 transform transition-all duration-300 px-6 py-3 rounded-xl font-bold text-white shadow-2xl hover:shadow-blue-500/25 flex items-center space-x-2 space-x-reverse">
              <Send className="w-5 h-5" />
              <span>إرسال تذكير جماعي</span>
            </button>

            <button className="bg-white/10 backdrop-blur-lg border border-white/20 hover:border-white/40 px-4 py-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse text-white">
              <Download className="w-5 h-5" />
              <span>تصدير</span>
            </button>

            <button
              onClick={loadDebtors}
              disabled={loading}
              className="bg-white/10 backdrop-blur-lg border border-white/20 hover:border-white/40 px-4 py-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse text-white disabled:opacity-50"
            >
              <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
              <span>تحديث</span>
            </button>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">إجمالي المدينين</p>
                <p className="text-3xl font-bold text-white">{totalDebtors}</p>
              </div>
              <div className="bg-red-500/20 p-3 rounded-xl">
                <Users className="w-6 h-6 text-red-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">إجمالي الديون</p>
                <p className="text-3xl font-bold text-red-400">
                  {formatCurrency(totalDebtAmount)}
                </p>
              </div>
              <div className="bg-red-500/20 p-3 rounded-xl">
                <TrendingDown className="w-6 h-6 text-red-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">متوسط الدين</p>
                <p className="text-3xl font-bold text-orange-400">
                  {formatCurrency(averageDebt)}
                </p>
              </div>
              <div className="bg-orange-500/20 p-3 rounded-xl">
                <Calculator className="w-6 h-6 text-orange-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">حالات حرجة</p>
                <p className="text-3xl font-bold text-red-400">{criticalDebtors}</p>
              </div>
              <div className="bg-red-500/20 p-3 rounded-xl">
                <AlertTriangle className="w-6 h-6 text-red-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">مخاطر عالية</p>
                <p className="text-3xl font-bold text-orange-400">{highRiskDebtors}</p>
              </div>
              <div className="bg-orange-500/20 p-3 rounded-xl">
                <Target className="w-6 h-6 text-orange-400" />
              </div>
            </div>
          </div>
        </div>

        {/* شريط البحث والفلاتر */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 mb-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
            {/* البحث */}
            <div className="lg:col-span-2">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="البحث بالاسم، الهاتف، أو العنوان..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full bg-white/10 border border-white/20 rounded-xl px-12 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-red-400 transition-all duration-300"
                />
              </div>
            </div>

            {/* فلتر مستوى المخاطر */}
            <div>
              <select
                value={filterRisk}
                onChange={(e) => setFilterRisk(e.target.value as any)}
                className="w-full bg-red-900/30 border border-red-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-red-400 transition-all duration-300 cursor-pointer hover:bg-red-800/40"
                style={{
                  backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23f87171' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: 'left 12px center',
                  backgroundSize: '16px'
                }}
              >
                <option value="all" className="bg-red-900 text-white">جميع المستويات</option>
                <option value="critical" className="bg-red-900 text-white">حرج</option>
                <option value="high" className="bg-red-900 text-white">عالي</option>
                <option value="medium" className="bg-red-900 text-white">متوسط</option>
                <option value="low" className="bg-red-900 text-white">منخفض</option>
              </select>
            </div>

            {/* الترتيب */}
            <div>
              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [field, order] = e.target.value.split('-');
                  setSortBy(field as any);
                  setSortOrder(order as any);
                }}
                className="w-full bg-red-900/30 border border-red-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-red-400 transition-all duration-300 cursor-pointer hover:bg-red-800/40"
                style={{
                  backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23f87171' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: 'left 12px center',
                  backgroundSize: '16px'
                }}
              >
                <option value="debt-desc" className="bg-red-900 text-white">الدين (الأعلى)</option>
                <option value="debt-asc" className="bg-red-900 text-white">الدين (الأقل)</option>
                <option value="name-asc" className="bg-red-900 text-white">الاسم (أ-ي)</option>
                <option value="name-desc" className="bg-red-900 text-white">الاسم (ي-أ)</option>
                <option value="risk-desc" className="bg-red-900 text-white">المخاطر (الأعلى)</option>
                <option value="lastPurchase-desc" className="bg-red-900 text-white">آخر شراء</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* جدول المدينين */}
      <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-white/5">
              <tr>
                <th className="text-right py-4 px-6 text-gray-300 font-semibold">العميل</th>
                <th className="text-right py-4 px-6 text-gray-300 font-semibold">الهاتف</th>
                <th className="text-right py-4 px-6 text-gray-300 font-semibold">مبلغ الدين</th>
                <th className="text-right py-4 px-6 text-gray-300 font-semibold">آخر شراء</th>
                <th className="text-right py-4 px-6 text-gray-300 font-semibold">مستوى المخاطر</th>
                <th className="text-center py-4 px-6 text-gray-300 font-semibold">الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr>
                  <td colSpan={6} className="text-center py-12">
                    <div className="flex flex-col items-center space-y-4">
                      <RefreshCw className="w-16 h-16 text-blue-400 animate-spin" />
                      <p className="text-gray-400 text-lg">جاري تحميل المدينون...</p>
                    </div>
                  </td>
                </tr>
              ) : filteredDebtors.length === 0 ? (
                <tr>
                  <td colSpan={6} className="text-center py-12">
                    <div className="flex flex-col items-center space-y-4">
                      <CreditCard className="w-16 h-16 text-gray-400" />
                      <p className="text-gray-400 text-lg">لا توجد ديون مسجلة</p>
                      <p className="text-gray-500 text-sm">جميع العملاء مسددون أو لا توجد مبيعات آجلة</p>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredDebtors.map((debtor, index) => (
                  <tr key={debtor.uniqueKey || `debtor-${debtor.id}-${index}-${Math.random()}`} className="border-t border-white/10 hover:bg-white/5 transition-all duration-300">
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <div className="bg-gradient-to-r from-red-500 to-orange-600 w-10 h-10 rounded-full flex items-center justify-center">
                          <span className="text-white font-bold text-sm">
                            {debtor.name.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <p className="text-white font-semibold">{debtor.name}</p>
                          <p className="text-gray-400 text-sm">{debtor.debt_count} دين مسجل</p>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-2 space-x-reverse text-gray-300">
                        <Phone className="w-4 h-4" />
                        <span>{debtor.phone}</span>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <TrendingDown className="w-4 h-4 text-red-400" />
                        <span className="font-bold text-red-400 text-lg">
                          {formatCurrency(debtor.remaining_debt)}
                        </span>
                      </div>
                    </td>
                    <td className="py-4 px-6 text-gray-300">
                      {formatDate(new Date(debtor.updated_at))}
                    </td>
                    <td className="py-4 px-6">
                      <div className={`inline-flex items-center space-x-2 space-x-reverse px-3 py-1 rounded-full border ${getRiskBgColor(debtor.riskLevel)}`}>
                        {getRiskIcon(debtor.riskLevel)}
                        <span className={`text-sm font-semibold ${getRiskColor(debtor.riskLevel)}`}>
                          {getRiskLabel(debtor.riskLevel)}
                        </span>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center justify-center space-x-2 space-x-reverse">
                        <button
                          onClick={() => {
                            setSelectedDebtor(debtor);
                            setShowDetailsModal(true);
                          }}
                          className="bg-blue-500/20 hover:bg-blue-500/30 p-2 rounded-lg transition-all duration-300 hover:scale-110"
                          title="عرض التفاصيل"
                        >
                          <Eye className="w-4 h-4 text-blue-400" />
                        </button>

                        <button
                          onClick={() => {
                            setSelectedDebtor(debtor);
                            setShowPaymentModal(true);
                          }}
                          className="bg-green-500/20 hover:bg-green-500/30 p-2 rounded-lg transition-all duration-300 hover:scale-110"
                          title="تسجيل دفعة"
                        >
                          <Wallet className="w-4 h-4 text-green-400" />
                        </button>

                        <button
                          onClick={() => {
                            setSelectedDebtor(debtor);
                            setShowReminderModal(true);
                          }}
                          className="bg-yellow-500/20 hover:bg-yellow-500/30 p-2 rounded-lg transition-all duration-300 hover:scale-110"
                          title="إرسال تذكير"
                        >
                          <Bell className="w-4 h-4 text-yellow-400" />
                        </button>

                        <button
                          className="bg-purple-500/20 hover:bg-purple-500/30 p-2 rounded-lg transition-all duration-300 hover:scale-110"
                          title="كشف حساب"
                        >
                          <FileText className="w-4 h-4 text-purple-400" />
                        </button>

                        <button
                          onClick={() => handleDeleteDebtor(debtor)}
                          className="bg-red-500/20 hover:bg-red-500/30 p-2 rounded-lg transition-all duration-300 hover:scale-110"
                          title="حذف فاتورة الدين"
                        >
                          <Trash2 className="w-4 h-4 text-red-400" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* مودال تفاصيل المدين */}
      {showDetailsModal && selectedDebtor && (
        <DebtorDetailsModal
          debtor={selectedDebtor}
          onClose={() => {
            setShowDetailsModal(false);
            setSelectedDebtor(null);
          }}
        />
      )}

      {/* مودال تسجيل دفعة */}
      {showPaymentModal && selectedDebtor && (
        <PaymentModal
          debtor={selectedDebtor}
          onClose={() => {
            setShowPaymentModal(false);
            setSelectedDebtor(null);
          }}
          onPayment={(payment) => {
            console.log('💰 تم تسجيل دفعة بنجاح:', payment);

            // تحديث الدين بعد الدفعة محلياً
            const updatedDebtors = debtors.map(d => {
              if (d.id === selectedDebtor.id) {
                const newRemainingDebt = d.remaining_debt - payment.amount;
                const newPaidAmount = d.paid_amount + payment.amount;

                console.log(`📊 تحديث دين العميل ${d.name}:`, {
                  oldDebt: d.remaining_debt,
                  payment: payment.amount,
                  newDebt: newRemainingDebt,
                  newPaid: newPaidAmount
                });

                return {
                  ...d,
                  remaining_debt: newRemainingDebt,
                  paid_amount: newPaidAmount,
                  // إعادة حساب مستوى المخاطر
                  riskLevel: calculateRiskLevel(newRemainingDebt, d.balance || 0)
                };
              }
              return d;
            }).filter(d => d.remaining_debt > 0.01); // إزالة العملاء الذين سددوا كامل الدين

            setDebtors(updatedDebtors);
            setShowPaymentModal(false);
            setSelectedDebtor(null);

            // إعادة تحميل البيانات من الخادم للتأكد من التحديث
            setTimeout(() => {
              loadDebtors();
            }, 1000);
          }}
        />
      )}

      {/* مودال إرسال تذكير */}
      {showReminderModal && selectedDebtor && (
        <ReminderModal
          debtor={selectedDebtor}
          onClose={() => {
            setShowReminderModal(false);
            setSelectedDebtor(null);
          }}
          onSend={() => {
            // إرسال التذكير
            setShowReminderModal(false);
            setSelectedDebtor(null);
          }}
        />
      )}

      {/* مودال تأكيد الحذف */}
      {showDeleteModal && selectedDebtor && deleteInfo && (
        <DeleteConfirmationModal
          debtor={selectedDebtor}
          deleteInfo={deleteInfo}
          onClose={() => {
            setShowDeleteModal(false);
            setSelectedDebtor(null);
            setDeleteInfo(null);
          }}
          onConfirm={confirmDelete}
        />
      )}
    </div>
  );
};

// مودال تفاصيل المدين
const DebtorDetailsModal: React.FC<{
  debtor: DebtorCustomer;
  onClose: () => void;
}> = ({ debtor, onClose }) => {
  const formatCurrency = (amount: number | string | undefined): string => {
    if (amount === undefined) return '0.00 دج';
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return `${numAmount.toFixed(2)} دج`;
  };
  const formatDate = (date: Date) => date.toLocaleDateString('fr-FR');

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl border border-white/20 w-full max-w-4xl max-h-[90vh] shadow-2xl overflow-hidden">
        <div className="p-6 border-b border-white/10">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-white flex items-center space-x-3 space-x-reverse">
              <Eye className="w-6 h-6 text-red-400" />
              <span>تفاصيل المدين</span>
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors duration-300"
            >
              <XCircle className="w-6 h-6" />
            </button>
          </div>
        </div>

        <div className="overflow-y-auto max-h-[calc(90vh-120px)] p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* معلومات العميل */}
            <div className="space-y-4">
              <h3 className="text-xl font-bold text-white mb-4">معلومات العميل</h3>

              <div className="bg-white/5 rounded-xl p-4">
                <div className="flex items-center space-x-3 space-x-reverse mb-4">
                  <div className="bg-gradient-to-r from-red-500 to-orange-600 w-12 h-12 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-lg">
                      {debtor.name.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <p className="text-white font-bold text-lg">{debtor.name}</p>
                    <p className="text-gray-400 text-sm">عميل مدين</p>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center space-x-3 space-x-reverse text-gray-300">
                    <Phone className="w-4 h-4" />
                    <span>{debtor.phone}</span>
                  </div>

                  <div className="flex items-center space-x-3 space-x-reverse text-gray-300">
                    <MapPin className="w-4 h-4" />
                    <span>{debtor.address}</span>
                  </div>
                </div>
              </div>

              {/* الملاحظات */}
              <div className="bg-white/5 rounded-xl p-4">
                <h4 className="text-white font-semibold mb-2">الملاحظات</h4>
                <p className="text-gray-300 text-sm">لا توجد ملاحظات</p>
              </div>
            </div>

            {/* الإحصائيات المالية */}
            <div className="space-y-4">
              <h3 className="text-xl font-bold text-white mb-4">الإحصائيات المالية</h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="bg-red-500/20 rounded-xl p-4 text-center border border-red-500/30">
                  <TrendingDown className="w-6 h-6 text-red-400 mx-auto mb-2" />
                  <p className="text-gray-300 text-sm mb-1">مبلغ الدين</p>
                  <p className="text-xl font-bold text-red-400">
                    {formatCurrency(debtor.remaining_debt)}
                  </p>
                </div>

                <div className="bg-blue-500/20 rounded-xl p-4 text-center border border-blue-500/30">
                  <DollarSign className="w-6 h-6 text-blue-400 mx-auto mb-2" />
                  <p className="text-gray-300 text-sm mb-1">إجمالي المشتريات</p>
                  <p className="text-xl font-bold text-white">
                    {formatCurrency(debtor.total_debt)}
                  </p>
                </div>

                <div className="bg-yellow-500/20 rounded-xl p-4 text-center border border-yellow-500/30">
                  <Calendar className="w-6 h-6 text-yellow-400 mx-auto mb-2" />
                  <p className="text-gray-300 text-sm mb-1">آخر شراء</p>
                  <p className="text-sm font-bold text-white">
                    {formatDate(new Date(debtor.updated_at))}
                  </p>
                </div>

                <div className="bg-purple-500/20 rounded-xl p-4 text-center border border-purple-500/30">
                  <Wallet className="w-6 h-6 text-purple-400 mx-auto mb-2" />
                  <p className="text-gray-300 text-sm mb-1">عدد الدفعات</p>
                  <p className="text-xl font-bold text-white">
                    {debtor.debt_count || 0}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* تاريخ الدفعات */}
          <div className="mt-8">
            <h3 className="text-xl font-bold text-white mb-4">تاريخ الدفعات</h3>

            <div className="text-center py-8">
              <Wallet className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-400">لا توجد دفعات مسجلة</p>
            </div>
          </div>
        </div>

        <div className="p-6 border-t border-white/10 bg-white/5">
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="bg-white/10 border border-white/20 hover:border-white/40 px-6 py-3 rounded-xl font-bold text-white transition-all duration-300"
            >
              إغلاق
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// مودال تسجيل دفعة
const PaymentModal: React.FC<{
  debtor: DebtorCustomer;
  onClose: () => void;
  onPayment: (payment: PaymentRecord) => void;
}> = ({ debtor, onClose, onPayment }) => {
  const [formData, setFormData] = useState({
    amount: 0,
    method: 'cash' as 'cash' | 'transfer' | 'check',
    description: ''
  });
  const [loading, setLoading] = useState(false);
  const [selectedDebt, setSelectedDebt] = useState<CustomerDebt | null>(null);

  // تحميل ديون العميل عند فتح المودال
  useEffect(() => {
    // اختيار أول دين غير مسدد
    const unpaidDebt = debtor.debts.find(debt => 
      debt.status === 'pending' || (debt.status === 'partial' && debt.remaining_amount > 0)
    );
    setSelectedDebt(unpaidDebt || null);
  }, [debtor.debts]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (formData.amount <= 0 || formData.amount > debtor.remaining_debt) {
      alert('❌ المبلغ غير صحيح أو أكبر من إجمالي الدين المتبقي');
      return;
    }

    setLoading(true);
    try {
      const paymentData = {
        customer_id: debtor.id,
        amount: formData.amount,
        payment_method: formData.method,
        notes: formData.description.trim() || 'دفعة من الدين'
      };

      console.log('🔄 إرسال طلب تسديد دين للعميل:', debtor.id);
      const response = await apiService.payCustomerDebt(debtor.id, paymentData);

      if (!response.success) {
        throw new Error(response.error || 'فشل في تسديد الدين');
      }

      console.log('✅ تم تسديد الدين بنجاح:', response);

      const payment: PaymentRecord = {
        id: response.payment?.id.toString() || Date.now().toString(),
        date: new Date(response.payment?.payment_date || new Date()),
        amount: formData.amount,
        method: formData.method,
        description: formData.description.trim() || 'دفعة من الدين'
      };

      const newRemainingDebt = response.total_remaining || (debtor.remaining_debt - formData.amount);
      if (newRemainingDebt <= 0.01) {
        console.log('🎉 تم تسديد كامل الدين للعميل:', debtor.name);
        alert(`🎉 تم تسديد كامل الدين للعميل "${debtor.name}" بنجاح!\nالمبلغ المدفوع: ${formData.amount.toFixed(2)} دج`);
      } else {
        console.log('💰 تم تسديد جزء من الدين للعميل:', debtor.name);
        alert(`✅ تم تسديد جزء من الدين بنجاح!\nالمبلغ المدفوع: ${formData.amount.toFixed(2)} دج\nالدين المتبقي: ${newRemainingDebt.toFixed(2)} دج`);
      }

      onPayment(payment);
    } catch (error) {
      console.error('خطأ في تسديد الدين:', error);
      alert('❌ حدث خطأ أثناء تسديد الدين. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number | string | undefined): string => {
    if (amount === undefined) return '0.00 دج';
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return `${numAmount.toFixed(2)} دج`;
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl border border-white/20 w-full max-w-md shadow-2xl">
        <div className="p-6 border-b border-white/10">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-white flex items-center space-x-3 space-x-reverse">
              <Wallet className="w-6 h-6 text-green-400" />
              <span>تسجيل دفعة</span>
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors duration-300"
            >
              <XCircle className="w-6 h-6" />
            </button>
          </div>
          <div className="mt-4 p-4 bg-white/5 rounded-xl">
            <p className="text-gray-300 text-sm">العميل: <span className="text-white font-semibold">{debtor.name}</span></p>
            {selectedDebt && (
              <>
                <p className="text-gray-300 text-sm">رقم الدين: <span className="text-white font-semibold">{selectedDebt.id}</span></p>
                <p className="text-gray-300 text-sm">المبلغ المتبقي: <span className="text-red-400 font-bold">{formatCurrency(selectedDebt.remaining_amount)}</span></p>
              </>
            )}
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div>
            <label className="block text-gray-300 text-sm font-semibold mb-2">
              مبلغ الدفعة (دج) *
            </label>
            <input
              type="number"
              step="0.01"
              min="0.01"
              max={parseFloat(debtor.remaining_debt.toString())}
              required
              value={formData.amount || ''}
              onChange={(e) => {
                const value = e.target.value;
                console.log('🔢 Input value:', value, 'Type:', typeof value);

                // إذا كان الحقل فارغ، اتركه فارغ
                if (value === '') {
                  setFormData({ ...formData, amount: 0 });
                  return;
                }

                // تحقق من أن القيمة رقم صحيح
                const numValue = parseFloat(value);
                console.log('🔢 Parsed value:', numValue, 'isNaN:', isNaN(numValue));

                if (!isNaN(numValue) && numValue >= 0) {
                  setFormData({ ...formData, amount: numValue });
                } else {
                  console.warn('⚠️ Invalid number:', value);
                }
              }}
              className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 transition-all duration-300"
              placeholder="أدخل مبلغ الدفعة"
            />
            <p className="text-gray-400 text-xs mt-1">الحد الأقصى: {formatCurrency(debtor.remaining_debt)}</p>
          </div>

          <div>
            <label className="block text-gray-300 text-sm font-semibold mb-2">
              طريقة الدفع
            </label>
            <div className="grid grid-cols-3 gap-3">
              <button
                type="button"
                onClick={() => setFormData({ ...formData, method: 'cash' })}
                className={`p-3 rounded-xl border-2 transition-all duration-300 ${
                  formData.method === 'cash'
                    ? 'border-green-400 bg-green-500/20 text-green-400'
                    : 'border-white/20 bg-white/5 text-gray-300 hover:border-green-400/50'
                }`}
              >
                <DollarSign className="w-5 h-5 mx-auto mb-1" />
                <span className="text-xs font-semibold">نقدي</span>
              </button>
              <button
                type="button"
                onClick={() => setFormData({ ...formData, method: 'transfer' })}
                className={`p-3 rounded-xl border-2 transition-all duration-300 ${
                  formData.method === 'transfer'
                    ? 'border-blue-400 bg-blue-500/20 text-blue-400'
                    : 'border-white/20 bg-white/5 text-gray-300 hover:border-blue-400/50'
                }`}
              >
                <Zap className="w-5 h-5 mx-auto mb-1" />
                <span className="text-xs font-semibold">تحويل</span>
              </button>
              <button
                type="button"
                onClick={() => setFormData({ ...formData, method: 'check' })}
                className={`p-3 rounded-xl border-2 transition-all duration-300 ${
                  formData.method === 'check'
                    ? 'border-purple-400 bg-purple-500/20 text-purple-400'
                    : 'border-white/20 bg-white/5 text-gray-300 hover:border-purple-400/50'
                }`}
              >
                <FileText className="w-5 h-5 mx-auto mb-1" />
                <span className="text-xs font-semibold">شيك</span>
              </button>
            </div>
          </div>

          <div>
            <label className="block text-gray-300 text-sm font-semibold mb-2">
              ملاحظات
            </label>
            <input
              type="text"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 transition-all duration-300"
              placeholder="ملاحظات إضافية (اختياري)"
            />
          </div>

          {formData.amount > 0 && (
            <div className="p-4 bg-white/5 rounded-xl">
              <p className="text-gray-300 text-sm">
                الدين بعد الدفعة:
                <span className="font-bold text-green-400 ml-2">
                  {formatCurrency(parseFloat(debtor.remaining_debt.toString()) - formData.amount)}
                </span>
              </p>
            </div>
          )}

          <div className="flex space-x-4 space-x-reverse pt-4">
            <button
              type="submit"
              disabled={loading || formData.amount <= 0 || formData.amount > parseFloat(debtor.remaining_debt.toString())}
              className="flex-1 bg-gradient-to-r from-green-500 to-emerald-400 hover:scale-105 transform transition-all duration-300 py-3 rounded-xl font-bold text-white shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'جاري التسديد...' : 'تسجيل الدفعة'}
            </button>
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-white/10 border border-white/20 hover:border-white/40 py-3 rounded-xl font-bold text-white transition-all duration-300"
            >
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// مودال إرسال تذكير
const ReminderModal: React.FC<{
  debtor: DebtorCustomer;
  onClose: () => void;
  onSend: () => void;
}> = ({ debtor, onClose, onSend }) => {
  const [reminderType, setReminderType] = useState<'sms' | 'call' | 'email'>('sms');
  const [message, setMessage] = useState(`مرحباً ${debtor.name}، نذكركم بوجود مستحقات بقيمة ${debtor.remaining_debt.toFixed(2)} دج. نرجو التكرم بالتسديد في أقرب وقت ممكن. شكراً لتعاونكم.`);

  const handleSend = () => {
    // هنا يتم إرسال التذكير
    console.log(`إرسال تذكير ${reminderType} إلى ${debtor.name}: ${message}`);
    onSend();
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl border border-white/20 w-full max-w-lg shadow-2xl">
        <div className="p-6 border-b border-white/10">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-white flex items-center space-x-3 space-x-reverse">
              <Bell className="w-6 h-6 text-yellow-400" />
              <span>إرسال تذكير</span>
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors duration-300"
            >
              <XCircle className="w-6 h-6" />
            </button>
          </div>
          <div className="mt-4 p-4 bg-white/5 rounded-xl">
            <p className="text-gray-300 text-sm">العميل: <span className="text-white font-semibold">{debtor.name}</span></p>
            <p className="text-gray-300 text-sm">الهاتف: <span className="text-white font-semibold">{debtor.phone}</span></p>
            <p className="text-gray-300 text-sm">مبلغ الدين: <span className="text-red-400 font-bold">{debtor.remaining_debt.toFixed(2)} دج</span></p>
          </div>
        </div>

        <div className="p-6 space-y-6">
          <div>
            <label className="block text-gray-300 text-sm font-semibold mb-3">
              طريقة التذكير
            </label>
            <div className="grid grid-cols-3 gap-3">
              <button
                onClick={() => setReminderType('sms')}
                className={`p-4 rounded-xl border-2 transition-all duration-300 ${
                  reminderType === 'sms'
                    ? 'border-green-400 bg-green-500/20 text-green-400'
                    : 'border-white/20 bg-white/5 text-gray-300 hover:border-green-400/50'
                }`}
              >
                <MessageSquare className="w-6 h-6 mx-auto mb-2" />
                <span className="text-sm font-semibold">رسالة نصية</span>
              </button>
              <button
                onClick={() => setReminderType('call')}
                className={`p-4 rounded-xl border-2 transition-all duration-300 ${
                  reminderType === 'call'
                    ? 'border-blue-400 bg-blue-500/20 text-blue-400'
                    : 'border-white/20 bg-white/5 text-gray-300 hover:border-blue-400/50'
                }`}
              >
                <Phone className="w-6 h-6 mx-auto mb-2" />
                <span className="text-sm font-semibold">مكالمة</span>
              </button>
              <button
                onClick={() => setReminderType('email')}
                className={`p-4 rounded-xl border-2 transition-all duration-300 ${
                  reminderType === 'email'
                    ? 'border-purple-400 bg-purple-500/20 text-purple-400'
                    : 'border-white/20 bg-white/5 text-gray-300 hover:border-purple-400/50'
                }`}
              >
                <Send className="w-6 h-6 mx-auto mb-2" />
                <span className="text-sm font-semibold">بريد إلكتروني</span>
              </button>
            </div>
          </div>

          <div>
            <label className="block text-gray-300 text-sm font-semibold mb-2">
              نص التذكير
            </label>
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-yellow-400 transition-all duration-300 resize-none"
              rows={4}
              placeholder="اكتب نص التذكير..."
            />
            <p className="text-gray-400 text-xs mt-1">{message.length} حرف</p>
          </div>

          <div className="flex space-x-4 space-x-reverse pt-4">
            <button
              onClick={handleSend}
              className="flex-1 bg-gradient-to-r from-yellow-500 to-orange-400 hover:scale-105 transform transition-all duration-300 py-3 rounded-xl font-bold text-white shadow-lg"
            >
              إرسال التذكير
            </button>
            <button
              onClick={onClose}
              className="flex-1 bg-white/10 border border-white/20 hover:border-white/40 py-3 rounded-xl font-bold text-white transition-all duration-300"
            >
              إلغاء
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// مودال تأكيد الحذف
const DeleteConfirmationModal: React.FC<{
  debtor: DebtorCustomer;
  deleteInfo: any;
  onClose: () => void;
  onConfirm: () => void;
}> = ({ debtor, deleteInfo, onClose, onConfirm }) => {
  const formatCurrency = (amount: number | string | undefined): string => {
    if (amount === undefined) return '0.00 دج';
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return `${numAmount.toFixed(2)} دج`;
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl border border-red-500/30 w-full max-w-md shadow-2xl overflow-hidden">
        <div className="p-6 border-b border-red-500/20">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-white flex items-center space-x-3 space-x-reverse">
              <Trash2 className="w-6 h-6 text-red-400" />
              <span>تأكيد الحذف</span>
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors duration-300"
            >
              <XCircle className="w-6 h-6" />
            </button>
          </div>
        </div>

        <div className="p-6">
          <div className="text-center mb-6">
            <div className="bg-red-500/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertTriangle className="w-8 h-8 text-red-400" />
            </div>
            <h3 className="text-xl font-bold text-white mb-2">
              هل أنت متأكد من حذف فاتورة دين العميل؟
            </h3>
            <p className="text-gray-300">
              سيتم حذف جميع البيانات المرتبطة بفاتورة الدين نهائياً
            </p>
          </div>

          {/* معلومات العميل */}
          <div className="bg-white/5 rounded-xl p-4 mb-6">
            <div className="flex items-center space-x-3 space-x-reverse mb-4">
              <div className="bg-red-500/20 w-12 h-12 rounded-full flex items-center justify-center">
                <span className="text-white font-bold">
                  {debtor.name.charAt(0)}
                </span>
              </div>
              <div>
                <p className="text-white font-semibold">{debtor.name}</p>
                <p className="text-gray-400 text-sm">{deleteInfo.invoiceNumber}</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-400">الرصيد المتبقي</p>
                <p className="text-red-400 font-bold">
                  {formatCurrency(deleteInfo.remainingBalance)}
                </p>
              </div>
              <div>
                <p className="text-gray-400">عدد المشتريات</p>
                <p className="text-white font-bold">{deleteInfo.itemsCount}</p>
              </div>
              <div>
                <p className="text-gray-400">عدد الدفعات</p>
                <p className="text-white font-bold">{deleteInfo.paymentsCount}</p>
              </div>
              <div>
                <p className="text-gray-400">حالة الدين</p>
                <p className={`font-bold ${deleteInfo.hasBalance ? 'text-red-400' : 'text-green-400'}`}>
                  {deleteInfo.hasBalance ? 'معلق' : 'مسدد'}
                </p>
              </div>
            </div>

            {deleteInfo.warning && (
              <div className="mt-4 p-3 bg-yellow-500/20 border border-yellow-500/30 rounded-lg">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <AlertTriangle className="w-4 h-4 text-yellow-400" />
                  <p className="text-yellow-400 text-sm font-semibold">
                    {deleteInfo.warning}
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* أزرار التأكيد */}
          <div className="flex space-x-4 space-x-reverse">
            <button
              onClick={onClose}
              className="flex-1 bg-gray-600 hover:bg-gray-700 px-4 py-3 rounded-xl font-bold text-white transition-all duration-300"
            >
              إلغاء
            </button>
            <button
              onClick={onConfirm}
              className="flex-1 bg-red-500 hover:bg-red-600 px-4 py-3 rounded-xl font-bold text-white transition-all duration-300 flex items-center justify-center space-x-2 space-x-reverse"
            >
              <Trash2 className="w-4 h-4" />
              <span>حذف نهائي</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Debtors;
