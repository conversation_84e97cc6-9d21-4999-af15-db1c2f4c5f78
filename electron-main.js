// 🖥️ Electron Main Process - تطبيق سطح المكتب
const { app, BrowserWindow, Menu, dialog, shell } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');
const isDev = process.env.NODE_ENV === 'development';

let mainWindow;
let serverProcess;

// إنشاء النافذة الرئيسية
function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        minWidth: 1000,
        minHeight: 600,
        icon: path.join(__dirname, 'assets', 'icon.png'),
        title: 'كشير توسار - نظام نقاط البيع',
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            webSecurity: true
        },
        show: false, // لا تظهر النافذة حتى تكون جاهزة
        titleBarStyle: 'default',
        autoHideMenuBar: false
    });

    // إنشاء قائمة مخصصة
    const menuTemplate = [
        {
            label: 'ملف',
            submenu: [
                {
                    label: 'إعادة تحميل',
                    accelerator: 'F5',
                    click: () => mainWindow.reload()
                },
                {
                    label: 'أدوات المطور',
                    accelerator: 'F12',
                    click: () => mainWindow.webContents.toggleDevTools()
                },
                { type: 'separator' },
                {
                    label: 'خروج',
                    accelerator: 'Alt+F4',
                    click: () => app.quit()
                }
            ]
        },
        {
            label: 'عرض',
            submenu: [
                {
                    label: 'تكبير',
                    accelerator: 'CmdOrCtrl+Plus',
                    click: () => {
                        const currentZoom = mainWindow.webContents.getZoomLevel();
                        mainWindow.webContents.setZoomLevel(currentZoom + 0.5);
                    }
                },
                {
                    label: 'تصغير',
                    accelerator: 'CmdOrCtrl+-',
                    click: () => {
                        const currentZoom = mainWindow.webContents.getZoomLevel();
                        mainWindow.webContents.setZoomLevel(currentZoom - 0.5);
                    }
                },
                {
                    label: 'حجم طبيعي',
                    accelerator: 'CmdOrCtrl+0',
                    click: () => mainWindow.webContents.setZoomLevel(0)
                },
                { type: 'separator' },
                {
                    label: 'ملء الشاشة',
                    accelerator: 'F11',
                    click: () => {
                        const isFullScreen = mainWindow.isFullScreen();
                        mainWindow.setFullScreen(!isFullScreen);
                    }
                }
            ]
        },
        {
            label: 'مساعدة',
            submenu: [
                {
                    label: 'حول البرنامج',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'حول كشير توسار',
                            message: 'كشير توسار - نظام نقاط البيع',
                            detail: 'نظام نقاط بيع متكامل باللغة العربية\nالإصدار: 1.0.0\nتطوير: فريق توسار',
                            buttons: ['موافق']
                        });
                    }
                },
                {
                    label: 'الدعم الفني',
                    click: () => {
                        shell.openExternal('mailto:<EMAIL>');
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(menuTemplate);
    Menu.setApplicationMenu(menu);

    // بدء الخادم الخلفي
    startBackendServer();

    // انتظار تشغيل الخادم ثم تحميل التطبيق
    setTimeout(() => {
        mainWindow.loadURL('http://localhost:5003');
        
        mainWindow.once('ready-to-show', () => {
            mainWindow.show();
            
            // رسالة ترحيب
            setTimeout(() => {
                dialog.showMessageBox(mainWindow, {
                    type: 'info',
                    title: 'مرحباً بك',
                    message: 'مرحباً بك في كشير توسار!',
                    detail: 'نظام نقاط البيع الخاص بك جاهز للاستخدام',
                    buttons: ['ابدأ الآن']
                });
            }, 2000);
        });
    }, 3000);

    // التعامل مع إغلاق النافذة
    mainWindow.on('closed', () => {
        mainWindow = null;
        if (serverProcess) {
            serverProcess.kill();
        }
    });

    // منع التنقل خارج التطبيق
    mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
        const parsedUrl = new URL(navigationUrl);
        if (parsedUrl.origin !== 'http://localhost:5003') {
            event.preventDefault();
        }
    });
}

// بدء الخادم الخلفي
function startBackendServer() {
    const serverPath = isDev 
        ? path.join(__dirname, 'backend', 'server.js')
        : path.join(process.resourcesPath, 'backend', 'server.js');
    
    console.log('🚀 بدء تشغيل الخادم الخلفي...');
    
    serverProcess = spawn('node', [serverPath], {
        cwd: isDev ? path.join(__dirname, 'backend') : path.join(process.resourcesPath, 'backend'),
        stdio: 'pipe'
    });

    serverProcess.stdout.on('data', (data) => {
        console.log(`خادم: ${data}`);
    });

    serverProcess.stderr.on('data', (data) => {
        console.error(`خطأ في الخادم: ${data}`);
    });

    serverProcess.on('close', (code) => {
        console.log(`الخادم توقف بالكود: ${code}`);
    });
}

// عند جاهزية التطبيق
app.whenReady().then(createWindow);

// إغلاق التطبيق عند إغلاق جميع النوافذ (Windows & Linux)
app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        if (serverProcess) {
            serverProcess.kill();
        }
        app.quit();
    }
});

// إعادة إنشاء النافذة عند النقر على أيقونة التطبيق (macOS)
app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// التعامل مع إغلاق التطبيق
app.on('before-quit', () => {
    if (serverProcess) {
        serverProcess.kill();
    }
});

// منع إنشاء عدة نسخ من التطبيق
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
    app.quit();
} else {
    app.on('second-instance', () => {
        if (mainWindow) {
            if (mainWindow.isMinimized()) mainWindow.restore();
            mainWindow.focus();
        }
    });
}
