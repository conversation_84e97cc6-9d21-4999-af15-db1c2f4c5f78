const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'pos_system',
  password: 'toossar',
  port: 5432,
});

async function checkSchema() {
  try {
    console.log('🔍 فحص schema قاعدة البيانات...');
    
    // فحص الجداول المتاحة
    const tablesResult = await pool.query(`
      SELECT table_schema, table_name 
      FROM information_schema.tables 
      WHERE table_type = 'BASE TABLE' 
      AND table_name IN ('sales', 'customers', 'products')
      ORDER BY table_schema, table_name
    `);
    
    console.log('📋 الجداول الموجودة:');
    tablesResult.rows.forEach(row => {
      console.log(`  - ${row.table_schema}.${row.table_name}`);
    });
    
    // فحص جدول sales تحديداً
    console.log('\n🔍 فحص جدول sales...');
    
    // محاولة الوصول لجدول sales بدون schema
    try {
      const salesCount1 = await pool.query('SELECT COUNT(*) FROM sales');
      console.log(`✅ sales (بدون schema): ${salesCount1.rows[0].count} سجل`);
    } catch (error) {
      console.log(`❌ sales (بدون schema): ${error.message}`);
    }
    
    // محاولة الوصول لجدول sales مع schema
    try {
      const salesCount2 = await pool.query('SELECT COUNT(*) FROM pos_system.sales');
      console.log(`✅ pos_system.sales: ${salesCount2.rows[0].count} سجل`);
    } catch (error) {
      console.log(`❌ pos_system.sales: ${error.message}`);
    }
    
    // محاولة الوصول لجدول sales مع schema public
    try {
      const salesCount3 = await pool.query('SELECT COUNT(*) FROM public.sales');
      console.log(`✅ public.sales: ${salesCount3.rows[0].count} سجل`);
    } catch (error) {
      console.log(`❌ public.sales: ${error.message}`);
    }
    
    // فحص آخر بيع تم إدراجه
    console.log('\n🔍 فحص آخر المبيعات...');
    try {
      const lastSales = await pool.query(`
        SELECT id, sale_number, total_amount, created_at 
        FROM sales 
        ORDER BY created_at DESC 
        LIMIT 5
      `);
      
      console.log('📊 آخر 5 مبيعات:');
      lastSales.rows.forEach(sale => {
        console.log(`  - ${sale.sale_number}: ${sale.total_amount} دج (${sale.created_at})`);
      });
    } catch (error) {
      console.log(`❌ خطأ في جلب المبيعات: ${error.message}`);
    }
    
  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await pool.end();
  }
}

checkSchema();
