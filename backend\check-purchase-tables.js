const pool = require('./database');

async function checkPurchaseTables() {
  try {
    console.log('🔍 فحص جداول المشتريات...');
    
    const client = await pool.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات');
    
    // فحص جميع الجداول
    const allTablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = $1
      ORDER BY table_name
    `, [process.env.DB_SCHEMA]);
    
    console.log('\n📊 جميع الجداول الموجودة:');
    allTablesResult.rows.forEach(row => {
      console.log('  ✓', row.table_name);
    });
    
    // البحث عن جداول المشتريات
    const purchaseTables = allTablesResult.rows.filter(row => 
      row.table_name.includes('purchase') || row.table_name.includes('supplier')
    );
    
    console.log('\n🔍 جداول المشتريات والموردين:');
    if (purchaseTables.length === 0) {
      console.log('  ❌ لا توجد جداول مشتريات');
    } else {
      purchaseTables.forEach(row => {
        console.log('  ✓', row.table_name);
      });
    }
    
    client.release();
    
  } catch (error) {
    console.error('❌ خطأ في فحص الجداول:', error.message);
  } finally {
    process.exit(0);
  }
}

checkPurchaseTables();
