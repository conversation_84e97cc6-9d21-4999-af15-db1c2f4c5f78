-- إنشاء جداول المشتريات والموردين
-- 🗄️ إنشاء جداول النظام المتكامل

-- 🏢 جدول الموردين
CREATE TABLE IF NOT EXISTS pos_system.suppliers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    address TEXT,
    balance DECIMAL(10,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 📋 جدول فواتير المشتريات
CREATE TABLE IF NOT EXISTS pos_system.purchase_invoices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    supplier_id UUID REFERENCES pos_system.suppliers(id),
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    amount_paid DECIMAL(10,2) DEFAULT 0,
    remaining_amount DECIMAL(10,2) GENERATED ALWAYS AS (total_amount - amount_paid) STORED,
    is_paid BOOLEAN GENERATED ALWAYS AS (amount_paid >= total_amount) STORED,
    invoice_date DATE NOT NULL DEFAULT CURRENT_DATE,
    due_date DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 📦 جدول عناصر فواتير المشتريات
CREATE TABLE IF NOT EXISTS pos_system.purchase_invoice_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    invoice_id UUID NOT NULL REFERENCES pos_system.purchase_invoices(id) ON DELETE CASCADE,
    product_id UUID REFERENCES pos_system.products(id),
    product_name VARCHAR(255) NOT NULL,
    quantity INTEGER NOT NULL,
    unit_cost DECIMAL(10,2) NOT NULL,
    discount DECIMAL(10,2) DEFAULT 0,
    total_cost DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 📊 جدول حركات المخزون
CREATE TABLE IF NOT EXISTS pos_system.inventory_movements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID NOT NULL REFERENCES pos_system.products(id),
    movement_type VARCHAR(10) NOT NULL CHECK (movement_type IN ('in', 'out')),
    quantity INTEGER NOT NULL,
    previous_stock INTEGER NOT NULL,
    new_stock INTEGER NOT NULL,
    reason VARCHAR(100) NOT NULL,
    reference_id UUID,
    reference_type VARCHAR(50),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 💳 جدول ديون الموردين
CREATE TABLE IF NOT EXISTS pos_system.supplier_debts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    supplier_id UUID NOT NULL REFERENCES pos_system.suppliers(id),
    invoice_id UUID REFERENCES pos_system.purchase_invoices(id),
    amount DECIMAL(10,2) NOT NULL,
    description TEXT,
    due_date DATE,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'overdue')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 💰 جدول دفعات ديون الموردين
CREATE TABLE IF NOT EXISTS pos_system.supplier_debt_payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    debt_id UUID NOT NULL REFERENCES pos_system.supplier_debts(id),
    supplier_id UUID NOT NULL REFERENCES pos_system.suppliers(id),
    amount DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(50) DEFAULT 'cash',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 🔧 إنشاء الفهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_suppliers_name ON pos_system.suppliers(name);
CREATE INDEX IF NOT EXISTS idx_suppliers_active ON pos_system.suppliers(is_active);
CREATE INDEX IF NOT EXISTS idx_purchase_invoices_supplier ON pos_system.purchase_invoices(supplier_id);
CREATE INDEX IF NOT EXISTS idx_purchase_invoices_date ON pos_system.purchase_invoices(invoice_date);
CREATE INDEX IF NOT EXISTS idx_purchase_invoices_number ON pos_system.purchase_invoices(invoice_number);
CREATE INDEX IF NOT EXISTS idx_purchase_invoice_items_invoice ON pos_system.purchase_invoice_items(invoice_id);
CREATE INDEX IF NOT EXISTS idx_purchase_invoice_items_product ON pos_system.purchase_invoice_items(product_id);
CREATE INDEX IF NOT EXISTS idx_inventory_movements_product ON pos_system.inventory_movements(product_id);
CREATE INDEX IF NOT EXISTS idx_inventory_movements_date ON pos_system.inventory_movements(created_at);
CREATE INDEX IF NOT EXISTS idx_supplier_debts_supplier ON pos_system.supplier_debts(supplier_id);
CREATE INDEX IF NOT EXISTS idx_supplier_debts_status ON pos_system.supplier_debts(status);
CREATE INDEX IF NOT EXISTS idx_supplier_debt_payments_debt ON pos_system.supplier_debt_payments(debt_id);
CREATE INDEX IF NOT EXISTS idx_supplier_debt_payments_supplier ON pos_system.supplier_debt_payments(supplier_id);

-- 🎯 إدراج بيانات تجريبية للموردين
INSERT INTO pos_system.suppliers (name, phone, address, balance) VALUES
('مورد الأجهزة الإلكترونية', '0555123456', 'شارع التجارة، الجزائر العاصمة', 0),
('شركة المواد الغذائية', '0666789012', 'حي الصناعة، وهران', 0),
('مورد الملابس والأزياء', '0777345678', 'سوق الجملة، قسنطينة', 0)
ON CONFLICT (id) DO NOTHING;

-- 🔄 إنشاء دالة تحديث الوقت
CREATE OR REPLACE FUNCTION pos_system.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 🔄 إنشاء المحفزات لتحديث الوقت تلقائياً
DROP TRIGGER IF EXISTS update_suppliers_updated_at ON pos_system.suppliers;
CREATE TRIGGER update_suppliers_updated_at
    BEFORE UPDATE ON pos_system.suppliers
    FOR EACH ROW
    EXECUTE FUNCTION pos_system.update_updated_at_column();

DROP TRIGGER IF EXISTS update_purchase_invoices_updated_at ON pos_system.purchase_invoices;
CREATE TRIGGER update_purchase_invoices_updated_at
    BEFORE UPDATE ON pos_system.purchase_invoices
    FOR EACH ROW
    EXECUTE FUNCTION pos_system.update_updated_at_column();

DROP TRIGGER IF EXISTS update_supplier_debts_updated_at ON pos_system.supplier_debts;
CREATE TRIGGER update_supplier_debts_updated_at
    BEFORE UPDATE ON pos_system.supplier_debts
    FOR EACH ROW
    EXECUTE FUNCTION pos_system.update_updated_at_column();

-- ✅ تأكيد إنشاء الجداول
SELECT 'تم إنشاء جداول المشتريات والموردين بنجاح!' as message;
