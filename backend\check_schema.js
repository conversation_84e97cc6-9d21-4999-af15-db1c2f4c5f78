const { Pool } = require('pg');

// الاتصال بقاعدة postgres الافتراضية أولاً
const adminPool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'postgres',
  password: 'toossar',
  port: 5432,
});

async function checkSchema() {
  try {
    console.log('🔍 فحص قواعد البيانات المتاحة...');

    // فحص قواعد البيانات الموجودة
    const dbResult = await adminPool.query(`
      SELECT datname FROM pg_database
      WHERE datistemplate = false
      ORDER BY datname
    `);

    console.log('📋 قواعد البيانات الموجودة:');
    dbResult.rows.forEach(row => {
      console.log(`  - ${row.datname}`);
    });

    // التحقق من وجود pos_system
    const posSystemExists = dbResult.rows.some(row => row.datname === 'pos_system');

    if (!posSystemExists) {
      console.log('\n❌ قاعدة البيانات pos_system غير موجودة!');
      console.log('💡 هذا يفسر لماذا البيع "ينجح" لكن لا يُحفظ');
      console.log('🔧 يجب إنشاء قاعدة البيانات أولاً');
      return;
    }

    console.log('\n✅ قاعدة البيانات pos_system موجودة');

    // الآن الاتصال بقاعدة pos_system
    const posPool = new Pool({
      user: 'postgres',
      host: 'localhost',
      database: 'pos_system',
      password: 'toossar',
      port: 5432,
    });

    // فحص الجداول
    const tablesResult = await posPool.query(`
      SELECT table_schema, table_name
      FROM information_schema.tables
      WHERE table_type = 'BASE TABLE'
      AND table_name IN ('sales', 'customers', 'products')
      ORDER BY table_schema, table_name
    `);

    console.log('\n📋 الجداول الموجودة في pos_system:');
    tablesResult.rows.forEach(row => {
      console.log(`  - ${row.table_schema}.${row.table_name}`);
    });

    // فحص عدد المبيعات
    try {
      const salesCount = await posPool.query('SELECT COUNT(*) FROM sales');
      console.log(`\n📊 عدد المبيعات: ${salesCount.rows[0].count}`);

      if (salesCount.rows[0].count > 0) {
        const lastSales = await posPool.query(`
          SELECT id, sale_number, total_amount, created_at
          FROM sales
          ORDER BY created_at DESC
          LIMIT 3
        `);

        console.log('\n� آخر 3 مبيعات:');
        lastSales.rows.forEach(sale => {
          console.log(`  - ${sale.sale_number}: ${sale.total_amount} دج`);
        });
      }
    } catch (error) {
      console.log(`\n❌ خطأ في الوصول لجدول sales: ${error.message}`);
    }

    await posPool.end();

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await adminPool.end();
    process.exit(0);
  }
}

checkSchema();
