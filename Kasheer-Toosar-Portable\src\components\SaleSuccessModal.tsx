import React, { useState, useEffect } from 'react';
import {
  Check<PERSON>ircle,
  Star,
  Sparkles,
  Trophy,
  Gift,
  Printer,
  Share2,
  Download,
  Mail,
  MessageCircle,
  Heart,
  Zap,
  Crown,
  Award,
  Target,
  TrendingUp,
  DollarSign,
  Clock,
  User,
  Package,
  X
} from 'lucide-react';

interface SaleSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  onOpenReceipt: () => void;
  saleData: {
    total: number;
    paid: number;
    change: number;
    items: Array<{
      name: string;
      quantity: number;
      price: number;
    }>;
    paymentMethod: string;
    customer?: string;
    saleNumber: string;
    timestamp: Date;
  };
}

const SaleSuccessModal: React.FC<SaleSuccessModalProps> = ({ isOpen, onClose, onOpenReceipt, saleData }) => {
  const [showConfetti, setShowConfetti] = useState(false);
  const [animationStep, setAnimationStep] = useState(0);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setShowConfetti(true);
      setAnimationStep(0);

      // Animation sequence
      const timeouts = [
        setTimeout(() => setAnimationStep(1), 300),
        setTimeout(() => setAnimationStep(2), 600),
        setTimeout(() => setAnimationStep(3), 900),
        setTimeout(() => setAnimationStep(4), 1200),
      ];

      return () => timeouts.forEach(clearTimeout);
    }
  }, [isOpen]);

  // إضافة وظيفة مفتاح Escape للإغلاق
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const handlePrint = () => {
    // Open receipt modal
    onOpenReceipt();
  };

  const handleShare = () => {
    // Share functionality
    if (navigator.share) {
      navigator.share({
        title: 'فاتورة البيع',
        text: `تم إتمام عملية بيع بقيمة ${saleData.total.toFixed(2)} دج`,
      });
    }
  };

  const handleEmail = () => {
    // Email functionality
    const subject = `فاتورة رقم ${saleData.saleNumber}`;
    const body = `تم إتمام عملية البيع بنجاح\nالإجمالي: ${saleData.total.toFixed(2)} دج`;
    window.open(`mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Confetti Animation */}
      {showConfetti && (
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          {[...Array(50)].map((_, i) => (
            <div
              key={i}
              className="absolute animate-bounce"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 2}s`,
                animationDuration: `${2 + Math.random() * 2}s`
              }}
            >
              {Math.random() > 0.5 ? (
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
              ) : (
                <Sparkles className="w-4 h-4 text-blue-400" />
              )}
            </div>
          ))}
        </div>
      )}

      {/* Modal */}
      <div className="relative bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900 rounded-3xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden border border-white/20">
        {/* Animated Background */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>
          <div className="absolute top-40 left-1/2 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000"></div>
        </div>

        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-6 right-6 z-10 p-2 bg-white/10 backdrop-blur-lg rounded-xl border border-white/20 hover:border-white/40 transition-all duration-300 hover:scale-110"
        >
          <X className="w-5 h-5 text-white" />
        </button>

        <div className="relative z-10 p-8">
          {/* Success Icon */}
          <div className="text-center mb-8">
            <div className={`relative inline-block transition-all duration-1000 ${animationStep >= 1 ? 'scale-100 opacity-100' : 'scale-0 opacity-0'}`}>
              <div className="w-32 h-32 bg-gradient-to-r from-green-500 to-emerald-400 rounded-full flex items-center justify-center shadow-2xl mx-auto relative">
                <CheckCircle className="w-16 h-16 text-white animate-pulse" />

                {/* Floating Icons */}
                <div className="absolute -top-4 -right-4 animate-bounce animation-delay-1000">
                  <Trophy className="w-8 h-8 text-yellow-400" />
                </div>
                <div className="absolute -bottom-4 -left-4 animate-bounce animation-delay-2000">
                  <Crown className="w-8 h-8 text-purple-400" />
                </div>
                <div className="absolute -top-4 -left-4 animate-bounce animation-delay-3000">
                  <Award className="w-8 h-8 text-blue-400" />
                </div>
                <div className="absolute -bottom-4 -right-4 animate-bounce animation-delay-4000">
                  <Zap className="w-8 h-8 text-orange-400" />
                </div>
              </div>

              {/* Success Ring */}
              <div className="absolute inset-0 border-4 border-green-400 rounded-full animate-ping opacity-30"></div>
              <div className="absolute inset-2 border-2 border-emerald-300 rounded-full animate-ping opacity-20 animation-delay-1000"></div>
            </div>
          </div>

          {/* Success Message */}
          <div className={`text-center mb-8 transition-all duration-1000 delay-300 ${animationStep >= 2 ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-green-400 via-emerald-300 to-green-500 bg-clip-text text-transparent mb-4">
              🎉 تم إتمام البيع بنجاح! 🎉
            </h1>
            <p className="text-xl text-slate-300 mb-2">عملية بيع احترافية ومتميزة</p>
            <div className="flex items-center justify-center space-x-reverse space-x-2 text-slate-400">
              <Clock className="w-4 h-4" />
              <span className="text-sm">{saleData.timestamp.toLocaleString('fr-FR')}</span>
            </div>
          </div>

          {/* Sale Summary */}
          <div className={`bg-white/5 backdrop-blur-xl rounded-2xl p-6 border border-white/10 mb-6 transition-all duration-1000 delay-600 ${animationStep >= 3 ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
            <div className="grid grid-cols-2 gap-6">
              {/* Left Column */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-slate-300 flex items-center">
                    <DollarSign className="w-4 h-4 ml-2 text-green-400" />
                    الإجمالي:
                  </span>
                  <span className="font-bold text-2xl text-green-400">{saleData.total.toFixed(2)} دج</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-300 flex items-center">
                    <Target className="w-4 h-4 ml-2 text-blue-400" />
                    المدفوع:
                  </span>
                  <span className="font-bold text-xl text-white">{saleData.paid.toFixed(2)} دج</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-300 flex items-center">
                    <TrendingUp className="w-4 h-4 ml-2 text-purple-400" />
                    الباقي:
                  </span>
                  <span className="font-bold text-xl text-yellow-400">{saleData.change.toFixed(2)} دج</span>
                </div>
              </div>

              {/* Right Column */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-slate-300 flex items-center">
                    <Package className="w-4 h-4 ml-2 text-orange-400" />
                    عدد الأصناف:
                  </span>
                  <span className="font-bold text-xl text-white">{saleData.items.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-300 flex items-center">
                    <Heart className="w-4 h-4 ml-2 text-pink-400" />
                    طريقة الدفع:
                  </span>
                  <span className="font-bold text-xl text-white">
                    {saleData.paymentMethod === 'cash' ? 'نقداً' :
                     saleData.paymentMethod === 'credit' ? 'آجل' : 'مختلط'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-300 flex items-center">
                    <User className="w-4 h-4 ml-2 text-cyan-400" />
                    رقم الفاتورة:
                  </span>
                  <span className="font-bold text-xl text-white font-mono">#{saleData.saleNumber}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className={`transition-all duration-1000 delay-900 ${animationStep >= 4 ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'}`}>
            {/* Toggle Details */}
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="w-full bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl py-3 mb-4 text-white font-medium hover:bg-white/20 transition-all duration-300 flex items-center justify-center space-x-reverse space-x-2"
            >
              <Package className="w-5 h-5" />
              <span>{showDetails ? 'إخفاء تفاصيل الفاتورة' : 'عرض تفاصيل الفاتورة'}</span>
            </button>

            {/* Item Details */}
            {showDetails && (
              <div className="bg-white/5 backdrop-blur-lg rounded-xl p-4 mb-4 border border-white/10 max-h-40 overflow-y-auto">
                <h3 className="text-lg font-bold text-white mb-3 flex items-center">
                  <Package className="w-5 h-5 ml-2 text-blue-400" />
                  تفاصيل المنتجات
                </h3>
                <div className="space-y-2">
                  {saleData.items.map((item, index) => (
                    <div key={index} className="flex items-center justify-between py-2 border-b border-white/10 last:border-b-0">
                      <div className="flex-1">
                        <span className="text-white font-medium">{item.name}</span>
                        <span className="text-slate-400 text-sm ml-2">× {item.quantity}</span>
                      </div>
                      <span className="text-green-400 font-bold">{(item.price * item.quantity).toFixed(2)} دج</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Action Buttons Grid */}
            <div className="grid grid-cols-2 gap-4 mb-6">
              <button
                onClick={handlePrint}
                className="bg-gradient-to-r from-blue-500 to-purple-500 hover:scale-105 transform transition-all duration-300 py-3 rounded-xl font-bold text-white shadow-lg hover:shadow-blue-500/25 flex items-center justify-center space-x-reverse space-x-2"
              >
                <Printer className="w-5 h-5" />
                <span>طباعة الفاتورة</span>
              </button>
              <button
                onClick={handleShare}
                className="bg-gradient-to-r from-green-500 to-emerald-400 hover:scale-105 transform transition-all duration-300 py-3 rounded-xl font-bold text-white shadow-lg hover:shadow-green-500/25 flex items-center justify-center space-x-reverse space-x-2"
              >
                <Share2 className="w-5 h-5" />
                <span>مشاركة</span>
              </button>
              <button
                onClick={handleEmail}
                className="bg-gradient-to-r from-orange-500 to-red-400 hover:scale-105 transform transition-all duration-300 py-3 rounded-xl font-bold text-white shadow-lg hover:shadow-orange-500/25 flex items-center justify-center space-x-reverse space-x-2"
              >
                <Mail className="w-5 h-5" />
                <span>إرسال بالإيميل</span>
              </button>
              <button
                className="bg-gradient-to-r from-purple-500 to-pink-500 hover:scale-105 transform transition-all duration-300 py-3 rounded-xl font-bold text-white shadow-lg hover:shadow-purple-500/25 flex items-center justify-center space-x-reverse space-x-2"
              >
                <MessageCircle className="w-5 h-5" />
                <span>واتساب</span>
              </button>
            </div>

            {/* Close Button */}
            <button
              onClick={onClose}
              className="w-full bg-gradient-to-r from-slate-600 to-slate-700 hover:scale-105 transform transition-all duration-300 py-4 rounded-xl font-bold text-white shadow-lg flex items-center justify-center space-x-reverse space-x-3 relative group"
            >
              <Gift className="w-6 h-6" />
              <div className="flex flex-col items-center">
                <span>إغلاق والعودة للبيع</span>
                <span className="text-xs text-slate-300 opacity-75 group-hover:opacity-100 transition-opacity">
                  أو اضغط Escape
                </span>
              </div>
              <Sparkles className="w-5 h-5 animate-pulse" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SaleSuccessModal;
