const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 5001;

app.use(cors());
app.use(express.json());

console.log('🚀 بدء تشغيل السيرفر السريع...');

// اختبار الاتصال
app.get('/api/test', (req, res) => {
  console.log('📞 تم استلام طلب اختبار');
  res.json({ 
    message: '✅ Backend API يعمل بنجاح!',
    timestamp: new Date().toISOString()
  });
});

// مسار المدينين
app.get('/api/customers/debtors', (req, res) => {
  console.log('📞 تم استلام طلب المدينين');
  const mockDebtors = [
    {
      id: '82431516-ec6b-44a7-904c-52632c1ced43',
      name: 'أح<PERSON><PERSON> محمد',
      phone: '0555123456',
      email: '<EMAIL>',
      address: 'الرياض، المملكة العربية السعودية',
      balance: 0,
      credit_limit: 5000,
      total_debt: 1500,
      paid_amount: 500,
      remaining_debt: 1000,
      debt_count: 2,
      created_at: '2024-01-15T10:30:00Z',
      updated_at: '2024-01-20T14:45:00Z'
    }
  ];
  res.json(mockDebtors);
});

// مسار ديون عميل محدد
app.get('/api/customers/:id/debts', (req, res) => {
  console.log('📞 تم استلام طلب ديون العميل:', req.params.id);
  const mockDebts = [
    {
      id: 'debt-1',
      customer_id: req.params.id,
      amount: 1000,
      paid_amount: 0,
      status: 'pending',
      sale_id: 'sale-1',
      created_at: '2024-01-15T10:30:00Z',
      updated_at: '2024-01-15T10:30:00Z'
    }
  ];
  res.json(mockDebts);
});

// مسار تسديد الدين
app.post('/api/customers/:id/pay-debt', (req, res) => {
  console.log('📞 تم استلام طلب تسديد دين للعميل:', req.params.id);
  console.log('📊 بيانات الدفع:', req.body);
  
  const { debt_id, amount, payment_method, notes } = req.body;
  
  if (!debt_id || !amount || amount <= 0) {
    return res.status(400).json({ error: 'بيانات الدفع غير صحيحة' });
  }

  res.json({
    message: 'تم تسديد الدين بنجاح',
    paid_amount: parseFloat(amount),
    new_status: 'partial'
  });
});

try {
  app.listen(PORT, () => {
    console.log(`🚀 السيرفر السريع يعمل على المنفذ ${PORT}`);
    console.log(`🔗 API متاح على: http://localhost:${PORT}/api`);
    console.log('✅ السيرفر جاهز للاستخدام!');
  });
} catch (error) {
  console.error('❌ خطأ في تشغيل السيرفر:', error);
}
