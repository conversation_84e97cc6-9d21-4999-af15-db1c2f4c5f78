import React from 'react';
import BarcodeGenerator from './BarcodeGenerator';
import { useApp } from '../context/AppContext';

interface ThermalReceiptProps {
  saleData: {
    total: number;
    paid: number;
    change: number;
    items: Array<{
      name: string;
      quantity: number;
      price: number;
    }>;
    paymentMethod: string;
    customer?: string;
    saleNumber: string;
    timestamp: Date;
  };
  companyInfo?: {
    name: string;
    address: string;
    phone: string;
    email?: string;
    taxNumber?: string;
  };
}

const ThermalReceipt: React.FC<ThermalReceiptProps> = ({
  saleData,
  companyInfo
}) => {
  const { settings } = useApp();

  // استخدام معلومات المتجر من الإعدادات أو القيم الافتراضية
  const storeInfo = companyInfo || {
    name: settings?.storeName || "متجر توسار الإلكتروني",
    address: settings?.storeAddress || "الجزائر - الجزائر العاصمة",
    phone: settings?.storePhone || "+213 XXX XXX XXX",
    email: settings?.storeEmail || "<EMAIL>",
    taxNumber: settings?.storeTaxNumber || "*********"
  };
  const subtotal = saleData.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const tax = subtotal * 0.05; // 5% ضريبة
  const discount = subtotal + tax - saleData.total;

  return (
    <div className="thermal-receipt" style={{ display: 'none' }}>
      <div
        id="thermal-receipt-content"
        style={{
          width: '80mm',
          fontFamily: 'monospace',
          fontSize: '12px',
          lineHeight: '1.2',
          color: '#000',
          backgroundColor: '#fff',
          padding: '10px',
          margin: '0',
          textAlign: 'center'
        }}
      >
        {/* Header */}
        <div style={{ marginBottom: '15px', borderBottom: '2px solid #000', paddingBottom: '10px' }}>
          <div style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '8px', letterSpacing: '1px' }}>
            ═══ {storeInfo.name} ═══
          </div>
          <div style={{ fontSize: '11px', lineHeight: '1.4', marginBottom: '3px' }}>
            📍 {storeInfo.address}
          </div>
          <div style={{ fontSize: '11px', marginBottom: '3px' }}>
            📞 {storeInfo.phone}
          </div>
          {storeInfo.email && (
            <div style={{ fontSize: '11px', marginBottom: '3px' }}>
              📧 {storeInfo.email}
            </div>
          )}
          {storeInfo.taxNumber && (
            <div style={{ fontSize: '11px', marginBottom: '5px' }}>
              🏢 الرقم الضريبي: {storeInfo.taxNumber}
            </div>
          )}
          <div style={{ fontSize: '12px', fontWeight: 'bold', marginTop: '8px' }}>
            ★ ★ ★ فاتورة بيع ★ ★ ★
          </div>
        </div>

        {/* Sale Info */}
        <div style={{ marginBottom: '15px', textAlign: 'left', backgroundColor: '#f8f8f8', padding: '8px', border: '1px solid #ddd' }}>
          <div style={{ textAlign: 'center', fontWeight: 'bold', marginBottom: '8px', fontSize: '12px' }}>
            ═══ معلومات الفاتورة ═══
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px', fontSize: '11px' }}>
            <span>🧾 رقم الفاتورة:</span>
            <span style={{ fontWeight: 'bold', fontFamily: 'monospace' }}>{saleData.saleNumber}</span>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px', fontSize: '11px' }}>
            <span>📅 التاريخ:</span>
            <span>{saleData.timestamp.toLocaleDateString('fr-FR')}</span>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px', fontSize: '11px' }}>
            <span>🕐 الوقت:</span>
            <span>{saleData.timestamp.toLocaleTimeString('fr-FR')}</span>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px', fontSize: '11px' }}>
            <span>👤 الكاشير:</span>
            <span>المستخدم الحالي</span>
          </div>
          {saleData.customer && saleData.customer !== 'عميل عادي' && (
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px', fontSize: '11px' }}>
              <span>🤝 العميل:</span>
              <span style={{ fontWeight: 'bold' }}>
                {typeof saleData.customer === 'string' ? saleData.customer : saleData.customer?.name || 'عميل غير محدد'}
              </span>
            </div>
          )}
        </div>

        {/* Items */}
        <div style={{ marginBottom: '15px', borderTop: '2px dashed #000', paddingTop: '10px' }}>
          <div style={{ fontWeight: 'bold', marginBottom: '10px', textAlign: 'center', fontSize: '13px' }}>
            ═══ تفاصيل المشتريات ═══
          </div>

          {/* Items Header */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            fontWeight: 'bold',
            borderBottom: '1px solid #000',
            paddingBottom: '3px',
            marginBottom: '5px',
            fontSize: '10px'
          }}>
            <span style={{ width: '40%', textAlign: 'left' }}>الصنف</span>
            <span style={{ width: '15%', textAlign: 'center' }}>الكمية</span>
            <span style={{ width: '20%', textAlign: 'center' }}>السعر</span>
            <span style={{ width: '25%', textAlign: 'right' }}>الإجمالي</span>
          </div>

          {/* Items List */}
          {saleData.items.map((item, index) => (
            <div key={index} style={{ marginBottom: '8px' }}>
              {/* Item Name */}
              <div style={{
                fontSize: '11px',
                fontWeight: 'bold',
                marginBottom: '2px',
                textAlign: 'left',
                wordWrap: 'break-word'
              }}>
                {item.name}
              </div>

              {/* Item Details */}
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                fontSize: '10px'
              }}>
                <span style={{ width: '40%' }}></span>
                <span style={{ width: '15%', textAlign: 'center' }}>{item.quantity}</span>
                <span style={{ width: '20%', textAlign: 'center' }}>{item.price.toFixed(2)}</span>
                <span style={{ width: '25%', textAlign: 'right', fontWeight: 'bold' }}>
                  {(item.price * item.quantity).toFixed(2)}
                </span>
              </div>
            </div>
          ))}
        </div>

        {/* Totals */}
        <div style={{
          borderTop: '2px dashed #000',
          paddingTop: '10px',
          marginBottom: '15px',
          textAlign: 'left',
          backgroundColor: '#f8f8f8',
          padding: '10px',
          border: '1px solid #ddd'
        }}>
          <div style={{ textAlign: 'center', fontWeight: 'bold', marginBottom: '8px', fontSize: '12px' }}>
            ═══ ملخص الفاتورة ═══
          </div>

          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px', fontSize: '11px' }}>
            <span>💰 المجموع الفرعي:</span>
            <span>{subtotal.toFixed(2)} دج</span>
          </div>

          {discount > 0 && (
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px', fontSize: '11px' }}>
              <span>🏷️ الخصم:</span>
              <span style={{ color: '#d00' }}>-{discount.toFixed(2)} دج</span>
            </div>
          )}

          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px', fontSize: '11px' }}>
            <span>📊 الضريبة (5%):</span>
            <span>{tax.toFixed(2)} دج</span>
          </div>

          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            fontWeight: 'bold',
            fontSize: '14px',
            borderTop: '2px solid #000',
            paddingTop: '8px',
            marginTop: '8px',
            backgroundColor: '#000',
            color: '#fff',
            padding: '8px',
            margin: '8px -10px -10px -10px'
          }}>
            <span>🎯 الإجمالي النهائي:</span>
            <span>{saleData.total.toFixed(2)} دج</span>
          </div>
        </div>

        {/* Payment Info */}
        <div style={{
          borderTop: '2px dashed #000',
          paddingTop: '10px',
          marginBottom: '15px',
          textAlign: 'left',
          backgroundColor: '#f0f0f0',
          padding: '10px',
          border: '1px solid #ccc'
        }}>
          <div style={{ textAlign: 'center', fontWeight: 'bold', marginBottom: '8px', fontSize: '12px' }}>
            ═══ معلومات الدفع ═══
          </div>

          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px', fontSize: '11px' }}>
            <span>💳 طريقة الدفع:</span>
            <span style={{ fontWeight: 'bold' }}>
              {saleData.paymentMethod === 'cash' ? '💵 نقداً' :
               saleData.paymentMethod === 'credit' ? '📝 آجل' : '🔄 مختلط'}
            </span>
          </div>

          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px', fontSize: '11px' }}>
            <span>💰 المبلغ المدفوع:</span>
            <span style={{ fontWeight: 'bold' }}>{(saleData.paid || 0).toFixed(2)} دج</span>
          </div>

          {(saleData.change || 0) > 0 && (
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              fontWeight: 'bold',
              fontSize: '12px',
              backgroundColor: '#e8f5e8',
              padding: '5px',
              marginTop: '5px',
              border: '1px solid #4CAF50'
            }}>
              <span>💸 الباقي:</span>
              <span style={{ color: '#2E7D32' }}>{(saleData.change || 0).toFixed(2)} دج</span>
            </div>
          )}
        </div>

        {/* Footer */}
        <div style={{
          borderTop: '2px solid #000',
          paddingTop: '12px',
          textAlign: 'center',
          fontSize: '11px'
        }}>
          <div style={{ marginBottom: '8px', fontWeight: 'bold', fontSize: '13px' }}>
            ★ ★ ★ شكراً لتسوقكم معنا ★ ★ ★
          </div>
          <div style={{ marginBottom: '5px', fontSize: '10px' }}>
            🙏 نتطلع لخدمتكم مرة أخرى 🙏
          </div>
          <div style={{ marginBottom: '10px', fontSize: '10px' }}>
            📞 للاستفسارات: {storeInfo.phone}
          </div>

          {/* QR Code Placeholder */}
          <div style={{
            border: '2px solid #000',
            width: '70px',
            height: '70px',
            margin: '12px auto',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '9px',
            fontWeight: 'bold',
            backgroundColor: '#f9f9f9'
          }}>
            📱 QR CODE
          </div>

          <div style={{ fontSize: '9px', marginBottom: '10px' }}>
            📲 امسح الكود للتقييم والمراجعة
          </div>

          {/* Rating Stars */}
          <div style={{ fontSize: '12px', marginBottom: '8px' }}>
            ⭐ ⭐ ⭐ ⭐ ⭐
          </div>
          <div style={{ fontSize: '9px', marginBottom: '10px' }}>
            قيم تجربتك معنا
          </div>

          {/* Barcode */}
          <div style={{
            marginTop: '12px',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center'
          }}>
            <BarcodeGenerator
              value={saleData.saleNumber}
              width={160}
              height={35}
              displayValue={true}
              fontSize={8}
              textAlign="center"
              textPosition="bottom"
              background="#ffffff"
              lineColor="#000000"
            />
          </div>

          {/* Thank You Message */}
          <div style={{
            marginTop: '15px',
            fontSize: '10px',
            fontStyle: 'italic',
            border: '1px dashed #666',
            padding: '8px',
            backgroundColor: '#f8f8f8'
          }}>
            💝 شكراً لثقتكم بنا 💝<br/>
            🌟 خدمة عملاء متميزة 🌟<br/>
            🚀 جودة عالية وأسعار منافسة 🚀
          </div>
        </div>

        {/* Cut Line */}
        <div style={{
          marginTop: '20px',
          textAlign: 'center',
          fontSize: '10px',
          fontWeight: 'bold'
        }}>
          ✂️ ═══════════════════════════════ ✂️
        </div>

        {/* Final Note */}
        <div style={{
          marginTop: '10px',
          textAlign: 'center',
          fontSize: '8px',
          color: '#666'
        }}>
          هذه الفاتورة مطبوعة إلكترونياً ولا تحتاج لختم
        </div>
      </div>
    </div>
  );
};

// Print Function
export const printThermalReceipt = (saleData: any, companyInfo?: any) => {
  // Create a temporary container
  const printContainer = document.createElement('div');
  printContainer.innerHTML = `
    <style>
      @media print {
        @page {
          size: 80mm auto;
          margin: 0;
        }
        body {
          margin: 0;
          padding: 0;
          font-family: 'Courier New', monospace;
          font-size: 12px;
          line-height: 1.2;
        }
        .no-print {
          display: none !important;
        }
      }
    </style>
  `;

  // Add the receipt content
  const receiptElement = document.getElementById('thermal-receipt-content');
  if (receiptElement) {
    printContainer.appendChild(receiptElement.cloneNode(true));
  }

  // Create print window
  const printWindow = window.open('', '_blank');
  if (printWindow) {
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>فاتورة ${saleData.saleNumber}</title>
          <style>
            @page {
              size: 80mm auto;
              margin: 2mm;
            }
            body {
              margin: 0;
              padding: 0;
              font-family: 'Courier New', monospace;
              font-size: 12px;
              line-height: 1.2;
              color: #000;
              background: #fff;
            }
            * {
              box-sizing: border-box;
            }
          </style>
        </head>
        <body>
          ${printContainer.innerHTML}
        </body>
      </html>
    `);

    printWindow.document.close();
    printWindow.focus();

    // Wait for content to load then print
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 500);
  }
};

export default ThermalReceipt;
