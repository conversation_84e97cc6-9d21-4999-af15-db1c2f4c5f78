# 🔧 تعليمات تطبيق الإصلاح يدوياً

## الطريقة 1: استخدام pgAdmin 4

### الخطوة 1: فتح pgAdmin
1. افتح pgAdmin 4
2. اتصل بخادم PostgreSQL
3. انتقل إلى قاعدة البيانات `pos_system`

### الخطوة 2: فتح Query Tool
1. انقر بزر الماوس الأيمن على قاعدة البيانات `pos_system`
2. اختر "Query Tool"

### الخطوة 3: تشغيل الأوامر
انسخ والصق الأوامر التالية واحداً تلو الآخر:

```sql
-- 1. تعديل جدول sale_items لجعل product_id يقبل NULL
ALTER TABLE sale_items 
ALTER COLUMN product_id DROP NOT NULL;

-- 2. إضافة أعمدة جديدة للمنتجات غير المعروفة
ALTER TABLE sale_items 
ADD COLUMN IF NOT EXISTS is_unknown_product BOOLEAN DEFAULT FALSE;

ALTER TABLE sale_items 
ADD COLUMN IF NOT EXISTS unknown_product_code VARCHAR(50);

-- 3. إنشاء فئة خاصة للمنتجات غير المعروفة
INSERT INTO categories (
    id,
    name,
    description,
    is_active
) VALUES (
    '00000000-0000-0000-0000-000000000001'::UUID,
    'منتجات غير معروفة',
    'فئة خاصة للمنتجات غير المعروفة في النظام',
    true
) ON CONFLICT (id) DO NOTHING;

-- 4. إنشاء منتج افتراضي للمنتجات غير المعروفة
INSERT INTO products (
    id,
    name,
    description,
    barcode,
    category_id,
    price,
    cost,
    stock,
    min_stock,
    unit,
    is_active
) VALUES (
    '00000000-0000-0000-0000-000000000001'::UUID,
    'منتج غير معروف',
    'منتج افتراضي للعناصر غير المعروفة في النظام',
    'UNKNOWN_PRODUCT',
    '00000000-0000-0000-0000-000000000001'::UUID,
    0.00,
    0.00,
    999999,
    0,
    'قطعة',
    true
) ON CONFLICT (id) DO NOTHING;

-- 5. إنشاء فهرس للمنتجات غير المعروفة
CREATE INDEX IF NOT EXISTS idx_sale_items_unknown 
ON sale_items(is_unknown_product) 
WHERE is_unknown_product = TRUE;

-- 6. إنشاء view لإحصائيات المنتجات غير المعروفة
CREATE OR REPLACE VIEW unknown_products_stats AS
SELECT 
    COUNT(*) as total_unknown_sales,
    SUM(quantity) as total_unknown_quantity,
    SUM(total_price) as total_unknown_revenue,
    AVG(unit_price) as avg_unknown_price,
    DATE(created_at) as sale_date
FROM sale_items 
WHERE is_unknown_product = TRUE
GROUP BY DATE(created_at)
ORDER BY sale_date DESC;
```

### الخطوة 4: التحقق من النجاح
شغل هذا الاستعلام للتحقق:

```sql
SELECT 
    column_name,
    is_nullable,
    data_type
FROM information_schema.columns 
WHERE table_name = 'sale_items' 
AND column_name IN ('product_id', 'is_unknown_product', 'unknown_product_code')
ORDER BY column_name;
```

يجب أن ترى:
- `product_id` - `YES` (يقبل NULL)
- `is_unknown_product` - `YES` - `boolean`
- `unknown_product_code` - `YES` - `character varying`

## الطريقة 2: استخدام سطر الأوامر

إذا كان لديك PostgreSQL مثبت:

```bash
# Windows
"C:\Program Files\PostgreSQL\16\bin\psql.exe" -h localhost -p 5432 -U postgres -d pos_system -f fix_unknown_products.sql

# أو إذا كان psql في PATH
psql -h localhost -p 5432 -U postgres -d pos_system -f fix_unknown_products.sql
```

## اختبار النظام

بعد تطبيق الإصلاح:

1. **إعادة تشغيل الخادم**:
   ```bash
   npm run dev
   ```

2. **اختبار البيع**:
   - افتح شاشة البيع
   - اكتب `/25.50` في حقل البحث
   - اضغط Enter
   - يجب أن ترى: "✨ تم إضافة منتج غير معروف بقيمة 25.50 دج"

3. **إكمال البيع**:
   - أكمل عملية البيع
   - يجب أن تتم بنجاح بدون أخطاء

## التحقق من البيانات

```sql
-- عرض آخر المنتجات غير المعروفة المباعة
SELECT 
    product_name,
    unknown_product_code,
    quantity,
    unit_price,
    total_price,
    created_at
FROM sale_items 
WHERE is_unknown_product = TRUE
ORDER BY created_at DESC
LIMIT 5;
```

## في حالة الأخطاء

### خطأ: "relation does not exist"
- تأكد من أنك متصل بقاعدة البيانات الصحيحة `pos_system`

### خطأ: "permission denied"
- تأكد من أن لديك صلاحيات المدير في PostgreSQL

### خطأ: "column already exists"
- هذا طبيعي، يعني أن العمود موجود بالفعل

## رسالة النجاح

عند اكتمال الإصلاح بنجاح، ستتمكن من:
- ✅ بيع أي منتج حتى لو لم يكن في قاعدة البيانات
- ✅ تتبع المنتجات غير المعروفة
- ✅ عرض إحصائيات منفصلة
- ✅ حفظ آمن للبيانات

**🎉 مبروك! تم إصلاح المشكلة بطريقة سحرية!**
