# اختبار بسيط للمنتج غير المعروف
Write-Host "🧪 اختبار المنتج غير المعروف البسيط..." -ForegroundColor Cyan

$data = @{
    productName = "منتج تجريبي"
    price = 50.00
    quantity = 1
    paymentMethod = "cash"
} | ConvertTo-Json

Write-Host "📤 إرسال البيع..." -ForegroundColor Yellow

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5002/api/sales/unknown-product" -Method POST -Body $data -ContentType "application/json"
    
    Write-Host "✅ نجح البيع!" -ForegroundColor Green
    Write-Host "🆔 معرف البيع: $($response.sale.id)" -ForegroundColor Green
    Write-Host "🔢 رقم البيع: $($response.sale.sale_number)" -ForegroundColor Green
    
    # التحقق من قاعدة البيانات
    Write-Host "🔍 التحقق من المبيعات..." -ForegroundColor Yellow
    $allSales = Invoke-RestMethod -Uri "http://localhost:5002/api/sales" -Method GET
    
    $foundSale = $allSales | Where-Object { $_.id -eq $response.sale.id }
    
    if ($foundSale) {
        Write-Host "✅ البيع موجود في قائمة المبيعات!" -ForegroundColor Green
        Write-Host "💰 المبلغ: $($foundSale.total_amount) دج" -ForegroundColor Green
    } else {
        Write-Host "❌ البيع غير موجود في قائمة المبيعات!" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ فشل البيع:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}
