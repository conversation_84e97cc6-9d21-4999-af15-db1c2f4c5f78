-- إنشاء قاعدة البيانات الرئيسية
-- Create main database for POS system

-- إنشاء قاعدة البيانات
CREATE DATABASE pos_system_db
    WITH 
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'en_US.UTF-8'
    LC_CTYPE = 'en_US.UTF-8'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- الاتصال بقاعدة البيانات
\c pos_system_db;

-- إنشاء امتدادات مفيدة
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- إنشاء schema للنظام
CREATE SCHEMA IF NOT EXISTS pos_system;

-- تعيين المسار الافتراضي
SET search_path TO pos_system, public;

-- إن<PERSON><PERSON><PERSON> أنواع البيانات المخصصة (ENUMS)
CREATE TYPE user_role AS ENUM ('admin', 'manager', 'cashier');
CREATE TYPE payment_method AS ENUM ('cash', 'card', 'credit', 'mixed');
CREATE TYPE sale_status AS ENUM ('completed', 'pending', 'cancelled', 'refunded');
CREATE TYPE transaction_type AS ENUM ('income', 'expense');
CREATE TYPE connection_type AS ENUM ('usb', 'network', 'bluetooth');
CREATE TYPE paper_type AS ENUM ('thermal', 'normal');
CREATE TYPE print_density AS ENUM ('light', 'medium', 'dark');
CREATE TYPE print_speed AS ENUM ('slow', 'medium', 'fast');
CREATE TYPE font_size AS ENUM ('small', 'medium', 'large');
CREATE TYPE line_spacing AS ENUM ('tight', 'normal', 'loose');
CREATE TYPE theme_type AS ENUM ('dark', 'light');

-- تعليق على قاعدة البيانات
COMMENT ON DATABASE pos_system_db IS 'نظام نقطة البيع المتكامل - قاعدة البيانات الرئيسية';
COMMENT ON SCHEMA pos_system IS 'مخطط النظام الرئيسي لجميع الجداول';
