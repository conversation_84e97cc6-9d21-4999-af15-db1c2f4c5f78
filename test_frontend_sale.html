<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بيع منتج غير معروف</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #10b981;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #059669;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            white-space: pre-wrap;
        }
        .success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        .error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار بيع منتج غير معروف</h1>
        <p>هذا الاختبار يحاكي بيع منتج غير معروف من الواجهة الأمامية</p>
        
        <button onclick="testUnknownProductSale()">🔮 اختبار بيع منتج غير معروف</button>
        <button onclick="checkLastSale()">📊 فحص آخر بيع</button>
        <button onclick="testConnection()">🔗 اختبار الاتصال</button>
        
        <div id="result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5002/api';
        
        async function apiRequest(endpoint, options = {}) {
            const url = `${API_BASE_URL}${endpoint}`;
            const config = {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers,
                },
                ...options,
            };
            
            console.log('🔗 API Request:', url, config.method || 'GET');
            const response = await fetch(url, config);
            console.log('📡 Response Status:', response.status, response.statusText);
            
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }
            
            return await response.json();
        }
        
        async function testUnknownProductSale() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '⏳ جاري اختبار بيع منتج غير معروف...';
            
            try {
                const saleData = {
                    customer_id: null,
                    items: [
                        {
                            product_id: null, // منتج غير معروف
                            product_name: "🔮 منتج تجريبي غير معروف - 30.75 دج",
                            quantity: 1,
                            unit_price: 30.75,
                            discount: 0,
                            total_price: 30.75
                        }
                    ],
                    subtotal: 30.75,
                    tax_amount: 0,
                    discount_amount: 0,
                    total_amount: 30.75,
                    payment_method: 'cash',
                    amount_paid: 30.75,
                    change_amount: 0,
                    notes: 'اختبار منتج غير معروف من الواجهة',
                    cashier_name: 'اختبار النظام'
                };
                
                const response = await apiRequest('/sales', {
                    method: 'POST',
                    body: JSON.stringify(saleData)
                });
                
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `✅ نجح البيع!
📊 رقم البيع: ${response.sale_number}
💰 المبلغ الإجمالي: ${response.total_amount} دج
🔮 تم بيع منتج غير معروف بنجاح!
⏰ الوقت: ${new Date().toLocaleString('ar')}`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ فشل البيع:
${error.message}
⏰ الوقت: ${new Date().toLocaleString('ar')}`;
                console.error('❌ خطأ في البيع:', error);
            }
        }
        
        async function checkLastSale() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '⏳ جاري فحص آخر بيع...';
            
            try {
                const sales = await apiRequest('/sales');
                
                if (sales && sales.length > 0) {
                    const lastSale = sales[0];
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `📊 آخر بيع في قاعدة البيانات:
📋 رقم البيع: ${lastSale.sale_number}
💰 المبلغ: ${lastSale.total_amount} دج
💳 طريقة الدفع: ${lastSale.payment_method}
📅 التاريخ: ${new Date(lastSale.created_at).toLocaleString('ar')}
✅ البيانات محفوظة في قاعدة البيانات!`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ لا توجد مبيعات في قاعدة البيانات';
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ فشل في جلب المبيعات:
${error.message}`;
                console.error('❌ خطأ في جلب المبيعات:', error);
            }
        }
        
        async function testConnection() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '⏳ جاري اختبار الاتصال...';
            
            try {
                const response = await apiRequest('/test');
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `✅ الاتصال يعمل بنجاح!
📡 رسالة الخادم: ${response.message}
⏰ وقت الخادم: ${new Date(response.timestamp).toLocaleString('ar')}`;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ فشل الاتصال:
${error.message}`;
                console.error('❌ خطأ في الاتصال:', error);
            }
        }
    </script>
</body>
</html>
