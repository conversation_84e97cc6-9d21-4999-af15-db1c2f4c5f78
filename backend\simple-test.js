const pool = require('./database');

async function simpleTest() {
  try {
    console.log('Testing connection...');
    const result = await pool.query('SELECT NOW()');
    console.log('✅ Connection successful:', result.rows[0]);
    
    // Check if customer_debts table exists
    const tableCheck = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'pos_system' 
        AND table_name = 'customer_debts'
      );
    `);
    console.log('Table exists:', tableCheck.rows[0].exists);
    
    if (tableCheck.rows[0].exists) {
      // Check table structure
      const columns = await pool.query(`
        SELECT column_name FROM information_schema.columns 
        WHERE table_schema = 'pos_system' AND table_name = 'customer_debts'
      `);
      console.log('Columns:', columns.rows.map(r => r.column_name));
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
  process.exit(0);
}

simpleTest();
