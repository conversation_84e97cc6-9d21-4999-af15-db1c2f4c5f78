const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 5000;

// Middleware
app.use(cors());
app.use(express.json());

console.log('🚀 بدء تشغيل سيرفر الأقسام...');

// اختبار الاتصال
app.get('/api/test', (req, res) => {
  console.log('📞 تم استلام طلب اختبار');
  res.json({ 
    message: '✅ Backend API يعمل بنجاح!',
    timestamp: new Date().toISOString()
  });
});

// مسار الأقسام المؤقت
app.get('/api/categories', (req, res) => {
  console.log('📂 تم استلام طلب الأقسام');
  
  // إرجاع بيانات تجريبية
  const mockCategories = [
    {
      id: '1',
      name: 'إلكترونيات',
      description: 'أجهزة إلكترونية',
      color: '#3b82f6',
      icon: 'Laptop',
      is_active: true,
      created_at: new Date().toISOString()
    },
    {
      id: '2', 
      name: 'ملابس',
      description: 'ملابس وأزياء',
      color: '#10b981',
      icon: 'Shirt',
      is_active: true,
      created_at: new Date().toISOString()
    }
  ];
  
  console.log('✅ إرسال بيانات الأقسام:', mockCategories.length, 'قسم');
  res.json(mockCategories);
});

app.post('/api/categories', (req, res) => {
  console.log('📂 تم استلام طلب إضافة قسم');
  console.log('البيانات المرسلة:', req.body);
  
  // إرجاع القسم الجديد
  const newCategory = {
    id: Date.now().toString(),
    name: req.body.name,
    description: req.body.description || '',
    color: req.body.color || '#3b82f6',
    icon: req.body.icon || 'Package',
    is_active: true,
    created_at: new Date().toISOString()
  };
  
  console.log('✅ تم إنشاء القسم:', newCategory);
  res.status(201).json(newCategory);
});

// مسار المنتجات المؤقت
app.get('/api/products', (req, res) => {
  console.log('📦 تم استلام طلب المنتجات');
  res.json([]);
});

app.post('/api/products', (req, res) => {
  console.log('📦 تم استلام طلب إضافة منتج');
  console.log('البيانات المرسلة:', req.body);
  
  const newProduct = {
    id: Date.now().toString(),
    name: req.body.name,
    description: req.body.description || '',
    barcode: req.body.barcode || '',
    category_id: req.body.category_id,
    price: req.body.price,
    cost: req.body.cost,
    stock: req.body.stock || 0,
    min_stock: req.body.min_stock || 0,
    is_active: true,
    created_at: new Date().toISOString()
  };
  
  console.log('✅ تم إنشاء المنتج:', newProduct);
  res.status(201).json(newProduct);
});

// مسار المدينين
app.get('/api/customers/debtors', (req, res) => {
  console.log('👥 تم استلام طلب المدينين');
  
  const mockDebtors = [
    {
      id: '82431516-ec6b-44a7-904c-52632c1ced43',
      name: 'عميل مدين',
      phone: '0123456789',
      email: '',
      address: 'عنوان العميل',
      balance: 0,
      credit_limit: 1000,
      created_at: '2025-06-14T13:54:33.437Z',
      updated_at: '2025-06-14T13:54:33.437Z',
      total_debt: 29.75,
      paid_amount: 0,
      remaining_debt: 29.75,
      debt_count: 1
    }
  ];
  
  console.log('✅ إرسال بيانات المدينين:', mockDebtors.length, 'مدين');
  res.json(mockDebtors);
});

// معالج الأخطاء العام
app.use((error, req, res, next) => {
  console.error('❌ خطأ عام:', error);
  res.status(500).json({ error: 'خطأ داخلي في الخادم' });
});

app.listen(PORT, () => {
  console.log(`🚀 السيرفر المبسط يعمل على المنفذ ${PORT}`);
  console.log(`🔗 API متاح على: http://localhost:${PORT}/api`);
});

// معالج إغلاق الخادم
process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف الخادم...');
  process.exit(0);
});
