@echo off
chcp 65001 >nul
title Kasheer Toosar - POS System
color 0A

echo.
echo ========================================
echo    Kasheer Toosar - POS System  
echo ========================================
echo.

REM Go to project directory
cd /d "%~dp0"

REM Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Node.js not found! Please install Node.js first.
    echo Download from: https://nodejs.org
    pause
    exit /b 1
)

echo Node.js found ✓

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo Failed to install dependencies!
        pause
        exit /b 1
    )
    echo Dependencies installed ✓
)

REM Build project using npx vite
echo Building project...
npx vite build
if %errorlevel% neq 0 (
    echo Build failed! Trying alternative method...
    
    REM Try direct vite command
    .\node_modules\.bin\vite.cmd build
    if %errorlevel% neq 0 (
        echo Build failed completely!
        echo Make sure all dependencies are installed.
        pause
        exit /b 1
    )
)

echo Build completed ✓

REM Start server
echo Starting server...
start /min "Kasheer Server" node backend\server.js

REM Wait and check server
timeout /t 3 /nobreak >nul

REM Simple server check
:check_server
echo Checking server...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:5003' -TimeoutSec 3 -UseBasicParsing; if($response.StatusCode -eq 200) { exit 0 } else { exit 1 } } catch { exit 1 }" >nul 2>&1

if %errorlevel% equ 0 (
    echo Server is running ✓
    goto success
)

timeout /t 2 /nobreak >nul
goto check_server

:success
echo Opening browser...
start http://localhost:5003

echo.
echo ========================================
echo   SUCCESS! System is running
echo   URL: http://localhost:5003
echo ========================================
echo.
echo Press any key to open system folder...
pause >nul
explorer .

echo.
echo Keep this window open to monitor the system.
echo Close this window to stop the server.
echo.
pause
