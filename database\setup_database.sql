-- سكريبت الإعداد الشامل لقاعدة البيانات
-- Complete Database Setup Script

-- تشغيل جميع ملفات الإعداد بالترتيب الصحيح

\echo 'بدء إعداد قاعدة البيانات...'
\echo 'Starting database setup...'

-- 1. إنشاء قاعدة البيانات والإعدادات الأساسية
\echo '1. إنشاء قاعدة البيانات...'
\i 01_create_database.sql

-- 2. إنشاء الجداول الأساسية
\echo '2. إنشاء الجداول الأساسية...'
\i 02_core_tables.sql

-- 3. إنشاء جداول المبيعات والمعاملات
\echo '3. إنشاء جداول المبيعات...'
\i 03_sales_tables.sql

-- 4. إن<PERSON>اء المشغلات والوظائف
\echo '4. إنشاء المشغلات والوظائف...'
\i 04_triggers_functions.sql

-- 5. إنشاء Views للتقارير
\echo '5. إنشاء Views للتقارير...'
\i 05_views_reports.sql

-- 6. إعداد الأمان والصلاحيات
\echo '6. إعداد الأمان والصلاحيات...'
\i 06_security_permissions.sql

-- إدراج بيانات تجريبية (اختيارية)
\echo '7. إدراج بيانات تجريبية...'

-- فئات تجريبية
INSERT INTO categories (name, description, color, icon) VALUES
('إلكترونيات', 'أجهزة إلكترونية ومعدات تقنية', '#3b82f6', 'Package'),
('ملابس', 'ملابس رجالية ونسائية وأطفال', '#ec4899', 'Tag'),
('طعام ومشروبات', 'مواد غذائية ومشروبات متنوعة', '#f59e0b', 'Palette'),
('مستحضرات تجميل', 'منتجات العناية والجمال', '#8b5cf6', 'AlertCircle'),
('رياضة', 'معدات ومستلزمات رياضية', '#06b6d4', 'MoreVertical')
ON CONFLICT (name) DO NOTHING;

-- عملاء تجريبيين
INSERT INTO customers (name, phone, address, balance, credit_limit) VALUES
('أحمد محمد', '+213555123456', 'الجزائر العاصمة', 0.00, 5000.00),
('فاطمة علي', '+213555234567', 'وهران', 0.00, 3000.00),
('محمد حسن', '+213555345678', 'قسنطينة', 0.00, 2000.00)
ON CONFLICT DO NOTHING;

-- موردين تجريبيين
INSERT INTO suppliers (name, phone, address, balance) VALUES
('شركة التقنية المتقدمة', '+213555111222', 'الجزائر العاصمة', 0.00),
('مؤسسة الملابس العصرية', '+213555333444', 'وهران', 0.00),
('شركة المواد الغذائية', '+213555555666', 'قسنطينة', 0.00)
ON CONFLICT DO NOTHING;

-- منتجات تجريبية
WITH category_ids AS (
    SELECT id, name FROM categories LIMIT 5
)
INSERT INTO products (name, description, barcode, category_id, price, cost, stock, min_stock, unit)
SELECT 
    product_data.name,
    product_data.description,
    product_data.barcode,
    c.id,
    product_data.price,
    product_data.cost,
    product_data.stock,
    product_data.min_stock,
    product_data.unit
FROM (VALUES
    ('لابتوب Dell', 'لابتوب Dell Inspiron 15', '1234567890123', 'إلكترونيات', 85000.00, 70000.00, 10, 2, 'قطعة'),
    ('قميص قطني', 'قميص قطني أزرق مقاس L', '2345678901234', 'ملابس', 2500.00, 1800.00, 50, 10, 'قطعة'),
    ('عصير برتقال', 'عصير برتقال طبيعي 1 لتر', '3456789012345', 'طعام ومشروبات', 180.00, 120.00, 100, 20, 'علبة'),
    ('كريم مرطب', 'كريم مرطب للوجه 50مل', '4567890123456', 'مستحضرات تجميل', 1200.00, 800.00, 30, 5, 'قطعة'),
    ('كرة قدم', 'كرة قدم جلدية حجم 5', '5678901234567', 'رياضة', 3500.00, 2500.00, 15, 3, 'قطعة')
) AS product_data(name, description, barcode, category_name, price, cost, stock, min_stock, unit)
JOIN category_ids c ON c.name = product_data.category_name
ON CONFLICT (barcode) DO NOTHING;

-- إنشاء وظائف مساعدة للتطبيق
CREATE OR REPLACE FUNCTION get_dashboard_stats()
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'today_sales', COALESCE((
            SELECT SUM(total_amount) 
            FROM sales 
            WHERE DATE(created_at) = CURRENT_DATE AND status = 'completed'
        ), 0),
        'today_orders', COALESCE((
            SELECT COUNT(*) 
            FROM sales 
            WHERE DATE(created_at) = CURRENT_DATE AND status = 'completed'
        ), 0),
        'total_products', (SELECT COUNT(*) FROM products WHERE is_active = true),
        'low_stock_products', (SELECT COUNT(*) FROM products WHERE stock <= min_stock AND is_active = true),
        'total_customers', (SELECT COUNT(*) FROM customers WHERE is_active = true),
        'pending_debts', COALESCE((
            SELECT SUM(remaining_amount) 
            FROM customer_debts 
            WHERE is_paid = false
        ), 0)
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- وظيفة البحث في المنتجات
CREATE OR REPLACE FUNCTION search_products(search_term TEXT)
RETURNS TABLE (
    id UUID,
    name VARCHAR,
    barcode VARCHAR,
    category_name VARCHAR,
    price DECIMAL,
    stock INTEGER,
    unit VARCHAR
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.name,
        p.barcode,
        COALESCE(c.name, 'غير محدد') as category_name,
        p.price,
        p.stock,
        p.unit
    FROM products p
    LEFT JOIN categories c ON p.category_id = c.id
    WHERE p.is_active = true
    AND (
        p.name ILIKE '%' || search_term || '%' OR
        p.barcode ILIKE '%' || search_term || '%' OR
        p.description ILIKE '%' || search_term || '%'
    )
    ORDER BY p.name;
END;
$$ LANGUAGE plpgsql;

-- إنشاء فهارس إضافية للأداء
CREATE INDEX IF NOT EXISTS idx_sales_date_status ON sales(created_at, status);
CREATE INDEX IF NOT EXISTS idx_financial_transactions_date_type ON financial_transactions(transaction_date, type);
CREATE INDEX IF NOT EXISTS idx_products_search ON products USING gin(to_tsvector('arabic', name || ' ' || COALESCE(description, '')));

-- تحديث إحصائيات الجداول
ANALYZE;

\echo 'تم إعداد قاعدة البيانات بنجاح!'
\echo 'Database setup completed successfully!'

-- عرض ملخص الإعداد
SELECT 
    'الجداول' as component,
    COUNT(*) as count
FROM information_schema.tables 
WHERE table_schema = 'pos_system'

UNION ALL

SELECT 
    'الـ Views' as component,
    COUNT(*) as count
FROM information_schema.views 
WHERE table_schema = 'pos_system'

UNION ALL

SELECT 
    'الوظائف' as component,
    COUNT(*) as count
FROM information_schema.routines 
WHERE routine_schema = 'pos_system'

UNION ALL

SELECT 
    'المشغلات' as component,
    COUNT(*) as count
FROM information_schema.triggers 
WHERE trigger_schema = 'pos_system';

\echo 'ملخص قاعدة البيانات:'
\echo 'Database Summary:'
