const { Pool } = require('pg');

// الاتصال بقاعدة postgres الافتراضية
const adminPool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'postgres',
  password: 'toossar',
  port: 5432,
});

async function createDatabase() {
  try {
    console.log('🔧 إنشاء قاعدة البيانات pos_system...');
    
    // إنشاء قاعدة البيانات
    await adminPool.query('CREATE DATABASE pos_system');
    console.log('✅ تم إنشاء قاعدة البيانات pos_system بنجاح!');
    
  } catch (error) {
    if (error.message.includes('already exists')) {
      console.log('✅ قاعدة البيانات pos_system موجودة بالفعل');
    } else {
      console.error('❌ خطأ في إنشاء قاعدة البيانات:', error.message);
    }
  } finally {
    await adminPool.end();
  }
}

createDatabase();
