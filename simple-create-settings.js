const { Pool } = require('pg');

const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'pos_system_db',
  user: 'postgres',
  password: 'toossar'
});

async function createSettings() {
  try {
    console.log('Creating settings table...');
    
    await pool.query(`
      CREATE TABLE IF NOT EXISTS settings (
        id SERIAL PRIMARY KEY,
        store_name VARCHAR(255) DEFAULT '',
        store_address TEXT DEFAULT '',
        store_phone VARCHAR(50) DEFAULT '',
        store_email VARCHAR(255) DEFAULT '',
        store_tax_number VARCHAR(100) DEFAULT '',
        store_logo TEXT DEFAULT '',
        user_name VARCHAR(255) DEFAULT '',
        user_email VARCHAR(255) DEFAULT '',
        user_role VARCHAR(50) DEFAULT 'admin',
        currency VARCHAR(10) DEFAULT 'DZD',
        language VARCHAR(10) DEFAULT 'ar',
        timezone VARCHAR(50) DEFAULT 'Africa/Algiers',
        date_format VARCHAR(20) DEFAULT 'DD/MM/YYYY',
        tax_rate DECIMAL(5,2) DEFAULT 19.00,
        enable_tax BOOLEAN DEFAULT true,
        enable_discount BOOLEAN DEFAULT true,
        enable_barcode BOOLEAN DEFAULT true,
        low_stock_alert BOOLEAN DEFAULT true,
        email_notifications BOOLEAN DEFAULT false,
        sound_notifications BOOLEAN DEFAULT true,
        theme VARCHAR(20) DEFAULT 'dark',
        primary_color VARCHAR(20) DEFAULT '#3b82f6',
        font_size VARCHAR(20) DEFAULT 'medium',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    console.log('Settings table created successfully!');
    
    // Insert default settings
    const result = await pool.query('SELECT COUNT(*) FROM settings');
    if (parseInt(result.rows[0].count) === 0) {
      await pool.query(`
        INSERT INTO settings (store_name, store_address, store_phone, store_email, user_name)
        VALUES ('متجر توسار الإلكتروني', 'الجزائر - الجزائر العاصمة', '+213 XXX XXX XXX', '<EMAIL>', 'المدير')
      `);
      console.log('Default settings inserted!');
    }
    
    console.log('Done!');
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

createSettings();
