const { Client } = require('pg');
require('dotenv').config();

async function createDatabase() {
  // الاتصال بقاعدة postgres الافتراضية لإنشاء قاعدة البيانات الجديدة
  const client = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: 'postgres', // قاعدة البيانات الافتراضية
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'toossar',
  });

  try {
    console.log('🔗 محاولة الاتصال بـ PostgreSQL...');
    await client.connect();
    console.log('✅ تم الاتصال بـ PostgreSQL بنجاح');

    // التحقق من وجود قاعدة البيانات
    const checkDbResult = await client.query(
      "SELECT 1 FROM pg_database WHERE datname = $1",
      ['clean_setup']
    );

    if (checkDbResult.rows.length === 0) {
      console.log('📦 إنشاء قاعدة البيانات clean_setup...');
      await client.query('CREATE DATABASE clean_setup');
      console.log('✅ تم إنشاء قاعدة البيانات clean_setup بنجاح');
    } else {
      console.log('✅ قاعدة البيانات clean_setup موجودة بالفعل');
    }

  } catch (error) {
    console.error('❌ خطأ في إنشاء قاعدة البيانات:', error.message);
    
    if (error.message.includes('password authentication failed')) {
      console.log('💡 تأكد من كلمة مرور PostgreSQL في ملف .env');
    } else if (error.message.includes('could not connect')) {
      console.log('💡 تأكد من تشغيل خدمة PostgreSQL');
    }
  } finally {
    await client.end();
  }
}

// تشغيل الدالة
createDatabase();
