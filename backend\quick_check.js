const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
});

async function quickCheck() {
  try {
    // تعيين schema
    await pool.query(`SET search_path TO ${process.env.DB_SCHEMA}, public`);
    
    console.log('🔍 فحص سريع للبيانات...\n');
    
    // فحص المبيعات
    const sales = await pool.query('SELECT COUNT(*) as count FROM sales WHERE status = \'completed\'');
    console.log('المبيعات المكتملة:', sales.rows[0].count);
    
    // فحص المنتجات
    const products = await pool.query('SELECT COUNT(*) as count, AVG(cost) as avg_cost FROM products WHERE cost > 0');
    console.log('المنتجات التي لها تكلفة:', products.rows[0].count);
    console.log('متوسط التكلفة:', parseFloat(products.rows[0].avg_cost || 0));
    
    // فحص عناصر المبيعات
    const items = await pool.query(`
      SELECT COUNT(*) as count 
      FROM sale_items si 
      JOIN sales s ON si.sale_id = s.id 
      WHERE s.status = 'completed'
    `);
    console.log('عناصر المبيعات:', items.rows[0].count);
    
    // حساب الربح
    const profit = await pool.query(`
      SELECT 
        COALESCE(SUM((si.unit_price - COALESCE(p.cost, 0)) * si.quantity), 0) as total_profit
      FROM sale_items si
      JOIN sales s ON si.sale_id = s.id
      LEFT JOIN products p ON si.product_id = p.id
      WHERE s.status = 'completed'
    `);
    console.log('إجمالي الأرباح:', parseFloat(profit.rows[0].total_profit));
    
    // عينة من البيانات
    const sample = await pool.query(`
      SELECT 
        p.name, p.cost, p.price, si.unit_price, si.quantity,
        (si.unit_price - COALESCE(p.cost, 0)) * si.quantity as profit
      FROM sale_items si
      JOIN sales s ON si.sale_id = s.id
      LEFT JOIN products p ON si.product_id = p.id
      WHERE s.status = 'completed'
      LIMIT 3
    `);
    
    console.log('\nعينة من البيانات:');
    sample.rows.forEach((row, i) => {
      console.log(`${i+1}. ${row.name}: تكلفة=${row.cost}, سعر=${row.unit_price}, كمية=${row.quantity}, ربح=${parseFloat(row.profit || 0)}`);
    });
    
  } catch (error) {
    console.error('خطأ:', error.message);
  } finally {
    await pool.end();
  }
}

quickCheck();
