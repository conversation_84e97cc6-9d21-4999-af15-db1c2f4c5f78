// اختبار إصلاح فواتير المشتريات
const { Pool } = require('pg');

const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'pos_system_db',
  user: 'postgres',
  password: 'toossar',
});

async function testPurchaseInvoices() {
  try {
    console.log('🔍 اختبار جداول فواتير المشتريات...');
    
    // اختبار وجود جدول purchase_invoices
    const tableCheck = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'pos_system' 
        AND table_name = 'purchase_invoices'
      );
    `);
    
    console.log('✅ جدول purchase_invoices موجود:', tableCheck.rows[0].exists);
    
    // اختبار وجود جدول purchase_invoice_items
    const itemsTableCheck = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'pos_system' 
        AND table_name = 'purchase_invoice_items'
      );
    `);
    
    console.log('✅ جدول purchase_invoice_items موجود:', itemsTableCheck.rows[0].exists);
    
    // جلب عدد الفواتير الموجودة
    const countResult = await pool.query('SELECT COUNT(*) as count FROM pos_system.purchase_invoices');
    console.log('📊 عدد فواتير المشتريات:', countResult.rows[0].count);
    
    // جلب عدد عناصر الفواتير
    const itemsCountResult = await pool.query('SELECT COUNT(*) as count FROM pos_system.purchase_invoice_items');
    console.log('📦 عدد عناصر الفواتير:', itemsCountResult.rows[0].count);
    
    console.log('✅ اختبار الجداول مكتمل بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
  } finally {
    await pool.end();
  }
}

testPurchaseInvoices();
