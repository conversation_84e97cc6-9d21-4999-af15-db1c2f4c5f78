const pool = require('./database');

async function checkProductsTable() {
  try {
    console.log('🔍 فحص هيكل جدول المنتجات...');
    
    // فحص أعمدة جدول المنتجات
    const columnsResult = await pool.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_schema = 'pos_system' 
      AND table_name = 'products'
      ORDER BY ordinal_position
    `);
    
    console.log('📋 أعمدة جدول المنتجات:');
    columnsResult.rows.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} (${col.is_nullable}) [${col.column_default || 'no default'}]`);
    });
    
    // فحص عدد المنتجات
    const countResult = await pool.query('SELECT COUNT(*) FROM pos_system.products');
    console.log('\n📊 عدد المنتجات:', countResult.rows[0].count);
    
    // عرض بعض المنتجات الموجودة
    const sampleResult = await pool.query('SELECT * FROM pos_system.products LIMIT 3');
    console.log('\n📦 عينة من المنتجات:');
    sampleResult.rows.forEach((product, index) => {
      console.log(`${index + 1}. ${product.name} - السعر: ${product.price}`);
    });
    
  } catch (error) {
    console.error('❌ خطأ في فحص الجدول:', error.message);
  }
  
  process.exit(0);
}

checkProductsTable();
