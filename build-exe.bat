@echo off
title 🏗️ بناء ملف التثبيت - كشير توسار
echo.
echo ========================================
echo 🏪 بناء ملف التثبيت - كشير توسار
echo ========================================
echo.

echo 📋 الخطوات المطلوبة:
echo 1. بناء الواجهة الأمامية
echo 2. تحضير ملفات Electron
echo 3. إنشاء ملف .exe و .msi
echo.

pause

echo 🚀 بدء العملية...
echo.

REM الخطوة 1: بناء الواجهة الأمامية
echo 📦 الخطوة 1: بناء الواجهة الأمامية...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ خطأ في بناء الواجهة الأمامية
    pause
    exit /b 1
)
echo ✅ تم بناء الواجهة الأمامية بنجاح
echo.

REM الخطوة 2: تثبيت Electron
echo 🔧 الخطوة 2: تثبيت Electron...
if not exist "node_modules\electron" (
    echo 📦 تثبيت Electron...
    call npm install electron electron-builder --save-dev
    if %errorlevel% neq 0 (
        echo ❌ خطأ في تثبيت Electron
        pause
        exit /b 1
    )
)
echo ✅ Electron جاهز
echo.

REM الخطوة 3: نسخ ملفات Electron
echo 📁 الخطوة 3: تحضير ملفات Electron...
copy electron-main.js main.js > nul
copy electron-package.json package-electron.json > nul

REM إنشاء مجلد assets إذا لم يكن موجوداً
if not exist "assets" mkdir assets

REM إنشاء أيقونة افتراضية إذا لم تكن موجودة
if not exist "assets\icon.ico" (
    echo 🎨 إنشاء أيقونة افتراضية...
    echo. > assets\icon.ico
)

echo ✅ تم تحضير الملفات
echo.

REM الخطوة 4: بناء التطبيق
echo 🏗️ الخطوة 4: بناء ملف التثبيت...
echo.
echo 📋 اختر نوع الملف المطلوب:
echo 1. ملف .exe (NSIS Installer)
echo 2. ملف .msi (Windows Installer)
echo 3. كلاهما
echo.
set /p choice="اختر (1/2/3): "

if "%choice%"=="1" goto build_exe
if "%choice%"=="2" goto build_msi
if "%choice%"=="3" goto build_both
goto invalid_choice

:build_exe
echo 🔨 بناء ملف .exe...
call npx electron-builder --win nsis
goto build_complete

:build_msi
echo 🔨 بناء ملف .msi...
call npx electron-builder --win msi
goto build_complete

:build_both
echo 🔨 بناء ملف .exe و .msi...
call npx electron-builder --win
goto build_complete

:invalid_choice
echo ❌ اختيار غير صحيح
pause
exit /b 1

:build_complete
if %errorlevel% neq 0 (
    echo.
    echo ❌ حدث خطأ أثناء البناء
    echo.
    echo 🔧 حلول مقترحة:
    echo 1. تأكد من تثبيت Node.js الإصدار 16 أو أحدث
    echo 2. تأكد من وجود ملف package.json صحيح
    echo 3. تأكد من وجود الأيقونات في مجلد assets
    echo 4. جرب تشغيل الأمر: npm install electron-builder --save-dev
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo 🎉 تم إنشاء ملف التثبيت بنجاح!
echo ========================================
echo.
echo 📁 الملفات موجودة في مجلد: dist
echo.
echo 📋 الملفات المنشأة:
dir dist\*.exe 2>nul
dir dist\*.msi 2>nul
echo.
echo 🚀 يمكنك الآن توزيع هذه الملفات على أجهزة العملاء
echo.
echo 📝 ملاحظات مهمة:
echo - تأكد من تثبيت PostgreSQL على جهاز العميل
echo - تأكد من تثبيت Node.js على جهاز العميل
echo - قم بإنشاء قاعدة البيانات قبل التشغيل
echo.

pause
