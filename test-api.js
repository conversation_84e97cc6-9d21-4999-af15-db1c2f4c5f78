// اختبار API الإحصائيات
const https = require('https');
const http = require('http');

function testDashboardStats() {
  console.log('🧪 اختبار API الإحصائيات...');

  const options = {
    hostname: 'localhost',
    port: 5002,
    path: '/api/financial/dashboard-stats',
    method: 'GET'
  };

  const req = http.request(options, (res) => {
    let data = '';

    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      try {
        const result = JSON.parse(data);

        if (result.success) {
          console.log('\n✅ نجح الاختبار!');
          console.log('📊 الإحصائيات الصحيحة:');
          console.log(`   طلبات اليوم: ${result.stats.today.orders} طلب`);
          console.log(`   إجمالي الطلبات: ${result.stats.total.orders} طلب`);
          console.log(`   مبيعات اليوم: ${result.stats.today.sales} دج`);
          console.log(`   إجمالي المبيعات: ${result.stats.total.revenue} دج`);
          console.log(`   عدد المنتجات: ${result.stats.total.products} منتج`);

          console.log('\n🎯 أفضل المنتجات:');
          result.stats.top_products.forEach((product, index) => {
            console.log(`   ${index + 1}. ${product.product_name}: ${product.quantity} قطعة`);
          });

        } else {
          console.log('❌ فشل الاختبار:', result.error);
        }

      } catch (error) {
        console.log('❌ خطأ في تحليل JSON:', error.message);
      }
    });
  });

  req.on('error', (error) => {
    console.log('❌ خطأ في الطلب:', error.message);
  });

  req.end();
}

testDashboardStats();
