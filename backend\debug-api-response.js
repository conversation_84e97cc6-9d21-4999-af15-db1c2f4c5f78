const fetch = require('node-fetch');

async function debugApiResponse() {
  console.log('🔍 فحص استجابة API للمبيعات...');
  
  try {
    const response = await fetch('http://localhost:5003/api/sales');
    const data = await response.json();
    
    if (data.success && data.sales) {
      console.log(`✅ تم جلب ${data.sales.length} مبيعة`);
      
      // البحث عن المبيعات بدون أسماء عملاء
      const salesWithoutCustomerName = data.sales.filter(sale => 
        !sale.customerName || sale.customerName === null || sale.customerName === 'null'
      );
      
      console.log(`\n📊 المبيعات بدون أسماء عملاء: ${salesWithoutCustomerName.length}`);
      
      if (salesWithoutCustomerName.length > 0) {
        console.log('\n🔍 تفاصيل المبيعات بدون أسماء عملاء:');
        salesWithoutCustomerName.slice(0, 5).forEach((sale, index) => {
          console.log(`\n${index + 1}. بيع رقم: ${sale.sale_number}`);
          console.log(`   - customerName: ${JSON.stringify(sale.customerName)}`);
          console.log(`   - customer_name: ${JSON.stringify(sale.customer_name)}`);
          console.log(`   - customer_id: ${JSON.stringify(sale.customer_id)}`);
          console.log(`   - total_amount: ${sale.total_amount}`);
          console.log(`   - payment_method: ${sale.payment_method}`);
          console.log(`   - created_at: ${sale.created_at}`);
        });
      }
      
      // عرض بعض المبيعات مع أسماء عملاء للمقارنة
      const salesWithCustomerName = data.sales.filter(sale => 
        sale.customerName && sale.customerName !== null && sale.customerName !== 'null' && sale.customerName !== 'عميل غير معروف'
      );
      
      console.log(`\n✅ المبيعات مع أسماء عملاء: ${salesWithCustomerName.length}`);
      
      if (salesWithCustomerName.length > 0) {
        console.log('\n📋 أمثلة على المبيعات مع أسماء عملاء:');
        salesWithCustomerName.slice(0, 3).forEach((sale, index) => {
          console.log(`\n${index + 1}. بيع رقم: ${sale.sale_number}`);
          console.log(`   - customerName: ${JSON.stringify(sale.customerName)}`);
          console.log(`   - customer_name: ${JSON.stringify(sale.customer_name)}`);
          console.log(`   - customer_id: ${JSON.stringify(sale.customer_id)}`);
        });
      }
      
      // التحقق من المبيعات التي تحتوي على "عميل غير معروف"
      const unknownCustomerSales = data.sales.filter(sale => 
        sale.customerName === 'عميل غير معروف'
      );
      
      console.log(`\n🔍 المبيعات مع "عميل غير معروف": ${unknownCustomerSales.length}`);
      
      if (unknownCustomerSales.length > 0) {
        console.log('\n📋 أمثلة على المبيعات مع "عميل غير معروف":');
        unknownCustomerSales.slice(0, 3).forEach((sale, index) => {
          console.log(`\n${index + 1}. بيع رقم: ${sale.sale_number}`);
          console.log(`   - customerName: ${JSON.stringify(sale.customerName)}`);
          console.log(`   - customer_name: ${JSON.stringify(sale.customer_name)}`);
          console.log(`   - customer_id: ${JSON.stringify(sale.customer_id)}`);
        });
      }
      
    } else {
      console.log('❌ خطأ في API:', data.error || 'استجابة غير صحيحة');
    }
    
  } catch (error) {
    console.error('❌ خطأ في الاتصال بـ API:', error.message);
  }
}

// تشغيل الفحص
if (require.main === module) {
  debugApiResponse()
    .then(() => {
      console.log('\n🎉 انتهى فحص API!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل فحص API:', error);
      process.exit(1);
    });
}

module.exports = debugApiResponse;
