const pool = require('./database');

async function cleanExpenses() {
  try {
    console.log('🧹 حذف البيانات الوهمية...');
    
    const result = await pool.query('DELETE FROM pos_system.expenses');
    console.log(`✅ تم حذف ${result.rowCount} مصروف وهمي`);
    
    const count = await pool.query('SELECT COUNT(*) as count FROM pos_system.expenses');
    console.log(`📊 عدد المصروفات المتبقية: ${count.rows[0].count}`);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ خطأ:', error.message);
    process.exit(1);
  }
}

cleanExpenses();
