-- إصلاح trigger توليد رقم الفاتورة الاحترافي لفواتير المشتريات

-- حذف الـ trigger والوظيفة القديمة إن وجدت
DROP TRIGGER IF EXISTS trigger_generate_purchase_number ON pos_system.purchase_invoices;
DROP FUNCTION IF EXISTS pos_system.generate_purchase_number();

-- إنشاء sequence جديد لأرقام الفواتير
DROP SEQUENCE IF EXISTS pos_system.purchase_number_seq;
CREATE SEQUENCE pos_system.purchase_number_seq START 1;

-- إنشاء وظيفة توليد رقم الفاتورة الاحترافي
CREATE OR REPLACE FUNCTION pos_system.generate_purchase_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.invoice_number IS NULL OR NEW.invoice_number = '' THEN
        NEW.invoice_number := 'PUR-' || TO_CHAR(CURRENT_DATE, 'YYYY') || '-' || 
                             LPAD(NEXTVAL('pos_system.purchase_number_seq')::TEXT, 4, '0');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إنشاء trigger جديد
CREATE TRIGGER trigger_generate_purchase_number
    BEFORE INSERT ON pos_system.purchase_invoices
    FOR EACH ROW EXECUTE FUNCTION pos_system.generate_purchase_number();

-- تحديث الفواتير الموجودة لتحمل أرقام احترافية (إذا كانت تحمل UUID)
UPDATE pos_system.purchase_invoices 
SET invoice_number = 'PUR-' || TO_CHAR(CURRENT_DATE, 'YYYY') || '-' || 
                    LPAD(ROW_NUMBER() OVER (ORDER BY created_at)::TEXT, 4, '0')
WHERE invoice_number ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
   OR invoice_number LIKE 'فاتورة-%';

-- إعادة تعيين sequence للرقم التالي
SELECT setval('pos_system.purchase_number_seq', 
              (SELECT COUNT(*) FROM pos_system.purchase_invoices) + 1);

-- عرض النتائج
SELECT 'تم إصلاح trigger توليد أرقام الفواتير بنجاح' as message;
SELECT COUNT(*) as total_invoices FROM pos_system.purchase_invoices;
SELECT invoice_number, created_at FROM pos_system.purchase_invoices ORDER BY created_at DESC LIMIT 5;
