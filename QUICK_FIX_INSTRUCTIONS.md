# 🚀 إصلاح سريع للمشكلة

## 🔧 المشكلة
الجداول `unknown_sales` و `unknown_products` غير موجودة في قاعدة البيانات.

## ✅ الحل السريع

### 1. افتح pgAdmin 4
- اتصل بقاعدة البيانات `pos_system_db`
- انقر بالزر الأيمن على قاعدة البيانات
- اختر **Query Tool**

### 2. شغل الكود
- انسخ محتوى ملف `SIMPLE_FIX.sql`
- الصق في Query Tool
- اضغط **F5** أو **Execute**

### 3. أعد تشغيل الخادم
```bash
cd backend
npm run dev
```

### 4. أعد تحميل الصفحة
- اضغط **F5** في المتصفح
- أو أعد تشغيل React: `npm start`

## 🧪 اختبار النظام

### في شاشة البيع:
1. اكتب `/100` واضغط Enter
2. أكمل عملية البيع
3. تحقق من لوحة التحكم

### النتائج المتوقعة:
- ✅ لا أخطاء في Console
- ✅ إحصائيات محدثة في لوحة التحكم
- ✅ ظهور المبيعات في صفحة الطلبات

## 🔍 فحص قاعدة البيانات

```sql
-- فحص الجداول
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'pos_system' 
AND table_name LIKE '%unknown%';

-- فحص البيانات
SELECT COUNT(*) as unknown_sales_count FROM pos_system.unknown_sales;
SELECT COUNT(*) as unknown_products_count FROM pos_system.unknown_products;
```

## 🆘 إذا استمرت المشكلة

### تحقق من:
1. **اتصال قاعدة البيانات**: تأكد من أن الخادم متصل بـ `pos_system_db`
2. **المخطط**: تأكد من وجود مخطط `pos_system`
3. **الصلاحيات**: تأكد من صلاحيات المستخدم

### أعد إنشاء قاعدة البيانات:
```sql
-- إذا لزم الأمر
DROP SCHEMA IF EXISTS pos_system CASCADE;
CREATE SCHEMA pos_system;
-- ثم شغل SIMPLE_FIX.sql
```

## ✅ علامات النجاح

عند النجاح ستجد:
- 🔮 لا أخطاء في Console
- 📊 إحصائيات محدثة
- 📋 مبيعات ظاهرة في الطلبات
- 💾 بيانات محفوظة في قاعدة البيانات
