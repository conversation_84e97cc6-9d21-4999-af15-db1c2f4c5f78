import React, { useState, useEffect } from 'react';
import {
  TrendingUp,
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  Calendar,
  DollarSign,
  Receipt,
  Tag,
  ShoppingCart,
  CreditCard,
  Banknote,
  ArrowUpRight,
  Target,
  Activity,
  CheckCircle,
  Clock,
  AlertTriangle,
  BarChart3,
  <PERSON><PERSON>hart,
  Calculator,
  Users,
  FileText,
  Download,
  RefreshCw,
  XCircle,
  Filter,
  Star,
  Award,
  Zap,
  TrendingDown,
  ArrowDownRight,
  Building,
  Package,
  Wallet,
  Gift,
  Crown,
  Layers,
  MoreVertical
} from 'lucide-react';

interface Income {
  id: string;
  transaction_number?: string;
  title: string;
  description: string;
  amount: number;
  source: 'sales' | 'debt_payment' | 'service' | 'other';
  category: string;
  date: Date;
  paymentMethod: 'cash' | 'transfer' | 'card' | 'check';
  customerName?: string;
  customerId?: string;
  receiptNumber?: string;
  status: 'confirmed' | 'pending' | 'cancelled';
  tags: string[];
  createdBy: string;
  notes?: string;
  relatedOrderId?: string;
  relatedDebtId?: string;
  reference_id?: string;
  reference_type?: string;
  transaction_date?: string;
  created_at?: string;
  updated_at?: string;
}

interface IncomeCategory {
  id: string;
  name: string;
  color: string;
  icon: React.ReactNode;
  totalAmount: number;
  percentage: number;
}

interface IncomeStats {
  totalIncome: number;
  todayIncome: number;
  monthlyIncome: number;
  yearlyIncome: number;
  salesIncome: number;
  debtPayments: number;
  averageIncome: number;
  topSource: string;
  growthRate: number;
}

interface YearlyData {
  year: number;
  totalIncome: number;
  salesIncome: number;
  debtPayments: number;
  otherIncome: number;
  growthRate: number;
}

const Income: React.FC = () => {
  const [incomes, setIncomes] = useState<Income[]>([]);
  const [categories, setCategories] = useState<IncomeCategory[]>([]);
  const [yearlyData, setYearlyData] = useState<YearlyData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSource, setSelectedSource] = useState<'all' | 'sales' | 'debt_payment' | 'service' | 'other'>('all');
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'confirmed' | 'pending' | 'cancelled'>('all');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<'all' | 'cash' | 'transfer' | 'card' | 'check'>('all');
  const [sortBy, setSortBy] = useState<'date' | 'amount' | 'source'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedIncome, setSelectedIncome] = useState<Income | null>(null);
  const [dateFilter, setDateFilter] = useState<'today' | 'week' | 'month' | 'quarter' | 'year' | 'all'>('month');
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [realIncomeData, setRealIncomeData] = useState<any>(null);

  const [newIncome, setNewIncome] = useState({
    title: '',
    description: '',
    amount: 0,
    source: 'service' as 'service' | 'other',
    category: '',
    customerName: '',
    customerId: '',
    paymentMethod: 'cash' as 'cash' | 'transfer' | 'card' | 'check',
    receiptNumber: '',
    notes: '',
    tags: [] as string[]
  });

  // توليد قائمة السنوات (السنة الحالية + 10 سنوات ماضية)
  const availableYears = Array.from({ length: 11 }, (_, i) => new Date().getFullYear() - i);

  // جلب البيانات المالية الحقيقية من API
  const fetchRealIncomeData = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('🔄 جلب البيانات المالية الحقيقية...');

      const response = await fetch('/api/financial/overview');
      if (!response.ok) {
        throw new Error('فشل في جلب البيانات المالية');
      }

      const data = await response.json();
      console.log('✅ تم جلب البيانات المالية:', data);
      setRealIncomeData(data);

      // تحويل البيانات إلى تنسيق المداخيل
      await convertRealDataToIncomes(data);
    } catch (err) {
      console.error('❌ خطأ في جلب البيانات المالية:', err);
      setError(err instanceof Error ? err.message : 'خطأ غير معروف');
    } finally {
      setLoading(false);
    }
  };

  // تحويل البيانات الحقيقية إلى تنسيق المداخيل
  const convertRealDataToIncomes = async (financialData: any) => {
    try {
      const convertedIncomes: Income[] = [];

      // جلب المعاملات المالية من نوع income
      const transactionsResponse = await fetch('/api/financial/transactions?type=income');
      if (transactionsResponse.ok) {
        const transactions = await transactionsResponse.json();

        transactions.forEach((transaction: any) => {
          convertedIncomes.push({
            id: transaction.id,
            transaction_number: transaction.transaction_number,
            title: transaction.description,
            description: transaction.notes || transaction.description,
            amount: parseFloat(transaction.amount),
            source: transaction.category === 'مبيعات' ? 'sales' :
                   transaction.category === 'دفع ديون' ? 'debt_payment' :
                   transaction.category === 'خدمات' ? 'service' : 'other',
            category: transaction.category,
            date: new Date(transaction.transaction_date),
            paymentMethod: transaction.payment_method === 'نقدي' ? 'cash' :
                          transaction.payment_method === 'تحويل' ? 'transfer' :
                          transaction.payment_method === 'بطاقة' ? 'card' :
                          transaction.payment_method === 'شيك' ? 'check' : 'cash',
            status: 'confirmed',
            tags: [],
            createdBy: 'النظام',
            reference_id: transaction.reference_id,
            reference_type: transaction.reference_type,
            transaction_date: transaction.transaction_date,
            created_at: transaction.created_at,
            updated_at: transaction.updated_at
          });
        });
      }

      // إضافة المبيعات كمداخيل (إذا لم تكن موجودة في المعاملات)
      if (financialData.revenue?.breakdown?.sales?.cash_sales > 0) {
        const salesIncome: Income = {
          id: 'sales-summary',
          title: 'إجمالي المبيعات النقدية',
          description: 'المبيعات المكتملة نقدياً',
          amount: financialData.revenue.breakdown.sales.cash_sales,
          source: 'sales',
          category: 'مبيعات',
          date: new Date(),
          paymentMethod: 'cash',
          status: 'confirmed',
          tags: ['تلقائي', 'مبيعات'],
          createdBy: 'النظام'
        };
        convertedIncomes.push(salesIncome);
      }

      // إضافة تحصيل الديون كمداخيل
      if (financialData.revenue?.breakdown?.debt_payments?.total_collected > 0) {
        const debtPaymentsIncome: Income = {
          id: 'debt-payments-summary',
          title: 'إجمالي تحصيل الديون',
          description: `تم تحصيل ${financialData.revenue.breakdown.debt_payments.payments_count} دفعة`,
          amount: financialData.revenue.breakdown.debt_payments.total_collected,
          source: 'debt_payment',
          category: 'دفع ديون',
          date: new Date(),
          paymentMethod: 'cash',
          status: 'confirmed',
          tags: ['تلقائي', 'ديون'],
          createdBy: 'النظام'
        };
        convertedIncomes.push(debtPaymentsIncome);
      }

      setIncomes(convertedIncomes);
      console.log('✅ تم تحويل البيانات إلى مداخيل:', convertedIncomes.length);
    } catch (err) {
      console.error('❌ خطأ في تحويل البيانات:', err);
    }
  };

  // تحميل البيانات عند بداية التشغيل
  useEffect(() => {
    fetchRealIncomeData();
  }, []);

  // إضافة دخل جديد
  const handleAddIncome = async () => {
    try {
      setLoading(true);
      console.log('🔄 إضافة دخل جديد:', newIncome);

      // تحضير البيانات للإرسال
      const incomeData = {
        type: 'income',
        description: newIncome.title,
        amount: parseFloat(newIncome.amount.toString()),
        category: newIncome.category || 'أخرى',
        payment_method: newIncome.paymentMethod === 'cash' ? 'نقدي' :
                       newIncome.paymentMethod === 'transfer' ? 'تحويل' :
                       newIncome.paymentMethod === 'card' ? 'بطاقة' :
                       newIncome.paymentMethod === 'check' ? 'شيك' : 'نقدي',
        transaction_date: new Date().toISOString().split('T')[0],
        notes: newIncome.notes || newIncome.description,
        reference_type: newIncome.source,
        reference_id: newIncome.customerId || null
      };

      const response = await fetch('/api/financial/transactions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(incomeData),
      });

      if (!response.ok) {
        throw new Error('فشل في إضافة الدخل');
      }

      const result = await response.json();
      console.log('✅ تم إضافة الدخل بنجاح:', result);

      // إعادة تحميل البيانات
      await fetchRealIncomeData();

      // إعادة تعيين النموذج
      setNewIncome({
        title: '',
        description: '',
        amount: 0,
        source: 'other',
        category: '',
        paymentMethod: 'cash',
        customerName: '',
        customerId: '',
        receiptNumber: '',
        notes: ''
      });

      setShowAddModal(false);
      alert('✅ تم إضافة الدخل بنجاح!');
    } catch (err) {
      console.error('❌ خطأ في إضافة الدخل:', err);
      alert('❌ فشل في إضافة الدخل: ' + (err instanceof Error ? err.message : 'خطأ غير معروف'));
    } finally {
      setLoading(false);
    }
  };

  // حساب الإحصائيات
  const stats: IncomeStats = {
    totalIncome: incomes.reduce((sum, income) => sum + income.amount, 0),
    todayIncome: incomes
      .filter(income => {
        const incomeDate = new Date(income.date);
        const today = new Date();
        return incomeDate.toDateString() === today.toDateString();
      })
      .reduce((sum, income) => sum + income.amount, 0),
    monthlyIncome: incomes
      .filter(income => {
        const incomeDate = new Date(income.date);
        const now = new Date();
        return incomeDate.getMonth() === now.getMonth() && incomeDate.getFullYear() === now.getFullYear();
      })
      .reduce((sum, income) => sum + income.amount, 0),
    yearlyIncome: incomes
      .filter(income => {
        const incomeDate = new Date(income.date);
        return incomeDate.getFullYear() === selectedYear;
      })
      .reduce((sum, income) => sum + income.amount, 0),
    salesIncome: incomes
      .filter(income => income.source === 'sales')
      .reduce((sum, income) => sum + income.amount, 0),
    debtPayments: incomes
      .filter(income => income.source === 'debt_payment')
      .reduce((sum, income) => sum + income.amount, 0),
    averageIncome: incomes.length > 0 ? incomes.reduce((sum, income) => sum + income.amount, 0) / incomes.length : 0,
    topSource: 'المبيعات',
    growthRate: 0 // سيتم حسابها لاحقاً
  };

  // فلترة المداخيل
  const filteredIncomes = incomes
    .filter(income => {
      const matchesSearch = income.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           income.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (income.customerName && income.customerName.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesSource = selectedSource === 'all' || income.source === selectedSource;
      const matchesStatus = selectedStatus === 'all' || income.status === selectedStatus;
      const matchesPaymentMethod = selectedPaymentMethod === 'all' || income.paymentMethod === selectedPaymentMethod;

      const incomeDate = new Date(income.date);
      const now = new Date();
      let matchesDate = true;

      switch (dateFilter) {
        case 'today':
          matchesDate = incomeDate.toDateString() === now.toDateString();
          break;
        case 'week':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          matchesDate = incomeDate >= weekAgo;
          break;
        case 'month':
          matchesDate = incomeDate.getMonth() === now.getMonth() && incomeDate.getFullYear() === now.getFullYear();
          break;
        case 'quarter':
          const quarter = Math.floor(now.getMonth() / 3);
          const incomeQuarter = Math.floor(incomeDate.getMonth() / 3);
          matchesDate = incomeQuarter === quarter && incomeDate.getFullYear() === now.getFullYear();
          break;
        case 'year':
          matchesDate = incomeDate.getFullYear() === selectedYear;
          break;
        default:
          matchesDate = true;
      }

      return matchesSearch && matchesSource && matchesStatus && matchesPaymentMethod && matchesDate;
    })
    .sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'date':
          aValue = a.date.getTime();
          bValue = b.date.getTime();
          break;
        case 'amount':
          aValue = a.amount;
          bValue = b.amount;
          break;
        case 'source':
          aValue = a.source.toLowerCase();
          bValue = b.source.toLowerCase();
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

  const formatCurrency = (amount: number) => `${amount.toLocaleString()} دج`;
  const formatDate = (date: Date) => date.toLocaleDateString('ar-DZ');
  const formatDateTime = (date: Date) => `${date.toLocaleDateString('ar-DZ')} ${date.toLocaleTimeString('ar-DZ')}`;

  const getSourceColor = (source: string) => {
    switch (source) {
      case 'sales': return 'text-green-400 bg-green-500/20 border-green-400/30';
      case 'debt_payment': return 'text-blue-400 bg-blue-500/20 border-blue-400/30';
      case 'service': return 'text-purple-400 bg-purple-500/20 border-purple-400/30';
      case 'other': return 'text-yellow-400 bg-yellow-500/20 border-yellow-400/30';
      default: return 'text-gray-400 bg-gray-500/20 border-gray-400/30';
    }
  };

  const getSourceText = (source: string) => {
    switch (source) {
      case 'sales': return 'مبيعات';
      case 'debt_payment': return 'دفع دين';
      case 'service': return 'خدمات';
      case 'other': return 'أخرى';
      default: return 'غير محدد';
    }
  };

  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'sales': return <ShoppingCart className="w-4 h-4" />;
      case 'debt_payment': return <CreditCard className="w-4 h-4" />;
      case 'service': return <Star className="w-4 h-4" />;
      case 'other': return <Gift className="w-4 h-4" />;
      default: return <DollarSign className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'text-green-400 bg-green-500/20 border-green-400/30';
      case 'pending': return 'text-yellow-400 bg-yellow-500/20 border-yellow-400/30';
      case 'cancelled': return 'text-red-400 bg-red-500/20 border-red-400/30';
      default: return 'text-gray-400 bg-gray-500/20 border-gray-400/30';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'confirmed': return 'مؤكد';
      case 'pending': return 'معلق';
      case 'cancelled': return 'ملغي';
      default: return 'غير محدد';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed': return <CheckCircle className="w-4 h-4" />;
      case 'pending': return <Clock className="w-4 h-4" />;
      case 'cancelled': return <XCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'cash': return <Banknote className="w-4 h-4 text-green-400" />;
      case 'transfer': return <ArrowUpRight className="w-4 h-4 text-blue-400" />;
      case 'card': return <CreditCard className="w-4 h-4 text-purple-400" />;
      case 'check': return <FileText className="w-4 h-4 text-orange-400" />;
      default: return <DollarSign className="w-4 h-4 text-gray-400" />;
    }
  };

  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case 'cash': return 'نقدي';
      case 'transfer': return 'تحويل';
      case 'card': return 'بطاقة';
      case 'check': return 'شيك';
      default: return 'غير محدد';
    }
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-green-900 to-slate-900 p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="bg-gradient-to-r from-green-500 to-emerald-600 p-3 rounded-2xl shadow-lg animate-pulse">
              <TrendingUp className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">📈 إدارة المداخيل</h1>
              <p className="text-gray-300">تتبع وإدارة جميع مصادر الدخل للشركة</p>
              <div className="mt-3 p-3 bg-green-500/10 border border-green-400/20 rounded-lg">
                <p className="text-green-300 text-sm">
                  💡 <strong>ملاحظة ذكية:</strong> المبيعات والديون تُضاف تلقائياً من صفحاتها المخصصة.
                  استخدم "إضافة دخل إضافي" للخدمات والاستشارات والإيجارات وغيرها.
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-4 space-x-reverse">
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-gradient-to-r from-green-500 to-emerald-600 hover:scale-105 transform transition-all duration-300 px-6 py-3 rounded-xl font-bold text-white shadow-2xl hover:shadow-green-500/25 flex items-center space-x-2 space-x-reverse"
            >
              <Plus className="w-5 h-5" />
              <span>إضافة دخل إضافي</span>
            </button>

            <button className="bg-white/10 backdrop-blur-lg border border-white/20 hover:border-white/40 px-4 py-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse text-white">
              <Download className="w-5 h-5" />
              <span>تصدير</span>
            </button>

            <button
              onClick={fetchRealIncomeData}
              disabled={loading}
              className="bg-white/10 backdrop-blur-lg border border-white/20 hover:border-white/40 px-4 py-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse text-white disabled:opacity-50"
            >
              <RefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
              <span>{loading ? 'جاري التحديث...' : 'تحديث'}</span>
            </button>
          </div>
        </div>

        {/* إحصائيات المداخيل */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-green-400/50 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">إجمالي المداخيل</p>
                <p className="text-3xl font-bold text-white">{formatCurrency(stats.totalIncome)}</p>
                <div className="flex items-center space-x-1 space-x-reverse mt-2">
                  <TrendingUp className="w-4 h-4 text-green-400" />
                  <span className="text-green-400 text-sm font-semibold">جميع الأوقات</span>
                </div>
              </div>
              <div className="bg-green-500/20 p-3 rounded-xl">
                <Calculator className="w-6 h-6 text-green-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-green-400/50 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">دخل اليوم</p>
                <p className="text-3xl font-bold text-white">{formatCurrency(stats.todayIncome)}</p>
                <div className="flex items-center space-x-1 space-x-reverse mt-2">
                  <Calendar className="w-4 h-4 text-blue-400" />
                  <span className="text-blue-400 text-sm font-semibold">اليوم</span>
                </div>
              </div>
              <div className="bg-blue-500/20 p-3 rounded-xl">
                <BarChart3 className="w-6 h-6 text-blue-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-green-400/50 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">دخل الشهر</p>
                <p className="text-3xl font-bold text-white">{formatCurrency(stats.monthlyIncome)}</p>
                <div className="flex items-center space-x-1 space-x-reverse mt-2">
                  <Target className="w-4 h-4 text-purple-400" />
                  <span className="text-purple-400 text-sm font-semibold">هذا الشهر</span>
                </div>
              </div>
              <div className="bg-purple-500/20 p-3 rounded-xl">
                <Target className="w-6 h-6 text-purple-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-green-400/50 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">دخل السنة</p>
                <p className="text-3xl font-bold text-white">{formatCurrency(stats.yearlyIncome)}</p>
                <div className="flex items-center space-x-1 space-x-reverse mt-2">
                  <Award className="w-4 h-4 text-yellow-400" />
                  <span className="text-yellow-400 text-sm font-semibold">{selectedYear}</span>
                </div>
              </div>
              <div className="bg-yellow-500/20 p-3 rounded-xl">
                <Award className="w-6 h-6 text-yellow-400" />
              </div>
            </div>
          </div>
        </div>

        {/* إحصائيات مصادر الدخل */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <h3 className="text-xl font-bold text-white mb-4 flex items-center space-x-2 space-x-reverse">
              <ShoppingCart className="w-6 h-6 text-green-400" />
              <span>المبيعات</span>
            </h3>
            <div className="text-center">
              <p className="text-3xl font-bold text-green-400 mb-2">{formatCurrency(stats.salesIncome)}</p>
              <p className="text-gray-300 text-sm">المصدر الرئيسي للدخل</p>
              <div className="w-full bg-gray-700 rounded-full h-2 mt-4">
                <div
                  className="bg-gradient-to-r from-green-500 to-emerald-600 h-2 rounded-full transition-all duration-1000"
                  style={{ width: stats.totalIncome > 0 ? `${(stats.salesIncome / stats.totalIncome) * 100}%` : '0%' }}
                ></div>
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <h3 className="text-xl font-bold text-white mb-4 flex items-center space-x-2 space-x-reverse">
              <CreditCard className="w-6 h-6 text-blue-400" />
              <span>دفع الديون</span>
            </h3>
            <div className="text-center">
              <p className="text-3xl font-bold text-blue-400 mb-2">{formatCurrency(stats.debtPayments)}</p>
              <p className="text-gray-300 text-sm">الديون المحصلة</p>
              <div className="w-full bg-gray-700 rounded-full h-2 mt-4">
                <div
                  className="bg-gradient-to-r from-blue-500 to-cyan-600 h-2 rounded-full transition-all duration-1000"
                  style={{ width: stats.totalIncome > 0 ? `${(stats.debtPayments / stats.totalIncome) * 100}%` : '0%' }}
                ></div>
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
            <h3 className="text-xl font-bold text-white mb-4 flex items-center space-x-2 space-x-reverse">
              <Activity className="w-6 h-6 text-purple-400" />
              <span>متوسط الدخل</span>
            </h3>
            <div className="text-center">
              <p className="text-3xl font-bold text-purple-400 mb-2">{formatCurrency(stats.averageIncome)}</p>
              <p className="text-gray-300 text-sm">لكل معاملة</p>
              <div className="flex items-center justify-center space-x-2 space-x-reverse mt-4">
                <Star className="w-5 h-5 text-yellow-400" />
                <span className="text-yellow-400 text-sm font-semibold">أداء ممتاز</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* شريط البحث والفلاتر الذكية */}
      <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 mb-8">
        <div className="grid grid-cols-1 lg:grid-cols-7 gap-4">
          {/* البحث */}
          <div className="lg:col-span-2">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="البحث في المداخيل..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-white/10 border border-white/20 rounded-xl px-12 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 transition-all duration-300"
              />
            </div>
          </div>

          {/* فلتر الفترة */}
          <div>
            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value as any)}
              className="w-full bg-green-900/30 border border-green-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-green-400 transition-all duration-300 cursor-pointer hover:bg-green-800/40"
              style={{
                backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2310b981' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'left 12px center',
                backgroundSize: '16px'
              }}
            >
              <option value="all" className="bg-green-900 text-white">كل الأوقات</option>
              <option value="today" className="bg-green-900 text-white">اليوم</option>
              <option value="week" className="bg-green-900 text-white">هذا الأسبوع</option>
              <option value="month" className="bg-green-900 text-white">هذا الشهر</option>
              <option value="quarter" className="bg-green-900 text-white">هذا الربع</option>
              <option value="year" className="bg-green-900 text-white">هذا العام</option>
            </select>
          </div>

          {/* فلتر السنة */}
          <div>
            <select
              value={selectedYear}
              onChange={(e) => setSelectedYear(parseInt(e.target.value))}
              className="w-full bg-green-900/30 border border-green-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-green-400 transition-all duration-300 cursor-pointer hover:bg-green-800/40"
              style={{
                backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2310b981' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'left 12px center',
                backgroundSize: '16px'
              }}
            >
              {availableYears.map(year => (
                <option key={year} value={year} className="bg-green-900 text-white">{year}</option>
              ))}
            </select>
          </div>

          {/* فلتر المصدر */}
          <div>
            <select
              value={selectedSource}
              onChange={(e) => setSelectedSource(e.target.value as any)}
              className="w-full bg-green-900/30 border border-green-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-green-400 transition-all duration-300 cursor-pointer hover:bg-green-800/40"
              style={{
                backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2310b981' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'left 12px center',
                backgroundSize: '16px'
              }}
            >
              <option value="all" className="bg-green-900 text-white">جميع المصادر</option>
              <option value="sales" className="bg-green-900 text-white">مبيعات</option>
              <option value="debt_payment" className="bg-green-900 text-white">دفع ديون</option>
              <option value="service" className="bg-green-900 text-white">خدمات</option>
              <option value="other" className="bg-green-900 text-white">أخرى</option>
            </select>
          </div>

          {/* فلتر طريقة الدفع */}
          <div>
            <select
              value={selectedPaymentMethod}
              onChange={(e) => setSelectedPaymentMethod(e.target.value as any)}
              className="w-full bg-green-900/30 border border-green-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-green-400 transition-all duration-300 cursor-pointer hover:bg-green-800/40"
              style={{
                backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2310b981' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'left 12px center',
                backgroundSize: '16px'
              }}
            >
              <option value="all" className="bg-green-900 text-white">جميع الطرق</option>
              <option value="cash" className="bg-green-900 text-white">نقدي</option>
              <option value="transfer" className="bg-green-900 text-white">تحويل</option>
              <option value="card" className="bg-green-900 text-white">بطاقة</option>
              <option value="check" className="bg-green-900 text-white">شيك</option>
            </select>
          </div>

          {/* فلتر الحالة */}
          <div>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value as any)}
              className="w-full bg-green-900/30 border border-green-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-green-400 transition-all duration-300 cursor-pointer hover:bg-green-800/40"
              style={{
                backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2310b981' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'left 12px center',
                backgroundSize: '16px'
              }}
            >
              <option value="all" className="bg-green-900 text-white">جميع الحالات</option>
              <option value="confirmed" className="bg-green-900 text-white">مؤكد</option>
              <option value="pending" className="bg-green-900 text-white">معلق</option>
              <option value="cancelled" className="bg-green-900 text-white">ملغي</option>
            </select>
          </div>

          {/* الترتيب */}
          <div>
            <select
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => {
                const [field, order] = e.target.value.split('-');
                setSortBy(field as any);
                setSortOrder(order as any);
              }}
              className="w-full bg-green-900/30 border border-green-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-green-400 transition-all duration-300 cursor-pointer hover:bg-green-800/40"
              style={{
                backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2310b981' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'left 12px center',
                backgroundSize: '16px'
              }}
            >
              <option value="date-desc" className="bg-green-900 text-white">التاريخ (الأحدث)</option>
              <option value="date-asc" className="bg-green-900 text-white">التاريخ (الأقدم)</option>
              <option value="amount-desc" className="bg-green-900 text-white">المبلغ (الأعلى)</option>
              <option value="amount-asc" className="bg-green-900 text-white">المبلغ (الأقل)</option>
              <option value="source-asc" className="bg-green-900 text-white">المصدر (أ-ي)</option>
            </select>
          </div>
        </div>
      </div>

      {/* رسالة الخطأ */}
      {error && (
        <div className="bg-red-500/10 border border-red-400/20 rounded-2xl p-4 mb-6">
          <div className="flex items-center space-x-3 space-x-reverse">
            <XCircle className="w-6 h-6 text-red-400" />
            <div>
              <h3 className="text-red-400 font-semibold">خطأ في تحميل البيانات</h3>
              <p className="text-red-300 text-sm">{error}</p>
            </div>
            <button
              onClick={fetchRealIncomeData}
              className="bg-red-500/20 hover:bg-red-500/30 text-red-400 px-4 py-2 rounded-lg transition-all duration-300"
            >
              إعادة المحاولة
            </button>
          </div>
        </div>
      )}

      {/* رسالة التحميل */}
      {loading && (
        <div className="bg-green-500/10 border border-green-400/20 rounded-2xl p-6 mb-6">
          <div className="flex items-center justify-center space-x-3 space-x-reverse">
            <RefreshCw className="w-6 h-6 text-green-400 animate-spin" />
            <p className="text-green-400 font-semibold">جاري تحميل البيانات المالية...</p>
          </div>
        </div>
      )}

      {/* قائمة المداخيل */}
      <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 overflow-hidden">
        {filteredIncomes.length === 0 && !loading ? (
          <div className="text-center py-16">
            <TrendingUp className="w-20 h-20 text-gray-400 mx-auto mb-6" />
            <h3 className="text-2xl font-bold text-white mb-4">
              {incomes.length === 0 ? 'لا توجد مداخيل مسجلة' : 'لا توجد نتائج للبحث'}
            </h3>
            <p className="text-gray-400 mb-4">
              {incomes.length === 0
                ? 'المبيعات والديون تُضاف تلقائياً من صفحاتها المخصصة'
                : 'جرب تغيير معايير البحث أو الفلترة'
              }
            </p>
            {incomes.length === 0 && (
              <div className="text-center">
                <p className="text-gray-500 text-sm mb-6">
                  يمكنك إضافة مداخيل إضافية مثل الخدمات والاستشارات والإيجارات
                </p>
                <button
                  onClick={() => setShowAddModal(true)}
                  className="bg-gradient-to-r from-green-500 to-emerald-600 hover:scale-105 transform transition-all duration-300 px-8 py-4 rounded-xl font-bold text-white shadow-2xl hover:shadow-green-500/25 flex items-center space-x-2 space-x-reverse mx-auto"
                >
                  <Plus className="w-6 h-6" />
                  <span>إضافة دخل إضافي</span>
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-white/5 border-b border-white/10">
                <tr>
                  <th className="text-right py-4 px-6 text-gray-300 font-semibold">الدخل</th>
                  <th className="text-right py-4 px-6 text-gray-300 font-semibold">المصدر</th>
                  <th className="text-right py-4 px-6 text-gray-300 font-semibold">المبلغ</th>
                  <th className="text-right py-4 px-6 text-gray-300 font-semibold">طريقة الدفع</th>
                  <th className="text-right py-4 px-6 text-gray-300 font-semibold">الحالة</th>
                  <th className="text-right py-4 px-6 text-gray-300 font-semibold">التاريخ</th>
                  <th className="text-center py-4 px-6 text-gray-300 font-semibold">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredIncomes.map((income) => (
                  <tr
                    key={income.id}
                    className="border-b border-white/5 hover:bg-white/5 transition-all duration-300"
                  >
                    <td className="py-4 px-6">
                      <div>
                        <p className="text-white font-semibold">{income.title}</p>
                        <p className="text-gray-400 text-sm">{income.description}</p>
                        {income.customerName && (
                          <p className="text-gray-500 text-xs">العميل: {income.customerName}</p>
                        )}
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div className={`inline-flex items-center space-x-2 space-x-reverse px-3 py-1 rounded-full text-sm font-medium border ${getSourceColor(income.source)}`}>
                        {getSourceIcon(income.source)}
                        <span>{getSourceText(income.source)}</span>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <p className="text-white font-bold text-lg">{formatCurrency(income.amount)}</p>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        {getPaymentMethodIcon(income.paymentMethod)}
                        <span className="text-gray-300 text-sm">{getPaymentMethodText(income.paymentMethod)}</span>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div className={`inline-flex items-center space-x-2 space-x-reverse px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(income.status)}`}>
                        {getStatusIcon(income.status)}
                        <span>{getStatusText(income.status)}</span>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <p className="text-gray-300 text-sm">{formatDate(income.date)}</p>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center justify-center space-x-2 space-x-reverse">
                        <button
                          onClick={() => {
                            setSelectedIncome(income);
                            setShowDetailsModal(true);
                          }}
                          className="bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 p-2 rounded-lg transition-all duration-300 hover:scale-110"
                          title="عرض التفاصيل"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => {
                            setSelectedIncome(income);
                            setShowEditModal(true);
                          }}
                          className="bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-400 p-2 rounded-lg transition-all duration-300 hover:scale-110"
                          title="تعديل"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          className="bg-red-500/20 hover:bg-red-500/30 text-red-400 p-2 rounded-lg transition-all duration-300 hover:scale-110"
                          title="حذف"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* نموذج إضافة دخل جديد */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gradient-to-br from-slate-900 via-green-900 to-slate-900 rounded-2xl border border-white/20 w-full max-w-4xl max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b border-white/20">
              <div className="flex items-center justify-between">
                <h3 className="text-2xl font-bold text-white flex items-center space-x-2 space-x-reverse">
                  <Plus className="w-6 h-6 text-green-400" />
                  <span>إضافة دخل إضافي</span>
                </h3>
                <div className="text-sm text-gray-400">
                  <p>المبيعات والديون تُضاف تلقائياً من النظام</p>
                </div>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="text-gray-400 hover:text-white transition-colors duration-300"
                >
                  <XCircle className="w-8 h-8" />
                </button>
              </div>
            </div>

            <div className="p-6 overflow-y-auto max-h-[70vh]">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* المعلومات الأساسية */}
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2 space-x-reverse">
                    <Receipt className="w-5 h-5 text-green-400" />
                    <span>المعلومات الأساسية</span>
                  </h4>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      عنوان الدخل الإضافي *
                    </label>
                    <input
                      type="text"
                      value={newIncome.title}
                      onChange={(e) => setNewIncome({...newIncome, title: e.target.value})}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 transition-all duration-300"
                      placeholder="مثل: خدمة استشارية، إيجار عقار، فوائد بنكية"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      الوصف
                    </label>
                    <textarea
                      value={newIncome.description}
                      onChange={(e) => setNewIncome({...newIncome, description: e.target.value})}
                      rows={3}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 transition-all duration-300 resize-none"
                      placeholder="تفاصيل إضافية عن الدخل..."
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      المبلغ (دج) *
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      value={newIncome.amount}
                      onChange={(e) => setNewIncome({...newIncome, amount: parseFloat(e.target.value) || 0})}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 transition-all duration-300"
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      نوع الدخل الإضافي *
                    </label>
                    <select
                      value={newIncome.source}
                      onChange={(e) => setNewIncome({...newIncome, source: e.target.value as any})}
                      className="w-full bg-green-900/30 border border-green-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-green-400 transition-all duration-300 cursor-pointer hover:bg-green-800/40"
                      style={{
                        backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2310b981' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                        backgroundRepeat: 'no-repeat',
                        backgroundPosition: 'left 12px center',
                        backgroundSize: '16px'
                      }}
                    >
                      <option value="service" className="bg-green-900 text-white">خدمات واستشارات</option>
                      <option value="other" className="bg-green-900 text-white">مصادر أخرى (إيجار، فوائد، etc.)</option>
                    </select>
                    <p className="text-xs text-gray-500 mt-1">
                      ملاحظة: المبيعات والديون تُضاف تلقائياً من صفحاتها المخصصة
                    </p>
                  </div>
                </div>

                {/* تفاصيل الدفع */}
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2 space-x-reverse">
                    <CreditCard className="w-5 h-5 text-blue-400" />
                    <span>تفاصيل الدفع</span>
                  </h4>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      اسم العميل
                    </label>
                    <input
                      type="text"
                      value={newIncome.customerName}
                      onChange={(e) => setNewIncome({...newIncome, customerName: e.target.value})}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 transition-all duration-300"
                      placeholder="اسم العميل أو الجهة الدافعة"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      طريقة الدفع *
                    </label>
                    <select
                      value={newIncome.paymentMethod}
                      onChange={(e) => setNewIncome({...newIncome, paymentMethod: e.target.value as any})}
                      className="w-full bg-green-900/30 border border-green-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-green-400 transition-all duration-300 cursor-pointer hover:bg-green-800/40"
                      style={{
                        backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2310b981' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                        backgroundRepeat: 'no-repeat',
                        backgroundPosition: 'left 12px center',
                        backgroundSize: '16px'
                      }}
                    >
                      <option value="cash" className="bg-green-900 text-white">نقدي</option>
                      <option value="transfer" className="bg-green-900 text-white">تحويل بنكي</option>
                      <option value="card" className="bg-green-900 text-white">بطاقة ائتمان</option>
                      <option value="check" className="bg-green-900 text-white">شيك</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      رقم الإيصال/الفاتورة
                    </label>
                    <input
                      type="text"
                      value={newIncome.receiptNumber}
                      onChange={(e) => setNewIncome({...newIncome, receiptNumber: e.target.value})}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 transition-all duration-300"
                      placeholder="رقم مرجعي للإيصال أو الفاتورة"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      الفئة
                    </label>
                    <input
                      type="text"
                      value={newIncome.category}
                      onChange={(e) => setNewIncome({...newIncome, category: e.target.value})}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 transition-all duration-300"
                      placeholder="سيتم تعيينها تلقائياً حسب المصدر"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      ملاحظات إضافية
                    </label>
                    <textarea
                      value={newIncome.notes}
                      onChange={(e) => setNewIncome({...newIncome, notes: e.target.value})}
                      rows={3}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 transition-all duration-300 resize-none"
                      placeholder="أي ملاحظات أو تفاصيل إضافية..."
                    />
                  </div>
                </div>
              </div>

              <div className="flex space-x-4 space-x-reverse pt-6 mt-6 border-t border-white/20">
                <button
                  onClick={handleAddIncome}
                  disabled={!newIncome.title.trim() || newIncome.amount <= 0 || loading}
                  className="flex-1 bg-gradient-to-r from-green-500 to-emerald-600 hover:scale-105 transform transition-all duration-300 py-3 rounded-xl font-bold text-white shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2 space-x-reverse"
                >
                  <Plus className="w-5 h-5" />
                  <span>إضافة الدخل الإضافي</span>
                </button>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="flex-1 bg-white/10 border border-white/20 hover:border-white/40 py-3 rounded-xl font-bold text-white transition-all duration-300 hover:scale-105"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Income;