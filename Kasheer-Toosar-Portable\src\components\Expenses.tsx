import React, { useState, useEffect } from 'react';
import {
  TrendingDown,
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  Calendar,
  DollarSign,
  Receipt,
  Tag,
  Building,
  Car,
  Zap,
  Wifi,
  Coffee,
  Fuel,
  Wrench,
  ShoppingBag,
  Users,
  Phone,
  FileText,
  Filter,
  Download,
  RefreshCw,
  XCircle,
  CheckCircle,
  AlertTriangle,
  Clock,
  BarChart3,
  PieChart,
  Calculator,
  CreditCard,
  Banknote,
  ArrowUpRight,
  ArrowDownRight,
  Target,
  Activity,
  Layers,
  MoreVertical,
  Home,
  Truck,
  Lightbulb,
  Briefcase,
  Heart,
  Scissors,
  Megaphone,
  Shield,
  Smartphone,
  Utensils
} from 'lucide-react';

interface Expense {
  id: string;
  transaction_number: string;
  type: 'expense';
  category: string;
  amount: number;
  description: string;
  reference_id?: string;
  reference_type?: string;
  payment_method?: string;
  notes?: string;
  transaction_date: string;
  created_at: string;
  updated_at: string;
}

interface ExpenseCategory {
  id: string;
  name: string;
  color: string;
  icon: React.ReactNode;
  budget: number;
  spent: number;
}

interface ExpenseStats {
  totalExpenses: number;
  monthlyExpenses: number;
  todayExpenses: number;
  categoriesCount: number;
  averageExpense: number;
  topCategory: string;
}

// فئات المصروفات الذكية مع الأيقونات
const expenseCategories = [
  { name: 'مشتريات', icon: <ShoppingBag className="w-5 h-5" />, color: '#10B981', budget: 100000 },
  { name: 'رواتب', icon: <Users className="w-5 h-5" />, color: '#3B82F6', budget: 50000 },
  { name: 'إيجار', icon: <Home className="w-5 h-5" />, color: '#8B5CF6', budget: 25000 },
  { name: 'مرافق', icon: <Zap className="w-5 h-5" />, color: '#F59E0B', budget: 15000 },
  { name: 'نقل', icon: <Truck className="w-5 h-5" />, color: '#EF4444', budget: 20000 },
  { name: 'صيانة', icon: <Wrench className="w-5 h-5" />, color: '#6B7280', budget: 18000 },
  { name: 'تسويق', icon: <Megaphone className="w-5 h-5" />, color: '#EC4899', budget: 30000 },
  { name: 'اتصالات', icon: <Phone className="w-5 h-5" />, color: '#06B6D4', budget: 8000 },
  { name: 'تأمين', icon: <Shield className="w-5 h-5" />, color: '#84CC16', budget: 12000 },
  { name: 'مكتبية', icon: <Briefcase className="w-5 h-5" />, color: '#F97316', budget: 10000 },
  { name: 'ضيافة', icon: <Utensils className="w-5 h-5" />, color: '#14B8A6', budget: 5000 },
  { name: 'أخرى', icon: <MoreVertical className="w-5 h-5" />, color: '#64748B', budget: 15000 }
];

const Expenses: React.FC = () => {
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'date' | 'amount' | 'category'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedExpense, setSelectedExpense] = useState<Expense | null>(null);
  const [dateFilter, setDateFilter] = useState<'today' | 'week' | 'month' | 'quarter' | 'year' | 'all'>('month');

  const [newExpense, setNewExpense] = useState({
    description: '',
    amount: '',
    category: '',
    payment_method: 'نقدي',
    notes: '',
    transaction_date: new Date().toISOString().split('T')[0]
  });

  // جلب المصروفات من قاعدة البيانات
  const fetchExpenses = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/financial/transactions?type=expense');
      if (!response.ok) {
        throw new Error('فشل في جلب المصروفات');
      }

      const data = await response.json();
      setExpenses(data);
    } catch (err) {
      console.error('خطأ في جلب المصروفات:', err);
      setError(err instanceof Error ? err.message : 'خطأ غير معروف');
    } finally {
      setLoading(false);
    }
  };

  // تحميل المصروفات عند بداية التشغيل
  useEffect(() => {
    fetchExpenses();
  }, []);

  // حساب الإحصائيات
  const stats: ExpenseStats = {
    totalExpenses: expenses.reduce((sum, expense) => sum + parseFloat(expense.amount.toString()), 0),
    monthlyExpenses: expenses
      .filter(expense => {
        const expenseDate = new Date(expense.transaction_date);
        const now = new Date();
        return expenseDate.getMonth() === now.getMonth() && expenseDate.getFullYear() === now.getFullYear();
      })
      .reduce((sum, expense) => sum + parseFloat(expense.amount.toString()), 0),
    todayExpenses: expenses
      .filter(expense => {
        const expenseDate = new Date(expense.transaction_date);
        const today = new Date();
        return expenseDate.toDateString() === today.toDateString();
      })
      .reduce((sum, expense) => sum + parseFloat(expense.amount.toString()), 0),
    categoriesCount: expenseCategories.length,
    averageExpense: expenses.length > 0 ? expenses.reduce((sum, expense) => sum + parseFloat(expense.amount.toString()), 0) / expenses.length : 0,
    topCategory: expenses.length > 0 ?
      Object.entries(
        expenses.reduce((acc, expense) => {
          acc[expense.category] = (acc[expense.category] || 0) + parseFloat(expense.amount.toString());
          return acc;
        }, {} as Record<string, number>)
      ).sort(([,a], [,b]) => b - a)[0]?.[0] || 'غير محدد' : 'غير محدد'
  };

  // فلترة المصروفات
  const filteredExpenses = expenses
    .filter(expense => {
      const matchesSearch = expense.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           expense.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (expense.notes && expense.notes.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesCategory = selectedCategory === 'all' || expense.category === selectedCategory;
      const matchesPaymentMethod = selectedPaymentMethod === 'all' || expense.payment_method === selectedPaymentMethod;

      const expenseDate = new Date(expense.transaction_date);
      const now = new Date();
      let matchesDate = true;

      switch (dateFilter) {
        case 'today':
          matchesDate = expenseDate.toDateString() === now.toDateString();
          break;
        case 'week':
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          matchesDate = expenseDate >= weekAgo;
          break;
        case 'month':
          matchesDate = expenseDate.getMonth() === now.getMonth() && expenseDate.getFullYear() === now.getFullYear();
          break;
        case 'quarter':
          const quarter = Math.floor(now.getMonth() / 3);
          const expenseQuarter = Math.floor(expenseDate.getMonth() / 3);
          matchesDate = expenseQuarter === quarter && expenseDate.getFullYear() === now.getFullYear();
          break;
        case 'year':
          matchesDate = expenseDate.getFullYear() === now.getFullYear();
          break;
        default:
          matchesDate = true;
      }

      return matchesSearch && matchesCategory && matchesPaymentMethod && matchesDate;
    })
    .sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'date':
          aValue = new Date(a.transaction_date).getTime();
          bValue = new Date(b.transaction_date).getTime();
          break;
        case 'amount':
          aValue = parseFloat(a.amount.toString());
          bValue = parseFloat(b.amount.toString());
          break;
        case 'category':
          aValue = a.category.toLowerCase();
          bValue = b.category.toLowerCase();
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

  const formatCurrency = (amount: number | string) => `${parseFloat(amount.toString()).toLocaleString()} دج`;
  const formatDate = (dateString: string) => new Date(dateString).toLocaleDateString('ar-DZ');
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return `${date.toLocaleDateString('ar-DZ')} ${date.toLocaleTimeString('ar-DZ')}`;
  };

  // الحصول على أيقونة الفئة
  const getCategoryIcon = (category: string) => {
    const categoryData = expenseCategories.find(cat => cat.name === category);
    return categoryData ? categoryData.icon : <Tag className="w-4 h-4" />;
  };

  // الحصول على لون الفئة
  const getCategoryColor = (category: string) => {
    const categoryData = expenseCategories.find(cat => cat.name === category);
    return categoryData ? categoryData.color : '#64748B';
  };

  const getPaymentMethodIcon = (method?: string) => {
    switch (method?.toLowerCase()) {
      case 'نقدي':
      case 'cash': return <Banknote className="w-4 h-4 text-green-400" />;
      case 'تحويل':
      case 'transfer': return <ArrowUpRight className="w-4 h-4 text-blue-400" />;
      case 'بطاقة':
      case 'card': return <CreditCard className="w-4 h-4 text-purple-400" />;
      case 'شيك':
      case 'check': return <FileText className="w-4 h-4 text-orange-400" />;
      default: return <DollarSign className="w-4 h-4 text-gray-400" />;
    }
  };

  // إضافة مصروف جديد
  const handleAddExpense = async () => {
    if (!newExpense.description.trim() || !newExpense.amount.trim() || !newExpense.category.trim()) {
      alert('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    try {
      setLoading(true);

      const expenseData = {
        type: 'expense',
        category: newExpense.category.trim(),
        amount: parseFloat(newExpense.amount),
        description: newExpense.description.trim(),
        payment_method: newExpense.payment_method,
        notes: newExpense.notes.trim(),
        transaction_date: newExpense.transaction_date
      };

      const response = await fetch('/api/financial/transactions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(expenseData),
      });

      if (!response.ok) {
        throw new Error('فشل في إضافة المصروف');
      }

      // إعادة تحميل المصروفات
      await fetchExpenses();

      // إعادة تعيين النموذج
      setNewExpense({
        description: '',
        amount: '',
        category: '',
        payment_method: 'نقدي',
        notes: '',
        transaction_date: new Date().toISOString().split('T')[0]
      });

      setShowAddModal(false);
      alert('تم إضافة المصروف بنجاح!');
    } catch (err) {
      console.error('خطأ في إضافة المصروف:', err);
      alert(err instanceof Error ? err.message : 'خطأ في إضافة المصروف');
    } finally {
      setLoading(false);
    }
  };

  // حذف مصروف
  const handleDeleteExpense = async (expenseId: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا المصروف؟')) {
      return;
    }

    try {
      setLoading(true);

      const response = await fetch(`/api/financial/transactions/${expenseId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('فشل في حذف المصروف');
      }

      // إعادة تحميل المصروفات
      await fetchExpenses();
      alert('تم حذف المصروف بنجاح!');
    } catch (err) {
      console.error('خطأ في حذف المصروف:', err);
      alert(err instanceof Error ? err.message : 'خطأ في حذف المصروف');
    } finally {
      setLoading(false);
    }
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-red-900 to-slate-900 p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="bg-gradient-to-r from-red-500 to-orange-600 p-3 rounded-2xl shadow-lg animate-pulse">
              <TrendingDown className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold text-white mb-2">📉 إدارة المصروفات</h1>
              <p className="text-gray-300">تتبع وإدارة جميع مصروفات الشركة</p>
            </div>
          </div>

          <div className="flex items-center space-x-4 space-x-reverse">
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-gradient-to-r from-green-500 to-emerald-600 hover:scale-105 transform transition-all duration-300 px-6 py-3 rounded-xl font-bold text-white shadow-2xl hover:shadow-green-500/25 flex items-center space-x-2 space-x-reverse"
            >
              <Plus className="w-5 h-5" />
              <span>إضافة مصروف</span>
            </button>

            <button className="bg-white/10 backdrop-blur-lg border border-white/20 hover:border-white/40 px-4 py-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse text-white">
              <Download className="w-5 h-5" />
              <span>تصدير</span>
            </button>

            <button className="bg-white/10 backdrop-blur-lg border border-white/20 hover:border-white/40 px-4 py-3 rounded-xl transition-all duration-300 hover:scale-105 flex items-center space-x-2 space-x-reverse text-white">
              <RefreshCw className="w-5 h-5" />
              <span>تحديث</span>
            </button>
          </div>
        </div>

        {/* إحصائيات المصروفات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-red-400/50 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">إجمالي المصروفات</p>
                <p className="text-3xl font-bold text-white">{formatCurrency(stats.totalExpenses)}</p>
                <div className="flex items-center space-x-1 space-x-reverse mt-2">
                  <TrendingDown className="w-4 h-4 text-red-400" />
                  <span className="text-red-400 text-sm font-semibold">جميع الأوقات</span>
                </div>
              </div>
              <div className="bg-red-500/20 p-3 rounded-xl">
                <Calculator className="w-6 h-6 text-red-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-red-400/50 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">مصروفات الشهر</p>
                <p className="text-3xl font-bold text-white">{formatCurrency(stats.monthlyExpenses)}</p>
                <div className="flex items-center space-x-1 space-x-reverse mt-2">
                  <Calendar className="w-4 h-4 text-blue-400" />
                  <span className="text-blue-400 text-sm font-semibold">هذا الشهر</span>
                </div>
              </div>
              <div className="bg-blue-500/20 p-3 rounded-xl">
                <BarChart3 className="w-6 h-6 text-blue-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-red-400/50 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">مصروفات اليوم</p>
                <p className="text-3xl font-bold text-white">{formatCurrency(stats.todayExpenses)}</p>
                <div className="flex items-center space-x-1 space-x-reverse mt-2">
                  <Clock className="w-4 h-4 text-yellow-400" />
                  <span className="text-yellow-400 text-sm font-semibold">اليوم</span>
                </div>
              </div>
              <div className="bg-yellow-500/20 p-3 rounded-xl">
                <AlertTriangle className="w-6 h-6 text-yellow-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 hover:border-red-400/50 transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm mb-1">متوسط المصروف</p>
                <p className="text-3xl font-bold text-white">{formatCurrency(stats.averageExpense)}</p>
                <div className="flex items-center space-x-1 space-x-reverse mt-2">
                  <Target className="w-4 h-4 text-purple-400" />
                  <span className="text-purple-400 text-sm font-semibold">لكل معاملة</span>
                </div>
              </div>
              <div className="bg-purple-500/20 p-3 rounded-xl">
                <Activity className="w-6 h-6 text-purple-400" />
              </div>
            </div>
          </div>
        </div>

        {/* فئات المصروفات السريعة */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 mb-8">
          <h3 className="text-xl font-bold text-white mb-4 flex items-center space-x-2 space-x-reverse">
            <Layers className="w-6 h-6 text-green-400" />
            <span>⚡ إضافة سريعة - فئات شائعة</span>
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {expenseCategories.map((category) => (
              <button
                key={category.name}
                onClick={() => {
                  setNewExpense({...newExpense, category: category.name});
                  setShowAddModal(true);
                }}
                className="bg-white/5 hover:bg-white/10 border border-white/20 hover:border-white/40 rounded-xl p-4 transition-all duration-300 hover:scale-105 flex flex-col items-center space-y-2 group"
                style={{ borderColor: `${category.color}30` }}
              >
                <div
                  className="p-3 rounded-lg group-hover:scale-110 transition-transform duration-300"
                  style={{ backgroundColor: `${category.color}20`, color: category.color }}
                >
                  {category.icon}
                </div>
                <span className="text-white text-sm font-medium">{category.name}</span>
                <span className="text-gray-400 text-xs">ميزانية: {formatCurrency(category.budget)}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* شريط البحث والفلاتر */}
      <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20 mb-8">
        <div className="grid grid-cols-1 lg:grid-cols-6 gap-4">
          {/* البحث */}
          <div className="lg:col-span-2">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="البحث في المصروفات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-white/10 border border-white/20 rounded-xl px-12 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-red-400 transition-all duration-300"
              />
            </div>
          </div>

          {/* فلتر الفترة */}
          <div>
            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value as any)}
              className="w-full bg-red-900/30 border border-red-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-red-400 transition-all duration-300 cursor-pointer hover:bg-red-800/40"
              style={{
                backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23f87171' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'left 12px center',
                backgroundSize: '16px'
              }}
            >
              <option value="all" className="bg-red-900 text-white">كل الأوقات</option>
              <option value="today" className="bg-red-900 text-white">اليوم</option>
              <option value="week" className="bg-red-900 text-white">هذا الأسبوع</option>
              <option value="month" className="bg-red-900 text-white">هذا الشهر</option>
              <option value="quarter" className="bg-red-900 text-white">هذا الربع</option>
              <option value="year" className="bg-red-900 text-white">هذا العام</option>
            </select>
          </div>

          {/* فلتر الفئة */}
          <div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="w-full bg-red-900/30 border border-red-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-red-400 transition-all duration-300 cursor-pointer hover:bg-red-800/40"
              style={{
                backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23f87171' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'left 12px center',
                backgroundSize: '16px'
              }}
            >
              <option value="all" className="bg-red-900 text-white">جميع الفئات</option>
              {expenseCategories.map(category => (
                <option key={category.name} value={category.name} className="bg-red-900 text-white">
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          {/* فلتر طريقة الدفع */}
          <div>
            <select
              value={selectedPaymentMethod}
              onChange={(e) => setSelectedPaymentMethod(e.target.value as any)}
              className="w-full bg-red-900/30 border border-red-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-red-400 transition-all duration-300 cursor-pointer hover:bg-red-800/40"
              style={{
                backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23f87171' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'left 12px center',
                backgroundSize: '16px'
              }}
            >
              <option value="all" className="bg-red-900 text-white">جميع الطرق</option>
              <option value="cash" className="bg-red-900 text-white">نقدي</option>
              <option value="transfer" className="bg-red-900 text-white">تحويل</option>
              <option value="card" className="bg-red-900 text-white">بطاقة</option>
              <option value="check" className="bg-red-900 text-white">شيك</option>
            </select>
          </div>

          {/* الترتيب */}
          <div>
            <select
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => {
                const [field, order] = e.target.value.split('-');
                setSortBy(field as any);
                setSortOrder(order as any);
              }}
              className="w-full bg-red-900/30 border border-red-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-red-400 transition-all duration-300 cursor-pointer hover:bg-red-800/40"
              style={{
                backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23f87171' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'left 12px center',
                backgroundSize: '16px'
              }}
            >
              <option value="date-desc" className="bg-red-900 text-white">التاريخ (الأحدث)</option>
              <option value="date-asc" className="bg-red-900 text-white">التاريخ (الأقدم)</option>
              <option value="amount-desc" className="bg-red-900 text-white">المبلغ (الأعلى)</option>
              <option value="amount-asc" className="bg-red-900 text-white">المبلغ (الأقل)</option>
              <option value="category-asc" className="bg-red-900 text-white">الفئة (أ-ي)</option>
            </select>
          </div>
        </div>
      </div>

      {/* قائمة المصروفات */}
      <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20 overflow-hidden">
        {loading ? (
          <div className="text-center py-16">
            <RefreshCw className="w-20 h-20 text-gray-400 mx-auto mb-6 animate-spin" />
            <h3 className="text-2xl font-bold text-white mb-4">جاري تحميل المصروفات...</h3>
            <p className="text-gray-400">يرجى الانتظار</p>
          </div>
        ) : error ? (
          <div className="text-center py-16">
            <AlertTriangle className="w-20 h-20 text-red-400 mx-auto mb-6" />
            <h3 className="text-2xl font-bold text-white mb-4">خطأ في تحميل المصروفات</h3>
            <p className="text-gray-400 mb-8">{error}</p>
            <button
              onClick={fetchExpenses}
              className="bg-gradient-to-r from-blue-500 to-blue-600 hover:scale-105 transform transition-all duration-300 px-8 py-4 rounded-xl font-bold text-white shadow-2xl hover:shadow-blue-500/25 flex items-center space-x-2 space-x-reverse mx-auto"
            >
              <RefreshCw className="w-6 h-6" />
              <span>إعادة المحاولة</span>
            </button>
          </div>
        ) : filteredExpenses.length === 0 ? (
          <div className="text-center py-16">
            <TrendingDown className="w-20 h-20 text-gray-400 mx-auto mb-6" />
            <h3 className="text-2xl font-bold text-white mb-4">
              {expenses.length === 0 ? 'لا توجد مصروفات مسجلة' : 'لا توجد نتائج للبحث'}
            </h3>
            <p className="text-gray-400 mb-8">
              {expenses.length === 0
                ? 'ابدأ بإضافة أول مصروف للشركة'
                : 'جرب تغيير معايير البحث أو الفلترة'
              }
            </p>
            {expenses.length === 0 && (
              <button
                onClick={() => setShowAddModal(true)}
                className="bg-gradient-to-r from-red-500 to-orange-600 hover:scale-105 transform transition-all duration-300 px-8 py-4 rounded-xl font-bold text-white shadow-2xl hover:shadow-red-500/25 flex items-center space-x-2 space-x-reverse mx-auto"
              >
                <Plus className="w-6 h-6" />
                <span>إضافة أول مصروف</span>
              </button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-white/5 border-b border-white/10">
                <tr>
                  <th className="text-right py-4 px-6 text-gray-300 font-semibold">المصروف</th>
                  <th className="text-right py-4 px-6 text-gray-300 font-semibold">الفئة</th>
                  <th className="text-right py-4 px-6 text-gray-300 font-semibold">المبلغ</th>
                  <th className="text-right py-4 px-6 text-gray-300 font-semibold">طريقة الدفع</th>
                  <th className="text-right py-4 px-6 text-gray-300 font-semibold">التاريخ</th>
                  <th className="text-center py-4 px-6 text-gray-300 font-semibold">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredExpenses.map((expense) => (
                  <tr
                    key={expense.id}
                    className="border-b border-white/5 hover:bg-white/5 transition-all duration-300"
                  >
                    <td className="py-4 px-6">
                      <div>
                        <p className="text-white font-semibold">{expense.description}</p>
                        <p className="text-gray-400 text-sm">رقم المعاملة: {expense.transaction_number}</p>
                        {expense.notes && (
                          <p className="text-gray-500 text-xs">ملاحظات: {expense.notes}</p>
                        )}
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div
                        className="inline-flex items-center space-x-2 space-x-reverse px-3 py-1 rounded-full text-sm font-medium"
                        style={{
                          backgroundColor: `${getCategoryColor(expense.category)}20`,
                          color: getCategoryColor(expense.category),
                          border: `1px solid ${getCategoryColor(expense.category)}30`
                        }}
                      >
                        {getCategoryIcon(expense.category)}
                        <span>{expense.category}</span>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <p className="text-white font-bold text-lg">{formatCurrency(expense.amount)}</p>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        {getPaymentMethodIcon(expense.payment_method)}
                        <span className="text-gray-300 text-sm">{expense.payment_method || 'غير محدد'}</span>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <p className="text-gray-300 text-sm">{formatDate(expense.transaction_date)}</p>
                      <p className="text-gray-500 text-xs">{formatDateTime(expense.created_at)}</p>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center justify-center space-x-2 space-x-reverse">
                        <button
                          onClick={() => {
                            setSelectedExpense(expense);
                            setShowDetailsModal(true);
                          }}
                          className="bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 p-2 rounded-lg transition-all duration-300 hover:scale-110"
                          title="عرض التفاصيل"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteExpense(expense.id)}
                          className="bg-red-500/20 hover:bg-red-500/30 text-red-400 p-2 rounded-lg transition-all duration-300 hover:scale-110"
                          title="حذف"
                          disabled={loading}
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* نموذج إضافة مصروف جديد */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gradient-to-br from-slate-900 via-red-900 to-slate-900 rounded-2xl border border-white/20 w-full max-w-4xl max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b border-white/20">
              <div className="flex items-center justify-between">
                <h3 className="text-2xl font-bold text-white flex items-center space-x-2 space-x-reverse">
                  <Plus className="w-6 h-6 text-green-400" />
                  <span>إضافة مصروف جديد</span>
                </h3>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="text-gray-400 hover:text-white transition-colors duration-300"
                >
                  <XCircle className="w-8 h-8" />
                </button>
              </div>
            </div>

            <div className="p-6 overflow-y-auto max-h-[70vh]">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* المعلومات الأساسية */}
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2 space-x-reverse">
                    <Receipt className="w-5 h-5 text-red-400" />
                    <span>المعلومات الأساسية</span>
                  </h4>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      وصف المصروف *
                    </label>
                    <input
                      type="text"
                      value={newExpense.description}
                      onChange={(e) => setNewExpense({...newExpense, description: e.target.value})}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-red-400 transition-all duration-300"
                      placeholder="مثل: فاتورة كهرباء، راتب موظف، إيجار مكتب"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      التاريخ *
                    </label>
                    <input
                      type="date"
                      value={newExpense.transaction_date}
                      onChange={(e) => setNewExpense({...newExpense, transaction_date: e.target.value})}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-red-400 transition-all duration-300"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      المبلغ (دج) *
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      value={newExpense.amount}
                      onChange={(e) => setNewExpense({...newExpense, amount: e.target.value})}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-red-400 transition-all duration-300"
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      الفئة *
                    </label>
                    <input
                      type="text"
                      value={newExpense.category}
                      onChange={(e) => setNewExpense({...newExpense, category: e.target.value})}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-red-400 transition-all duration-300"
                      placeholder="مثل: رواتب، إيجار، مرافق، تسويق"
                    />
                  </div>
                </div>

                {/* تفاصيل الدفع */}
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2 space-x-reverse">
                    <CreditCard className="w-5 h-5 text-blue-400" />
                    <span>تفاصيل الدفع</span>
                  </h4>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      طريقة الدفع *
                    </label>
                    <select
                      value={newExpense.payment_method}
                      onChange={(e) => setNewExpense({...newExpense, payment_method: e.target.value})}
                      className="w-full bg-red-900/30 border border-red-400/30 rounded-xl px-4 py-3 text-white focus:outline-none focus:border-red-400 transition-all duration-300 cursor-pointer hover:bg-red-800/40"
                      style={{
                        backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23f87171' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                        backgroundRepeat: 'no-repeat',
                        backgroundPosition: 'left 12px center',
                        backgroundSize: '16px'
                      }}
                    >
                      <option value="نقدي" className="bg-red-900 text-white">نقدي</option>
                      <option value="تحويل" className="bg-red-900 text-white">تحويل بنكي</option>
                      <option value="بطاقة" className="bg-red-900 text-white">بطاقة ائتمان</option>
                      <option value="شيك" className="bg-red-900 text-white">شيك</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-semibold mb-2">
                      ملاحظات إضافية
                    </label>
                    <textarea
                      value={newExpense.notes}
                      onChange={(e) => setNewExpense({...newExpense, notes: e.target.value})}
                      rows={3}
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-red-400 transition-all duration-300 resize-none"
                      placeholder="أي ملاحظات أو تفاصيل إضافية..."
                    />
                  </div>
                </div>
              </div>

              <div className="flex space-x-4 space-x-reverse pt-6 mt-6 border-t border-white/20">
                <button
                  onClick={handleAddExpense}
                  disabled={!newExpense.description.trim() || !newExpense.amount.trim() || !newExpense.category.trim()}
                  className="flex-1 bg-gradient-to-r from-green-500 to-emerald-600 hover:scale-105 transform transition-all duration-300 py-3 rounded-xl font-bold text-white shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2 space-x-reverse"
                >
                  <Plus className="w-5 h-5" />
                  <span>إضافة المصروف</span>
                </button>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="flex-1 bg-white/10 border border-white/20 hover:border-white/40 py-3 rounded-xl font-bold text-white transition-all duration-300 hover:scale-105"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* نموذج عرض تفاصيل المصروف */}
      {showDetailsModal && selectedExpense && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gradient-to-br from-slate-900 via-red-900 to-slate-900 rounded-2xl border border-white/20 w-full max-w-3xl max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b border-white/20">
              <div className="flex items-center justify-between">
                <h3 className="text-2xl font-bold text-white flex items-center space-x-2 space-x-reverse">
                  <Eye className="w-6 h-6 text-blue-400" />
                  <span>تفاصيل المصروف</span>
                </h3>
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="text-gray-400 hover:text-white transition-colors duration-300"
                >
                  <XCircle className="w-8 h-8" />
                </button>
              </div>
            </div>

            <div className="p-6 overflow-y-auto max-h-[70vh]">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* المعلومات الأساسية */}
                <div className="bg-white/5 rounded-xl p-6 border border-white/10">
                  <h4 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2 space-x-reverse">
                    <Receipt className="w-5 h-5 text-red-400" />
                    <span>المعلومات الأساسية</span>
                  </h4>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-gray-400 text-sm mb-1">رقم المعاملة</label>
                      <p className="text-white font-mono text-lg">{selectedExpense.transaction_number}</p>
                    </div>

                    <div>
                      <label className="block text-gray-400 text-sm mb-1">وصف المصروف</label>
                      <p className="text-white font-semibold text-lg">{selectedExpense.description}</p>
                    </div>

                    <div>
                      <label className="block text-gray-400 text-sm mb-1">الفئة</label>
                      <div
                        className="inline-flex items-center space-x-2 space-x-reverse px-3 py-2 rounded-lg text-sm font-medium"
                        style={{
                          backgroundColor: `${getCategoryColor(selectedExpense.category)}20`,
                          color: getCategoryColor(selectedExpense.category),
                          border: `1px solid ${getCategoryColor(selectedExpense.category)}30`
                        }}
                      >
                        {getCategoryIcon(selectedExpense.category)}
                        <span>{selectedExpense.category}</span>
                      </div>
                    </div>

                    <div>
                      <label className="block text-gray-400 text-sm mb-1">المبلغ</label>
                      <p className="text-white font-bold text-3xl">{formatCurrency(selectedExpense.amount)}</p>
                    </div>
                  </div>
                </div>

                {/* تفاصيل الدفع والتواريخ */}
                <div className="bg-white/5 rounded-xl p-6 border border-white/10">
                  <h4 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2 space-x-reverse">
                    <CreditCard className="w-5 h-5 text-blue-400" />
                    <span>تفاصيل الدفع والتواريخ</span>
                  </h4>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-gray-400 text-sm mb-1">طريقة الدفع</label>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        {getPaymentMethodIcon(selectedExpense.payment_method)}
                        <span className="text-white font-medium">{selectedExpense.payment_method || 'غير محدد'}</span>
                      </div>
                    </div>

                    <div>
                      <label className="block text-gray-400 text-sm mb-1">تاريخ المعاملة</label>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <Calendar className="w-4 h-4 text-blue-400" />
                        <span className="text-white font-medium">{formatDate(selectedExpense.transaction_date)}</span>
                      </div>
                    </div>

                    <div>
                      <label className="block text-gray-400 text-sm mb-1">تاريخ الإنشاء</label>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <Clock className="w-4 h-4 text-green-400" />
                        <span className="text-white font-medium">{formatDateTime(selectedExpense.created_at)}</span>
                      </div>
                    </div>

                    <div>
                      <label className="block text-gray-400 text-sm mb-1">آخر تحديث</label>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <RefreshCw className="w-4 h-4 text-yellow-400" />
                        <span className="text-white font-medium">{formatDateTime(selectedExpense.updated_at)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* الملاحظات */}
              {selectedExpense.notes && (
                <div className="bg-white/5 rounded-xl p-6 border border-white/10 mt-6">
                  <h4 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2 space-x-reverse">
                    <FileText className="w-5 h-5 text-yellow-400" />
                    <span>الملاحظات</span>
                  </h4>
                  <p className="text-gray-300 leading-relaxed">{selectedExpense.notes}</p>
                </div>
              )}

              {/* معلومات المرجع */}
              {selectedExpense.reference_id && (
                <div className="bg-white/5 rounded-xl p-6 border border-white/10 mt-6">
                  <h4 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2 space-x-reverse">
                    <Tag className="w-5 h-5 text-purple-400" />
                    <span>معلومات المرجع</span>
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-gray-400 text-sm mb-1">معرف المرجع</label>
                      <p className="text-white font-mono">{selectedExpense.reference_id}</p>
                    </div>
                    {selectedExpense.reference_type && (
                      <div>
                        <label className="block text-gray-400 text-sm mb-1">نوع المرجع</label>
                        <p className="text-white font-medium">{selectedExpense.reference_type}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div className="flex space-x-4 space-x-reverse pt-6 mt-6 border-t border-white/20">
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="flex-1 bg-white/10 border border-white/20 hover:border-white/40 py-3 rounded-xl font-bold text-white transition-all duration-300 hover:scale-105"
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Expenses;
