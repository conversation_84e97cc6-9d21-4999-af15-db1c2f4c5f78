const pool = require('./database');
const fetch = require('node-fetch');

async function debugDebtorsIssue() {
  console.log('🔍 فحص مشكلة العملاء المدينين...');
  
  try {
    // 1. فحص الديون في قاعدة البيانات مباشرة
    console.log('\n1️⃣ فحص الديون مباشرة من قاعدة البيانات...');
    const debtsResult = await pool.query(`
      SELECT
        cd.id as debt_id,
        cd.customer_id,
        cd.amount,
        cd.paid_amount,
        cd.remaining_amount,
        cd.status,
        cd.created_at,
        c.name as customer_name,
        c.phone as customer_phone,
        s.sale_number
      FROM pos_system.customer_debts cd
      LEFT JOIN pos_system.customers c ON cd.customer_id = c.id
      LEFT JOIN pos_system.sales s ON cd.sale_id = s.id
      WHERE cd.status IN ('pending', 'partial')
      ORDER BY cd.created_at DESC
    `);
    
    console.log(`✅ تم العثور على ${debtsResult.rows.length} دين:`);
    debtsResult.rows.forEach((debt, index) => {
      console.log(`\n${index + 1}. دين رقم: ${debt.debt_id}`);
      console.log(`   - العميل: ${debt.customer_name || 'غير محدد'} (${debt.customer_phone || 'بدون هاتف'})`);
      console.log(`   - معرف العميل: ${debt.customer_id}`);
      console.log(`   - رقم البيع: ${debt.sale_number}`);
      console.log(`   - المبلغ الإجمالي: ${debt.amount} دج`);
      console.log(`   - المبلغ المدفوع: ${debt.paid_amount} دج`);
      console.log(`   - المبلغ المتبقي: ${debt.remaining_amount} دج`);
      console.log(`   - الحالة: ${debt.status}`);
      console.log(`   - تاريخ الإنشاء: ${debt.created_at}`);
    });
    
    // 2. فحص API endpoint للمدينين
    console.log('\n2️⃣ فحص API endpoint للمدينين...');
    try {
      const response = await fetch('http://localhost:5003/api/customers/debtors');
      const debtorsData = await response.json();
      
      console.log(`✅ API يُرجع ${Array.isArray(debtorsData) ? debtorsData.length : 'بيانات غير صحيحة'} مدين`);
      
      if (Array.isArray(debtorsData)) {
        debtorsData.forEach((debtor, index) => {
          console.log(`\n${index + 1}. مدين من API:`);
          console.log(`   - الاسم: ${debtor.name || 'غير محدد'}`);
          console.log(`   - الهاتف: ${debtor.phone || 'غير محدد'}`);
          console.log(`   - معرف العميل: ${debtor.id}`);
          console.log(`   - إجمالي الدين: ${debtor.total_debt || 'غير محدد'} دج`);
          console.log(`   - المبلغ المتبقي: ${debtor.remaining_debt || 'غير محدد'} دج`);
        });
      } else {
        console.log('❌ API لا يُرجع مصفوفة:', debtorsData);
      }
      
    } catch (apiError) {
      console.log('❌ خطأ في API المدينين:', apiError.message);
    }
    
    // 3. فحص آخر المبيعات الآجلة
    console.log('\n3️⃣ فحص آخر المبيعات الآجلة...');
    const creditSalesResult = await pool.query(`
      SELECT
        s.id,
        s.sale_number,
        s.customer_id,
        s.total_amount,
        s.is_debt_sale,
        s.created_at,
        c.name as customer_name,
        c.phone as customer_phone
      FROM pos_system.sales s
      LEFT JOIN pos_system.customers c ON s.customer_id = c.id
      WHERE s.is_debt_sale = true
      ORDER BY s.created_at DESC
      LIMIT 5
    `);
    
    console.log(`✅ آخر ${creditSalesResult.rows.length} مبيعة آجلة:`);
    creditSalesResult.rows.forEach((sale, index) => {
      console.log(`\n${index + 1}. بيع رقم: ${sale.sale_number}`);
      console.log(`   - العميل: ${sale.customer_name || 'غير محدد'} (${sale.customer_phone || 'بدون هاتف'})`);
      console.log(`   - معرف العميل: ${sale.customer_id}`);
      console.log(`   - المبلغ: ${sale.total_amount} دج`);
      console.log(`   - تاريخ البيع: ${sale.created_at}`);
    });
    
    // 4. فحص العملاء في قاعدة البيانات
    console.log('\n4️⃣ فحص العملاء المتاحين...');
    const customersResult = await pool.query(`
      SELECT id, name, phone, email, is_active
      FROM pos_system.customers
      WHERE is_active = true
      ORDER BY created_at DESC
      LIMIT 10
    `);
    
    console.log(`✅ العملاء المتاحين (${customersResult.rows.length}):`);
    customersResult.rows.forEach((customer, index) => {
      console.log(`   ${index + 1}. ${customer.name} (${customer.phone}) - معرف: ${customer.id}`);
    });
    
  } catch (error) {
    console.error('❌ خطأ في فحص المدينين:', error.message);
    console.error('تفاصيل الخطأ:', error);
  }
}

// تشغيل الفحص
if (require.main === module) {
  debugDebtorsIssue()
    .then(() => {
      console.log('\n🎉 انتهى فحص مشكلة المدينين!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل فحص المدينين:', error);
      process.exit(1);
    });
}

module.exports = debugDebtorsIssue;
