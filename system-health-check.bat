@echo off
chcp 65001 > nul
title 🔍 كشير توسار - فحص صحة النظام
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🔍 فحص صحة النظام                            ║
echo ║                   كشير توسار                               ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

set "error_count=0"
set "warning_count=0"

echo 📋 بدء الفحص الشامل...
echo.

REM ═══════════════════════════════════════════════════════════════
echo 🔧 فحص المتطلبات الأساسية
echo ═══════════════════════════════════════════════════════════════

REM فحص Node.js
echo 📦 Node.js:
node --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('node --version') do set "node_version=%%i"
    echo ✅ مثبت - الإصدار: !node_version!
) else (
    echo ❌ غير مثبت
    set /a error_count+=1
)

REM فحص npm
echo 📦 npm:
npm --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('npm --version') do set "npm_version=%%i"
    echo ✅ مثبت - الإصدار: !npm_version!
) else (
    echo ❌ غير مثبت
    set /a error_count+=1
)

REM فحص PostgreSQL
echo 🗄️ PostgreSQL:
psql --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=3" %%i in ('psql --version') do set "pg_version=%%i"
    echo ✅ مثبت - الإصدار: !pg_version!
) else (
    echo ⚠️  غير مثبت أو غير متاح في PATH
    set /a warning_count+=1
)

echo.

REM ═══════════════════════════════════════════════════════════════
echo 📁 فحص هيكل المشروع
echo ═══════════════════════════════════════════════════════════════

REM فحص الملفات الأساسية
set "required_files=package.json electron-main.js test-electron.bat quick-build.bat"
for %%f in (%required_files%) do (
    if exist "%%f" (
        echo ✅ %%f
    ) else (
        echo ❌ %%f مفقود
        set /a error_count+=1
    )
)

REM فحص المجلدات الأساسية
set "required_dirs=src backend database assets"
for %%d in (%required_dirs%) do (
    if exist "%%d" (
        echo ✅ مجلد %%d/
    ) else (
        echo ❌ مجلد %%d/ مفقود
        set /a error_count+=1
    )
)

echo.

REM ═══════════════════════════════════════════════════════════════
echo 📦 فحص التبعيات
echo ═══════════════════════════════════════════════════════════════

REM فحص node_modules الرئيسي
if exist "node_modules" (
    echo ✅ node_modules/ موجود
    
    REM فحص التبعيات المهمة
    set "important_deps=react typescript vite electron tailwindcss"
    for %%d in (%important_deps%) do (
        if exist "node_modules\%%d" (
            echo ✅ %%d مثبت
        ) else (
            echo ⚠️  %%d غير مثبت
            set /a warning_count+=1
        )
    )
) else (
    echo ❌ node_modules/ مفقود
    echo 💡 قم بتشغيل: npm install
    set /a error_count+=1
)

REM فحص تبعيات Backend
if exist "backend\node_modules" (
    echo ✅ backend/node_modules/ موجود
) else (
    echo ⚠️  backend/node_modules/ مفقود
    echo 💡 قم بتشغيل: cd backend && npm install
    set /a warning_count+=1
)

echo.

REM ═══════════════════════════════════════════════════════════════
echo 🔌 فحص المنافذ
echo ═══════════════════════════════════════════════════════════════

REM فحص المنفذ 3000
netstat -an | findstr ":3000" >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  المنفذ 3000 مستخدم
    set /a warning_count+=1
) else (
    echo ✅ المنفذ 3000 متاح
)

REM فحص المنفذ 5003
netstat -an | findstr ":5003" >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  المنفذ 5003 مستخدم
    set /a warning_count+=1
) else (
    echo ✅ المنفذ 5003 متاح
)

REM فحص المنفذ 5432 (PostgreSQL)
netstat -an | findstr ":5432" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ المنفذ 5432 مستخدم (PostgreSQL يعمل)
) else (
    echo ⚠️  المنفذ 5432 غير مستخدم (PostgreSQL قد لا يعمل)
    set /a warning_count+=1
)

echo.

REM ═══════════════════════════════════════════════════════════════
echo 🔧 فحص ملفات التكوين
echo ═══════════════════════════════════════════════════════════════

REM فحص package.json
if exist "package.json" (
    findstr "\"type\": \"module\"" package.json >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ package.json يحتوي على ES modules
    ) else (
        echo ⚠️  package.json لا يحتوي على ES modules
        set /a warning_count+=1
    )
)

REM فحص package-electron-fixed.json
if exist "package-electron-fixed.json" (
    echo ✅ package-electron-fixed.json موجود
) else (
    echo ❌ package-electron-fixed.json مفقود
    echo 💡 هذا الملف مطلوب لحل مشكلة ES modules في Electron
    set /a error_count+=1
)

REM فحص vite.config.ts
if exist "vite.config.ts" (
    echo ✅ vite.config.ts موجود
) else (
    echo ⚠️  vite.config.ts مفقود
    set /a warning_count+=1
)

echo.

REM ═══════════════════════════════════════════════════════════════
echo 📊 ملخص النتائج
echo ═══════════════════════════════════════════════════════════════

echo 🔍 إجمالي الفحوصات المكتملة
echo 📈 الأخطاء: %error_count%
echo 📈 التحذيرات: %warning_count%
echo.

if %error_count% equ 0 (
    if %warning_count% equ 0 (
        echo ╔══════════════════════════════════════════════════════════════╗
        echo ║                    🎉 النظام سليم تماماً!                   ║
        echo ║                يمكنك تشغيل التطبيق بأمان                   ║
        echo ╚══════════════════════════════════════════════════════════════╝
        color 0A
    ) else (
        echo ╔══════════════════════════════════════════════════════════════╗
        echo ║                  ⚠️  النظام يعمل مع تحذيرات                ║
        echo ║              يُنصح بحل التحذيرات للأداء الأمثل             ║
        echo ╚══════════════════════════════════════════════════════════════╝
        color 0E
    )
) else (
    echo ╔══════════════════════════════════════════════════════════════╗
    echo ║                    ❌ يوجد أخطاء في النظام!                ║
    echo ║                يجب حل الأخطاء قبل التشغيل                  ║
    echo ╚══════════════════════════════════════════════════════════════╝
    color 0C
)

echo.

REM ═══════════════════════════════════════════════════════════════
echo 💡 الخطوات التالية المقترحة
echo ═══════════════════════════════════════════════════════════════

if %error_count% gtr 0 (
    echo 🔧 لحل الأخطاء:
    echo 1. تثبيت المتطلبات المفقودة
    echo 2. تشغيل: npm install
    echo 3. تشغيل: cd backend ^&^& npm install
    echo 4. إعادة تشغيل هذا الفحص
    echo.
)

if %warning_count% gtr 0 (
    echo ⚠️  لحل التحذيرات:
    echo 1. إيقاف العمليات على المنافذ المستخدمة
    echo 2. تثبيت PostgreSQL إذا لم يكن مثبتاً
    echo 3. تحديث التبعيات المفقودة
    echo.
)

echo 🚀 للتشغيل السريع:
echo - test-electron.bat (اختبار شامل)
echo - start-system.bat (تشغيل سريع)
echo - quick-build.bat (بناء ملف التثبيت)
echo.

pause
