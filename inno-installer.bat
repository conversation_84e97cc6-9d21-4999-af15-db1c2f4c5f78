@echo off
title Inno Setup Installer

echo Creating installer with Inno Setup...

echo Step 1: Building project...
call npm run build

echo Step 2: Creating Inno Setup script...
echo [Setup] > setup.iss
echo AppName=Kasheer Toosar >> setup.iss
echo AppVersion=1.0 >> setup.iss
echo DefaultDirName={pf}\Kasheer Toosar >> setup.iss
echo DefaultGroupName=Kasheer Toosar >> setup.iss
echo OutputBaseFilename=Kasheer-Toosar-Installer >> setup.iss
echo Compression=lzma >> setup.iss
echo SolidCompression=yes >> setup.iss
echo. >> setup.iss
echo [Files] >> setup.iss
echo Source: "backend\*"; DestDir: "{app}\backend"; Flags: ignoreversion recursesubdirs >> setup.iss
echo Source: "dist\*"; DestDir: "{app}\dist"; Flags: ignoreversion recursesubdirs >> setup.iss
echo Source: "node_modules\*"; DestDir: "{app}\node_modules"; Flags: ignoreversion recursesubdirs >> setup.iss
echo Source: "package.json"; DestDir: "{app}"; Flags: ignoreversion >> setup.iss
echo Source: "start.bat"; DestDir: "{app}"; Flags: ignoreversion >> setup.iss
echo. >> setup.iss
echo [Icons] >> setup.iss
echo Name: "{group}\Kasheer Toosar"; Filename: "{app}\start.bat" >> setup.iss
echo Name: "{commondesktop}\Kasheer Toosar"; Filename: "{app}\start.bat" >> setup.iss

echo Step 3: Downloading Inno Setup...
if not exist "innosetup.exe" (
    powershell -Command "Invoke-WebRequest -Uri 'https://jrsoftware.org/download.php/is.exe' -OutFile 'innosetup.exe'"
)

echo Step 4: Installing Inno Setup...
if not exist "InnoSetup" (
    innosetup.exe /VERYSILENT /DIR=InnoSetup
    timeout /t 10 /nobreak >nul
)

echo Step 5: Compiling installer...
"InnoSetup\ISCC.exe" setup.iss

echo.
echo Installer created: Output\Kasheer-Toosar-Installer.exe
pause
