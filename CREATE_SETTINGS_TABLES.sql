-- 🔧 إنشاء جداول الإعدادات لحل مشكلة خطأ 500
-- انسخ والصق هذا الكود في pgAdmin Query Tool واضغط F5

-- تعيين المخطط
SET search_path TO pos_system, public;

-- 1. إن<PERSON><PERSON><PERSON> جدول الإعدادات العامة
CREATE TABLE IF NOT EXISTS settings (
    id SERIAL PRIMARY KEY,
    store_name VARCHAR(255) DEFAULT 'متجر توسار الإلكتروني',
    store_address TEXT DEFAULT 'الجزائر - الجزائر العاصمة',
    store_phone VARCHAR(50) DEFAULT '+213 XXX XXX XXX',
    store_email VARCHAR(255) DEFAULT '<EMAIL>',
    store_tax_number VARCHAR(100) DEFAULT '',
    store_logo TEXT DEFAULT '',
    user_name VARCHAR(255) DEFAULT 'المدير',
    user_email VARCHAR(255) DEFAULT '',
    user_role VARCHAR(50) DEFAULT 'admin',
    currency VARCHAR(10) DEFAULT 'DZD',
    language VARCHAR(10) DEFAULT 'ar',
    timezone VARCHAR(50) DEFAULT 'Africa/Algiers',
    date_format VARCHAR(20) DEFAULT 'DD/MM/YYYY',
    tax_rate DECIMAL(5,2) DEFAULT 19.00,
    enable_tax BOOLEAN DEFAULT true,
    enable_discount BOOLEAN DEFAULT true,
    enable_barcode BOOLEAN DEFAULT true,
    low_stock_alert BOOLEAN DEFAULT true,
    email_notifications BOOLEAN DEFAULT false,
    sound_notifications BOOLEAN DEFAULT true,
    theme VARCHAR(20) DEFAULT 'dark',
    primary_color VARCHAR(20) DEFAULT '#3b82f6',
    font_size VARCHAR(20) DEFAULT 'medium',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. إنشاء جدول إعدادات الطابعة
CREATE TABLE IF NOT EXISTS printer_settings (
    id SERIAL PRIMARY KEY,
    printer_name VARCHAR(255) DEFAULT 'Default Printer',
    paper_size VARCHAR(20) DEFAULT 'A4',
    print_logo BOOLEAN DEFAULT true,
    print_header BOOLEAN DEFAULT true,
    print_footer BOOLEAN DEFAULT true,
    header_text TEXT DEFAULT 'متجر توسار الإلكتروني',
    footer_text TEXT DEFAULT 'شكراً لزيارتكم',
    font_family VARCHAR(100) DEFAULT 'Arial',
    font_size INTEGER DEFAULT 12,
    margin_top INTEGER DEFAULT 10,
    margin_bottom INTEGER DEFAULT 10,
    margin_left INTEGER DEFAULT 10,
    margin_right INTEGER DEFAULT 10,
    auto_print BOOLEAN DEFAULT false,
    print_barcode BOOLEAN DEFAULT true,
    print_qr_code BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. إدراج إعدادات افتراضية إذا لم تكن موجودة
DO $$
BEGIN
    -- إدراج إعدادات عامة افتراضية
    IF NOT EXISTS (SELECT 1 FROM settings) THEN
        INSERT INTO settings (
            store_name, store_address, store_phone, store_email, user_name,
            currency, language, theme
        ) VALUES (
            'متجر توسار الإلكتروني',
            'الجزائر - الجزائر العاصمة',
            '+213 XXX XXX XXX',
            '<EMAIL>',
            'المدير',
            'DZD',
            'ar',
            'dark'
        );
        RAISE NOTICE '✅ تم إدراج الإعدادات العامة الافتراضية';
    ELSE
        RAISE NOTICE 'ℹ️ الإعدادات العامة موجودة مسبقاً';
    END IF;
    
    -- إدراج إعدادات طابعة افتراضية
    IF NOT EXISTS (SELECT 1 FROM printer_settings) THEN
        INSERT INTO printer_settings (
            printer_name, paper_size, print_logo, print_header, print_footer,
            header_text, footer_text
        ) VALUES (
            'Default Printer',
            'A4',
            true,
            true,
            true,
            'متجر توسار الإلكتروني',
            'شكراً لزيارتكم'
        );
        RAISE NOTICE '✅ تم إدراج إعدادات الطابعة الافتراضية';
    ELSE
        RAISE NOTICE 'ℹ️ إعدادات الطابعة موجودة مسبقاً';
    END IF;
END $$;

-- 4. إنشاء الفهارس
CREATE INDEX IF NOT EXISTS idx_settings_store_name ON settings(store_name);
CREATE INDEX IF NOT EXISTS idx_printer_settings_name ON printer_settings(printer_name);

-- 5. فحص النتائج
SELECT 
    'settings' as table_name,
    COUNT(*) as record_count,
    store_name,
    user_name,
    theme
FROM settings
GROUP BY store_name, user_name, theme

UNION ALL

SELECT 
    'printer_settings' as table_name,
    COUNT(*) as record_count,
    printer_name as store_name,
    paper_size as user_name,
    CASE WHEN print_logo THEN 'with_logo' ELSE 'no_logo' END as theme
FROM printer_settings
GROUP BY printer_name, paper_size, print_logo;

-- رسالة نجاح
DO $$
BEGIN
    RAISE NOTICE '🎉 تم إنشاء جداول الإعدادات بنجاح!';
    RAISE NOTICE '✅ الآن لن تحصل على خطأ 500 في /api/settings';
    RAISE NOTICE '🔄 أعد تشغيل الخادم: npm run dev';
    RAISE NOTICE '🧪 جرب الوصول إلى: http://localhost:5002/api/settings';
END $$;
