# 🔧 إصلاح مشكلة فقدان الاتصال

## 🎯 المشكلة
- فقدان الاتصال مع الخادم
- خطأ 500 في `/api/settings`
- المنفذ 5002 مستخدم بالفعل

## ✅ الحل السريع

### 1. إيقا<PERSON> العمليات المتضاربة:
```bash
# في Command Prompt أو PowerShell
taskkill /f /im node.exe
```

### 2. إعادة تشغيل الخادم:
```bash
cd backend
npm run dev
```

### 3. إذا لم يعمل، جرب:
```bash
cd backend
node server.js
```

### 4. إذا استمرت المشكلة:
```bash
# تغيير المنفذ مؤقتاً
cd backend
set PORT=5003
node server.js
```

## 🔍 فحص الاتصال

### تحقق من الخادم:
- افتح المتصفح
- اذهب إلى: `http://localhost:5002`
- يجب أن ترى رسالة من الخادم

### تحقق من API:
- اذهب إلى: `http://localhost:5002/api/products`
- يجب أن ترى قائمة المنتجات

## 🚀 إعادة تشغيل كامل

### 1. أغلق كل شيء:
```bash
# أغلق المتصفح
# أغلق VS Code
# أغلق كل terminals
```

### 2. أعد تشغيل الخادم:
```bash
cd backend
npm run dev
```

### 3. أعد تشغيل Frontend:
```bash
npm start
```

## 🔧 إصلاح مشاكل شائعة

### إذا كان المنفذ مستخدم:
```bash
# Windows
netstat -ano | findstr :5002
taskkill /PID [رقم العملية] /F

# أو غير المنفذ في backend/server.js
const PORT = process.env.PORT || 5003;
```

### إذا كانت قاعدة البيانات غير متصلة:
```bash
# تأكد من تشغيل PostgreSQL
# تحقق من الاتصال في pgAdmin
```

### إذا كان هناك خطأ في الكود:
```bash
# تحقق من console في backend
# ابحث عن أخطاء في terminal
```

## 🎯 علامات النجاح

عند نجاح الإصلاح ستجد:
- ✅ **الخادم يعمل**: رسالة "Server running on port 5002"
- ✅ **API يستجيب**: `http://localhost:5002/api/products`
- ✅ **Frontend متصل**: لا أخطاء في Console
- ✅ **البيانات تحمل**: المنتجات تظهر في شاشة البيع

## 🆘 إذا استمرت المشكلة

### أعد تشغيل الكمبيوتر:
أحياناً هذا يحل مشاكل المنافذ المعلقة.

### تحقق من Firewall:
تأكد أن المنفذ 5002 غير محجوب.

### استخدم منفذ مختلف:
```javascript
// في backend/server.js
const PORT = process.env.PORT || 5003; // غير إلى 5003
```

```javascript
// في src/services/api.js (في AppContext)
baseURL: 'http://localhost:5003/api', // غير إلى 5003
```

## 📞 خطوات الطوارئ

إذا لم يعمل أي شيء:

1. **أعد تشغيل الكمبيوتر**
2. **افتح Command Prompt كـ Administrator**
3. **شغل الأوامر:**
   ```bash
   cd "C:\Users\<USER>\Music\كشير توسار\backend"
   npm run dev
   ```
4. **في نافذة أخرى:**
   ```bash
   cd "C:\Users\<USER>\Music\كشير توسار"
   npm start
   ```

**هذا يجب أن يحل المشكلة! 🚀**
