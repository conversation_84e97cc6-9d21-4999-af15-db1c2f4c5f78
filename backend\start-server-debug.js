// تشغيل الخادم مع تتبع الأخطاء
console.log('🚀 بدء تشغيل الخادم...');

process.on('uncaughtException', (error) => {
  console.error('❌ خطأ غير معالج:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ رفض غير معالج:', reason);
});

try {
  const express = require('express');
  const cors = require('cors');
  require('dotenv').config();
  
  console.log('✅ تم تحميل المكتبات الأساسية');
  
  const app = express();
  const PORT = process.env.PORT || 5002;
  
  // Middleware
  app.use(cors());
  app.use(express.json());
  
  console.log('✅ تم إعداد Middleware');
  
  // Routes
  app.use('/api/purchases', require('./routes/purchases'));
  
  console.log('✅ تم تحميل routes');
  
  // اختبار الاتصال
  app.get('/api/test', (req, res) => {
    res.json({
      message: '✅ Backend API يعمل بنجاح!',
      timestamp: new Date().toISOString()
    });
  });
  
  app.listen(PORT, () => {
    console.log(`🚀 Server يعمل على المنفذ ${PORT}`);
    console.log(`🔗 API متاح على: http://localhost:${PORT}/api`);
  });
  
} catch (error) {
  console.error('❌ خطأ في تشغيل الخادم:', error);
  console.error('❌ تفاصيل الخطأ:', error.stack);
}
