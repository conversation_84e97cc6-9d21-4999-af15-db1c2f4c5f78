-- إنشاء جدول الإعدادات العامة
CREATE TABLE IF NOT EXISTS settings (
    id SERIAL PRIMARY KEY,
    -- إعدادات المتجر
    store_name VARCHAR(255) NOT NULL DEFAULT '',
    store_address TEXT DEFAULT '',
    store_phone VARCHAR(50) DEFAULT '',
    store_email VARCHAR(255) DEFAULT '',
    store_tax_number VARCHAR(100) DEFAULT '',
    store_logo TEXT DEFAULT '',
    
    -- إعدادات المستخدم
    user_name VARCHAR(255) NOT NULL DEFAULT '',
    user_email VARCHAR(255) DEFAULT '',
    user_role VARCHAR(50) DEFAULT 'admin',
    
    -- إعدادات النظام
    currency VARCHAR(10) DEFAULT 'DZD',
    language VARCHAR(10) DEFAULT 'ar',
    timezone VARCHAR(50) DEFAULT 'Africa/Algiers',
    date_format VARCHAR(20) DEFAULT 'DD/MM/YYYY',
    
    -- إ<PERSON>د<PERSON><PERSON><PERSON> الضرائب والمبيعات
    tax_rate DECIMAL(5,2) DEFAULT 19.00,
    enable_tax BOOLEAN DEFAULT true,
    enable_discount BOOLEAN DEFAULT true,
    enable_barcode BOOLEAN DEFAULT true,
    
    -- إعدادات التنبيهات
    low_stock_alert BOOLEAN DEFAULT true,
    email_notifications BOOLEAN DEFAULT false,
    sound_notifications BOOLEAN DEFAULT true,
    
    -- إعدادات المظهر
    theme VARCHAR(20) DEFAULT 'dark',
    primary_color VARCHAR(20) DEFAULT '#3b82f6',
    font_size VARCHAR(20) DEFAULT 'medium',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إدراج إعدادات افتراضية
INSERT INTO settings (
    store_name, store_address, store_phone, store_email,
    user_name, currency, language
) VALUES (
    'متجر توسار الإلكتروني',
    'الجزائر - الجزائر العاصمة', 
    '+213 XXX XXX XXX',
    '<EMAIL>',
    'المدير',
    'DZD',
    'ar'
) ON CONFLICT DO NOTHING;
