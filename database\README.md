# 🗄️ قاعدة البيانات - نظام نقطة البيع
# Database Setup - POS System

## 📋 **متطلبات النظام**

### **قاعدة البيانات:**
- PostgreSQL 13+ 
- Extensions: `uuid-ossp`, `pgcrypto`
- Memory: 2GB+ RAM
- Storage: 10GB+ available space

### **الأدوات المطلوبة:**
- `psql` command line tool
- pgAdmin (اختياري للإدارة المرئية)
- pg_dump/pg_restore للنسخ الاحتياطي

## 🚀 **التشغيل السريع**

### **1. إعداد PostgreSQL:**
```bash
# تثبيت PostgreSQL (Ubuntu/Debian)
sudo apt update
sudo apt install postgresql postgresql-contrib

# تثبيت PostgreSQL (CentOS/RHEL)
sudo yum install postgresql-server postgresql-contrib
sudo postgresql-setup initdb
sudo systemctl start postgresql
sudo systemctl enable postgresql

# تثبيت PostgreSQL (Windows)
# تحميل من: https://www.postgresql.org/download/windows/
```

### **2. إنشاء قاعدة البيانات:**
```bash
# الاتصال بـ PostgreSQL
sudo -u postgres psql

# تشغيل سكريبت الإعداد الشامل
\i setup_database.sql

# أو تشغيل الملفات منفردة:
\i 01_create_database.sql
\i 02_core_tables.sql
\i 03_sales_tables.sql
\i 04_triggers_functions.sql
\i 05_views_reports.sql
\i 06_security_permissions.sql
```

### **3. التحقق من الإعداد:**
```sql
-- التحقق من الجداول
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'pos_system';

-- التحقق من البيانات التجريبية
SELECT COUNT(*) FROM pos_system.products;
SELECT COUNT(*) FROM pos_system.categories;
SELECT COUNT(*) FROM pos_system.customers;
```

## 🔧 **إعدادات الاتصال**

### **ملف الاتصال (.env):**
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=pos_system_db
DB_USER=pos_admin_user
DB_PASSWORD=admin_secure_password_2024
DB_SCHEMA=pos_system

# Connection Pool
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_IDLE_TIMEOUT=30000
```

### **سلسلة الاتصال:**
```
postgresql://pos_admin_user:admin_secure_password_2024@localhost:5432/pos_system_db
```

## 👥 **المستخدمون والصلاحيات**

### **المستخدمون الافتراضيون:**
| المستخدم | كلمة المرور | الدور | الصلاحيات |
|----------|-------------|-------|-----------|
| `pos_admin_user` | `admin_secure_password_2024` | Admin | كاملة |
| `pos_manager_user` | `manager_secure_password_2024` | Manager | إدارية |
| `pos_cashier_user` | `cashier_secure_password_2024` | Cashier | محدودة |
| `pos_readonly_user` | `readonly_secure_password_2024` | ReadOnly | قراءة فقط |

### **تغيير كلمات المرور:**
```sql
-- تغيير كلمة مرور المدير
ALTER USER pos_admin_user PASSWORD 'new_secure_password';

-- تغيير كلمة مرور المدير العام
ALTER USER pos_manager_user PASSWORD 'new_manager_password';
```

## 📊 **الجداول الرئيسية**

### **الجداول الأساسية:**
- `settings` - الإعدادات العامة
- `printer_settings` - إعدادات الطابعة
- `categories` - فئات المنتجات
- `products` - المنتجات
- `customers` - العملاء
- `suppliers` - الموردين

### **جداول المعاملات:**
- `sales` - المبيعات
- `sale_items` - عناصر المبيعات
- `purchase_invoices` - فواتير المشتريات
- `purchase_invoice_items` - عناصر فواتير المشتريات
- `financial_transactions` - المعاملات المالية
- `inventory_movements` - حركة المخزون

### **جداول الديون:**
- `customer_debts` - ديون العملاء
- `customer_debt_payments` - مدفوعات الديون

## 🔍 **استعلامات مفيدة**

### **إحصائيات سريعة:**
```sql
-- إحصائيات اليوم
SELECT * FROM get_dashboard_stats();

-- المنتجات منخفضة المخزون
SELECT * FROM low_stock_products;

-- أفضل المنتجات مبيعاً
SELECT * FROM top_selling_products LIMIT 10;

-- الديون المستحقة
SELECT * FROM outstanding_debts;
```

### **البحث في المنتجات:**
```sql
-- البحث بالاسم أو الباركود
SELECT * FROM search_products('لابتوب');

-- المنتجات حسب الفئة
SELECT * FROM products p
JOIN categories c ON p.category_id = c.id
WHERE c.name = 'إلكترونيات';
```

## 🔄 **النسخ الاحتياطي والاستعادة**

### **إنشاء نسخة احتياطية:**
```bash
# نسخة احتياطية كاملة
pg_dump -h localhost -U pos_admin_user -d pos_system_db > backup_$(date +%Y%m%d_%H%M%S).sql

# نسخة احتياطية مضغوطة
pg_dump -h localhost -U pos_admin_user -Fc -d pos_system_db > backup_$(date +%Y%m%d_%H%M%S).dump

# نسخة احتياطية للبيانات فقط
pg_dump -h localhost -U pos_admin_user -a -d pos_system_db > data_backup_$(date +%Y%m%d_%H%M%S).sql
```

### **استعادة النسخة الاحتياطية:**
```bash
# استعادة من ملف SQL
psql -h localhost -U pos_admin_user -d pos_system_db < backup_20241201_143000.sql

# استعادة من ملف مضغوط
pg_restore -h localhost -U pos_admin_user -d pos_system_db backup_20241201_143000.dump

# استعادة مع إعادة إنشاء قاعدة البيانات
dropdb -h localhost -U postgres pos_system_db
createdb -h localhost -U postgres pos_system_db
pg_restore -h localhost -U pos_admin_user -d pos_system_db backup_20241201_143000.dump
```

## 🔧 **الصيانة الدورية**

### **تحديث الإحصائيات:**
```sql
-- تحديث إحصائيات الجداول
ANALYZE;

-- تحديث إحصائيات جدول معين
ANALYZE products;
```

### **تنظيف البيانات القديمة:**
```sql
-- تنظيف سجلات المراجعة القديمة
SELECT cleanup_old_audit_logs();

-- حذف المبيعات الملغية القديمة (أكثر من سنة)
DELETE FROM sales 
WHERE status = 'cancelled' 
AND created_at < CURRENT_DATE - INTERVAL '1 year';
```

### **إعادة فهرسة:**
```sql
-- إعادة فهرسة جدول معين
REINDEX TABLE products;

-- إعادة فهرسة قاعدة البيانات
REINDEX DATABASE pos_system_db;
```

## 🚨 **استكشاف الأخطاء**

### **مشاكل الاتصال:**
```bash
# التحقق من حالة PostgreSQL
sudo systemctl status postgresql

# التحقق من المنافذ
netstat -tlnp | grep 5432

# اختبار الاتصال
psql -h localhost -U pos_admin_user -d pos_system_db -c "SELECT version();"
```

### **مشاكل الأداء:**
```sql
-- عرض الاستعلامات البطيئة
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC LIMIT 10;

-- عرض حجم الجداول
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'pos_system'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

## 📞 **الدعم والمساعدة**

### **الوثائق:**
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [pgAdmin Documentation](https://www.pgadmin.org/docs/)

### **المجتمع:**
- [PostgreSQL Community](https://www.postgresql.org/community/)
- [Stack Overflow - PostgreSQL](https://stackoverflow.com/questions/tagged/postgresql)

### **الأدوات المفيدة:**
- pgAdmin - إدارة مرئية
- DBeaver - عميل قاعدة بيانات
- pg_stat_statements - مراقبة الأداء
- pg_cron - المهام المجدولة

- cd backend
- npm run dev 

- powershell -Command "Set-Location 'C:\Users\<USER>\Music\???? ?????\backend'; node server.js"