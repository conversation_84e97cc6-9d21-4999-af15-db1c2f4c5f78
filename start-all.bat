@echo off
echo 🚀 بدء تشغيل نظام نقطة البيع الكامل...
echo.

echo 📡 تشغيل الخادم الخلفي...
start "Backend Server" cmd /k "cd backend && node server.js"

echo ⏳ انتظار تشغيل الخادم...
timeout /t 3 /nobreak > nul

echo 🌐 تشغيل الواجهة الأمامية...
start "Frontend" cmd /k "npm start"

echo.
echo ✅ تم تشغيل النظام بنجاح!
echo 📡 الخادم الخلفي: http://localhost:5002
echo 🌐 الواجهة الأمامية: http://localhost:3000
echo.
pause
