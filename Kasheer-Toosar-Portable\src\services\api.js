// 🔄 API Service للاتصال بقاعدة البيانات الحقيقية
class ApiService {
  constructor() {
    this.baseURL = 'http://localhost:5003/api';
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;

    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error || errorData.message || errorMessage;
        } catch {
          errorMessage = errorText || errorMessage;
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      // تحسين معالجة أخطاء الاتصال
      if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        console.error('❌ خطأ في الاتصال بالخادم:', error);
        throw new Error('خطأ في الاتصال بالخادم - تأكد من تشغيل الخادم الخلفي');
      } else if (error.name === 'AbortError') {
        console.error('❌ تم إلغاء الطلب:', error);
        throw new Error('تم إلغاء الطلب - انتهت مهلة الاتصال');
      } else {
        console.error('❌ API Error:', error);
        throw error;
      }
    }
  }

  // المنتجات
  async getProducts() {
    return this.request('/products');
  }

  async createProduct(product) {
    return this.request('/products', {
      method: 'POST',
      body: JSON.stringify(product),
    });
  }

  async updateProduct(id, product) {
    return this.request(`/products/${id}`, {
      method: 'PUT',
      body: JSON.stringify(product),
    });
  }

  async deleteProduct(id) {
    return this.request(`/products/${id}`, {
      method: 'DELETE',
    });
  }

  // الفئات
  async getCategories() {
    return this.request('/categories');
  }

  async createCategory(category) {
    return this.request('/categories', {
      method: 'POST',
      body: JSON.stringify(category),
    });
  }

  async updateCategory(id, category) {
    return this.request(`/categories/${id}`, {
      method: 'PUT',
      body: JSON.stringify(category),
    });
  }

  async deleteCategory(id) {
    return this.request(`/categories/${id}`, {
      method: 'DELETE',
    });
  }

  async deleteAllCategories() {
    return this.request('/categories/all', {
      method: 'DELETE',
    });
  }

  // العملاء
  async getCustomers() {
    return this.request('/customers');
  }

  async createCustomer(customer) {
    return this.request('/customers', {
      method: 'POST',
      body: JSON.stringify(customer),
    });
  }

  async updateCustomer(id, customer) {
    return this.request(`/customers/${id}`, {
      method: 'PUT',
      body: JSON.stringify(customer),
    });
  }

  async deleteCustomer(id) {
    return this.request(`/customers/${id}`, {
      method: 'DELETE',
    });
  }

  async updateCustomerBalance(customerId, amount, operation, description) {
    return this.request(`/customers/${customerId}/balance`, {
      method: 'PUT',
      body: JSON.stringify({ amount, operation, description }),
    });
  }

  // المبيعات
  async getSales() {
    return this.request('/sales');
  }

  async createSale(sale) {
    return this.request('/sales', {
      method: 'POST',
      body: JSON.stringify(sale),
    });
  }

  // 💰 إنشاء مبيعة آجلة مع تسجيل الدين
  async createCreditSale(saleData) {
    return this.request('/sales/credit', {
      method: 'POST',
      body: JSON.stringify(saleData),
    });
  }

  async getDailySalesStats() {
    return this.request('/sales/stats/daily');
  }

  // الإعدادات
  async getSettings() {
    return this.request('/settings');
  }

  async updateSettings(settings) {
    return this.request('/settings', {
      method: 'PUT',
      body: JSON.stringify(settings),
    });
  }

  // الإحصائيات
  async getDashboardStats() {
    return this.request('/financial/dashboard-stats');
  }

  // المعاملات المالية
  async getFinancialTransactions() {
    return this.request('/financial/transactions');
  }

  async createFinancialTransaction(transaction) {
    return this.request('/financial/transactions', {
      method: 'POST',
      body: JSON.stringify(transaction),
    });
  }

  async updateFinancialTransaction(id, transaction) {
    return this.request(`/financial/transactions/${id}`, {
      method: 'PUT',
      body: JSON.stringify(transaction),
    });
  }

  async deleteFinancialTransaction(id) {
    return this.request(`/financial/transactions/${id}`, {
      method: 'DELETE',
    });
  }

  // المشتريات
  async getPurchases() {
    return this.request('/purchases');
  }

  async createPurchase(purchase) {
    return this.request('/purchases', {
      method: 'POST',
      body: JSON.stringify(purchase),
    });
  }

  async updatePurchase(id, purchase) {
    return this.request(`/purchases/${id}`, {
      method: 'PUT',
      body: JSON.stringify(purchase),
    });
  }

  async deletePurchase(id) {
    return this.request(`/purchases/${id}`, {
      method: 'DELETE',
    });
  }

  // الموردين
  async getSuppliers() {
    return this.request('/suppliers');
  }

  async createSupplier(supplier) {
    return this.request('/suppliers', {
      method: 'POST',
      body: JSON.stringify(supplier),
    });
  }

  async updateSupplier(id, supplier) {
    return this.request(`/suppliers/${id}`, {
      method: 'PUT',
      body: JSON.stringify(supplier),
    });
  }

  async deleteSupplier(id) {
    return this.request(`/suppliers/${id}`, {
      method: 'DELETE',
    });
  }

  // حركات المخزون
  async getInventoryMovements() {
    return this.request('/inventory/movements');
  }

  async createInventoryMovement(movement) {
    return this.request('/inventory/movements', {
      method: 'POST',
      body: JSON.stringify(movement),
    });
  }

  // 💰 إدارة الديون - النظام الجديد
  async getDebtorCustomers() {
    return this.request('/customers/debtors');
  }

  async getCustomerDebtInvoice(customerId) {
    return this.request(`/debt/customer/${customerId}/debt-invoice`);
  }

  async addDebtPurchase(data) {
    return this.request('/debt/add-purchase', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async makeDebtPayment(data) {
    return this.request('/debt/payment', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getDebtInvoices() {
    return this.request('/debt/invoices');
  }

  async getDebtPayments(invoiceId) {
    return this.request(`/debt/invoices/${invoiceId}/payments`);
  }

  // 🗑️ حذف فاتورة دين عميل
  async deleteCustomerDebtInvoice(customerId, confirmDelete = false) {
    return this.request(`/debt/customer/${customerId}/debt-invoice`, {
      method: 'DELETE',
      body: JSON.stringify({ confirmDelete }),
    });
  }

  // 🗑️ حذف دفعة معينة
  async deleteDebtPayment(paymentId) {
    return this.request(`/debt/payment/${paymentId}`, {
      method: 'DELETE',
    });
  }

  // 📊 إحصائيات الديون
  async getDebtStats() {
    return this.request('/debt/stats');
  }

  // 📋 الحصول على قائمة العملاء المدينين
  async getDebtors() {
    return this.request('/customers/debtors');
  }

  // 📋 الحصول على ديون عميل معين
  async getCustomerDebts(customerId) {
    return this.request(`/debt/customer/${customerId}/debt-invoice`);
  }

  // 💰 تسديد دين عميل
  async payCustomerDebt(customerId, paymentData) {
    return this.request(`/customers/${customerId}/pay-debt`, {
      method: 'POST',
      body: JSON.stringify(paymentData),
    });
  }
}

const apiService = new ApiService();
export default apiService;
