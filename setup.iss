[Setup] 
AppName=Kasheer Toosar 
AppVersion=1.0 
DefaultDirName={pf}\Kasheer Toosar 
DefaultGroupName=Kasheer Toosar 
OutputBaseFilename=Kasheer-Toosar-Installer 
Compression=lzma 
SolidCompression=yes 
 
[Files] 
Source: "backend\*"; DestDir: "{app}\backend"; Flags: ignoreversion recursesubdirs 
Source: "dist\*"; DestDir: "{app}\dist"; Flags: ignoreversion recursesubdirs 
Source: "node_modules\*"; DestDir: "{app}\node_modules"; Flags: ignoreversion recursesubdirs 
Source: "package.json"; DestDir: "{app}"; Flags: ignoreversion 
Source: "start.bat"; DestDir: "{app}"; Flags: ignoreversion 
 
[Icons] 
Name: "{group}\Kasheer Toosar"; Filename: "{app}\start.bat" 
Name: "{commondesktop}\Kasheer Toosar"; Filename: "{app}\start.bat" 
