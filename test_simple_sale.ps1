$headers = @{
    "Content-Type" = "application/json"
}

$body = @{
    customer_id = $null
    items = @(
        @{
            product_id = $null
            product_name = "Unknown Product Test - 25.50"
            quantity = 1
            unit_price = 25.50
            discount = 0
            total_price = 25.50
        }
    )
    subtotal = 25.50
    tax_amount = 0
    discount_amount = 0
    total_amount = 25.50
    payment_method = "cash"
    amount_paid = 25.50
    change_amount = 0
    notes = "Test unknown product"
    cashier_name = "Test System"
} | ConvertTo-Json -Depth 3

Write-Host "Testing unknown product sale..." -ForegroundColor Yellow
Write-Host "Sending request..." -ForegroundColor Cyan

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5002/api/sales" -Method POST -Headers $headers -Body $body
    
    Write-Host "SUCCESS! Sale completed!" -ForegroundColor Green
    Write-Host "Sale Number: $($response.sale_number)" -ForegroundColor Green
    Write-Host "Total Amount: $($response.total_amount)" -ForegroundColor Green
    Write-Host "Unknown product sold successfully!" -ForegroundColor Magenta
    
} catch {
    Write-Host "FAILED:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    if ($_.ErrorDetails) {
        Write-Host "Details: $($_.ErrorDetails.Message)" -ForegroundColor Red
    }
}
