const http = require('http');

function testSmartAnalysisAPI() {
  const options = {
    hostname: 'localhost',
    port: 5002,
    path: '/api/financial/smart-analysis',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  };

  const req = http.request(options, (res) => {
    console.log(`Status: ${res.statusCode}`);
    console.log(`Headers: ${JSON.stringify(res.headers)}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const response = JSON.parse(data);
        console.log('✅ API Response:');
        console.log(JSON.stringify(response, null, 2));
      } catch (error) {
        console.log('❌ Raw Response:');
        console.log(data);
      }
    });
  });

  req.on('error', (error) => {
    console.error('❌ Request Error:', error.message);
  });

  req.end();
}

console.log('🧪 اختبار API التحليل الذكي...');
testSmartAnalysisAPI();
