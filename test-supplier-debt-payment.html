<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار دفع ديون الموردين</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار دفع ديون الموردين</h1>
        
        <!-- اختبار إضافة دين جديد -->
        <div class="section">
            <h2>1️⃣ إضافة دين جديد للمورد</h2>
            <div class="form-group">
                <label>معرف المورد:</label>
                <input type="text" id="supplierId" placeholder="أدخل معرف المورد">
            </div>
            <div class="form-group">
                <label>مبلغ الدين:</label>
                <input type="number" id="debtAmount" placeholder="أدخل مبلغ الدين" value="1000">
            </div>
            <div class="form-group">
                <label>وصف الدين:</label>
                <input type="text" id="debtDescription" placeholder="وصف الدين" value="فاتورة مشتريات اختبار">
            </div>
            <button onclick="addSupplierDebt()">إضافة دين جديد</button>
            <div id="addDebtResult" class="result" style="display: none;"></div>
        </div>

        <!-- اختبار دفع دين -->
        <div class="section">
            <h2>2️⃣ دفع دين مورد</h2>
            <div class="form-group">
                <label>معرف الدين:</label>
                <input type="text" id="debtId" placeholder="أدخل معرف الدين">
            </div>
            <div class="form-group">
                <label>مبلغ الدفع:</label>
                <input type="number" id="paymentAmount" placeholder="أدخل مبلغ الدفع" value="500">
            </div>
            <div class="form-group">
                <label>طريقة الدفع:</label>
                <select id="paymentMethod">
                    <option value="cash">نقدي</option>
                    <option value="transfer">تحويل بنكي</option>
                    <option value="check">شيك</option>
                </select>
            </div>
            <div class="form-group">
                <label>ملاحظات:</label>
                <textarea id="paymentNotes" placeholder="ملاحظات إضافية"></textarea>
            </div>
            <button onclick="paySupplierDebt()">دفع الدين</button>
            <div id="payDebtResult" class="result" style="display: none;"></div>
        </div>

        <!-- اختبار جلب ديون الموردين -->
        <div class="section">
            <h2>3️⃣ جلب ديون الموردين</h2>
            <button onclick="getSupplierDebts()">جلب جميع ديون الموردين</button>
            <div id="getDebtsResult" class="result" style="display: none;"></div>
        </div>

        <!-- اختبار جلب المصروفات -->
        <div class="section">
            <h2>4️⃣ جلب المصروفات</h2>
            <button onclick="getExpenses()">جلب جميع المصروفات</button>
            <div id="getExpensesResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5002/api';

        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = JSON.stringify(data, null, 2);
        }

        function showInfo(elementId, message) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result info';
            element.textContent = message;
        }

        async function addSupplierDebt() {
            const supplierId = document.getElementById('supplierId').value;
            const amount = document.getElementById('debtAmount').value;
            const description = document.getElementById('debtDescription').value;

            if (!supplierId || !amount) {
                showResult('addDebtResult', { error: 'يرجى ملء معرف المورد والمبلغ' }, true);
                return;
            }

            try {
                showInfo('addDebtResult', 'جاري إضافة الدين...');
                
                const response = await fetch(`${API_BASE}/supplier-debts`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        supplier_id: supplierId,
                        amount: parseFloat(amount),
                        description: description
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    showResult('addDebtResult', data);
                    // تحديث معرف الدين في حقل الدفع
                    if (data.debt && data.debt.id) {
                        document.getElementById('debtId').value = data.debt.id;
                    }
                } else {
                    showResult('addDebtResult', data, true);
                }
            } catch (error) {
                showResult('addDebtResult', { error: error.message }, true);
            }
        }

        async function paySupplierDebt() {
            const debtId = document.getElementById('debtId').value;
            const amount = document.getElementById('paymentAmount').value;
            const paymentMethod = document.getElementById('paymentMethod').value;
            const notes = document.getElementById('paymentNotes').value;

            if (!debtId || !amount) {
                showResult('payDebtResult', { error: 'يرجى ملء معرف الدين والمبلغ' }, true);
                return;
            }

            try {
                showInfo('payDebtResult', 'جاري دفع الدين...');
                
                const response = await fetch(`${API_BASE}/supplier-debts/${debtId}/pay`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        amount: parseFloat(amount),
                        payment_method: paymentMethod,
                        notes: notes
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    showResult('payDebtResult', data);
                } else {
                    showResult('payDebtResult', data, true);
                }
            } catch (error) {
                showResult('payDebtResult', { error: error.message }, true);
            }
        }

        async function getSupplierDebts() {
            try {
                showInfo('getDebtsResult', 'جاري جلب ديون الموردين...');
                
                const response = await fetch(`${API_BASE}/supplier-debts`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult('getDebtsResult', data);
                } else {
                    showResult('getDebtsResult', data, true);
                }
            } catch (error) {
                showResult('getDebtsResult', { error: error.message }, true);
            }
        }

        async function getExpenses() {
            try {
                showInfo('getExpensesResult', 'جاري جلب المصروفات...');
                
                const response = await fetch(`${API_BASE}/expenses`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult('getExpensesResult', data);
                } else {
                    showResult('getExpensesResult', data, true);
                }
            } catch (error) {
                showResult('getExpensesResult', { error: error.message }, true);
            }
        }
    </script>
</body>
</html>
