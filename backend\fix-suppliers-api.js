const pool = require('./database');

async function fixSuppliersAPI() {
  try {
    console.log('🔧 فحص وإصلاح API الموردين...');
    
    // اختبار الاتصال الأساسي
    const client = await pool.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
    
    // التحقق من وجود جدول الموردين
    const tableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'pos_system' 
        AND table_name = 'suppliers'
      );
    `);
    
    console.log('📋 جدول الموردين موجود:', tableCheck.rows[0].exists);
    
    if (tableCheck.rows[0].exists) {
      // الحصول على هيكل الجدول
      const structure = await client.query(`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_schema = 'pos_system' 
        AND table_name = 'suppliers'
        ORDER BY ordinal_position;
      `);
      
      console.log('📊 هيكل جدول الموردين:');
      structure.rows.forEach(col => {
        console.log(`  - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
      });
      
      // التحقق من وجود جدول المشتريات
      const purchasesCheck = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'pos_system' 
          AND table_name = 'purchases'
        );
      `);
      
      console.log('📦 جدول المشتريات موجود:', purchasesCheck.rows[0].exists);
      
      // إذا كان جدول المشتريات غير موجود، قم بإنشائه
      if (!purchasesCheck.rows[0].exists) {
        console.log('⚠️ جدول المشتريات غير موجود. سيتم إنشاؤه...');
        
        await client.query(`
          CREATE TABLE pos_system.purchases (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            invoice_number VARCHAR(50) NOT NULL,
            supplier_id UUID REFERENCES pos_system.suppliers(id),
            subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
            tax_amount DECIMAL(10,2) DEFAULT 0,
            discount_amount DECIMAL(10,2) DEFAULT 0,
            total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
            amount_paid DECIMAL(10,2) DEFAULT 0,
            payment_method VARCHAR(50) DEFAULT 'cash',
            payment_status VARCHAR(50) DEFAULT 'paid',
            notes TEXT,
            is_active BOOLEAN DEFAULT true,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          );
          
          CREATE INDEX idx_purchases_supplier ON pos_system.purchases(supplier_id);
          CREATE INDEX idx_purchases_date ON pos_system.purchases(created_at);
        `);
        
        console.log('✅ تم إنشاء جدول المشتريات بنجاح');
      }
      
      // إضافة موردين تجريبيين إذا كان الجدول فارغًا
      const suppliersCount = await client.query('SELECT COUNT(*) FROM pos_system.suppliers');
      
      if (parseInt(suppliersCount.rows[0].count) === 0) {
        console.log('⚠️ لا يوجد موردين. سيتم إضافة موردين تجريبيين...');
        
        await client.query(`
          INSERT INTO pos_system.suppliers (name, phone, address, balance) VALUES
          ('شركة التقنية المتقدمة', '+213555111222', 'الجزائر العاصمة', 0.00),
          ('مؤسسة الملابس العصرية', '+213555333444', 'وهران', 0.00),
          ('شركة المواد الغذائية', '+213555555666', 'قسنطينة', 0.00)
        `);
        
        console.log('✅ تم إضافة موردين تجريبيين بنجاح');
      }
      
      console.log('👥 عدد الموردين:', suppliersCount.rows[0].count);
      
      // اختبار الاستعلام الفعلي من API
      console.log('🔍 اختبار استعلام API الموردين...');
      
      try {
        const testQuery = await client.query(`
          SELECT
            s.id,
            s.name,
            s.phone,
            s.address,
            s.balance,
            s.is_active,
            s.created_at,
            s.updated_at,
            
            COALESCE(COUNT(DISTINCT p.id), 0) as total_invoices,
            COALESCE(SUM(p.total_amount), 0) as total_purchases,
            COALESCE(SUM(p.amount_paid), 0) as total_paid,
            COALESCE(SUM(p.total_amount - p.amount_paid), 0) as total_debt,
            COALESCE(AVG(p.total_amount), 0) as average_invoice_amount,
            MAX(p.created_at) as last_purchase_date,
            COUNT(CASE WHEN (p.total_amount - p.amount_paid) > 0 THEN 1 END) as unpaid_invoices_count,
            COALESCE(SUM(CASE WHEN (p.total_amount - p.amount_paid) > 0 THEN (p.total_amount - p.amount_paid) END), 0) as outstanding_debt

          FROM pos_system.suppliers s
          LEFT JOIN pos_system.purchases p ON s.id = p.supplier_id
          WHERE s.is_active = true
          GROUP BY s.id, s.name, s.phone, s.address, s.balance, s.is_active, s.created_at, s.updated_at
          ORDER BY s.name
          LIMIT 5
        `);
        
        console.log('✅ استعلام API الموردين نجح، تم إرجاع', testQuery.rows.length, 'مورد');
        
        if (testQuery.rows.length > 0) {
          console.log('📋 أول مورد:');
          console.log(testQuery.rows[0]);
        }
        
      } catch (queryError) {
        console.error('❌ خطأ في استعلام API الموردين:', queryError.message);
        console.error('❌ تفاصيل الخطأ:', queryError.stack);
      }
      
    } else {
      console.log('❌ جدول الموردين غير موجود!');
    }
    
    client.release();
    
  } catch (error) {
    console.error('❌ خطأ:', error.message);
    console.error('❌ تفاصيل الخطأ:', error.stack);
  }
}

fixSuppliersAPI().then(() => {
  console.log('✅ انتهى الفحص');
  process.exit();
}).catch(err => {
  console.error('❌ فشل الفحص:', err);
  process.exit(1);
});
