# 📋 ملخص نظام إدارة ديون العملاء

## 🎯 نظرة عامة
تم تطوير نظام شامل لإدارة ديون العملاء في نظام نقاط البيع، يتضمن:
- إنشاء مبيعات آجلة (بيع بالدين)
- تتبع ديون العملاء
- تسديد الديون (جزئي أو كامل)
- إدارة قائمة العملاء المدينين

## 🗄️ هيكل قاعدة البيانات

### جدول `customer_debts`
```sql
CREATE TABLE pos_system.customer_debts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES pos_system.customers(id),
    sale_id UUID NOT NULL REFERENCES pos_system.sales(id),
    amount DECIMAL(10,2) NOT NULL,
    paid_amount DECIMAL(10,2) DEFAULT 0.00,
    remaining_amount DECIMAL(10,2) GENERATED ALWAYS AS (amount - paid_amount) STORED,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'partial', 'paid')),
    debt_date DATE NOT NULL DEFAULT CURRENT_DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### جدول `customer_debt_payments`
```sql
CREATE TABLE pos_system.customer_debt_payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    debt_id UUID NOT NULL REFERENCES pos_system.customer_debts(id),
    amount DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(50) NOT NULL DEFAULT 'cash',
    payment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔄 سير العمل (Workflow)

### 1. إنشاء بيع آجل
```
العميل → اختيار المنتجات → البيع بالأجل → إنشاء سجل في sales → إنشاء سجل في customer_debts
```

### 2. تتبع الديون
```
customer_debts → العملاء مع status IN ('pending', 'partial') → قائمة المدينين
```

### 3. تسديد الديون
```
دفعة → customer_debt_payments → تحديث customer_debts → تغيير الحالة حسب المبلغ المتبقي
```

## 🛠️ API Endpoints

### إنشاء بيع آجل
```http
POST /api/sales/credit
Content-Type: application/json

{
  "customer_id": "uuid",
  "items": [
    {
      "product_id": "uuid",
      "product_name": "اسم المنتج",
      "quantity": 2,
      "unit_price": 100.00,
      "total_price": 200.00
    }
  ],
  "subtotal": 200.00,
  "tax_amount": 0,
  "discount_amount": 0,
  "total_amount": 200.00,
  "notes": "ملاحظات",
  "cashier_name": "اسم الكاشير"
}
```

### الحصول على العملاء المدينين
```http
GET /api/customers/debtors
```

### تسديد دين
```http
POST /api/customers/debts/:debtId/pay
Content-Type: application/json

{
  "amount": 100.00,
  "payment_method": "cash",
  "notes": "دفعة جزئية"
}
```

### الحصول على ديون عميل محدد
```http
GET /api/customers/:customerId/debts
```

## 📊 حالات الدين

| الحالة | الوصف | المبلغ المتبقي |
|--------|-------|----------------|
| `pending` | دين جديد لم يسدد | = المبلغ الأصلي |
| `partial` | دين مسدد جزئياً | > 0 و < المبلغ الأصلي |
| `paid` | دين مسدد بالكامل | = 0 |

## 🧪 الاختبارات

### اختبار إنشاء بيع آجل
```bash
node test-credit-sale-system.js
```

### اختبار تسديد الديون
```bash
node test-debt-payment-direct.js
```

## ✅ الميزات المنجزة

### ✅ إنشاء البيع الآجل
- [x] إنشاء بيع جديد مع payment_method = 'credit'
- [x] إضافة عناصر البيع
- [x] تحديث المخزون
- [x] إنشاء سجل دين في customer_debts
- [x] ربط الدين بالبيع والعميل

### ✅ إدارة الديون
- [x] عرض قائمة العملاء المدينين
- [x] حساب المبلغ المتبقي تلقائياً
- [x] تتبع حالة الدين (pending/partial/paid)
- [x] عرض تفاصيل ديون عميل محدد

### ✅ تسديد الديون
- [x] تسجيل دفعات جزئية
- [x] تسجيل دفعات كاملة
- [x] تحديث حالة الدين تلقائياً
- [x] تاريخ المدفوعات لكل دين
- [x] طرق دفع متعددة (نقد، بطاقة، إلخ)

### ✅ التحقق والاختبار
- [x] اختبارات شاملة للنظام
- [x] التحقق من سلامة البيانات
- [x] اختبار السيناريوهات المختلفة
- [x] التحقق من API endpoints

## 🔍 مثال عملي

### إنشاء بيع آجل للعميل "محمد"
```
1. العميل: محمد (0770303791)
2. المنتج: كوكيز بطعم الموز × 2
3. السعر: 155 دج × 2 = 310 دج
4. النتيجة:
   - بيع جديد برقم: INV-000123
   - دين جديد: 310 دج (حالة: pending)
   - العميل يظهر في قائمة المدينين
```

### تسديد جزئي
```
1. دفعة أولى: 125 دج
2. النتيجة:
   - المبلغ المدفوع: 125 دج
   - المبلغ المتبقي: 185 دج
   - الحالة: partial
   - العميل لا يزال في قائمة المدينين
```

### تسديد نهائي
```
1. دفعة ثانية: 185 دج
2. النتيجة:
   - المبلغ المدفوع: 310 دج
   - المبلغ المتبقي: 0 دج
   - الحالة: paid
   - العميل يختفي من قائمة المدينين
```

## 🚀 الخطوات التالية المقترحة

### تحسينات إضافية
- [ ] إضافة تواريخ استحقاق للديون
- [ ] تقارير الديون (يومية، شهرية)
- [ ] تنبيهات للديون المتأخرة
- [ ] حدود ائتمان للعملاء
- [ ] طباعة فواتير الديون
- [ ] إحصائيات الديون في لوحة التحكم

### تحسينات الواجهة
- [ ] صفحة إدارة الديون في الواجهة الأمامية
- [ ] نموذج تسديد الديون
- [ ] عرض تاريخ المدفوعات
- [ ] تصدير تقارير الديون

## 📝 ملاحظات مهمة

1. **الأمان**: جميع العمليات تستخدم transactions لضمان سلامة البيانات
2. **الأداء**: تم إضافة فهارس على الجداول لتحسين الأداء
3. **المرونة**: النظام يدعم طرق دفع متعددة وملاحظات مخصصة
4. **التتبع**: كل عملية لها timestamp وتاريخ إنشاء
5. **الربط**: الديون مربوطة بالمبيعات والعملاء بعلاقات foreign key

## 🎉 الخلاصة

تم تطوير نظام شامل ومتكامل لإدارة ديون العملاء يغطي جميع المتطلبات:
- ✅ إنشاء مبيعات آجلة
- ✅ تتبع الديون
- ✅ تسديد الديون
- ✅ إدارة قائمة المدينين
- ✅ اختبارات شاملة

النظام جاهز للاستخدام ويمكن دمجه مع الواجهة الأمامية.
