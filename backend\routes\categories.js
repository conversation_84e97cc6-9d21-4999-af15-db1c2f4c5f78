const express = require('express');
const router = express.Router();
const pool = require('../database');

// عرض جميع الفئات
router.get('/', async (req, res) => {
  try {
    console.log('📂 تم استلام طلب عرض الأقسام');

    const result = await pool.query(
      'SELECT * FROM pos_system.categories WHERE is_active = true ORDER BY created_at DESC'
    );

    // إضافة icon افتراضي للأقسام
    const categoriesWithIcons = result.rows.map(category => ({
      ...category,
      icon: 'Package' // icon افتراضي
    }));

    console.log('✅ إرسال بيانات الأقسام:', categoriesWithIcons.length, 'قسم');
    res.json(categoriesWithIcons);
  } catch (error) {
    console.error('خطأ في عرض الفئات:', error);
    res.status(500).json({ error: 'خطأ في عرض الفئات' });
  }
});

// إضافة فئة جديدة
router.post('/', async (req, res) => {
  try {
    const { name, description, color, icon } = req.body;

    console.log('📂 بيانات الفئة المرسلة:', req.body);

    // استعلام بدون عمود icon
    const result = await pool.query(
      `INSERT INTO pos_system.categories (name, description, color)
       VALUES ($1, $2, $3)
       RETURNING *`,
      [name, description, color || '#3b82f6']
    );

    // إضافة icon للاستجابة
    const categoryWithIcon = {
      ...result.rows[0],
      icon: icon || 'Package'
    };

    console.log('✅ تم إنشاء القسم:', categoryWithIcon);
    res.status(201).json(categoryWithIcon);
  } catch (error) {
    console.error('❌ خطأ في إضافة الفئة:');
    console.error('رسالة:', error.message);
    console.error('كود:', error.code);
    console.error('تفاصيل:', error.detail);
    console.error('مكان:', error.where);

    if (error.code === '23505') {
      res.status(400).json({ error: 'اسم الفئة موجود مسبقاً' });
    } else {
      res.status(500).json({ error: 'خطأ في إضافة الفئة: ' + error.message });
    }
  }
});

// تعديل فئة
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, color, icon } = req.body;

    const result = await pool.query(
      `UPDATE pos_system.categories
       SET name = $1, description = $2, color = $3, updated_at = CURRENT_TIMESTAMP
       WHERE id = $4
       RETURNING *`,
      [name, description, color, id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'الفئة غير موجودة' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('خطأ في تعديل الفئة:', error);
    res.status(500).json({ error: 'خطأ في تعديل الفئة' });
  }
});

// حذف جميع الفئات (حذف منطقي) - يجب أن يكون قبل route حذف فئة واحدة
router.delete('/delete-all', async (req, res) => {
  try {
    console.log('🗑️ تم استلام طلب حذف جميع الأقسام');

    const result = await pool.query(
      'UPDATE pos_system.categories SET is_active = false WHERE is_active = true RETURNING *'
    );

    console.log(`✅ تم حذف ${result.rows.length} قسم بنجاح`);
    res.json({
      message: `تم حذف جميع الأقسام بنجاح (${result.rows.length} قسم)`,
      deletedCount: result.rows.length
    });
  } catch (error) {
    console.error('❌ خطأ في حذف جميع الأقسام:', error);
    res.status(500).json({ error: 'خطأ في حذف جميع الأقسام' });
  }
});

// حذف فئة (حذف منطقي)
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query(
      'UPDATE pos_system.categories SET is_active = false WHERE id = $1 RETURNING *',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'الفئة غير موجودة' });
    }

    res.json({ message: 'تم حذف الفئة بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف الفئة:', error);
    res.status(500).json({ error: 'خطأ في حذف الفئة' });
  }
});

module.exports = router;
