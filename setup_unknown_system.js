const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'pos_system_db',
  password: 'toossar',
  port: 5432,
});

async function setupUnknownSystem() {
  const client = await pool.connect();
  
  try {
    console.log('🔮 بدء إعداد نظام المنتجات غير المعروفة...');
    
    await client.query('BEGIN');
    
    // تعيين المخطط
    await client.query('SET search_path TO pos_system, public');
    
    // 1. إنشاء جدول المنتجات غير المعروفة
    console.log('📦 إنشاء جدول unknown_products...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS unknown_products (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          name VARCHAR(255) NOT NULL,
          price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
          description TEXT DEFAULT 'منتج غير معروف',
          barcode VARCHAR(100) UNIQUE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // 2. إنشاء جدول المبيعات غير المعروفة
    console.log('💰 إنشاء جدول unknown_sales...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS unknown_sales (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          sale_number VARCHAR(50) UNIQUE NOT NULL,
          product_name VARCHAR(255) NOT NULL,
          quantity INTEGER NOT NULL DEFAULT 1 CHECK (quantity > 0),
          unit_price DECIMAL(10,2) NOT NULL CHECK (unit_price >= 0),
          total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount >= 0),
          payment_method VARCHAR(20) DEFAULT 'cash',
          notes TEXT DEFAULT 'منتج غير معروف',
          cashier_name VARCHAR(255) DEFAULT 'النظام',
          sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // 3. تعديل جدول sale_items لدعم المنتجات غير المعروفة
    console.log('🔧 تعديل جدول sale_items...');
    try {
      await client.query('ALTER TABLE sale_items ALTER COLUMN product_id DROP NOT NULL');
      console.log('✅ تم جعل product_id يقبل NULL');
    } catch (error) {
      console.log('⚠️ product_id يقبل NULL مسبقاً');
    }
    
    try {
      await client.query('ALTER TABLE sale_items ADD COLUMN IF NOT EXISTS is_unknown BOOLEAN DEFAULT FALSE');
      console.log('✅ تم إضافة عمود is_unknown');
    } catch (error) {
      console.log('⚠️ عمود is_unknown موجود مسبقاً');
    }
    
    try {
      await client.query('ALTER TABLE sale_items ADD COLUMN IF NOT EXISTS unknown_product_code VARCHAR(50)');
      console.log('✅ تم إضافة عمود unknown_product_code');
    } catch (error) {
      console.log('⚠️ عمود unknown_product_code موجود مسبقاً');
    }
    
    // 4. إنشاء الفهارس
    console.log('📊 إنشاء الفهارس...');
    await client.query('CREATE INDEX IF NOT EXISTS idx_unknown_products_name ON unknown_products(name)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_unknown_products_price ON unknown_products(price)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_unknown_sales_date ON unknown_sales(sale_date)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_unknown_sales_product ON unknown_sales(product_name)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_sale_items_unknown ON sale_items(is_unknown) WHERE is_unknown = TRUE');
    
    // 5. إنشاء sequence لأرقام المبيعات غير المعروفة
    console.log('🔢 إنشاء sequence...');
    await client.query('CREATE SEQUENCE IF NOT EXISTS unknown_sale_number_seq START 1');
    
    // 6. وظيفة توليد رقم البيع غير المعروف
    console.log('⚙️ إنشاء الوظائف...');
    await client.query(`
      CREATE OR REPLACE FUNCTION generate_unknown_sale_number()
      RETURNS TRIGGER AS $$
      BEGIN
          IF NEW.sale_number IS NULL OR NEW.sale_number = '' THEN
              NEW.sale_number := 'UNK-' || TO_CHAR(CURRENT_DATE, 'YYYYMMDD') || '-' ||
                                LPAD(NEXTVAL('unknown_sale_number_seq')::TEXT, 4, '0');
          END IF;
          RETURN NEW;
      END;
      $$ language 'plpgsql'
    `);
    
    // 7. إنشاء trigger لتوليد رقم البيع
    console.log('🎯 إنشاء المشغلات...');
    await client.query('DROP TRIGGER IF EXISTS trigger_generate_unknown_sale_number ON unknown_sales');
    await client.query(`
      CREATE TRIGGER trigger_generate_unknown_sale_number
          BEFORE INSERT ON unknown_sales
          FOR EACH ROW EXECUTE FUNCTION generate_unknown_sale_number()
    `);
    
    // 8. وظيفة تحديث updated_at للمنتجات غير المعروفة
    await client.query(`
      CREATE TRIGGER update_unknown_products_updated_at 
          BEFORE UPDATE ON unknown_products
          FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()
    `);
    
    await client.query('COMMIT');
    
    console.log('✅ تم إعداد نظام المنتجات غير المعروفة بنجاح!');
    console.log('🔮 النظام جاهز لاستقبال المنتجات غير المعروفة');
    
    // اختبار سريع
    console.log('\n🧪 اختبار سريع...');
    const testResult = await client.query(`
      SELECT 
        (SELECT COUNT(*) FROM unknown_products) as unknown_products_count,
        (SELECT COUNT(*) FROM unknown_sales) as unknown_sales_count
    `);
    
    console.log(`📦 عدد المنتجات غير المعروفة: ${testResult.rows[0].unknown_products_count}`);
    console.log(`💰 عدد المبيعات غير المعروفة: ${testResult.rows[0].unknown_sales_count}`);
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('❌ خطأ في إعداد النظام:', error.message);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

setupUnknownSystem()
  .then(() => {
    console.log('\n🎉 تم إعداد النظام بنجاح! يمكنك الآن استخدام المنتجات غير المعروفة');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 فشل في إعداد النظام:', error);
    process.exit(1);
  });
