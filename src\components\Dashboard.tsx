import React, { useState, useEffect } from 'react';
import {
  TrendingUp,
  ShoppingCart,
  DollarSign,
  Package,
  Users,
  Clock,
  Settings,
  Star,
  Zap,
  Activity,
  BarChart3,
  PieChart,
  Target,
  Award,
  Sparkles,
  Eye,
  Heart,
  Flame,
  <PERSON><PERSON><PERSON>riangle,
  Brain
} from 'lucide-react';
import { useApp } from '../context/AppContext';
import SmartAnalysis from './SmartAnalysis';

const Dashboard: React.FC = () => {
  const { dashboardStats, refreshDashboardStats, getLowStockProducts } = useApp();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [showSmartAnalysis, setShowSmartAnalysis] = useState(false);
  const [animatedValues, setAnimatedValues] = useState({
    sales: 0,
    orders: 0,
    profits: 0,
    products: 0
  });

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    // تحديث الإحصائيات عند تحميل الصفحة
    refreshDashboardStats().catch(error => {
      console.error('خطأ في تحديث إحصائيات لوحة التحكم:', error);
    });

    return () => clearInterval(timer);
  }, []); // إزالة dependencies لتجنب infinite loop

  // useEffect منفصل لتحديث القيم المتحركة
  useEffect(() => {
    const animateValues = () => {
      setTimeout(() => setAnimatedValues({
        sales: dashboardStats.todaySales,
        orders: dashboardStats.todayOrders,
        profits: dashboardStats.totalProfit,
        products: dashboardStats.totalProducts
      }), 500);
    };
    animateValues();
  }, [dashboardStats]);

  const lowStockProducts = getLowStockProducts();

  const heroStats = [
    {
      title: 'مبيعات اليوم',
      value: animatedValues.sales.toLocaleString(),
      subtitle: 'دج',
      color: 'from-blue-500 to-cyan-400',
      icon: DollarSign,
      change: `${dashboardStats.todaySales > 0 ? '+' : ''}${((dashboardStats.todaySales / Math.max(dashboardStats.totalRevenue, 1)) * 100).toFixed(1)}%`,
      trend: 'up'
    },
    {
      title: 'طلبات اليوم',
      value: animatedValues.orders.toString(),
      subtitle: 'طلب',
      color: 'from-emerald-500 to-green-400',
      icon: ShoppingCart,
      change: `${dashboardStats.todayOrders}`,
      trend: 'up'
    },
    {
      title: 'إجمالي الأرباح',
      value: animatedValues.profits.toLocaleString(),
      subtitle: 'دج',
      color: 'from-purple-500 to-pink-400',
      icon: TrendingUp,
      change: `${dashboardStats.totalProfit > 0 ? '+' : ''}${dashboardStats.totalProfit.toLocaleString()}`,
      trend: 'up'
    },
    {
      title: 'المنتجات',
      value: animatedValues.products.toString(),
      subtitle: 'منتج',
      color: 'from-orange-500 to-red-400',
      icon: Package,
      change: `${lowStockProducts.length} نفد`,
      trend: lowStockProducts.length > 0 ? 'down' : 'up'
    }
  ];

  const quickActions = [
    { title: 'إضافة منتج', icon: Package, color: 'bg-gradient-to-r from-blue-600 to-blue-700', action: () => {} },
    { title: 'طلب جديد', icon: ShoppingCart, color: 'bg-gradient-to-r from-green-600 to-green-700', action: () => {} },
    { title: 'التحليل الذكي', icon: Brain, color: 'bg-gradient-to-r from-purple-600 to-pink-600', action: () => setShowSmartAnalysis(true) },
    { title: 'التقارير', icon: BarChart3, color: 'bg-gradient-to-r from-purple-600 to-purple-700', action: () => {} },
    { title: 'الإعدادات', icon: Settings, color: 'bg-gradient-to-r from-orange-600 to-orange-700', action: () => {} }
  ];

  // تحويل المبيعات الحديثة إلى أنشطة
  const recentActivities = dashboardStats.recentSales.length > 0
    ? dashboardStats.recentSales.slice(0, 4).map(sale => ({
        action: `مبيعة ${sale.saleNumber} - ${sale.total.toLocaleString()} دج`,
        time: `${Math.floor((new Date().getTime() - new Date(sale.date).getTime()) / (1000 * 60))} دقيقة`,
        icon: ShoppingCart,
        color: 'text-green-400'
      }))
    : [
        { action: 'لا توجد مبيعات حديثة', time: 'ابدأ البيع الآن', icon: ShoppingCart, color: 'text-gray-400' },
        { action: 'أضف منتجات جديدة', time: 'لزيادة المبيعات', icon: Package, color: 'text-blue-400' },
        { action: 'راجع المخزون', time: 'تأكد من التوفر', icon: AlertTriangle, color: 'text-yellow-400' },
        { action: 'ابدأ رحلة النجاح', time: 'معاً نحو القمة', icon: Target, color: 'text-purple-400' }
      ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6 space-y-8">
      {/* Animated Background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-cyan-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>
        <div className="absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000"></div>
      </div>

      {/* Header */}
      <div className="relative z-10 flex items-center justify-between">
        <div className="flex items-center space-x-reverse space-x-6">
          <div className="relative">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-2xl">
              <Sparkles className="w-8 h-8 text-white animate-pulse" />
            </div>
            <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
              <div className="w-2 h-2 bg-white rounded-full animate-ping"></div>
            </div>
          </div>
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-white via-purple-200 to-cyan-200 bg-clip-text text-transparent mb-2">
              🚀 لوحة التحكم الخيالية
            </h1>
            <p className="text-slate-300 text-lg">مرحباً بك في عالم الإبداع والتكنولوجيا المتطورة</p>
            <div className="flex items-center mt-2 text-sm text-slate-400">
              <Clock className="w-4 h-4 ml-2" />
              <span>{currentTime.toLocaleTimeString('ar-SA')}</span>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-reverse space-x-4">
          {quickActions.map((action, index) => (
            <button
              key={index}
              onClick={action.action}
              className={`${action.color} hover:scale-105 transform transition-all duration-300 px-6 py-3 rounded-xl flex items-center space-x-reverse space-x-2 shadow-lg hover:shadow-2xl`}
            >
              <action.icon className="w-5 h-5 text-white" />
              <span className="text-white font-medium">{action.title}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Hero Stats */}
      <div className="relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {heroStats.map((stat, index) => (
            <div
              key={index}
              className="group relative overflow-hidden bg-white/10 backdrop-blur-lg rounded-3xl p-6 border border-white/20 hover:border-white/40 transition-all duration-500 hover:scale-105 hover:shadow-2xl"
            >
              {/* Gradient Background */}
              <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-20 group-hover:opacity-30 transition-opacity duration-500`}></div>

              {/* Content */}
              <div className="relative z-10">
                <div className="flex items-center justify-between mb-4">
                  <div className={`w-14 h-14 bg-gradient-to-r ${stat.color} rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                    <stat.icon className="w-7 h-7 text-white" />
                  </div>
                  <div className="text-right">
                    <div className={`text-sm font-medium text-green-400 flex items-center`}>
                      <TrendingUp className="w-4 h-4 ml-1" />
                      {stat.change}
                    </div>
                  </div>
                </div>

                <div className="text-right">
                  <h3 className="text-slate-300 text-sm font-medium mb-2">{stat.title}</h3>
                  <div className="flex items-baseline justify-end space-x-reverse space-x-2">
                    <span className="text-3xl font-bold text-white">{stat.value}</span>
                    <span className="text-slate-400 text-sm">{stat.subtitle}</span>
                  </div>
                </div>

                {/* Animated Progress Bar */}
                <div className="mt-4 w-full bg-white/10 rounded-full h-2 overflow-hidden">
                  <div
                    className={`h-full bg-gradient-to-r ${stat.color} rounded-full transition-all duration-1000 ease-out`}
                    style={{ width: `${Math.min(100, (index + 1) * 25)}%` }}
                  ></div>
                </div>
              </div>

              {/* Hover Effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
            </div>
          ))}
        </div>
      </div>

      {/* Interactive Charts Section */}
      <div className="relative z-10 grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 3D Sales Chart */}
        <div className="group relative overflow-hidden bg-white/5 backdrop-blur-xl rounded-3xl p-8 border border-white/10 hover:border-purple-500/50 transition-all duration-500">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-cyan-500/10 opacity-50 group-hover:opacity-70 transition-opacity duration-500"></div>

          <div className="relative z-10">
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center space-x-reverse space-x-4">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-400 rounded-2xl flex items-center justify-center">
                  <BarChart3 className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-white">📊 المبيعات التفاعلية</h3>
                  <p className="text-slate-300 text-sm">تحليل متقدم للأداء</p>
                </div>
              </div>
              <div className="flex items-center space-x-reverse space-x-2">
                <button className="px-4 py-2 bg-gradient-to-r from-blue-500 to-cyan-400 rounded-xl text-white text-sm font-medium hover:scale-105 transition-transform duration-300">
                  الشهر الحالي
                </button>
              </div>
            </div>

            <div className="mb-6">
              <div className="flex items-baseline justify-end space-x-reverse space-x-2 mb-2">
                <span className="text-4xl font-bold bg-gradient-to-r from-blue-400 to-cyan-300 bg-clip-text text-transparent">
                  {dashboardStats.totalRevenue.toLocaleString()}
                </span>
                <span className="text-slate-400 text-lg">دج</span>
              </div>
              <div className="flex items-center justify-end space-x-reverse space-x-2">
                <TrendingUp className="w-4 h-4 text-green-400" />
                <span className="text-green-400 text-sm font-medium">
                  إجمالي الإيرادات من {dashboardStats.recentSales.length} مبيعة
                </span>
              </div>
            </div>

            {/* 3D Chart Placeholder */}
            <div className="relative h-64 bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl p-6 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-cyan-500/20 animate-pulse"></div>
              <div className="relative z-10 h-full flex items-center justify-center">
                <div className="text-center">
                  <Activity className="w-16 h-16 text-blue-400 mx-auto mb-4 animate-bounce" />
                  <p className="text-slate-300 font-medium">مخطط تفاعلي ثلاثي الأبعاد</p>
                  <p className="text-slate-400 text-sm mt-2">يعرض البيانات بشكل مبهر</p>
                </div>
              </div>
            </div>

            <div className="mt-6 grid grid-cols-3 gap-4">
              {[
                {
                  label: 'اليوم',
                  value: `${dashboardStats.todaySales.toLocaleString()} دج`,
                  color: 'from-blue-400 to-blue-600'
                },
                {
                  label: 'الطلبات',
                  value: `${dashboardStats.todayOrders}`,
                  color: 'from-green-400 to-green-600'
                },
                {
                  label: 'المنتجات',
                  value: `${dashboardStats.totalProducts}`,
                  color: 'from-purple-400 to-purple-600'
                }
              ].map((item, index) => (
                <div key={index} className="text-center p-4 bg-white/5 rounded-xl border border-white/10">
                  <p className="text-slate-400 text-sm mb-1">{item.label}</p>
                  <p className={`text-lg font-bold bg-gradient-to-r ${item.color} bg-clip-text text-transparent`}>
                    {item.value}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* AI Profits Analysis */}
        <div className="group relative overflow-hidden bg-white/5 backdrop-blur-xl rounded-3xl p-8 border border-white/10 hover:border-green-500/50 transition-all duration-500">
          <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 via-emerald-500/10 to-teal-500/10 opacity-50 group-hover:opacity-70 transition-opacity duration-500"></div>

          <div className="relative z-10">
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center space-x-reverse space-x-4">
                <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-400 rounded-2xl flex items-center justify-center">
                  <PieChart className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-white">🤖 تحليل الأرباح بالذكاء الاصطناعي</h3>
                  <p className="text-slate-300 text-sm">توقعات ذكية للمستقبل</p>
                </div>
              </div>
              <div className="flex items-center space-x-reverse space-x-2">
                <button
                  onClick={() => setShowSmartAnalysis(true)}
                  className="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-400 rounded-xl text-white text-sm font-medium hover:scale-105 transition-transform duration-300 flex items-center space-x-reverse space-x-2"
                >
                  <Brain className="w-4 h-4" />
                  <span>تحليل ذكي متقدم</span>
                </button>
              </div>
            </div>

            <div className="mb-6">
              <div className="flex items-baseline justify-end space-x-reverse space-x-2 mb-2">
                <span className="text-4xl font-bold bg-gradient-to-r from-green-400 to-emerald-300 bg-clip-text text-transparent">
                  {dashboardStats.totalProfit.toLocaleString()}
                </span>
                <span className="text-slate-400 text-lg">دج</span>
              </div>
              <div className="flex items-center justify-end space-x-reverse space-x-2">
                <Zap className="w-4 h-4 text-yellow-400" />
                <span className="text-yellow-400 text-sm font-medium">
                  إجمالي الأرباح من جميع المبيعات
                </span>
              </div>
            </div>

            {/* AI Analysis Visualization */}
            <div className="relative h-64 bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-2xl p-6 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-green-500/20 via-emerald-500/20 to-teal-500/20 animate-pulse"></div>
              <div className="relative z-10 h-full flex items-center justify-center">
                <div className="text-center">
                  <Target className="w-16 h-16 text-green-400 mx-auto mb-4 animate-spin" />
                  <p className="text-slate-300 font-medium">تحليل ذكي متقدم</p>
                  <p className="text-slate-400 text-sm mt-2">يتنبأ بالاتجاهات المستقبلية</p>
                </div>
              </div>
            </div>

            <div className="mt-6 grid grid-cols-3 gap-4">
              {[
                {
                  label: 'الأرباح',
                  value: `${dashboardStats.totalProfit.toLocaleString()} دج`,
                  color: 'from-green-400 to-green-600'
                },
                {
                  label: 'الإيرادات',
                  value: `${dashboardStats.totalRevenue.toLocaleString()} دج`,
                  color: 'from-emerald-400 to-emerald-600'
                },
                {
                  label: 'نفد المخزون',
                  value: `${lowStockProducts.length}`,
                  color: lowStockProducts.length > 0 ? 'from-red-400 to-red-600' : 'from-teal-400 to-teal-600'
                }
              ].map((item, index) => (
                <div key={index} className="text-center p-4 bg-white/5 rounded-xl border border-white/10">
                  <p className="text-slate-400 text-sm mb-1">{item.label}</p>
                  <p className={`text-lg font-bold bg-gradient-to-r ${item.color} bg-clip-text text-transparent`}>
                    {item.value}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Advanced Analytics Section */}
      <div className="relative z-10 grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Real-time Activities */}
        <div className="lg:col-span-2 group relative overflow-hidden bg-white/5 backdrop-blur-xl rounded-3xl p-8 border border-white/10 hover:border-cyan-500/50 transition-all duration-500">
          <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/10 via-blue-500/10 to-purple-500/10 opacity-50 group-hover:opacity-70 transition-opacity duration-500"></div>

          <div className="relative z-10">
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center space-x-reverse space-x-4">
                <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-blue-400 rounded-2xl flex items-center justify-center">
                  <Activity className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-white">⚡ النشاطات المباشرة</h3>
                  <p className="text-slate-300 text-sm">تحديثات فورية للأحداث</p>
                </div>
              </div>
              <div className="flex items-center space-x-reverse space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-ping"></div>
                <span className="text-green-400 text-sm font-medium">مباشر</span>
              </div>
            </div>

            <div className="space-y-4">
              {recentActivities.map((activity, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-white/5 rounded-2xl border border-white/10 hover:border-white/20 transition-all duration-300 group/item">
                  <div className="flex items-center space-x-reverse space-x-4">
                    <div className={`w-10 h-10 bg-gradient-to-r from-slate-600 to-slate-700 rounded-xl flex items-center justify-center group-hover/item:scale-110 transition-transform duration-300`}>
                      <activity.icon className={`w-5 h-5 ${activity.color}`} />
                    </div>
                    <div>
                      <p className="text-white font-medium">{activity.action}</p>
                      <p className="text-slate-400 text-sm">{activity.time}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-reverse space-x-2">
                    <Eye className="w-4 h-4 text-slate-400 hover:text-white transition-colors cursor-pointer" />
                    <Heart className="w-4 h-4 text-slate-400 hover:text-red-400 transition-colors cursor-pointer" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="group relative overflow-hidden bg-white/5 backdrop-blur-xl rounded-3xl p-8 border border-white/10 hover:border-orange-500/50 transition-all duration-500">
          <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 via-red-500/10 to-pink-500/10 opacity-50 group-hover:opacity-70 transition-opacity duration-500"></div>

          <div className="relative z-10">
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center space-x-reverse space-x-4">
                <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-400 rounded-2xl flex items-center justify-center">
                  <Award className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-white">🏆 مقاييس الأداء</h3>
                  <p className="text-slate-300 text-sm">إنجازات مميزة</p>
                </div>
              </div>
            </div>

            <div className="space-y-6">
              {[
                {
                  title: 'أفضل منتج',
                  value: dashboardStats.topSellingProducts.length > 0
                    ? dashboardStats.topSellingProducts[0].productName
                    : 'لا توجد مبيعات',
                  icon: Star,
                  color: 'text-yellow-400'
                },
                {
                  title: 'إجمالي المنتجات',
                  value: dashboardStats.totalProducts.toString(),
                  icon: Package,
                  color: 'text-blue-400'
                },
                {
                  title: 'منتجات نفدت',
                  value: lowStockProducts.length.toString(),
                  icon: AlertTriangle,
                  color: lowStockProducts.length > 0 ? 'text-red-400' : 'text-green-400'
                },
                {
                  title: 'إجمالي الإيرادات',
                  value: `${dashboardStats.totalRevenue.toLocaleString()} دج`,
                  icon: TrendingUp,
                  color: 'text-green-400'
                }
              ].map((metric, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-white/5 rounded-2xl border border-white/10">
                  <div className="flex items-center space-x-reverse space-x-3">
                    <metric.icon className={`w-6 h-6 ${metric.color}`} />
                    <div>
                      <p className="text-slate-400 text-sm">{metric.title}</p>
                      <p className="text-white font-medium">{metric.value}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Smart Analysis Modal */}
      {showSmartAnalysis && (
        <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm">
          <div className="absolute inset-0 overflow-auto">
            <div className="relative">
              <button
                onClick={() => setShowSmartAnalysis(false)}
                className="fixed top-4 right-4 z-60 bg-red-500 hover:bg-red-600 text-white p-3 rounded-full shadow-lg transition-colors"
              >
                ✕
              </button>
              <SmartAnalysis />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;