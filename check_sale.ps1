Write-Host "Checking last sale..." -ForegroundColor Yellow

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5002/api/sales" -Method GET
    
    if ($response -and $response.Count -gt 0) {
        $lastSale = $response[0]
        Write-Host "SUCCESS! Found sales in database!" -ForegroundColor Green
        Write-Host "Last Sale Number: $($lastSale.sale_number)" -ForegroundColor Green
        Write-Host "Total Amount: $($lastSale.total_amount)" -ForegroundColor Green
        Write-Host "Date: $($lastSale.created_at)" -ForegroundColor Green
        Write-Host "Payment Method: $($lastSale.payment_method)" -ForegroundColor Green
        Write-Host "Sales are being saved to database!" -ForegroundColor Magenta
    } else {
        Write-Host "No sales found in database" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "FAILED to get sales:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
}
