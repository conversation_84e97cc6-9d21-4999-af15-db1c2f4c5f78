/* CSS للطباعة - الفاتورة الاحترافية */

@media print {
  /* إعدادات الصفحة */
  @page {
    size: A4;
    margin: 15mm;
  }

  /* إعدادات عامة للطباعة */
  body {
    margin: 0;
    padding: 0;
    font-family: 'Aria<PERSON>', 'Segoe UI', sans-serif;
    color: #000 !important;
    background: #fff !important;
    line-height: 1.4;
    font-size: 12px;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  * {
    box-sizing: border-box;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  /* منع كسر الصفحات في الأقسام المهمة */
  .invoice-header {
    page-break-inside: avoid;
    break-inside: avoid;
  }

  .invoice-details {
    page-break-inside: avoid;
    break-inside: avoid;
  }

  .items-table {
    page-break-inside: avoid;
    break-inside: avoid;
  }

  .totals-section {
    page-break-inside: avoid;
    break-inside: avoid;
  }

  .invoice-footer {
    page-break-inside: avoid;
    break-inside: avoid;
  }

  /* منع كسر الجداول */
  table {
    page-break-inside: avoid;
    break-inside: avoid;
  }

  tr {
    page-break-inside: avoid;
    break-inside: avoid;
  }

  /* تحسين أحجام النصوص للطباعة */
  h1 {
    font-size: 18px !important;
    margin: 0 0 8px 0 !important;
    line-height: 1.3 !important;
  }

  h2 {
    font-size: 16px !important;
    margin: 0 0 6px 0 !important;
    line-height: 1.3 !important;
  }

  h3 {
    font-size: 14px !important;
    margin: 0 0 6px 0 !important;
    line-height: 1.3 !important;
  }

  h4 {
    font-size: 12px !important;
    margin: 0 0 4px 0 !important;
    line-height: 1.3 !important;
  }

  p {
    margin: 0 0 4px 0 !important;
    line-height: 1.4 !important;
  }

  div {
    margin: 0 !important;
  }

  table {
    margin: 0 0 12px 0 !important;
  }

  td, th {
    padding: 8px 12px !important;
    line-height: 1.4 !important;
  }

  /* تحسين المسافات */
  .mb-2 { margin-bottom: 4px !important; }
  .mb-1 { margin-bottom: 2px !important; }
  .pb-1 { padding-bottom: 2px !important; }
  .pt-1 { padding-top: 2px !important; }
  .p-2 { padding: 4px !important; }
  .p-1 { padding: 2px !important; }
  .gap-2 { gap: 4px !important; }
  .gap-1 { gap: 2px !important; }
  .space-y-1 > * + * { margin-top: 2px !important; }
  .space-y-0\.5 > * + * { margin-top: 1px !important; }
  .space-y-0 > * + * { margin-top: 0 !important; }

  /* تحسين الحدود والألوان */
  .border-2 { border-width: 1px !important; }
  .border-b-2 { border-bottom-width: 1px !important; }
  .border-t-2 { border-top-width: 1px !important; }

  /* ضمان ظهور الألوان */
  .bg-gray-800 {
    background-color: #1f2937 !important;
    color: #ffffff !important;
  }

  .text-white {
    color: #ffffff !important;
  }

  .text-gray-800 {
    color: #1f2937 !important;
  }

  .text-gray-600 {
    color: #4b5563 !important;
  }

  .text-green-600 {
    color: #059669 !important;
  }

  .text-red-600 {
    color: #dc2626 !important;
  }

  .text-yellow-500 {
    color: #eab308 !important;
  }

  /* تحسين الجداول */
  .border-collapse {
    border-collapse: collapse !important;
  }

  .border {
    border: 1px solid #000 !important;
  }

  .border-gray-800 {
    border-color: #1f2937 !important;
  }

  .border-gray-300 {
    border-color: #d1d5db !important;
  }

  .border-gray-400 {
    border-color: #9ca3af !important;
  }

  /* إخفاء العناصر غير المطلوبة للطباعة */
  .no-print {
    display: none !important;
  }

  /* تحسين الباركود */
  canvas {
    max-width: 100% !important;
    height: auto !important;
  }

  /* تحسين الشبكة */
  .grid {
    display: grid !important;
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }

  /* تحسين النصوص */
  .text-xs { font-size: 11px !important; }
  .text-sm { font-size: 12px !important; }
  .text-base { font-size: 14px !important; }
  .text-lg { font-size: 14px !important; }
  .text-xl { font-size: 16px !important; }
  .text-2xl { font-size: 18px !important; }

  .font-bold { font-weight: bold !important; }
  .font-semibold { font-weight: 600 !important; }
  .font-medium { font-weight: 500 !important; }

  .text-center { text-align: center !important; }
  .text-right { text-align: right !important; }
  .text-left { text-align: left !important; }

  .leading-tight { line-height: 1.25 !important; }
  .leading-relaxed { line-height: 1.5 !important; }

  /* تحسين المرونة */
  .flex {
    display: flex !important;
  }

  .items-center {
    align-items: center !important;
  }

  .items-start {
    align-items: flex-start !important;
  }

  .justify-between {
    justify-content: space-between !important;
  }

  .justify-center {
    justify-content: center !important;
  }

  /* تحسين الخلفيات */
  .bg-gray-100 {
    background-color: #f3f4f6 !important;
  }

  .bg-white {
    background-color: #ffffff !important;
  }

  /* تحسين الحواف المدورة */
  .rounded {
    border-radius: 4px !important;
  }

  .rounded-full {
    border-radius: 50% !important;
  }

  /* تحسين العرض والارتفاع */
  .w-full { width: 100% !important; }
  .h-full { height: 100% !important; }
  .w-8 { width: 32px !important; }
  .h-8 { height: 32px !important; }
  .w-12 { width: 48px !important; }
  .h-12 { height: 48px !important; }
  .w-16 { width: 64px !important; }
  .h-16 { height: 64px !important; }
  .w-20 { width: 80px !important; }
  .w-24 { width: 96px !important; }
  .w-32 { width: 128px !important; }
  .h-4 { height: 16px !important; }
  .h-6 { height: 24px !important; }
  .h-8 { height: 32px !important; }

  /* تحسين الألوان الإضافية */
  .text-blue-600 { color: #2563eb !important; }
  .text-purple-600 { color: #9333ea !important; }
  .text-yellow-600 { color: #ca8a04 !important; }
  .text-indigo-600 { color: #4f46e5 !important; }
  .text-emerald-600 { color: #059669 !important; }

  .bg-blue-50 { background-color: #eff6ff !important; }
  .bg-green-50 { background-color: #f0fdf4 !important; }
  .bg-purple-50 { background-color: #faf5ff !important; }
  .bg-yellow-50 { background-color: #fefce8 !important; }
  .bg-indigo-50 { background-color: #eef2ff !important; }
  .bg-emerald-50 { background-color: #ecfdf5 !important; }

  .border-blue-200 { border-color: #bfdbfe !important; }
  .border-green-200 { border-color: #bbf7d0 !important; }
  .border-purple-200 { border-color: #e9d5ff !important; }
  .border-yellow-200 { border-color: #fef3c7 !important; }
  .border-blue-500 { border-color: #3b82f6 !important; }
  .border-green-500 { border-color: #22c55e !important; }
  .border-orange-500 { border-color: #f97316 !important; }
  .border-purple-400 { border-color: #c084fc !important; }
  .border-blue-600 { border-color: #2563eb !important; }

  /* تحسين التدرجات */
  .bg-gradient-to-r, .bg-gradient-to-br { background: #f3f4f6 !important; }
  .from-gray-800, .to-gray-700 { background-color: #1f2937 !important; }
  .from-blue-600, .to-indigo-600 { background-color: #2563eb !important; }
  .from-blue-50, .to-indigo-50 { background-color: #eff6ff !important; }
  .from-green-50, .to-emerald-50 { background-color: #f0fdf4 !important; }

  /* إزالة الظلال للطباعة */
  .shadow-lg, .shadow-inner { box-shadow: none !important; }
}
