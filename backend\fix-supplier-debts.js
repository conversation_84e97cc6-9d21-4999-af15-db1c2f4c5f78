const pool = require('./database');
const fs = require('fs');

async function fixSupplierDebtsTable() {
  try {
    console.log('🔧 بدء إصلاح جدول ديون الموردين...');

    // قراءة ملف SQL
    const sqlContent = fs.readFileSync('../fix-supplier-debts-table.sql', 'utf8');
    
    // تنفيذ الاستعلامات
    const result = await pool.query(sqlContent);
    
    console.log('✅ تم إصلاح جدول supplier_debts بنجاح');
    console.log('📊 النتائج:', result);

    // التحقق من البيانات الحالية
    const debtsCheck = await pool.query(`
      SELECT 
        COUNT(*) as total_debts,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_debts,
        COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_debts,
        COUNT(CASE WHEN status = 'partial' THEN 1 END) as partial_debts
      FROM pos_system.supplier_debts
    `);
    
    console.log('📊 إحصائيات الديون:');
    console.log(debtsCheck.rows[0]);

  } catch (error) {
    console.error('❌ خطأ في إصلاح جدول supplier_debts:', error);
  } finally {
    process.exit(0);
  }
}

fixSupplierDebtsTable();
