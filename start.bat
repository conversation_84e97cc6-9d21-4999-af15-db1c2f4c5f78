@echo off
echo 🚀 بدء تشغيل نظام نقاط البيع
echo ============================

echo 📦 بدء تشغيل الخادم الخلفي...
cd backend
start "Backend Server" cmd /k "npm run dev"

echo ⏳ انتظار تشغيل الخادم الخلفي...
timeout /t 3 /nobreak > nul

echo 🌐 بدء تشغيل الواجهة الأمامية...
cd ..
start "Frontend Server" cmd /k "npm run dev"

echo ✅ تم تشغيل النظام بنجاح!
echo 📱 الواجهة الأمامية: http://localhost:3000
echo 🔧 الخادم الخلفي: http://localhost:5003
echo.
echo اضغط أي مفتاح للإغلاق...
pause > nul
