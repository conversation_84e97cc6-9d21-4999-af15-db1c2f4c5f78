-- 🔮 إعداد سريع لنظام المنتجات غير المعروفة
-- نسخ والصق هذا الكود في pgAdmin Query Tool

-- تعيين المخطط
SET search_path TO pos_system, public;

-- 1. إن<PERSON>اء جدول المنتجات غير المعروفة
CREATE TABLE IF NOT EXISTS unknown_products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    description TEXT DEFAULT 'منتج غير معروف',
    barcode VARCHAR(100) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. إنشاء جدول المبيعات غير المعروفة
CREATE TABLE IF NOT EXISTS unknown_sales (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sale_number VARCHAR(50) UNIQUE NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1 CHECK (quantity > 0),
    unit_price DECIMAL(10,2) NOT NULL CHECK (unit_price >= 0),
    total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount >= 0),
    payment_method VARCHAR(20) DEFAULT 'cash',
    notes TEXT DEFAULT 'منتج غير معروف',
    cashier_name VARCHAR(255) DEFAULT 'النظام',
    sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. تعديل جدول sale_items لدعم المنتجات غير المعروفة
DO $$
BEGIN
    -- جعل product_id يقبل NULL
    BEGIN
        ALTER TABLE sale_items ALTER COLUMN product_id DROP NOT NULL;
        RAISE NOTICE 'تم جعل product_id يقبل NULL';
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'product_id يقبل NULL مسبقاً';
    END;
    
    -- إضافة عمود is_unknown
    BEGIN
        ALTER TABLE sale_items ADD COLUMN is_unknown BOOLEAN DEFAULT FALSE;
        RAISE NOTICE 'تم إضافة عمود is_unknown';
    EXCEPTION
        WHEN duplicate_column THEN
            RAISE NOTICE 'عمود is_unknown موجود مسبقاً';
    END;
    
    -- إضافة عمود unknown_product_code
    BEGIN
        ALTER TABLE sale_items ADD COLUMN unknown_product_code VARCHAR(50);
        RAISE NOTICE 'تم إضافة عمود unknown_product_code';
    EXCEPTION
        WHEN duplicate_column THEN
            RAISE NOTICE 'عمود unknown_product_code موجود مسبقاً';
    END;
END $$;

-- 4. إنشاء الفهارس
CREATE INDEX IF NOT EXISTS idx_unknown_products_name ON unknown_products(name);
CREATE INDEX IF NOT EXISTS idx_unknown_products_price ON unknown_products(price);
CREATE INDEX IF NOT EXISTS idx_unknown_sales_date ON unknown_sales(sale_date);
CREATE INDEX IF NOT EXISTS idx_unknown_sales_product ON unknown_sales(product_name);
CREATE INDEX IF NOT EXISTS idx_sale_items_unknown ON sale_items(is_unknown) WHERE is_unknown = TRUE;

-- 5. إنشاء sequence لأرقام المبيعات غير المعروفة
CREATE SEQUENCE IF NOT EXISTS unknown_sale_number_seq START 1;

-- 6. وظيفة توليد رقم البيع غير المعروف
CREATE OR REPLACE FUNCTION generate_unknown_sale_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.sale_number IS NULL OR NEW.sale_number = '' THEN
        NEW.sale_number := 'UNK-' || TO_CHAR(CURRENT_DATE, 'YYYYMMDD') || '-' ||
                          LPAD(NEXTVAL('unknown_sale_number_seq')::TEXT, 4, '0');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 7. إنشاء trigger لتوليد رقم البيع
DROP TRIGGER IF EXISTS trigger_generate_unknown_sale_number ON unknown_sales;
CREATE TRIGGER trigger_generate_unknown_sale_number
    BEFORE INSERT ON unknown_sales
    FOR EACH ROW EXECUTE FUNCTION generate_unknown_sale_number();

-- 8. وظيفة تحديث updated_at للمنتجات غير المعروفة
DROP TRIGGER IF EXISTS update_unknown_products_updated_at ON unknown_products;
CREATE TRIGGER update_unknown_products_updated_at 
    BEFORE UPDATE ON unknown_products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 9. اختبار سريع
DO $$
DECLARE
    unknown_products_count INTEGER;
    unknown_sales_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO unknown_products_count FROM unknown_products;
    SELECT COUNT(*) INTO unknown_sales_count FROM unknown_sales;
    
    RAISE NOTICE '✅ تم إعداد النظام بنجاح!';
    RAISE NOTICE '📦 عدد المنتجات غير المعروفة: %', unknown_products_count;
    RAISE NOTICE '💰 عدد المبيعات غير المعروفة: %', unknown_sales_count;
    RAISE NOTICE '🔮 النظام جاهز لاستقبال المنتجات غير المعروفة';
END $$;
