const pool = require('./database');

async function checkStock() {
  try {
    console.log('🔍 فحص المخزون الحالي...');

    // عرض جميع المنتجات ومخزونها
    const result = await pool.query(`
      SELECT 
        id, name, stock, min_stock, price, is_active
      FROM products 
      ORDER BY stock ASC
    `);

    console.log('\n📦 المنتجات والمخزون:');
    console.log('='.repeat(80));
    
    if (result.rows.length === 0) {
      console.log('❌ لا توجد منتجات في قاعدة البيانات');
      return;
    }

    result.rows.forEach((product, index) => {
      const stockStatus = product.stock <= 0 ? '❌ نفد' : 
                         product.stock <= product.min_stock ? '⚠️ منخفض' : '✅ متوفر';
      
      console.log(`${index + 1}. ${product.name}`);
      console.log(`   المخزون: ${product.stock} | الحد الأدنى: ${product.min_stock} | السعر: ${product.price} دج`);
      console.log(`   الحالة: ${stockStatus} | نشط: ${product.is_active ? 'نعم' : 'لا'}`);
      console.log(`   ID: ${product.id}`);
      console.log('-'.repeat(60));
    });

    // إحصائيات سريعة
    const totalProducts = result.rows.length;
    const outOfStock = result.rows.filter(p => p.stock <= 0).length;
    const lowStock = result.rows.filter(p => p.stock > 0 && p.stock <= p.min_stock).length;
    const inStock = result.rows.filter(p => p.stock > p.min_stock).length;

    console.log('\n📊 إحصائيات المخزون:');
    console.log(`إجمالي المنتجات: ${totalProducts}`);
    console.log(`نفد المخزون: ${outOfStock}`);
    console.log(`مخزون منخفض: ${lowStock}`);
    console.log(`مخزون متوفر: ${inStock}`);

  } catch (error) {
    console.error('❌ خطأ في فحص المخزون:', error);
  } finally {
    process.exit();
  }
}

checkStock();
