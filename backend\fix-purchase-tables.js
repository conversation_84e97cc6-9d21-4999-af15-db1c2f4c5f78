const pool = require('./database');

async function fixPurchaseTables() {
  try {
    console.log('🔧 فحص وإصلاح جداول المشتريات...');
    
    const client = await pool.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
    
    // التحقق من وجود جدول purchases
    const purchasesCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'pos_system' 
        AND table_name = 'purchases'
      );
    `);
    
    console.log('📦 جدول purchases موجود:', purchasesCheck.rows[0].exists);
    
    if (!purchasesCheck.rows[0].exists) {
      console.log('⚠️ جدول purchases غير موجود. سيتم إنشاؤه...');
      
      await client.query(`
        CREATE TABLE pos_system.purchases (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          invoice_number VARCHAR(50) UNIQUE NOT NULL,
          supplier_id UUID REFERENCES pos_system.suppliers(id),
          subtotal DECIMAL(10,2) DEFAULT 0,
          tax_amount DECIMAL(10,2) DEFAULT 0,
          discount_amount DECIMAL(10,2) DEFAULT 0,
          total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
          amount_paid DECIMAL(10,2) DEFAULT 0,
          payment_method VARCHAR(50) DEFAULT 'cash',
          payment_status VARCHAR(50) DEFAULT 'paid',
          notes TEXT,
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `);
      
      console.log('✅ تم إنشاء جدول purchases بنجاح');
    }
    
    // التحقق من وجود جدول purchase_items
    const itemsCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'pos_system' 
        AND table_name = 'purchase_items'
      );
    `);
    
    console.log('📋 جدول purchase_items موجود:', itemsCheck.rows[0].exists);
    
    if (!itemsCheck.rows[0].exists) {
      console.log('⚠️ جدول purchase_items غير موجود. سيتم إنشاؤه...');
      
      await client.query(`
        CREATE TABLE pos_system.purchase_items (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          purchase_id UUID NOT NULL REFERENCES pos_system.purchases(id) ON DELETE CASCADE,
          product_id UUID REFERENCES pos_system.products(id),
          product_name VARCHAR(255) NOT NULL,
          quantity INTEGER NOT NULL,
          unit_price DECIMAL(10,2) NOT NULL,
          discount DECIMAL(10,2) DEFAULT 0,
          total_price DECIMAL(10,2) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX idx_purchase_items_purchase ON pos_system.purchase_items(purchase_id);
        CREATE INDEX idx_purchase_items_product ON pos_system.purchase_items(product_id);
      `);
      
      console.log('✅ تم إنشاء جدول purchase_items بنجاح');
    }
    
    // التحقق من هيكل جدول purchases
    const purchasesStructure = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_schema = 'pos_system' 
      AND table_name = 'purchases'
      ORDER BY ordinal_position;
    `);
    
    console.log('📊 هيكل جدول purchases:');
    purchasesStructure.rows.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
    });
    
    // التحقق من هيكل جدول purchase_items
    const itemsStructure = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_schema = 'pos_system' 
      AND table_name = 'purchase_items'
      ORDER BY ordinal_position;
    `);
    
    console.log('📋 هيكل جدول purchase_items:');
    itemsStructure.rows.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
    });
    
    // عد الفواتير الموجودة
    const purchasesCount = await client.query('SELECT COUNT(*) FROM pos_system.purchases');
    console.log('📊 عدد فواتير المشتريات:', purchasesCount.rows[0].count);
    
    client.release();
    
  } catch (error) {
    console.error('❌ خطأ:', error.message);
    console.error('❌ تفاصيل الخطأ:', error.stack);
  }
}

fixPurchaseTables().then(() => {
  console.log('✅ انتهى الفحص');
  process.exit();
}).catch(err => {
  console.error('❌ فشل الفحص:', err);
  process.exit(1);
});
