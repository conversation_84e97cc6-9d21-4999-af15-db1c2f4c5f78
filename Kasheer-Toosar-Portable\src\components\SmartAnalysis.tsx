import React, { useState, useEffect } from 'react';
import {
  Brain,
  TrendingUp,
  TrendingDown,
  Zap,
  Target,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  PieChart,
  Activity,
  Sparkles,
  Lightbulb,
  Eye,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react';
import apiService from '../services/api';

interface SmartAnalysisData {
  success: boolean;
  message: string;
  analysis: {
    trends: {
      growth_rate: number;
      avg_daily_revenue: number;
      avg_daily_profit: number;
      avg_daily_orders: number;
      trend_direction: string;
    };
    predictions: {
      next_week: { revenue: number; profit: number };
      next_month: { revenue: number; profit: number };
      confidence: number;
    };
    top_products: Array<{
      name: string;
      total_sold: number;
      daily_demand: string;
      days_until_stockout: number;
      profit_margin: number;
    }>;
    customer_patterns: Array<{
      time: string;
      transactions: number;
      avg_value: number;
    }>;
    recommendations: Array<{
      type: string;
      priority: string;
      message: string;
      action: string;
      impact: string;
    }>;
    smart_kpis: {
      inventory_health: number;
      demand_stability: number;
      profit_efficiency: number;
    };
  };
  ai_insights: string[];
}

const SmartAnalysis: React.FC = () => {
  const [analysisData, setAnalysisData] = useState<SmartAnalysisData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadSmartAnalysis = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('🤖 تحميل التحليل الذكي المتقدم...');
      // جرب الخادم الأساسي أولاً، ثم التجريبي
      let response;
      try {
        response = await apiService.getSmartAnalysis();
      } catch (mainError) {
        console.log('🧪 جرب الخادم التجريبي...');
        response = await apiService.getSmartAnalysisTest();
      }

      if (response && response.success) {
        setAnalysisData(response);
        console.log('✅ تم تحميل التحليل الذكي بنجاح');
      } else {
        throw new Error(response?.message || 'استجابة غير صحيحة من الخادم');
      }
    } catch (error: any) {
      console.error('❌ خطأ في تحميل التحليل الذكي:', error);
      const errorMessage = error?.message || error?.toString() || 'فشل في تحميل التحليل الذكي';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSmartAnalysis();
  }, []);

  const getTrendIcon = (direction: string) => {
    switch (direction) {
      case 'صاعد قوي': return <ArrowUp className="w-5 h-5 text-green-400" />;
      case 'صاعد': return <TrendingUp className="w-5 h-5 text-green-400" />;
      case 'مستقر': return <Minus className="w-5 h-5 text-yellow-400" />;
      case 'هابط': return <TrendingDown className="w-5 h-5 text-red-400" />;
      default: return <Activity className="w-5 h-5 text-gray-400" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'عالية': return 'from-red-500 to-pink-500';
      case 'متوسطة': return 'from-yellow-500 to-orange-500';
      case 'منخفضة': return 'from-blue-500 to-cyan-500';
      default: return 'from-gray-500 to-slate-500';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Brain className="w-16 h-16 text-purple-400 mx-auto mb-4 animate-pulse" />
              <h3 className="text-xl font-bold text-white mb-2">🤖 التحليل الذكي قيد التشغيل</h3>
              <p className="text-slate-400">جاري تحليل البيانات والتنبؤ بالاتجاهات المستقبلية...</p>
              <div className="mt-4 flex justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-400"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !analysisData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <AlertTriangle className="w-16 h-16 text-red-400 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-white mb-2">خطأ في التحليل الذكي</h3>
              <p className="text-slate-400 mb-4">{error}</p>
              <button
                onClick={loadSmartAnalysis}
                className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-colors"
              >
                إعادة المحاولة
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const { analysis } = analysisData;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
      <div className="max-w-7xl mx-auto space-y-6">

        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Brain className="w-12 h-12 text-purple-400 ml-3" />
            <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              التحليل الذكي المتقدم
            </h1>
          </div>
          <p className="text-slate-300 text-lg">🤖 يتنبأ بالاتجاهات المستقبلية ويقدم توصيات ذكية</p>
          <button
            onClick={loadSmartAnalysis}
            className="mt-4 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-6 py-2 rounded-lg transition-all duration-300 transform hover:scale-105"
          >
            <Sparkles className="w-4 h-4 inline ml-2" />
            تحديث التحليل
          </button>
        </div>

        {/* AI Insights */}
        <div className="bg-gradient-to-r from-purple-900/50 to-pink-900/50 backdrop-blur-sm border border-purple-500/30 rounded-2xl p-6">
          <div className="flex items-center mb-4">
            <Eye className="w-6 h-6 text-purple-400 ml-3" />
            <h2 className="text-xl font-bold text-white">رؤى الذكاء الاصطناعي</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {analysisData.ai_insights.map((insight, index) => (
              <div key={index} className="bg-white/5 rounded-lg p-3 border border-white/10">
                <p className="text-slate-300">{insight}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Trends & Predictions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

          {/* Current Trends */}
          <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6">
            <div className="flex items-center mb-6">
              <BarChart3 className="w-6 h-6 text-blue-400 ml-3" />
              <h3 className="text-xl font-bold text-white">الاتجاهات الحالية</h3>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-white/5 rounded-lg">
                <div className="flex items-center">
                  {getTrendIcon(analysis.trends.trend_direction)}
                  <span className="text-white font-medium mr-3">اتجاه النمو</span>
                </div>
                <div className="text-right">
                  <span className="text-2xl font-bold text-white">{analysis.trends.growth_rate.toFixed(1)}%</span>
                  <p className="text-sm text-slate-400">{analysis.trends.trend_direction}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="bg-white/5 rounded-lg p-4 text-center">
                  <p className="text-slate-400 text-sm">متوسط الإيرادات اليومية</p>
                  <p className="text-xl font-bold text-green-400">{analysis.trends.avg_daily_revenue.toLocaleString()} دج</p>
                </div>
                <div className="bg-white/5 rounded-lg p-4 text-center">
                  <p className="text-slate-400 text-sm">متوسط الأرباح اليومية</p>
                  <p className="text-xl font-bold text-purple-400">{analysis.trends.avg_daily_profit.toLocaleString()} دج</p>
                </div>
              </div>
            </div>
          </div>

          {/* Future Predictions */}
          <div className="bg-gradient-to-br from-purple-800/50 to-pink-800/50 backdrop-blur-sm border border-purple-500/30 rounded-2xl p-6">
            <div className="flex items-center mb-6">
              <Target className="w-6 h-6 text-pink-400 ml-3" />
              <h3 className="text-xl font-bold text-white">التنبؤات المستقبلية</h3>
              <div className="mr-auto bg-green-500/20 text-green-400 px-3 py-1 rounded-full text-sm">
                ثقة {analysis.predictions.confidence}%
              </div>
            </div>

            <div className="space-y-4">
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3">الأسبوع القادم</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-slate-400 text-sm">إيرادات متوقعة</p>
                    <p className="text-lg font-bold text-green-400">{analysis.predictions.next_week.revenue.toLocaleString()} دج</p>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm">أرباح متوقعة</p>
                    <p className="text-lg font-bold text-purple-400">{analysis.predictions.next_week.profit.toLocaleString()} دج</p>
                  </div>
                </div>
              </div>

              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3">الشهر القادم</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-slate-400 text-sm">إيرادات متوقعة</p>
                    <p className="text-lg font-bold text-green-400">{analysis.predictions.next_month.revenue.toLocaleString()} دج</p>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm">أرباح متوقعة</p>
                    <p className="text-lg font-bold text-purple-400">{analysis.predictions.next_month.profit.toLocaleString()} دج</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Smart KPIs */}
        <div className="bg-gradient-to-r from-cyan-900/50 to-blue-900/50 backdrop-blur-sm border border-cyan-500/30 rounded-2xl p-6">
          <div className="flex items-center mb-6">
            <Zap className="w-6 h-6 text-cyan-400 ml-3" />
            <h3 className="text-xl font-bold text-white">مؤشرات الأداء الذكية</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-20 h-20 mx-auto mb-4 relative">
                <svg className="w-20 h-20 transform -rotate-90">
                  <circle cx="40" cy="40" r="36" stroke="currentColor" strokeWidth="8" fill="none" className="text-gray-700" />
                  <circle
                    cx="40" cy="40" r="36" stroke="currentColor" strokeWidth="8" fill="none"
                    className="text-green-400"
                    strokeDasharray={`${2 * Math.PI * 36}`}
                    strokeDashoffset={`${2 * Math.PI * 36 * (1 - analysis.smart_kpis.inventory_health / 100)}`}
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-lg font-bold text-white">{Math.round(analysis.smart_kpis.inventory_health)}%</span>
                </div>
              </div>
              <h4 className="text-white font-medium">صحة المخزون</h4>
              <p className="text-slate-400 text-sm">مستوى الأمان</p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 mx-auto mb-4 relative">
                <svg className="w-20 h-20 transform -rotate-90">
                  <circle cx="40" cy="40" r="36" stroke="currentColor" strokeWidth="8" fill="none" className="text-gray-700" />
                  <circle
                    cx="40" cy="40" r="36" stroke="currentColor" strokeWidth="8" fill="none"
                    className="text-blue-400"
                    strokeDasharray={`${2 * Math.PI * 36}`}
                    strokeDashoffset={`${2 * Math.PI * 36 * (1 - analysis.smart_kpis.demand_stability / 100)}`}
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-lg font-bold text-white">{Math.round(analysis.smart_kpis.demand_stability)}%</span>
                </div>
              </div>
              <h4 className="text-white font-medium">استقرار الطلب</h4>
              <p className="text-slate-400 text-sm">ثبات المبيعات</p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 mx-auto mb-4 relative">
                <svg className="w-20 h-20 transform -rotate-90">
                  <circle cx="40" cy="40" r="36" stroke="currentColor" strokeWidth="8" fill="none" className="text-gray-700" />
                  <circle
                    cx="40" cy="40" r="36" stroke="currentColor" strokeWidth="8" fill="none"
                    className="text-purple-400"
                    strokeDasharray={`${2 * Math.PI * 36}`}
                    strokeDashoffset={`${2 * Math.PI * 36 * (1 - analysis.smart_kpis.profit_efficiency / 100)}`}
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-lg font-bold text-white">{Math.round(analysis.smart_kpis.profit_efficiency)}%</span>
                </div>
              </div>
              <h4 className="text-white font-medium">كفاءة الربح</h4>
              <p className="text-slate-400 text-sm">هامش الربحية</p>
            </div>
          </div>
        </div>

        {/* Smart Recommendations */}
        {analysis.recommendations.length > 0 && (
          <div className="bg-gradient-to-br from-orange-900/50 to-red-900/50 backdrop-blur-sm border border-orange-500/30 rounded-2xl p-6">
            <div className="flex items-center mb-6">
              <Lightbulb className="w-6 h-6 text-orange-400 ml-3" />
              <h3 className="text-xl font-bold text-white">التوصيات الذكية</h3>
            </div>

            <div className="space-y-4">
              {analysis.recommendations.map((rec, index) => (
                <div key={index} className={`bg-gradient-to-r ${getPriorityColor(rec.priority)}/20 border border-white/10 rounded-lg p-4`}>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center mb-2">
                        <span className={`bg-gradient-to-r ${getPriorityColor(rec.priority)} text-white px-2 py-1 rounded text-xs font-medium ml-3`}>
                          {rec.priority}
                        </span>
                        <span className="text-slate-400 text-sm">{rec.type}</span>
                      </div>
                      <p className="text-white font-medium mb-2">{rec.message}</p>
                      <p className="text-slate-300 text-sm mb-1">📋 الإجراء: {rec.action}</p>
                      <p className="text-slate-400 text-sm">🎯 التأثير: {rec.impact}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Top Products Analysis */}
        <div className="bg-gradient-to-br from-emerald-900/50 to-teal-900/50 backdrop-blur-sm border border-emerald-500/30 rounded-2xl p-6">
          <div className="flex items-center mb-6">
            <PieChart className="w-6 h-6 text-emerald-400 ml-3" />
            <h3 className="text-xl font-bold text-white">تحليل أداء المنتجات</h3>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-white/10">
                  <th className="text-right text-slate-400 font-medium py-3">المنتج</th>
                  <th className="text-right text-slate-400 font-medium py-3">المبيعات</th>
                  <th className="text-right text-slate-400 font-medium py-3">الطلب اليومي</th>
                  <th className="text-right text-slate-400 font-medium py-3">أيام حتى النفاد</th>
                  <th className="text-right text-slate-400 font-medium py-3">هامش الربح</th>
                </tr>
              </thead>
              <tbody>
                {analysis.top_products.slice(0, 5).map((product, index) => (
                  <tr key={index} className="border-b border-white/5">
                    <td className="py-3 text-white font-medium">{product.name}</td>
                    <td className="py-3 text-slate-300">{product.total_sold}</td>
                    <td className="py-3 text-slate-300">{product.daily_demand}</td>
                    <td className="py-3">
                      <span className={`px-2 py-1 rounded text-xs ${
                        product.days_until_stockout < 7 ? 'bg-red-500/20 text-red-400' :
                        product.days_until_stockout < 30 ? 'bg-yellow-500/20 text-yellow-400' :
                        'bg-green-500/20 text-green-400'
                      }`}>
                        {product.days_until_stockout > 999 ? '∞' : product.days_until_stockout}
                      </span>
                    </td>
                    <td className="py-3 text-emerald-400 font-medium">{product.profit_margin.toFixed(1)}%</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Customer Patterns */}
        {analysis.customer_patterns.length > 0 && (
          <div className="bg-gradient-to-br from-indigo-900/50 to-purple-900/50 backdrop-blur-sm border border-indigo-500/30 rounded-2xl p-6">
            <div className="flex items-center mb-6">
              <Clock className="w-6 h-6 text-indigo-400 ml-3" />
              <h3 className="text-xl font-bold text-white">أنماط العملاء الذكية</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {analysis.customer_patterns.map((pattern, index) => (
                <div key={index} className="bg-white/5 rounded-lg p-4 text-center">
                  <h4 className="text-white font-medium mb-2">{pattern.time}</h4>
                  <p className="text-indigo-400 text-lg font-bold">{pattern.transactions} معاملة</p>
                  <p className="text-slate-400 text-sm">متوسط القيمة: {pattern.avg_value.toLocaleString()} دج</p>
                </div>
              ))}
            </div>
          </div>
        )}

      </div>
    </div>
  );
};

export default SmartAnalysis;
